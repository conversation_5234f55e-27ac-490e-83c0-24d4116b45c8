正在读取软件包列表...
正在分析软件包的依赖关系树...
正在读取状态信息...
bison 已经是最新版 (2:3.8.2+dfsg-1build1)。
flex 已经是最新版 (2.6.4-8build2)。
下列软件包是自动安装的并且现在不需要了：
  bladerf castxml catch2 fonts-lyx fonts-mathjax freeglut3 libad9361-0
  libairspy0 libbladerf2 libcodec2-1.0 libcppunit-1.15-0 libcppunit-dev
  libeigen3-dev libfdt1 libfmt-dev libfmt8 libgmp-dev libgmpxx4ldbl
  libgnuradio-analog3.10.1 libgnuradio-audio3.10.1 libgnuradio-blocks3.10.1
  libgnuradio-channels3.10.1 libgnuradio-digital3.10.1 libgnuradio-dtv3.10.1
  libgnuradio-fec3.10.1 libgnuradio-fft3.10.1 libgnuradio-filter3.10.1
  libgnuradio-iio3.10.1 libgnuradio-network3.10.1 libgnuradio-pdu3.10.1
  libgnuradio-pmt3.10.1 libgnuradio-qtgui3.10.1 libgnuradio-runtime3.10.1
  libgnuradio-soapy3.10.1 libgnuradio-trellis3.10.1
  libgnuradio-video-sdl3.10.1 libgnuradio-vocoder3.10.1
  libgnuradio-wavelet3.10.1 libgnuradio-zeromq3.10.1 libgsl27 libgslcblas0
  libgsm1 libgsm1-dev libhackrf0 libhamlib4 libiio0 libjs-mathjax
  liblimesuite20.10-1 libmirisdr0 libnorm1 libosmosdr0 libpgm-5.3-0
  libportaudio2 libqhull-r8.0 libqt5designer5 libqt5help5 libqt5opengl5
  libqt5printsupport5 libqt5sql5 libqt5sql5-sqlite libqt5test5 libqt5xml5
  libqwt-qt5-6 librtaudio6 librte-eal22 librte-ethdev22 librte-hash22
  librte-kvargs22 librte-mbuf22 librte-mempool22 librte-meter22 librte-net22
  librte-rcu22 librte-ring22 librte-telemetry22 librtlsdr0 libsdl1.2debian
  libsoapysdr0.8 libspdlog-dev libspdlog1 libtecla1 libthrift-0.16.0
  libthrift-dev libvolk2-bin libvolk2-dev libvolk2.5 libzmq5 limesuite-udev
  pybind11-dev pyqt5-dev-tools python-matplotlib-data python3-appdirs
  python3-attr python3-brotli python3-bs4 python3-click-plugins python3-cycler
  python3-fonttools python3-fs python3-html5lib python3-kiwisolver
  python3-lxml python3-lz4 python3-matplotlib python3-mpmath python3-networkx
  python3-opengl python3-packaging python3-py python3-pygccxml python3-pyqt5
  python3-pyqt5.qtopengl python3-pyqt5.qwt python3-pyqt5.sip python3-pyqtgraph
  python3-sip python3-soupsieve python3-sympy python3-thrift python3-ufolib2
  python3-unicodedata2 python3-webencodings python3-zmq qtbase5-dev-tools
  qtchooser soapyosmo-common0.8 soapysdr-tools soapysdr0.8-module-airspy
  soapysdr0.8-module-audio soapysdr0.8-module-bladerf
  soapysdr0.8-module-hackrf soapysdr0.8-module-lms7 soapysdr0.8-module-mirisdr
  soapysdr0.8-module-osmosdr soapysdr0.8-module-redpitaya
  soapysdr0.8-module-remote soapysdr0.8-module-rfspace
  soapysdr0.8-module-rtlsdr unicode-data
使用'sudo apt autoremove'来卸载它(它们)。
升级了 0 个软件包，新安装了 0 个软件包，要卸载 0 个软件包，有 74 个软件包未被升级。
正克隆到 '/tmp/asn1c'...
注意：正在切换到 '940dd5fa9f3917913fd487b13dfddfacd0ded06e'。

您正处于分离头指针状态。您可以查看、做试验性的修改及提交，并且您可以在切换
回一个分支时，丢弃在此状态下所做的提交而不对分支造成影响。

如果您想要通过创建分支来保留在此状态下所做的提交，您可以通过在 switch 命令
中添加参数 -c 来实现（现在或稍后）。例如：

  git switch -c <新分支名>

或者撤销此操作：

  git switch -

通过将配置变量 advice.detachedHead 设置为 false 来关闭此建议

HEAD 目前位于 940dd5fa Update Makefile.am
commit 940dd5fa9f3917913fd487b13dfddfacd0ded06e
Author: Mouse <<EMAIL>>
Date:   Fri Apr 5 16:15:01 2024 -0400

    Update Makefile.am
    
    Address YACC warning about POSIX not taking string literals
autoreconf: export WARNINGS=
autoreconf: Entering directory '.'
autoreconf: configure.ac: not using Gettext
autoreconf: running: aclocal -I m4
autoreconf: configure.ac: tracing
autoreconf: configure.ac: creating directory config
autoreconf: running: libtoolize --copy
libtoolize: putting auxiliary files in AC_CONFIG_AUX_DIR, 'config'.
libtoolize: copying file 'config/ltmain.sh'
libtoolize: putting macros in AC_CONFIG_MACRO_DIRS, 'm4'.
libtoolize: copying file 'm4/libtool.m4'
libtoolize: copying file 'm4/ltoptions.m4'
libtoolize: copying file 'm4/ltsugar.m4'
libtoolize: copying file 'm4/ltversion.m4'
libtoolize: copying file 'm4/lt~obsolete.m4'
autoreconf: configure.ac: not using Intltool
autoreconf: configure.ac: not using Gtkdoc
autoreconf: running: aclocal -I m4
autoreconf: running: /usr/bin/autoconf
configure.ac:4: warning: The macro `AC_CONFIG_HEADER' is obsolete.
configure.ac:4: You should run autoupdate.
./lib/autoconf/status.m4:719: AC_CONFIG_HEADER is expanded from...
configure.ac:4: the top level
configure.ac:15: warning: AC_PROG_LEX without either yywrap or noyywrap is obsolete
./lib/autoconf/programs.m4:716: _AC_PROG_LEX is expanded from...
./lib/autoconf/programs.m4:709: AC_PROG_LEX is expanded from...
aclocal.m4:728: AM_PROG_LEX is expanded from...
configure.ac:15: the top level
configure.ac:264: warning: The macro `AC_HEADER_STDC' is obsolete.
configure.ac:264: You should run autoupdate.
./lib/autoconf/headers.m4:704: AC_HEADER_STDC is expanded from...
configure.ac:264: the top level
autoreconf: running: /usr/bin/autoheader
autoreconf: running: automake --add-missing --copy --no-force
configure.ac:17: installing 'config/ar-lib'
configure.ac:9: installing 'config/compile'
configure.ac:31: installing 'config/config.guess'
configure.ac:31: installing 'config/config.sub'
configure.ac:5: installing 'config/install-sh'
configure.ac:5: installing 'config/missing'
parallel-tests: installing 'config/test-driver'
asn1-tools/enber/Makefile.am: installing 'config/depcomp'
configure.ac: installing 'config/ylwrap'
autoreconf: Leaving directory '.'
checking for a BSD-compatible install... /usr/bin/install -c
checking whether build environment is sane... yes
checking for a race-free mkdir -p... /usr/bin/mkdir -p
checking for gawk... no
checking for mawk... mawk
checking whether make sets $(MAKE)... yes
checking whether make supports nested variables... yes
checking how to create a pax tar archive... gnutar
checking for gcc... gcc
checking whether the C compiler works... yes
checking for C compiler default output file name... a.out
checking for suffix of executables... 
checking whether we are cross compiling... no
checking for suffix of object files... o
checking whether the compiler supports GNU C... yes
checking whether gcc accepts -g... yes
checking for gcc option to enable C11 features... none needed
checking whether gcc understands -c and -o together... yes
checking whether make supports the include directive... yes (GNU style)
checking dependency style of gcc... gcc3
checking how to run the C preprocessor... gcc -E
checking whether ln -s works... yes
checking whether make sets $(MAKE)... (cached) yes
checking for bison... bison -y
checking for flex... flex
checking for lex output file root... lex.yy
checking for lex library... none needed
checking for library containing yywrap... -ll
checking whether yytext is a pointer... yes
checking for ar... ar
checking the archiver (ar) interface... ar
checking for g++... g++
checking whether the compiler supports GNU C++... yes
checking whether g++ accepts -g... yes
checking for g++ option to enable C++11 features... none needed
checking dependency style of g++... gcc3
checking for special C compiler options needed for large files... no
checking for _FILE_OFFSET_BITS value needed for large files... no
checking build system type... x86_64-pc-linux-gnu
checking host system type... x86_64-pc-linux-gnu
checking how to print strings... printf
checking for a sed that does not truncate output... /usr/bin/sed
checking for grep that handles long lines and -e... /usr/bin/grep
checking for egrep... /usr/bin/grep -E
checking for fgrep... /usr/bin/grep -F
checking for ld used by gcc... /usr/bin/ld
checking if the linker (/usr/bin/ld) is GNU ld... yes
checking for BSD- or MS-compatible name lister (nm)... /usr/bin/nm -B
checking the name lister (/usr/bin/nm -B) interface... BSD nm
checking the maximum length of command line arguments... 1572864
checking how to convert x86_64-pc-linux-gnu file names to x86_64-pc-linux-gnu format... func_convert_file_noop
checking how to convert x86_64-pc-linux-gnu file names to toolchain format... func_convert_file_noop
checking for /usr/bin/ld option to reload object files... -r
checking for objdump... objdump
checking how to recognize dependent libraries... pass_all
checking for dlltool... no
checking how to associate runtime and link libraries... printf %s\n
checking for archiver @FILE support... @
checking for strip... strip
checking for ranlib... ranlib
checking command to parse /usr/bin/nm -B output from gcc object... ok
checking for sysroot... no
checking for a working dd... /usr/bin/dd
checking how to truncate binary pipes... /usr/bin/dd bs=4096 count=1
checking for mt... mt
checking if mt is a manifest tool... no
checking for stdio.h... yes
checking for stdlib.h... yes
checking for string.h... yes
checking for inttypes.h... yes
checking for stdint.h... yes
checking for strings.h... yes
checking for sys/stat.h... yes
checking for sys/types.h... yes
checking for unistd.h... yes
checking for dlfcn.h... yes
checking for objdir... .libs
checking if gcc supports -fno-rtti -fno-exceptions... no
checking for gcc option to produce PIC... -fPIC -DPIC
checking if gcc PIC flag -fPIC -DPIC works... yes
checking if gcc static flag -static works... yes
checking if gcc supports -c -o file.o... yes
checking if gcc supports -c -o file.o... (cached) yes
checking whether the gcc linker (/usr/bin/ld -m elf_x86_64) supports shared libraries... yes
checking whether -lc should be explicitly linked in... no
checking dynamic linker characteristics... GNU/Linux ld.so
checking how to hardcode library paths into programs... immediate
checking whether stripping libraries is possible... yes
checking if libtool supports shared libraries... yes
checking whether to build shared libraries... yes
checking whether to build static libraries... yes
checking how to run the C++ preprocessor... g++ -E
checking for ld used by g++... /usr/bin/ld -m elf_x86_64
checking if the linker (/usr/bin/ld -m elf_x86_64) is GNU ld... yes
checking whether the g++ linker (/usr/bin/ld -m elf_x86_64) supports shared libraries... yes
checking for g++ option to produce PIC... -fPIC -DPIC
checking if g++ PIC flag -fPIC -DPIC works... yes
checking if g++ static flag -static works... yes
checking if g++ supports -c -o file.o... yes
checking if g++ supports -c -o file.o... (cached) yes
checking whether the g++ linker (/usr/bin/ld -m elf_x86_64) supports shared libraries... yes
checking dynamic linker characteristics... (cached) GNU/Linux ld.so
checking how to hardcode library paths into programs... immediate
checking whether make supports nested variables... (cached) yes
checking whether C compiler accepts -Wall... yes
checking whether C compiler accepts -Wcast-qual... yes
checking whether C compiler accepts -Wchar-subscripts... yes
checking whether C compiler accepts -Wmissing-prototypes... yes
checking whether C compiler accepts -Wmissing-declarations... yes
checking whether C compiler accepts -Wno-error=attributes... yes
checking whether C compiler accepts -Wno-error=cast-align... yes
checking whether C compiler accepts -Wno-error=visibility... no
checking whether C compiler accepts -Wno-error=parentheses-equality... no
checking whether C compiler accepts -std=gnu99... yes
checking whether C compiler accepts -Wno-error=unused-variable... yes
checking whether to build with code coverage support... no
checking whether C compiler accepts -std=c89... yes
checking whether C compiler accepts -Wpedantic... yes
checking whether C compiler accepts -Wno-long-long... yes
checking whether C compiler and linker accept -fsanitize=undefined... yes
checking whether C compiler accepts -fno-sanitize-recover=undefined... yes
checking whether C compiler and linker accept -fsanitize=unsigned-integer-overflow... no
checking whether C compiler accepts -fno-sanitize-recover=unsigned-integer-overflow... no
checking whether C compiler and linker accept -fsanitize=nullability... no
checking whether C compiler accepts -fno-sanitize-recover=nullability... no
checking whether C compiler accepts -fno-omit-frame-pointer... yes
checking whether C compiler and linker accept -fsanitize=address... yes
