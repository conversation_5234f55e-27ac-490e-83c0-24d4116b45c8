# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/openairinterface5g

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/openairinterface5g/cmake_targets/ran_build/build

# Include any dependencies generated for this target.
include CMakeFiles/CONFIG_LIB.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/CONFIG_LIB.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/CONFIG_LIB.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/CONFIG_LIB.dir/flags.make

CMakeFiles/CONFIG_LIB.dir/common/config/config_load_configmodule.c.o: CMakeFiles/CONFIG_LIB.dir/flags.make
CMakeFiles/CONFIG_LIB.dir/common/config/config_load_configmodule.c.o: ../../../common/config/config_load_configmodule.c
CMakeFiles/CONFIG_LIB.dir/common/config/config_load_configmodule.c.o: CMakeFiles/CONFIG_LIB.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/openairinterface5g/cmake_targets/ran_build/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/CONFIG_LIB.dir/common/config/config_load_configmodule.c.o"
	/usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/CONFIG_LIB.dir/common/config/config_load_configmodule.c.o -MF CMakeFiles/CONFIG_LIB.dir/common/config/config_load_configmodule.c.o.d -o CMakeFiles/CONFIG_LIB.dir/common/config/config_load_configmodule.c.o -c /home/<USER>/openairinterface5g/common/config/config_load_configmodule.c

CMakeFiles/CONFIG_LIB.dir/common/config/config_load_configmodule.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/CONFIG_LIB.dir/common/config/config_load_configmodule.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/openairinterface5g/common/config/config_load_configmodule.c > CMakeFiles/CONFIG_LIB.dir/common/config/config_load_configmodule.c.i

CMakeFiles/CONFIG_LIB.dir/common/config/config_load_configmodule.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/CONFIG_LIB.dir/common/config/config_load_configmodule.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/openairinterface5g/common/config/config_load_configmodule.c -o CMakeFiles/CONFIG_LIB.dir/common/config/config_load_configmodule.c.s

CMakeFiles/CONFIG_LIB.dir/common/config/config_userapi.c.o: CMakeFiles/CONFIG_LIB.dir/flags.make
CMakeFiles/CONFIG_LIB.dir/common/config/config_userapi.c.o: ../../../common/config/config_userapi.c
CMakeFiles/CONFIG_LIB.dir/common/config/config_userapi.c.o: CMakeFiles/CONFIG_LIB.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/openairinterface5g/cmake_targets/ran_build/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/CONFIG_LIB.dir/common/config/config_userapi.c.o"
	/usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/CONFIG_LIB.dir/common/config/config_userapi.c.o -MF CMakeFiles/CONFIG_LIB.dir/common/config/config_userapi.c.o.d -o CMakeFiles/CONFIG_LIB.dir/common/config/config_userapi.c.o -c /home/<USER>/openairinterface5g/common/config/config_userapi.c

CMakeFiles/CONFIG_LIB.dir/common/config/config_userapi.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/CONFIG_LIB.dir/common/config/config_userapi.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/openairinterface5g/common/config/config_userapi.c > CMakeFiles/CONFIG_LIB.dir/common/config/config_userapi.c.i

CMakeFiles/CONFIG_LIB.dir/common/config/config_userapi.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/CONFIG_LIB.dir/common/config/config_userapi.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/openairinterface5g/common/config/config_userapi.c -o CMakeFiles/CONFIG_LIB.dir/common/config/config_userapi.c.s

CMakeFiles/CONFIG_LIB.dir/common/config/config_cmdline.c.o: CMakeFiles/CONFIG_LIB.dir/flags.make
CMakeFiles/CONFIG_LIB.dir/common/config/config_cmdline.c.o: ../../../common/config/config_cmdline.c
CMakeFiles/CONFIG_LIB.dir/common/config/config_cmdline.c.o: CMakeFiles/CONFIG_LIB.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/openairinterface5g/cmake_targets/ran_build/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/CONFIG_LIB.dir/common/config/config_cmdline.c.o"
	/usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/CONFIG_LIB.dir/common/config/config_cmdline.c.o -MF CMakeFiles/CONFIG_LIB.dir/common/config/config_cmdline.c.o.d -o CMakeFiles/CONFIG_LIB.dir/common/config/config_cmdline.c.o -c /home/<USER>/openairinterface5g/common/config/config_cmdline.c

CMakeFiles/CONFIG_LIB.dir/common/config/config_cmdline.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/CONFIG_LIB.dir/common/config/config_cmdline.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/openairinterface5g/common/config/config_cmdline.c > CMakeFiles/CONFIG_LIB.dir/common/config/config_cmdline.c.i

CMakeFiles/CONFIG_LIB.dir/common/config/config_cmdline.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/CONFIG_LIB.dir/common/config/config_cmdline.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/openairinterface5g/common/config/config_cmdline.c -o CMakeFiles/CONFIG_LIB.dir/common/config/config_cmdline.c.s

# Object files for target CONFIG_LIB
CONFIG_LIB_OBJECTS = \
"CMakeFiles/CONFIG_LIB.dir/common/config/config_load_configmodule.c.o" \
"CMakeFiles/CONFIG_LIB.dir/common/config/config_userapi.c.o" \
"CMakeFiles/CONFIG_LIB.dir/common/config/config_cmdline.c.o"

# External object files for target CONFIG_LIB
CONFIG_LIB_EXTERNAL_OBJECTS = \
"/home/<USER>/openairinterface5g/cmake_targets/ran_build/build/CMakeFiles/config_internals.dir/common/config/config_common.c.o"

libCONFIG_LIB.a: CMakeFiles/CONFIG_LIB.dir/common/config/config_load_configmodule.c.o
libCONFIG_LIB.a: CMakeFiles/CONFIG_LIB.dir/common/config/config_userapi.c.o
libCONFIG_LIB.a: CMakeFiles/CONFIG_LIB.dir/common/config/config_cmdline.c.o
libCONFIG_LIB.a: CMakeFiles/config_internals.dir/common/config/config_common.c.o
libCONFIG_LIB.a: CMakeFiles/CONFIG_LIB.dir/build.make
libCONFIG_LIB.a: CMakeFiles/CONFIG_LIB.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/openairinterface5g/cmake_targets/ran_build/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking C static library libCONFIG_LIB.a"
	$(CMAKE_COMMAND) -P CMakeFiles/CONFIG_LIB.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/CONFIG_LIB.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/CONFIG_LIB.dir/build: libCONFIG_LIB.a
.PHONY : CMakeFiles/CONFIG_LIB.dir/build

CMakeFiles/CONFIG_LIB.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/CONFIG_LIB.dir/cmake_clean.cmake
.PHONY : CMakeFiles/CONFIG_LIB.dir/clean

CMakeFiles/CONFIG_LIB.dir/depend:
	cd /home/<USER>/openairinterface5g/cmake_targets/ran_build/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/openairinterface5g /home/<USER>/openairinterface5g /home/<USER>/openairinterface5g/cmake_targets/ran_build/build /home/<USER>/openairinterface5g/cmake_targets/ran_build/build /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/CMakeFiles/CONFIG_LIB.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/CONFIG_LIB.dir/depend

