# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/openairinterface5g

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/openairinterface5g/cmake_targets/ran_build/build

# Include any dependencies generated for this target.
include CMakeFiles/GTPV1U.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/GTPV1U.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/GTPV1U.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/GTPV1U.dir/flags.make

CMakeFiles/GTPV1U.dir/openair2/RRC/LTE/rrc_eNB_GTPV1U.c.o: CMakeFiles/GTPV1U.dir/flags.make
CMakeFiles/GTPV1U.dir/openair2/RRC/LTE/rrc_eNB_GTPV1U.c.o: ../../../openair2/RRC/LTE/rrc_eNB_GTPV1U.c
CMakeFiles/GTPV1U.dir/openair2/RRC/LTE/rrc_eNB_GTPV1U.c.o: CMakeFiles/GTPV1U.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/openairinterface5g/cmake_targets/ran_build/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/GTPV1U.dir/openair2/RRC/LTE/rrc_eNB_GTPV1U.c.o"
	/usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/GTPV1U.dir/openair2/RRC/LTE/rrc_eNB_GTPV1U.c.o -MF CMakeFiles/GTPV1U.dir/openair2/RRC/LTE/rrc_eNB_GTPV1U.c.o.d -o CMakeFiles/GTPV1U.dir/openair2/RRC/LTE/rrc_eNB_GTPV1U.c.o -c /home/<USER>/openairinterface5g/openair2/RRC/LTE/rrc_eNB_GTPV1U.c

CMakeFiles/GTPV1U.dir/openair2/RRC/LTE/rrc_eNB_GTPV1U.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/GTPV1U.dir/openair2/RRC/LTE/rrc_eNB_GTPV1U.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/openairinterface5g/openair2/RRC/LTE/rrc_eNB_GTPV1U.c > CMakeFiles/GTPV1U.dir/openair2/RRC/LTE/rrc_eNB_GTPV1U.c.i

CMakeFiles/GTPV1U.dir/openair2/RRC/LTE/rrc_eNB_GTPV1U.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/GTPV1U.dir/openair2/RRC/LTE/rrc_eNB_GTPV1U.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/openairinterface5g/openair2/RRC/LTE/rrc_eNB_GTPV1U.c -o CMakeFiles/GTPV1U.dir/openair2/RRC/LTE/rrc_eNB_GTPV1U.c.s

CMakeFiles/GTPV1U.dir/openair3/ocp-gtpu/gtp_itf.cpp.o: CMakeFiles/GTPV1U.dir/flags.make
CMakeFiles/GTPV1U.dir/openair3/ocp-gtpu/gtp_itf.cpp.o: ../../../openair3/ocp-gtpu/gtp_itf.cpp
CMakeFiles/GTPV1U.dir/openair3/ocp-gtpu/gtp_itf.cpp.o: CMakeFiles/GTPV1U.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/openairinterface5g/cmake_targets/ran_build/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/GTPV1U.dir/openair3/ocp-gtpu/gtp_itf.cpp.o"
	/usr/bin/ccache /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/GTPV1U.dir/openair3/ocp-gtpu/gtp_itf.cpp.o -MF CMakeFiles/GTPV1U.dir/openair3/ocp-gtpu/gtp_itf.cpp.o.d -o CMakeFiles/GTPV1U.dir/openair3/ocp-gtpu/gtp_itf.cpp.o -c /home/<USER>/openairinterface5g/openair3/ocp-gtpu/gtp_itf.cpp

CMakeFiles/GTPV1U.dir/openair3/ocp-gtpu/gtp_itf.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/GTPV1U.dir/openair3/ocp-gtpu/gtp_itf.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/openairinterface5g/openair3/ocp-gtpu/gtp_itf.cpp > CMakeFiles/GTPV1U.dir/openair3/ocp-gtpu/gtp_itf.cpp.i

CMakeFiles/GTPV1U.dir/openair3/ocp-gtpu/gtp_itf.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/GTPV1U.dir/openair3/ocp-gtpu/gtp_itf.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/openairinterface5g/openair3/ocp-gtpu/gtp_itf.cpp -o CMakeFiles/GTPV1U.dir/openair3/ocp-gtpu/gtp_itf.cpp.s

# Object files for target GTPV1U
GTPV1U_OBJECTS = \
"CMakeFiles/GTPV1U.dir/openair2/RRC/LTE/rrc_eNB_GTPV1U.c.o" \
"CMakeFiles/GTPV1U.dir/openair3/ocp-gtpu/gtp_itf.cpp.o"

# External object files for target GTPV1U
GTPV1U_EXTERNAL_OBJECTS =

libGTPV1U.a: CMakeFiles/GTPV1U.dir/openair2/RRC/LTE/rrc_eNB_GTPV1U.c.o
libGTPV1U.a: CMakeFiles/GTPV1U.dir/openair3/ocp-gtpu/gtp_itf.cpp.o
libGTPV1U.a: CMakeFiles/GTPV1U.dir/build.make
libGTPV1U.a: CMakeFiles/GTPV1U.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/openairinterface5g/cmake_targets/ran_build/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX static library libGTPV1U.a"
	$(CMAKE_COMMAND) -P CMakeFiles/GTPV1U.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/GTPV1U.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/GTPV1U.dir/build: libGTPV1U.a
.PHONY : CMakeFiles/GTPV1U.dir/build

CMakeFiles/GTPV1U.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/GTPV1U.dir/cmake_clean.cmake
.PHONY : CMakeFiles/GTPV1U.dir/clean

CMakeFiles/GTPV1U.dir/depend:
	cd /home/<USER>/openairinterface5g/cmake_targets/ran_build/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/openairinterface5g /home/<USER>/openairinterface5g /home/<USER>/openairinterface5g/cmake_targets/ran_build/build /home/<USER>/openairinterface5g/cmake_targets/ran_build/build /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/CMakeFiles/GTPV1U.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/GTPV1U.dir/depend

