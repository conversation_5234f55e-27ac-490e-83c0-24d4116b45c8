# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/openairinterface5g

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/openairinterface5g/cmake_targets/ran_build/build

# Include any dependencies generated for this target.
include CMakeFiles/HASHTABLE.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/HASHTABLE.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/HASHTABLE.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/HASHTABLE.dir/flags.make

CMakeFiles/HASHTABLE.dir/common/utils/hashtable/hashtable.c.o: CMakeFiles/HASHTABLE.dir/flags.make
CMakeFiles/HASHTABLE.dir/common/utils/hashtable/hashtable.c.o: ../../../common/utils/hashtable/hashtable.c
CMakeFiles/HASHTABLE.dir/common/utils/hashtable/hashtable.c.o: CMakeFiles/HASHTABLE.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/openairinterface5g/cmake_targets/ran_build/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/HASHTABLE.dir/common/utils/hashtable/hashtable.c.o"
	/usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/HASHTABLE.dir/common/utils/hashtable/hashtable.c.o -MF CMakeFiles/HASHTABLE.dir/common/utils/hashtable/hashtable.c.o.d -o CMakeFiles/HASHTABLE.dir/common/utils/hashtable/hashtable.c.o -c /home/<USER>/openairinterface5g/common/utils/hashtable/hashtable.c

CMakeFiles/HASHTABLE.dir/common/utils/hashtable/hashtable.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/HASHTABLE.dir/common/utils/hashtable/hashtable.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/openairinterface5g/common/utils/hashtable/hashtable.c > CMakeFiles/HASHTABLE.dir/common/utils/hashtable/hashtable.c.i

CMakeFiles/HASHTABLE.dir/common/utils/hashtable/hashtable.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/HASHTABLE.dir/common/utils/hashtable/hashtable.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/openairinterface5g/common/utils/hashtable/hashtable.c -o CMakeFiles/HASHTABLE.dir/common/utils/hashtable/hashtable.c.s

CMakeFiles/HASHTABLE.dir/common/utils/hashtable/obj_hashtable.c.o: CMakeFiles/HASHTABLE.dir/flags.make
CMakeFiles/HASHTABLE.dir/common/utils/hashtable/obj_hashtable.c.o: ../../../common/utils/hashtable/obj_hashtable.c
CMakeFiles/HASHTABLE.dir/common/utils/hashtable/obj_hashtable.c.o: CMakeFiles/HASHTABLE.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/openairinterface5g/cmake_targets/ran_build/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/HASHTABLE.dir/common/utils/hashtable/obj_hashtable.c.o"
	/usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/HASHTABLE.dir/common/utils/hashtable/obj_hashtable.c.o -MF CMakeFiles/HASHTABLE.dir/common/utils/hashtable/obj_hashtable.c.o.d -o CMakeFiles/HASHTABLE.dir/common/utils/hashtable/obj_hashtable.c.o -c /home/<USER>/openairinterface5g/common/utils/hashtable/obj_hashtable.c

CMakeFiles/HASHTABLE.dir/common/utils/hashtable/obj_hashtable.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/HASHTABLE.dir/common/utils/hashtable/obj_hashtable.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/openairinterface5g/common/utils/hashtable/obj_hashtable.c > CMakeFiles/HASHTABLE.dir/common/utils/hashtable/obj_hashtable.c.i

CMakeFiles/HASHTABLE.dir/common/utils/hashtable/obj_hashtable.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/HASHTABLE.dir/common/utils/hashtable/obj_hashtable.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/openairinterface5g/common/utils/hashtable/obj_hashtable.c -o CMakeFiles/HASHTABLE.dir/common/utils/hashtable/obj_hashtable.c.s

# Object files for target HASHTABLE
HASHTABLE_OBJECTS = \
"CMakeFiles/HASHTABLE.dir/common/utils/hashtable/hashtable.c.o" \
"CMakeFiles/HASHTABLE.dir/common/utils/hashtable/obj_hashtable.c.o"

# External object files for target HASHTABLE
HASHTABLE_EXTERNAL_OBJECTS =

libHASHTABLE.a: CMakeFiles/HASHTABLE.dir/common/utils/hashtable/hashtable.c.o
libHASHTABLE.a: CMakeFiles/HASHTABLE.dir/common/utils/hashtable/obj_hashtable.c.o
libHASHTABLE.a: CMakeFiles/HASHTABLE.dir/build.make
libHASHTABLE.a: CMakeFiles/HASHTABLE.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/openairinterface5g/cmake_targets/ran_build/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking C static library libHASHTABLE.a"
	$(CMAKE_COMMAND) -P CMakeFiles/HASHTABLE.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/HASHTABLE.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/HASHTABLE.dir/build: libHASHTABLE.a
.PHONY : CMakeFiles/HASHTABLE.dir/build

CMakeFiles/HASHTABLE.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/HASHTABLE.dir/cmake_clean.cmake
.PHONY : CMakeFiles/HASHTABLE.dir/clean

CMakeFiles/HASHTABLE.dir/depend:
	cd /home/<USER>/openairinterface5g/cmake_targets/ran_build/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/openairinterface5g /home/<USER>/openairinterface5g /home/<USER>/openairinterface5g/cmake_targets/ran_build/build /home/<USER>/openairinterface5g/cmake_targets/ran_build/build /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/CMakeFiles/HASHTABLE.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/HASHTABLE.dir/depend

