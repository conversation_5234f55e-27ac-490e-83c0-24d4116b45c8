# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DAVX2 -DCMAKE_BUILD_TYPE=\"RelWithDebInfo\" -DHAVE_ARPA_INET_H=1 -DHAVE_FCNTL_H=1 -DHAVE_GETTIMEOFDAY=1 -DHAVE_INTTYPES_H=1 -DHAVE_LIBSCTP -DHAVE_MALLOC=1 -DHAVE_MEMORY_H=1 -DHAVE_MEMSET=1 -DHAVE_NETINET_IN_H -DHAVE_SOCKET=1 -DHAVE_STDINT_H=1 -DHAVE_STDLIB_H=1 -DHAVE_STRERROR=1 -DHAVE_STRINGS_H=1 -DHAVE_STRING_H=1 -DHAVE_SYS_SOCKET_H=1 -DHAVE_SYS_STAT_H=1 -DHAVE_SYS_TIME_H=1 -DHAVE_SYS_TYPES_H=1 -DHAVE_UNISTD_H=1 -DMAX_NUM_CCs=1 -DNAS_BUILT_IN_UE -DNB_ANTENNAS_RX=4 -DNB_ANTENNAS_TX=4 -DNUMBER_OF_UE_MAX_NB_IoT=16 -DRRC_DEFAULT_RAB_IS_AM -DSTDC_HEADERS=1 -DT_TRACER

CXX_INCLUDES = -I/home/<USER>/openairinterface5g/openair3/S1AP -I/home/<USER>/openairinterface5g/openair3/NGAP -I/home/<USER>/openairinterface5g/openair2/X2AP -I/home/<USER>/openairinterface5g/radio/COMMON -I/home/<USER>/openairinterface5g/cmake_targets/ran_build/build -I/home/<USER>/openairinterface5g/executables -I/home/<USER>/openairinterface5g/openair2/COMMON -I/home/<USER>/openairinterface5g/openair2/UTIL -I/home/<USER>/openairinterface5g/openair2/UTIL/LOG -I/home/<USER>/openairinterface5g/openair3/COMMON -I/home/<USER>/openairinterface5g/openair3/UTILS -I/home/<USER>/openairinterface5g/nfapi/open-nFAPI/nfapi/public_inc -I/home/<USER>/openairinterface5g/nfapi/open-nFAPI/common/public_inc -I/home/<USER>/openairinterface5g/nfapi/open-nFAPI/pnf/public_inc -I/home/<USER>/openairinterface5g/nfapi/open-nFAPI/nfapi/inc -I/home/<USER>/openairinterface5g/nfapi/open-nFAPI/sim_common/inc -I/home/<USER>/openairinterface5g/nfapi/open-nFAPI/pnf_sim/inc -I/home/<USER>/openairinterface5g/openair1 -I/home/<USER>/openairinterface5g/openair2 -I/home/<USER>/openairinterface5g/openair3/NAS/TOOLS -I/home/<USER>/openairinterface5g/openair2/ENB_APP -I/home/<USER>/openairinterface5g/openair2/GNB_APP -I/home/<USER>/openairinterface5g/openair2/MCE_APP -I/home/<USER>/openairinterface5g/openair2/LAYER2/RLC -I/home/<USER>/openairinterface5g/openair2/LAYER2/PDCP_v10.1.0 -I/home/<USER>/openairinterface5g/openair2/RRC/LTE/MESSAGES -I/home/<USER>/openairinterface5g/openair2/RRC/LTE -I/home/<USER>/openairinterface5g/common/utils -I/home/<USER>/openairinterface5g/common/utils/collection -I/home/<USER>/openairinterface5g/common/utils/ocp_itti -I/home/<USER>/openairinterface5g/openair3/NAS/COMMON -I/home/<USER>/openairinterface5g/openair3/NAS/COMMON/API/NETWORK -I/home/<USER>/openairinterface5g/openair3/NAS/COMMON/EMM/MSG -I/home/<USER>/openairinterface5g/openair3/NAS/COMMON/ESM/MSG -I/home/<USER>/openairinterface5g/openair3/NAS/UE/ESM -I/home/<USER>/openairinterface5g/openair3/NAS/UE/EMM -I/home/<USER>/openairinterface5g/openair3/NAS/UE/API/USER -I/home/<USER>/openairinterface5g/openair3/NAS/COMMON/IES -I/home/<USER>/openairinterface5g/openair3/NAS/COMMON/UTIL -I/home/<USER>/openairinterface5g/openair3/SECU -I/home/<USER>/openairinterface5g/openair3/SCTP -I/home/<USER>/openairinterface5g/openair2/M2AP -I/home/<USER>/openairinterface5g/openair2/F1AP -I/home/<USER>/openairinterface5g/openair3/ocp-gtpu -I/home/<USER>/openairinterface5g/openair3/M3AP -I/home/<USER>/openairinterface5g/openair3/MME_APP -I/home/<USER>/openairinterface5g/openair2/UTIL/OSA -I/home/<USER>/openairinterface5g/openair2/UTIL/MEM -I/home/<USER>/openairinterface5g/openair2/UTIL/LISTS -I/home/<USER>/openairinterface5g/openair2/UTIL/FIFO -I/home/<USER>/openairinterface5g/openair2/UTIL/MATH -I/home/<USER>/openairinterface5g/openair2/UTIL/TIMER -I/home/<USER>/openairinterface5g/openair2/UTIL/OTG -I/home/<USER>/openairinterface5g/openair2/UTIL/OPT -I/home/<USER>/openairinterface5g -I/home/<USER>/openairinterface5g/common/utils/T -I/home/<USER>/openairinterface5g/cmake_targets/ran_build/build/common/utils/T -I/home/<USER>/openairinterface5g/common/utils/hashtable -I/home/<USER>/openairinterface5g/nfapi/open-nFAPI/pnf/inc -I/home/<USER>/openairinterface5g/nfapi/open-nFAPI/vnf/public_inc -I/home/<USER>/openairinterface5g/nfapi/open-nFAPI/vnf/inc -I/home/<USER>/openairinterface5g/nfapi/oai_integration -I/home/<USER>/openairinterface5g/openair2/NR_UE_PHY_INTERFACE -I/home/<USER>/openairinterface5g/openair2/LAYER2 -I/home/<USER>/openairinterface5g/openair1/SCHED_NR_UE -I/home/<USER>/openairinterface5g/openair3/ocp-gtp -I/home/<USER>/openairinterface5g/openair3/NAS/NR_UE -I/home/<USER>/openairinterface5g/openair3/NAS/UE -I/home/<USER>/openairinterface5g/openair3/NAS/UE/API/USIM -I/home/<USER>/openairinterface5g/openair3/NAS/UE/EMM/MSG -I/home/<USER>/openairinterface5g/openair3/NAS/UE/EMM/SAP -I/home/<USER>/openairinterface5g/openair3/NAS/UE/ESM/SAP -I/home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/LTE/MESSAGES -I/home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES

CXX_FLAGS =  -DSIMDE_X86_AVX_NATIVE -DSIMDE_X86_AVX_NATIVE -DSIMDE_X86_F16C_NATIVE -DSIMDE_X86_FMA_NATIVE -DSIMDE_X86_GFNI_NATIVE -DSIMDE_X86_MMX_NATIVE -DSIMDE_X86_PCLMUL_NATIVE -DSIMDE_X86_SSE2_NATIVE -DSIMDE_X86_SSE3_NATIVE -DSIMDE_X86_SSE_NATIVE -DSIMDE_X86_XOP_HAVE_COM_ -DSIMDE_X86_XOP_NATIVE -mno-avx512f -march=native -DSIMDE_X86_AVX2_NATIVE -DSIMDE_X86_VPCLMULQDQ_NATIVE -march=native  -pipe -fPIC -Wall -fno-strict-aliasing -rdynamic -Wno-packed-bitfield-compat -std=c++11  -O2 -g    -DASN_DISABLE_OER_SUPPORT -DHAVE_CONFIG_H -DHAVE_CONFIG_H_

