# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/openairinterface5g

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/openairinterface5g/cmake_targets/ran_build/build

# Include any dependencies generated for this target.
include CMakeFiles/ITTI.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/ITTI.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/ITTI.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/ITTI.dir/flags.make

CMakeFiles/ITTI.dir/common/utils/ocp_itti/intertask_interface.cpp.o: CMakeFiles/ITTI.dir/flags.make
CMakeFiles/ITTI.dir/common/utils/ocp_itti/intertask_interface.cpp.o: ../../../common/utils/ocp_itti/intertask_interface.cpp
CMakeFiles/ITTI.dir/common/utils/ocp_itti/intertask_interface.cpp.o: CMakeFiles/ITTI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/openairinterface5g/cmake_targets/ran_build/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/ITTI.dir/common/utils/ocp_itti/intertask_interface.cpp.o"
	/usr/bin/ccache /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/ITTI.dir/common/utils/ocp_itti/intertask_interface.cpp.o -MF CMakeFiles/ITTI.dir/common/utils/ocp_itti/intertask_interface.cpp.o.d -o CMakeFiles/ITTI.dir/common/utils/ocp_itti/intertask_interface.cpp.o -c /home/<USER>/openairinterface5g/common/utils/ocp_itti/intertask_interface.cpp

CMakeFiles/ITTI.dir/common/utils/ocp_itti/intertask_interface.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ITTI.dir/common/utils/ocp_itti/intertask_interface.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/openairinterface5g/common/utils/ocp_itti/intertask_interface.cpp > CMakeFiles/ITTI.dir/common/utils/ocp_itti/intertask_interface.cpp.i

CMakeFiles/ITTI.dir/common/utils/ocp_itti/intertask_interface.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ITTI.dir/common/utils/ocp_itti/intertask_interface.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/openairinterface5g/common/utils/ocp_itti/intertask_interface.cpp -o CMakeFiles/ITTI.dir/common/utils/ocp_itti/intertask_interface.cpp.s

# Object files for target ITTI
ITTI_OBJECTS = \
"CMakeFiles/ITTI.dir/common/utils/ocp_itti/intertask_interface.cpp.o"

# External object files for target ITTI
ITTI_EXTERNAL_OBJECTS =

libITTI.a: CMakeFiles/ITTI.dir/common/utils/ocp_itti/intertask_interface.cpp.o
libITTI.a: CMakeFiles/ITTI.dir/build.make
libITTI.a: CMakeFiles/ITTI.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/openairinterface5g/cmake_targets/ran_build/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX static library libITTI.a"
	$(CMAKE_COMMAND) -P CMakeFiles/ITTI.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/ITTI.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/ITTI.dir/build: libITTI.a
.PHONY : CMakeFiles/ITTI.dir/build

CMakeFiles/ITTI.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/ITTI.dir/cmake_clean.cmake
.PHONY : CMakeFiles/ITTI.dir/clean

CMakeFiles/ITTI.dir/depend:
	cd /home/<USER>/openairinterface5g/cmake_targets/ran_build/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/openairinterface5g /home/<USER>/openairinterface5g /home/<USER>/openairinterface5g/cmake_targets/ran_build/build /home/<USER>/openairinterface5g/cmake_targets/ran_build/build /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/CMakeFiles/ITTI.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/ITTI.dir/depend

