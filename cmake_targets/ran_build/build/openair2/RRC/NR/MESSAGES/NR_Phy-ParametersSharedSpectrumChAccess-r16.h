/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_Phy_ParametersSharedSpectrumChAccess_r16_H_
#define	_NR_Phy_ParametersSharedSpectrumChAccess_r16_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_Phy_ParametersSharedSpectrumChAccess_r16__ss_SINR_Meas_r16 {
	NR_Phy_ParametersSharedSpectrumChAccess_r16__ss_SINR_Meas_r16_supported	= 0
} e_NR_Phy_ParametersSharedSpectrumChAccess_r16__ss_SINR_Meas_r16;
typedef enum NR_Phy_ParametersSharedSpectrumChAccess_r16__sp_CSI_ReportPUCCH_r16 {
	NR_Phy_ParametersSharedSpectrumChAccess_r16__sp_CSI_ReportPUCCH_r16_supported	= 0
} e_NR_Phy_ParametersSharedSpectrumChAccess_r16__sp_CSI_ReportPUCCH_r16;
typedef enum NR_Phy_ParametersSharedSpectrumChAccess_r16__sp_CSI_ReportPUSCH_r16 {
	NR_Phy_ParametersSharedSpectrumChAccess_r16__sp_CSI_ReportPUSCH_r16_supported	= 0
} e_NR_Phy_ParametersSharedSpectrumChAccess_r16__sp_CSI_ReportPUSCH_r16;
typedef enum NR_Phy_ParametersSharedSpectrumChAccess_r16__dynamicSFI_r16 {
	NR_Phy_ParametersSharedSpectrumChAccess_r16__dynamicSFI_r16_supported	= 0
} e_NR_Phy_ParametersSharedSpectrumChAccess_r16__dynamicSFI_r16;
typedef enum NR_Phy_ParametersSharedSpectrumChAccess_r16__mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16__sameSymbol_r16 {
	NR_Phy_ParametersSharedSpectrumChAccess_r16__mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16__sameSymbol_r16_supported	= 0
} e_NR_Phy_ParametersSharedSpectrumChAccess_r16__mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16__sameSymbol_r16;
typedef enum NR_Phy_ParametersSharedSpectrumChAccess_r16__mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16__diffSymbol_r16 {
	NR_Phy_ParametersSharedSpectrumChAccess_r16__mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16__diffSymbol_r16_supported	= 0
} e_NR_Phy_ParametersSharedSpectrumChAccess_r16__mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16__diffSymbol_r16;
typedef enum NR_Phy_ParametersSharedSpectrumChAccess_r16__mux_SR_HARQ_ACK_PUCCH_r16 {
	NR_Phy_ParametersSharedSpectrumChAccess_r16__mux_SR_HARQ_ACK_PUCCH_r16_supported	= 0
} e_NR_Phy_ParametersSharedSpectrumChAccess_r16__mux_SR_HARQ_ACK_PUCCH_r16;
typedef enum NR_Phy_ParametersSharedSpectrumChAccess_r16__mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_r16 {
	NR_Phy_ParametersSharedSpectrumChAccess_r16__mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_r16_supported	= 0
} e_NR_Phy_ParametersSharedSpectrumChAccess_r16__mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_r16;
typedef enum NR_Phy_ParametersSharedSpectrumChAccess_r16__mux_HARQ_ACK_PUSCH_DiffSymbol_r16 {
	NR_Phy_ParametersSharedSpectrumChAccess_r16__mux_HARQ_ACK_PUSCH_DiffSymbol_r16_supported	= 0
} e_NR_Phy_ParametersSharedSpectrumChAccess_r16__mux_HARQ_ACK_PUSCH_DiffSymbol_r16;
typedef enum NR_Phy_ParametersSharedSpectrumChAccess_r16__pucch_Repetition_F1_3_4_r16 {
	NR_Phy_ParametersSharedSpectrumChAccess_r16__pucch_Repetition_F1_3_4_r16_supported	= 0
} e_NR_Phy_ParametersSharedSpectrumChAccess_r16__pucch_Repetition_F1_3_4_r16;
typedef enum NR_Phy_ParametersSharedSpectrumChAccess_r16__type1_PUSCH_RepetitionMultiSlots_r16 {
	NR_Phy_ParametersSharedSpectrumChAccess_r16__type1_PUSCH_RepetitionMultiSlots_r16_supported	= 0
} e_NR_Phy_ParametersSharedSpectrumChAccess_r16__type1_PUSCH_RepetitionMultiSlots_r16;
typedef enum NR_Phy_ParametersSharedSpectrumChAccess_r16__type2_PUSCH_RepetitionMultiSlots_r16 {
	NR_Phy_ParametersSharedSpectrumChAccess_r16__type2_PUSCH_RepetitionMultiSlots_r16_supported	= 0
} e_NR_Phy_ParametersSharedSpectrumChAccess_r16__type2_PUSCH_RepetitionMultiSlots_r16;
typedef enum NR_Phy_ParametersSharedSpectrumChAccess_r16__pusch_RepetitionMultiSlots_r16 {
	NR_Phy_ParametersSharedSpectrumChAccess_r16__pusch_RepetitionMultiSlots_r16_supported	= 0
} e_NR_Phy_ParametersSharedSpectrumChAccess_r16__pusch_RepetitionMultiSlots_r16;
typedef enum NR_Phy_ParametersSharedSpectrumChAccess_r16__pdsch_RepetitionMultiSlots_r16 {
	NR_Phy_ParametersSharedSpectrumChAccess_r16__pdsch_RepetitionMultiSlots_r16_supported	= 0
} e_NR_Phy_ParametersSharedSpectrumChAccess_r16__pdsch_RepetitionMultiSlots_r16;
typedef enum NR_Phy_ParametersSharedSpectrumChAccess_r16__downlinkSPS_r16 {
	NR_Phy_ParametersSharedSpectrumChAccess_r16__downlinkSPS_r16_supported	= 0
} e_NR_Phy_ParametersSharedSpectrumChAccess_r16__downlinkSPS_r16;
typedef enum NR_Phy_ParametersSharedSpectrumChAccess_r16__configuredUL_GrantType1_r16 {
	NR_Phy_ParametersSharedSpectrumChAccess_r16__configuredUL_GrantType1_r16_supported	= 0
} e_NR_Phy_ParametersSharedSpectrumChAccess_r16__configuredUL_GrantType1_r16;
typedef enum NR_Phy_ParametersSharedSpectrumChAccess_r16__configuredUL_GrantType2_r16 {
	NR_Phy_ParametersSharedSpectrumChAccess_r16__configuredUL_GrantType2_r16_supported	= 0
} e_NR_Phy_ParametersSharedSpectrumChAccess_r16__configuredUL_GrantType2_r16;
typedef enum NR_Phy_ParametersSharedSpectrumChAccess_r16__pre_EmptIndication_DL_r16 {
	NR_Phy_ParametersSharedSpectrumChAccess_r16__pre_EmptIndication_DL_r16_supported	= 0
} e_NR_Phy_ParametersSharedSpectrumChAccess_r16__pre_EmptIndication_DL_r16;

/* NR_Phy-ParametersSharedSpectrumChAccess-r16 */
typedef struct NR_Phy_ParametersSharedSpectrumChAccess_r16 {
	long	*ss_SINR_Meas_r16;	/* OPTIONAL */
	long	*sp_CSI_ReportPUCCH_r16;	/* OPTIONAL */
	long	*sp_CSI_ReportPUSCH_r16;	/* OPTIONAL */
	long	*dynamicSFI_r16;	/* OPTIONAL */
	struct NR_Phy_ParametersSharedSpectrumChAccess_r16__mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16 {
		long	*sameSymbol_r16;	/* OPTIONAL */
		long	*diffSymbol_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16;
	long	*mux_SR_HARQ_ACK_PUCCH_r16;	/* OPTIONAL */
	long	*mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_r16;	/* OPTIONAL */
	long	*mux_HARQ_ACK_PUSCH_DiffSymbol_r16;	/* OPTIONAL */
	long	*pucch_Repetition_F1_3_4_r16;	/* OPTIONAL */
	long	*type1_PUSCH_RepetitionMultiSlots_r16;	/* OPTIONAL */
	long	*type2_PUSCH_RepetitionMultiSlots_r16;	/* OPTIONAL */
	long	*pusch_RepetitionMultiSlots_r16;	/* OPTIONAL */
	long	*pdsch_RepetitionMultiSlots_r16;	/* OPTIONAL */
	long	*downlinkSPS_r16;	/* OPTIONAL */
	long	*configuredUL_GrantType1_r16;	/* OPTIONAL */
	long	*configuredUL_GrantType2_r16;	/* OPTIONAL */
	long	*pre_EmptIndication_DL_r16;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_Phy_ParametersSharedSpectrumChAccess_r16_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_ss_SINR_Meas_r16_2;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_sp_CSI_ReportPUCCH_r16_4;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_sp_CSI_ReportPUSCH_r16_6;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dynamicSFI_r16_8;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_sameSymbol_r16_11;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_diffSymbol_r16_13;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mux_SR_HARQ_ACK_PUCCH_r16_15;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_r16_17;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_r16_19;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pucch_Repetition_F1_3_4_r16_21;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_type1_PUSCH_RepetitionMultiSlots_r16_23;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_type2_PUSCH_RepetitionMultiSlots_r16_25;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pusch_RepetitionMultiSlots_r16_27;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pdsch_RepetitionMultiSlots_r16_29;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_downlinkSPS_r16_31;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_configuredUL_GrantType1_r16_33;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_configuredUL_GrantType2_r16_35;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pre_EmptIndication_DL_r16_37;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_Phy_ParametersSharedSpectrumChAccess_r16;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_Phy_ParametersSharedSpectrumChAccess_r16_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_Phy_ParametersSharedSpectrumChAccess_r16_1[17];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_Phy_ParametersSharedSpectrumChAccess_r16_H_ */
#include <asn_internal.h>
