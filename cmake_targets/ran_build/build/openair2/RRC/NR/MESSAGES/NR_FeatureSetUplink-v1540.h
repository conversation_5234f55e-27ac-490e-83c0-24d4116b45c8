/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_FeatureSetUplink_v1540_H_
#define	_NR_FeatureSetUplink_v1540_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_FeatureSetUplink_v1540__zeroSlotOffsetAperiodicSRS {
	NR_FeatureSetUplink_v1540__zeroSlotOffsetAperiodicSRS_supported	= 0
} e_NR_FeatureSetUplink_v1540__zeroSlotOffsetAperiodicSRS;
typedef enum NR_FeatureSetUplink_v1540__pa_PhaseDiscontinuityImpacts {
	NR_FeatureSetUplink_v1540__pa_PhaseDiscontinuityImpacts_supported	= 0
} e_NR_FeatureSetUplink_v1540__pa_PhaseDiscontinuityImpacts;
typedef enum NR_FeatureSetUplink_v1540__pusch_SeparationWithGap {
	NR_FeatureSetUplink_v1540__pusch_SeparationWithGap_supported	= 0
} e_NR_FeatureSetUplink_v1540__pusch_SeparationWithGap;
typedef enum NR_FeatureSetUplink_v1540__ul_MCS_TableAlt_DynamicIndication {
	NR_FeatureSetUplink_v1540__ul_MCS_TableAlt_DynamicIndication_supported	= 0
} e_NR_FeatureSetUplink_v1540__ul_MCS_TableAlt_DynamicIndication;

/* Forward declarations */
struct NR_ProcessingParameters;

/* NR_FeatureSetUplink-v1540 */
typedef struct NR_FeatureSetUplink_v1540 {
	long	*zeroSlotOffsetAperiodicSRS;	/* OPTIONAL */
	long	*pa_PhaseDiscontinuityImpacts;	/* OPTIONAL */
	long	*pusch_SeparationWithGap;	/* OPTIONAL */
	struct NR_FeatureSetUplink_v1540__pusch_ProcessingType2 {
		struct NR_ProcessingParameters	*scs_15kHz;	/* OPTIONAL */
		struct NR_ProcessingParameters	*scs_30kHz;	/* OPTIONAL */
		struct NR_ProcessingParameters	*scs_60kHz;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *pusch_ProcessingType2;
	long	*ul_MCS_TableAlt_DynamicIndication;	/* OPTIONAL */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_FeatureSetUplink_v1540_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_zeroSlotOffsetAperiodicSRS_2;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pa_PhaseDiscontinuityImpacts_4;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pusch_SeparationWithGap_6;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_ul_MCS_TableAlt_DynamicIndication_12;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_FeatureSetUplink_v1540;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_FeatureSetUplink_v1540_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_FeatureSetUplink_v1540_1[5];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_ProcessingParameters.h"

#endif	/* _NR_FeatureSetUplink_v1540_H_ */
#include <asn_internal.h>
