/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_MBSInterestIndication_r17_IEs_H_
#define	_NR_MBSInterestIndication_r17_IEs_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <OCTET_STRING.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_MBSInterestIndication_r17_IEs__mbs_Priority_r17 {
	NR_MBSInterestIndication_r17_IEs__mbs_Priority_r17_true	= 0
} e_NR_MBSInterestIndication_r17_IEs__mbs_Priority_r17;

/* Forward declarations */
struct NR_CarrierFreqListMBS_r17;
struct NR_MBS_ServiceList_r17;

/* NR_MBSInterestIndication-r17-IEs */
typedef struct NR_MBSInterestIndication_r17_IEs {
	struct NR_CarrierFreqListMBS_r17	*mbs_FreqList_r17;	/* OPTIONAL */
	long	*mbs_Priority_r17;	/* OPTIONAL */
	struct NR_MBS_ServiceList_r17	*mbs_ServiceList_r17;	/* OPTIONAL */
	OCTET_STRING_t	*lateNonCriticalExtension;	/* OPTIONAL */
	struct NR_MBSInterestIndication_r17_IEs__nonCriticalExtension {
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *nonCriticalExtension;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_MBSInterestIndication_r17_IEs_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mbs_Priority_r17_3;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_MBSInterestIndication_r17_IEs;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_MBSInterestIndication_r17_IEs_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_MBSInterestIndication_r17_IEs_1[5];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_CarrierFreqListMBS-r17.h"
#include "NR_MBS-ServiceList-r17.h"

#endif	/* _NR_MBSInterestIndication_r17_IEs_H_ */
#include <asn_internal.h>
