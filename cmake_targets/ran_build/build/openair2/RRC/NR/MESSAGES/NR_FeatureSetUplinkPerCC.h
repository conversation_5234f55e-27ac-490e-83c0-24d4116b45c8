/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_FeatureSetUplinkPerCC_H_
#define	_NR_FeatureSetUplinkPerCC_H_


#include <asn_application.h>

/* Including external dependencies */
#include "NR_SubcarrierSpacing.h"
#include "NR_SupportedBandwidth.h"
#include <NativeEnumerated.h>
#include "NR_MIMO-LayersUL.h"
#include "NR_ModulationOrder.h"
#include <NativeInteger.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_FeatureSetUplinkPerCC__channelBW_90mhz {
	NR_FeatureSetUplinkPerCC__channelBW_90mhz_supported	= 0
} e_NR_FeatureSetUplinkPerCC__channelBW_90mhz;

/* NR_FeatureSetUplinkPerCC */
typedef struct NR_FeatureSetUplinkPerCC {
	NR_SubcarrierSpacing_t	 supportedSubcarrierSpacingUL;
	NR_SupportedBandwidth_t	 supportedBandwidthUL;
	long	*channelBW_90mhz;	/* OPTIONAL */
	struct NR_FeatureSetUplinkPerCC__mimo_CB_PUSCH {
		NR_MIMO_LayersUL_t	*maxNumberMIMO_LayersCB_PUSCH;	/* OPTIONAL */
		long	 maxNumberSRS_ResourcePerSet;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *mimo_CB_PUSCH;
	NR_MIMO_LayersUL_t	*maxNumberMIMO_LayersNonCB_PUSCH;	/* OPTIONAL */
	NR_ModulationOrder_t	*supportedModulationOrderUL;	/* OPTIONAL */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_FeatureSetUplinkPerCC_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_channelBW_90mhz_4;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_FeatureSetUplinkPerCC;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_FeatureSetUplinkPerCC_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_FeatureSetUplinkPerCC_1[6];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_FeatureSetUplinkPerCC_H_ */
#include <asn_internal.h>
