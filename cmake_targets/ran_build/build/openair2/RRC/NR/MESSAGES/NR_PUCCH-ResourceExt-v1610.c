/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_PUCCH-ResourceExt-v1610.h"

static int
memb_NR_scs15_constraint_4(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 9L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_scs30_constraint_4(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 4L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_rb_SetIndex_r16_constraint_2(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 4L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_interlace1_v1610_constraint_7(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 9L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_nrofPRBs_r17_constraint_20(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 16L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_scs15_constr_5 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  9 }	/* (0..9) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_scs30_constr_6 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  4 }	/* (0..4) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_interlace0_r16_constr_4 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_rb_SetIndex_r16_constr_3 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  4 }	/* (0..4) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_occ_Length_v1610_constr_10 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_occ_Index_v1610_constr_13 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_interlace1_v1610_constr_8 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  9 }	/* (0..9) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_format_v1610_constr_7 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_nrofPRBs_r17_constr_21 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (1..16) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pucch_RepetitionNrofSlots_r17_constr_22 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static asn_TYPE_member_t asn_MBR_NR_interlace0_r16_4[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PUCCH_ResourceExt_v1610__interlaceAllocation_r16__interlace0_r16, choice.scs15),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_scs15_constr_5,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_scs15_constraint_4
		},
		0, 0, /* No default value */
		"scs15"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PUCCH_ResourceExt_v1610__interlaceAllocation_r16__interlace0_r16, choice.scs30),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_scs30_constr_6,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_scs30_constraint_4
		},
		0, 0, /* No default value */
		"scs30"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_NR_interlace0_r16_tag2el_4[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* scs15 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* scs30 */
};
static asn_CHOICE_specifics_t asn_SPC_NR_interlace0_r16_specs_4 = {
	sizeof(struct NR_PUCCH_ResourceExt_v1610__interlaceAllocation_r16__interlace0_r16),
	offsetof(struct NR_PUCCH_ResourceExt_v1610__interlaceAllocation_r16__interlace0_r16, _asn_ctx),
	offsetof(struct NR_PUCCH_ResourceExt_v1610__interlaceAllocation_r16__interlace0_r16, present),
	sizeof(((struct NR_PUCCH_ResourceExt_v1610__interlaceAllocation_r16__interlace0_r16 *)0)->present),
	asn_MAP_NR_interlace0_r16_tag2el_4,
	2,	/* Count of tags in the map */
	0, 0,
	-1	/* Extensions start */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_interlace0_r16_4 = {
	"interlace0-r16",
	"interlace0-r16",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_interlace0_r16_constr_4,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		CHOICE_constraint
	},
	asn_MBR_NR_interlace0_r16_4,
	2,	/* Elements count */
	&asn_SPC_NR_interlace0_r16_specs_4	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_interlaceAllocation_r16_2[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PUCCH_ResourceExt_v1610__interlaceAllocation_r16, rb_SetIndex_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_rb_SetIndex_r16_constr_3,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_rb_SetIndex_r16_constraint_2
		},
		0, 0, /* No default value */
		"rb-SetIndex-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PUCCH_ResourceExt_v1610__interlaceAllocation_r16, interlace0_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_interlace0_r16_4,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"interlace0-r16"
		},
};
static const ber_tlv_tag_t asn_DEF_NR_interlaceAllocation_r16_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_interlaceAllocation_r16_tag2el_2[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* rb-SetIndex-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* interlace0-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_interlaceAllocation_r16_specs_2 = {
	sizeof(struct NR_PUCCH_ResourceExt_v1610__interlaceAllocation_r16),
	offsetof(struct NR_PUCCH_ResourceExt_v1610__interlaceAllocation_r16, _asn_ctx),
	asn_MAP_NR_interlaceAllocation_r16_tag2el_2,
	2,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_interlaceAllocation_r16_2 = {
	"interlaceAllocation-r16",
	"interlaceAllocation-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_interlaceAllocation_r16_tags_2,
	sizeof(asn_DEF_NR_interlaceAllocation_r16_tags_2)
		/sizeof(asn_DEF_NR_interlaceAllocation_r16_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_interlaceAllocation_r16_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_interlaceAllocation_r16_tags_2)
		/sizeof(asn_DEF_NR_interlaceAllocation_r16_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_interlaceAllocation_r16_2,
	2,	/* Elements count */
	&asn_SPC_NR_interlaceAllocation_r16_specs_2	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_occ_Length_v1610_value2enum_10[] = {
	{ 0,	2,	"n2" },
	{ 1,	2,	"n4" }
};
static const unsigned int asn_MAP_NR_occ_Length_v1610_enum2value_10[] = {
	0,	/* n2(0) */
	1	/* n4(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_occ_Length_v1610_specs_10 = {
	asn_MAP_NR_occ_Length_v1610_value2enum_10,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_occ_Length_v1610_enum2value_10,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_occ_Length_v1610_tags_10[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_occ_Length_v1610_10 = {
	"occ-Length-v1610",
	"occ-Length-v1610",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_occ_Length_v1610_tags_10,
	sizeof(asn_DEF_NR_occ_Length_v1610_tags_10)
		/sizeof(asn_DEF_NR_occ_Length_v1610_tags_10[0]) - 1, /* 1 */
	asn_DEF_NR_occ_Length_v1610_tags_10,	/* Same as above */
	sizeof(asn_DEF_NR_occ_Length_v1610_tags_10)
		/sizeof(asn_DEF_NR_occ_Length_v1610_tags_10[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_occ_Length_v1610_constr_10,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_occ_Length_v1610_specs_10	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_occ_Index_v1610_value2enum_13[] = {
	{ 0,	2,	"n0" },
	{ 1,	2,	"n1" },
	{ 2,	2,	"n2" },
	{ 3,	2,	"n3" }
};
static const unsigned int asn_MAP_NR_occ_Index_v1610_enum2value_13[] = {
	0,	/* n0(0) */
	1,	/* n1(1) */
	2,	/* n2(2) */
	3	/* n3(3) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_occ_Index_v1610_specs_13 = {
	asn_MAP_NR_occ_Index_v1610_value2enum_13,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_occ_Index_v1610_enum2value_13,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_occ_Index_v1610_tags_13[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_occ_Index_v1610_13 = {
	"occ-Index-v1610",
	"occ-Index-v1610",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_occ_Index_v1610_tags_13,
	sizeof(asn_DEF_NR_occ_Index_v1610_tags_13)
		/sizeof(asn_DEF_NR_occ_Index_v1610_tags_13[0]) - 1, /* 1 */
	asn_DEF_NR_occ_Index_v1610_tags_13,	/* Same as above */
	sizeof(asn_DEF_NR_occ_Index_v1610_tags_13)
		/sizeof(asn_DEF_NR_occ_Index_v1610_tags_13[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_occ_Index_v1610_constr_13,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_occ_Index_v1610_specs_13	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_occ_v1610_9[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_PUCCH_ResourceExt_v1610__format_v1610__occ_v1610, occ_Length_v1610),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_occ_Length_v1610_10,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"occ-Length-v1610"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PUCCH_ResourceExt_v1610__format_v1610__occ_v1610, occ_Index_v1610),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_occ_Index_v1610_13,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"occ-Index-v1610"
		},
};
static const int asn_MAP_NR_occ_v1610_oms_9[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_occ_v1610_tags_9[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_occ_v1610_tag2el_9[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* occ-Length-v1610 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* occ-Index-v1610 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_occ_v1610_specs_9 = {
	sizeof(struct NR_PUCCH_ResourceExt_v1610__format_v1610__occ_v1610),
	offsetof(struct NR_PUCCH_ResourceExt_v1610__format_v1610__occ_v1610, _asn_ctx),
	asn_MAP_NR_occ_v1610_tag2el_9,
	2,	/* Count of tags in the map */
	asn_MAP_NR_occ_v1610_oms_9,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_occ_v1610_9 = {
	"occ-v1610",
	"occ-v1610",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_occ_v1610_tags_9,
	sizeof(asn_DEF_NR_occ_v1610_tags_9)
		/sizeof(asn_DEF_NR_occ_v1610_tags_9[0]) - 1, /* 1 */
	asn_DEF_NR_occ_v1610_tags_9,	/* Same as above */
	sizeof(asn_DEF_NR_occ_v1610_tags_9)
		/sizeof(asn_DEF_NR_occ_v1610_tags_9[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_occ_v1610_9,
	2,	/* Elements count */
	&asn_SPC_NR_occ_v1610_specs_9	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_format_v1610_7[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PUCCH_ResourceExt_v1610__format_v1610, choice.interlace1_v1610),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_interlace1_v1610_constr_8,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_interlace1_v1610_constraint_7
		},
		0, 0, /* No default value */
		"interlace1-v1610"
		},
	{ ATF_POINTER, 0, offsetof(struct NR_PUCCH_ResourceExt_v1610__format_v1610, choice.occ_v1610),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_occ_v1610_9,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"occ-v1610"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_NR_format_v1610_tag2el_7[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* interlace1-v1610 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* occ-v1610 */
};
static asn_CHOICE_specifics_t asn_SPC_NR_format_v1610_specs_7 = {
	sizeof(struct NR_PUCCH_ResourceExt_v1610__format_v1610),
	offsetof(struct NR_PUCCH_ResourceExt_v1610__format_v1610, _asn_ctx),
	offsetof(struct NR_PUCCH_ResourceExt_v1610__format_v1610, present),
	sizeof(((struct NR_PUCCH_ResourceExt_v1610__format_v1610 *)0)->present),
	asn_MAP_NR_format_v1610_tag2el_7,
	2,	/* Count of tags in the map */
	0, 0,
	-1	/* Extensions start */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_format_v1610_7 = {
	"format-v1610",
	"format-v1610",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_format_v1610_constr_7,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		CHOICE_constraint
	},
	asn_MBR_NR_format_v1610_7,
	2,	/* Elements count */
	&asn_SPC_NR_format_v1610_specs_7	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_formatExt_v1700_20[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PUCCH_ResourceExt_v1610__ext1__formatExt_v1700, nrofPRBs_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_nrofPRBs_r17_constr_21,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_nrofPRBs_r17_constraint_20
		},
		0, 0, /* No default value */
		"nrofPRBs-r17"
		},
};
static const ber_tlv_tag_t asn_DEF_NR_formatExt_v1700_tags_20[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_formatExt_v1700_tag2el_20[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* nrofPRBs-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_formatExt_v1700_specs_20 = {
	sizeof(struct NR_PUCCH_ResourceExt_v1610__ext1__formatExt_v1700),
	offsetof(struct NR_PUCCH_ResourceExt_v1610__ext1__formatExt_v1700, _asn_ctx),
	asn_MAP_NR_formatExt_v1700_tag2el_20,
	1,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_formatExt_v1700_20 = {
	"formatExt-v1700",
	"formatExt-v1700",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_formatExt_v1700_tags_20,
	sizeof(asn_DEF_NR_formatExt_v1700_tags_20)
		/sizeof(asn_DEF_NR_formatExt_v1700_tags_20[0]) - 1, /* 1 */
	asn_DEF_NR_formatExt_v1700_tags_20,	/* Same as above */
	sizeof(asn_DEF_NR_formatExt_v1700_tags_20)
		/sizeof(asn_DEF_NR_formatExt_v1700_tags_20[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_formatExt_v1700_20,
	1,	/* Elements count */
	&asn_SPC_NR_formatExt_v1700_specs_20	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pucch_RepetitionNrofSlots_r17_value2enum_22[] = {
	{ 0,	2,	"n1" },
	{ 1,	2,	"n2" },
	{ 2,	2,	"n4" },
	{ 3,	2,	"n8" }
};
static const unsigned int asn_MAP_NR_pucch_RepetitionNrofSlots_r17_enum2value_22[] = {
	0,	/* n1(0) */
	1,	/* n2(1) */
	2,	/* n4(2) */
	3	/* n8(3) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pucch_RepetitionNrofSlots_r17_specs_22 = {
	asn_MAP_NR_pucch_RepetitionNrofSlots_r17_value2enum_22,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pucch_RepetitionNrofSlots_r17_enum2value_22,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pucch_RepetitionNrofSlots_r17_tags_22[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pucch_RepetitionNrofSlots_r17_22 = {
	"pucch-RepetitionNrofSlots-r17",
	"pucch-RepetitionNrofSlots-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pucch_RepetitionNrofSlots_r17_tags_22,
	sizeof(asn_DEF_NR_pucch_RepetitionNrofSlots_r17_tags_22)
		/sizeof(asn_DEF_NR_pucch_RepetitionNrofSlots_r17_tags_22[0]) - 1, /* 1 */
	asn_DEF_NR_pucch_RepetitionNrofSlots_r17_tags_22,	/* Same as above */
	sizeof(asn_DEF_NR_pucch_RepetitionNrofSlots_r17_tags_22)
		/sizeof(asn_DEF_NR_pucch_RepetitionNrofSlots_r17_tags_22[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pucch_RepetitionNrofSlots_r17_constr_22,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pucch_RepetitionNrofSlots_r17_specs_22	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext1_19[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_PUCCH_ResourceExt_v1610__ext1, formatExt_v1700),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_formatExt_v1700_20,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"formatExt-v1700"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PUCCH_ResourceExt_v1610__ext1, pucch_RepetitionNrofSlots_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pucch_RepetitionNrofSlots_r17_22,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pucch-RepetitionNrofSlots-r17"
		},
};
static const int asn_MAP_NR_ext1_oms_19[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_19[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_19[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* formatExt-v1700 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* pucch-RepetitionNrofSlots-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_19 = {
	sizeof(struct NR_PUCCH_ResourceExt_v1610__ext1),
	offsetof(struct NR_PUCCH_ResourceExt_v1610__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_19,
	2,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_19,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_19 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_19,
	sizeof(asn_DEF_NR_ext1_tags_19)
		/sizeof(asn_DEF_NR_ext1_tags_19[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_19,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_19)
		/sizeof(asn_DEF_NR_ext1_tags_19[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_19,
	2,	/* Elements count */
	&asn_SPC_NR_ext1_specs_19	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_PUCCH_ResourceExt_v1610_1[] = {
	{ ATF_POINTER, 3, offsetof(struct NR_PUCCH_ResourceExt_v1610, interlaceAllocation_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_interlaceAllocation_r16_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"interlaceAllocation-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_PUCCH_ResourceExt_v1610, format_v1610),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_format_v1610_7,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"format-v1610"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PUCCH_ResourceExt_v1610, ext1),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		0,
		&asn_DEF_NR_ext1_19,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
};
static const int asn_MAP_NR_PUCCH_ResourceExt_v1610_oms_1[] = { 0, 1, 2 };
static const ber_tlv_tag_t asn_DEF_NR_PUCCH_ResourceExt_v1610_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_PUCCH_ResourceExt_v1610_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* interlaceAllocation-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* format-v1610 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* ext1 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_PUCCH_ResourceExt_v1610_specs_1 = {
	sizeof(struct NR_PUCCH_ResourceExt_v1610),
	offsetof(struct NR_PUCCH_ResourceExt_v1610, _asn_ctx),
	asn_MAP_NR_PUCCH_ResourceExt_v1610_tag2el_1,
	3,	/* Count of tags in the map */
	asn_MAP_NR_PUCCH_ResourceExt_v1610_oms_1,	/* Optional members */
	2, 1,	/* Root/Additions */
	2,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_PUCCH_ResourceExt_v1610 = {
	"PUCCH-ResourceExt-v1610",
	"PUCCH-ResourceExt-v1610",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_PUCCH_ResourceExt_v1610_tags_1,
	sizeof(asn_DEF_NR_PUCCH_ResourceExt_v1610_tags_1)
		/sizeof(asn_DEF_NR_PUCCH_ResourceExt_v1610_tags_1[0]), /* 1 */
	asn_DEF_NR_PUCCH_ResourceExt_v1610_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_PUCCH_ResourceExt_v1610_tags_1)
		/sizeof(asn_DEF_NR_PUCCH_ResourceExt_v1610_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_PUCCH_ResourceExt_v1610_1,
	3,	/* Elements count */
	&asn_SPC_NR_PUCCH_ResourceExt_v1610_specs_1	/* Additional specs */
};

