/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_FeatureSetUplink-v1640.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_scs_15kHz_r16_constr_5 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_scs_30kHz_r16_constr_9 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_scs_60kHz_r16_constr_13 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_scs_15kHz_r16_value2enum_5[] = {
	{ 0,	4,	"set1" },
	{ 1,	4,	"set2" },
	{ 2,	4,	"set3" }
};
static const unsigned int asn_MAP_NR_scs_15kHz_r16_enum2value_5[] = {
	0,	/* set1(0) */
	1,	/* set2(1) */
	2	/* set3(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_scs_15kHz_r16_specs_5 = {
	asn_MAP_NR_scs_15kHz_r16_value2enum_5,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_scs_15kHz_r16_enum2value_5,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_scs_15kHz_r16_tags_5[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_scs_15kHz_r16_5 = {
	"scs-15kHz-r16",
	"scs-15kHz-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_scs_15kHz_r16_tags_5,
	sizeof(asn_DEF_NR_scs_15kHz_r16_tags_5)
		/sizeof(asn_DEF_NR_scs_15kHz_r16_tags_5[0]) - 1, /* 1 */
	asn_DEF_NR_scs_15kHz_r16_tags_5,	/* Same as above */
	sizeof(asn_DEF_NR_scs_15kHz_r16_tags_5)
		/sizeof(asn_DEF_NR_scs_15kHz_r16_tags_5[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_scs_15kHz_r16_constr_5,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_scs_15kHz_r16_specs_5	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_scs_30kHz_r16_value2enum_9[] = {
	{ 0,	4,	"set1" },
	{ 1,	4,	"set2" },
	{ 2,	4,	"set3" }
};
static const unsigned int asn_MAP_NR_scs_30kHz_r16_enum2value_9[] = {
	0,	/* set1(0) */
	1,	/* set2(1) */
	2	/* set3(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_scs_30kHz_r16_specs_9 = {
	asn_MAP_NR_scs_30kHz_r16_value2enum_9,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_scs_30kHz_r16_enum2value_9,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_scs_30kHz_r16_tags_9[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_scs_30kHz_r16_9 = {
	"scs-30kHz-r16",
	"scs-30kHz-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_scs_30kHz_r16_tags_9,
	sizeof(asn_DEF_NR_scs_30kHz_r16_tags_9)
		/sizeof(asn_DEF_NR_scs_30kHz_r16_tags_9[0]) - 1, /* 1 */
	asn_DEF_NR_scs_30kHz_r16_tags_9,	/* Same as above */
	sizeof(asn_DEF_NR_scs_30kHz_r16_tags_9)
		/sizeof(asn_DEF_NR_scs_30kHz_r16_tags_9[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_scs_30kHz_r16_constr_9,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_scs_30kHz_r16_specs_9	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_scs_60kHz_r16_value2enum_13[] = {
	{ 0,	4,	"set1" },
	{ 1,	4,	"set2" },
	{ 2,	4,	"set3" }
};
static const unsigned int asn_MAP_NR_scs_60kHz_r16_enum2value_13[] = {
	0,	/* set1(0) */
	1,	/* set2(1) */
	2	/* set3(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_scs_60kHz_r16_specs_13 = {
	asn_MAP_NR_scs_60kHz_r16_value2enum_13,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_scs_60kHz_r16_enum2value_13,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_scs_60kHz_r16_tags_13[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_scs_60kHz_r16_13 = {
	"scs-60kHz-r16",
	"scs-60kHz-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_scs_60kHz_r16_tags_13,
	sizeof(asn_DEF_NR_scs_60kHz_r16_tags_13)
		/sizeof(asn_DEF_NR_scs_60kHz_r16_tags_13[0]) - 1, /* 1 */
	asn_DEF_NR_scs_60kHz_r16_tags_13,	/* Same as above */
	sizeof(asn_DEF_NR_scs_60kHz_r16_tags_13)
		/sizeof(asn_DEF_NR_scs_60kHz_r16_tags_13[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_scs_60kHz_r16_constr_13,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_scs_60kHz_r16_specs_13	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16_4[] = {
	{ ATF_POINTER, 3, offsetof(struct NR_FeatureSetUplink_v1640__offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16, scs_15kHz_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_scs_15kHz_r16_5,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs-15kHz-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_FeatureSetUplink_v1640__offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16, scs_30kHz_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_scs_30kHz_r16_9,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs-30kHz-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_FeatureSetUplink_v1640__offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16, scs_60kHz_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_scs_60kHz_r16_13,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs-60kHz-r16"
		},
};
static const int asn_MAP_NR_offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16_oms_4[] = { 0, 1, 2 };
static const ber_tlv_tag_t asn_DEF_NR_offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16_tags_4[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16_tag2el_4[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* scs-15kHz-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* scs-30kHz-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* scs-60kHz-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16_specs_4 = {
	sizeof(struct NR_FeatureSetUplink_v1640__offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16),
	offsetof(struct NR_FeatureSetUplink_v1640__offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16, _asn_ctx),
	asn_MAP_NR_offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16_tag2el_4,
	3,	/* Count of tags in the map */
	asn_MAP_NR_offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16_oms_4,	/* Optional members */
	3, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16_4 = {
	"offsetSRS-CB-PUSCH-PDCCH-MonitorAnyOccWithSpanGap-fr1-r16",
	"offsetSRS-CB-PUSCH-PDCCH-MonitorAnyOccWithSpanGap-fr1-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16_tags_4,
	sizeof(asn_DEF_NR_offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16_tags_4)
		/sizeof(asn_DEF_NR_offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16_tags_4[0]) - 1, /* 1 */
	asn_DEF_NR_offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16_tags_4,	/* Same as above */
	sizeof(asn_DEF_NR_offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16_tags_4)
		/sizeof(asn_DEF_NR_offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16_tags_4[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16_4,
	3,	/* Elements count */
	&asn_SPC_NR_offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16_specs_4	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_FeatureSetUplink_v1640_1[] = {
	{ ATF_POINTER, 3, offsetof(struct NR_FeatureSetUplink_v1640, twoHARQ_ACK_Codebook_type1_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_SubSlot_Config_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"twoHARQ-ACK-Codebook-type1-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_FeatureSetUplink_v1640, twoHARQ_ACK_Codebook_type2_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_SubSlot_Config_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"twoHARQ-ACK-Codebook-type2-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_FeatureSetUplink_v1640, offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		0,
		&asn_DEF_NR_offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16_4,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"offsetSRS-CB-PUSCH-PDCCH-MonitorAnyOccWithSpanGap-fr1-r16"
		},
};
static const int asn_MAP_NR_FeatureSetUplink_v1640_oms_1[] = { 0, 1, 2 };
static const ber_tlv_tag_t asn_DEF_NR_FeatureSetUplink_v1640_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_FeatureSetUplink_v1640_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* twoHARQ-ACK-Codebook-type1-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* twoHARQ-ACK-Codebook-type2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* offsetSRS-CB-PUSCH-PDCCH-MonitorAnyOccWithSpanGap-fr1-r16 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_FeatureSetUplink_v1640_specs_1 = {
	sizeof(struct NR_FeatureSetUplink_v1640),
	offsetof(struct NR_FeatureSetUplink_v1640, _asn_ctx),
	asn_MAP_NR_FeatureSetUplink_v1640_tag2el_1,
	3,	/* Count of tags in the map */
	asn_MAP_NR_FeatureSetUplink_v1640_oms_1,	/* Optional members */
	3, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_FeatureSetUplink_v1640 = {
	"FeatureSetUplink-v1640",
	"FeatureSetUplink-v1640",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_FeatureSetUplink_v1640_tags_1,
	sizeof(asn_DEF_NR_FeatureSetUplink_v1640_tags_1)
		/sizeof(asn_DEF_NR_FeatureSetUplink_v1640_tags_1[0]), /* 1 */
	asn_DEF_NR_FeatureSetUplink_v1640_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_FeatureSetUplink_v1640_tags_1)
		/sizeof(asn_DEF_NR_FeatureSetUplink_v1640_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_FeatureSetUplink_v1640_1,
	3,	/* Elements count */
	&asn_SPC_NR_FeatureSetUplink_v1640_specs_1	/* Additional specs */
};

