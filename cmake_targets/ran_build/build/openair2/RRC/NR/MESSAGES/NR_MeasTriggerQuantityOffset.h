/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_MeasTriggerQuantityOffset_H_
#define	_NR_MeasTriggerQuantityOffset_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include <constr_CHOICE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_MeasTriggerQuantityOffset_PR {
	NR_MeasTriggerQuantityOffset_PR_NOTHING,	/* No components present */
	NR_MeasTriggerQuantityOffset_PR_rsrp,
	NR_MeasTriggerQuantityOffset_PR_rsrq,
	NR_MeasTriggerQuantityOffset_PR_sinr
} NR_MeasTriggerQuantityOffset_PR;

/* NR_MeasTriggerQuantityOffset */
typedef struct NR_MeasTriggerQuantityOffset {
	NR_MeasTriggerQuantityOffset_PR present;
	union NR_MeasTriggerQuantityOffset_u {
		long	 rsrp;
		long	 rsrq;
		long	 sinr;
	} choice;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_MeasTriggerQuantityOffset_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_NR_MeasTriggerQuantityOffset;
extern asn_CHOICE_specifics_t asn_SPC_NR_MeasTriggerQuantityOffset_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_MeasTriggerQuantityOffset_1[3];
extern asn_per_constraints_t asn_PER_type_NR_MeasTriggerQuantityOffset_constr_1;

#ifdef __cplusplus
}
#endif

#endif	/* _NR_MeasTriggerQuantityOffset_H_ */
#include <asn_internal.h>
