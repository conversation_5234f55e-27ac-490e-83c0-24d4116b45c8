/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_MIB_H_
#define	_NR_MIB_H_


#include <asn_application.h>

/* Including external dependencies */
#include <BIT_STRING.h>
#include <NativeEnumerated.h>
#include <NativeInteger.h>
#include "NR_PDCCH-ConfigSIB1.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_MIB__subCarrierSpacingCommon {
	NR_MIB__subCarrierSpacingCommon_scs15or60	= 0,
	NR_MIB__subCarrierSpacingCommon_scs30or120	= 1
} e_NR_MIB__subCarrierSpacingCommon;
typedef enum NR_MIB__dmrs_TypeA_Position {
	NR_MIB__dmrs_TypeA_Position_pos2	= 0,
	NR_MIB__dmrs_TypeA_Position_pos3	= 1
} e_NR_MIB__dmrs_TypeA_Position;
typedef enum NR_MIB__cellBarred {
	NR_MIB__cellBarred_barred	= 0,
	NR_MIB__cellBarred_notBarred	= 1
} e_NR_MIB__cellBarred;
typedef enum NR_MIB__intraFreqReselection {
	NR_MIB__intraFreqReselection_allowed	= 0,
	NR_MIB__intraFreqReselection_notAllowed	= 1
} e_NR_MIB__intraFreqReselection;

/* NR_MIB */
typedef struct NR_MIB {
	BIT_STRING_t	 systemFrameNumber;
	long	 subCarrierSpacingCommon;
	long	 ssb_SubcarrierOffset;
	long	 dmrs_TypeA_Position;
	NR_PDCCH_ConfigSIB1_t	 pdcch_ConfigSIB1;
	long	 cellBarred;
	long	 intraFreqReselection;
	BIT_STRING_t	 spare;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_MIB_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_subCarrierSpacingCommon_3;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dmrs_TypeA_Position_7;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_cellBarred_11;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_intraFreqReselection_14;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_MIB;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_MIB_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_MIB_1[8];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_MIB_H_ */
#include <asn_internal.h>
