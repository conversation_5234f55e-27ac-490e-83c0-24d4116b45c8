/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_RRCReconfiguration_v1610_IEs_H_
#define	_NR_RRCReconfiguration_v1610_IEs_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <OCTET_STRING.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_RRCReconfiguration_v1610_IEs__daps_SourceRelease_r16 {
	NR_RRCReconfiguration_v1610_IEs__daps_SourceRelease_r16_true	= 0
} e_NR_RRCReconfiguration_v1610_IEs__daps_SourceRelease_r16;

/* Forward declarations */
struct NR_OtherConfig_v1610;
struct NR_SetupRelease_BAP_Config_r16;
struct NR_IAB_IP_AddressConfigurationList_r16;
struct NR_ConditionalReconfiguration_r16;
struct NR_SetupRelease_T316_r16;
struct NR_SetupRelease_NeedForGapsConfigNR_r16;
struct NR_SetupRelease_OnDemandSIB_Request_r16;
struct NR_SetupRelease_SL_ConfigDedicatedNR_r16;
struct NR_SetupRelease_SL_ConfigDedicatedEUTRA_Info_r16;
struct NR_SSB_MTC;
struct NR_RRCReconfiguration_v1700_IEs;

/* NR_RRCReconfiguration-v1610-IEs */
typedef struct NR_RRCReconfiguration_v1610_IEs {
	struct NR_OtherConfig_v1610	*otherConfig_v1610;	/* OPTIONAL */
	struct NR_SetupRelease_BAP_Config_r16	*bap_Config_r16;	/* OPTIONAL */
	struct NR_IAB_IP_AddressConfigurationList_r16	*iab_IP_AddressConfigurationList_r16;	/* OPTIONAL */
	struct NR_ConditionalReconfiguration_r16	*conditionalReconfiguration_r16;	/* OPTIONAL */
	long	*daps_SourceRelease_r16;	/* OPTIONAL */
	struct NR_SetupRelease_T316_r16	*t316_r16;	/* OPTIONAL */
	struct NR_SetupRelease_NeedForGapsConfigNR_r16	*needForGapsConfigNR_r16;	/* OPTIONAL */
	struct NR_SetupRelease_OnDemandSIB_Request_r16	*onDemandSIB_Request_r16;	/* OPTIONAL */
	OCTET_STRING_t	*dedicatedPosSysInfoDelivery_r16;	/* OPTIONAL */
	struct NR_SetupRelease_SL_ConfigDedicatedNR_r16	*sl_ConfigDedicatedNR_r16;	/* OPTIONAL */
	struct NR_SetupRelease_SL_ConfigDedicatedEUTRA_Info_r16	*sl_ConfigDedicatedEUTRA_Info_r16;	/* OPTIONAL */
	struct NR_SSB_MTC	*targetCellSMTC_SCG_r16;	/* OPTIONAL */
	struct NR_RRCReconfiguration_v1700_IEs	*nonCriticalExtension;	/* OPTIONAL */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_RRCReconfiguration_v1610_IEs_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_daps_SourceRelease_r16_6;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_RRCReconfiguration_v1610_IEs;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_RRCReconfiguration_v1610_IEs_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_RRCReconfiguration_v1610_IEs_1[13];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_OtherConfig-v1610.h"
#include "NR_SetupRelease.h"
#include "NR_IAB-IP-AddressConfigurationList-r16.h"
#include "NR_ConditionalReconfiguration-r16.h"
#include "NR_SSB-MTC.h"
#include "NR_RRCReconfiguration-v1700-IEs.h"

#endif	/* _NR_RRCReconfiguration_v1610_IEs_H_ */
#include <asn_internal.h>
