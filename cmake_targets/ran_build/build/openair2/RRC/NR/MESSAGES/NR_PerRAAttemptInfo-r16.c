/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_PerRAAttemptInfo-r16.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_fallbackToFourStepRA_r17_constr_6 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_fallbackToFourStepRA_r17_value2enum_6[] = {
	{ 0,	4,	"true" }
};
static const unsigned int asn_MAP_NR_fallbackToFourStepRA_r17_enum2value_6[] = {
	0	/* true(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_fallbackToFourStepRA_r17_specs_6 = {
	asn_MAP_NR_fallbackToFourStepRA_r17_value2enum_6,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_fallbackToFourStepRA_r17_enum2value_6,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_fallbackToFourStepRA_r17_tags_6[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_fallbackToFourStepRA_r17_6 = {
	"fallbackToFourStepRA-r17",
	"fallbackToFourStepRA-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_fallbackToFourStepRA_r17_tags_6,
	sizeof(asn_DEF_NR_fallbackToFourStepRA_r17_tags_6)
		/sizeof(asn_DEF_NR_fallbackToFourStepRA_r17_tags_6[0]) - 1, /* 1 */
	asn_DEF_NR_fallbackToFourStepRA_r17_tags_6,	/* Same as above */
	sizeof(asn_DEF_NR_fallbackToFourStepRA_r17_tags_6)
		/sizeof(asn_DEF_NR_fallbackToFourStepRA_r17_tags_6[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_fallbackToFourStepRA_r17_constr_6,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_fallbackToFourStepRA_r17_specs_6	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext1_5[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_PerRAAttemptInfo_r16__ext1, fallbackToFourStepRA_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_fallbackToFourStepRA_r17_6,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"fallbackToFourStepRA-r17"
		},
};
static const int asn_MAP_NR_ext1_oms_5[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_5[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_5[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* fallbackToFourStepRA-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_5 = {
	sizeof(struct NR_PerRAAttemptInfo_r16__ext1),
	offsetof(struct NR_PerRAAttemptInfo_r16__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_5,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_5,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_5 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_5,
	sizeof(asn_DEF_NR_ext1_tags_5)
		/sizeof(asn_DEF_NR_ext1_tags_5[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_5,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_5)
		/sizeof(asn_DEF_NR_ext1_tags_5[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_5,
	1,	/* Elements count */
	&asn_SPC_NR_ext1_specs_5	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_PerRAAttemptInfo_r16_1[] = {
	{ ATF_POINTER, 3, offsetof(struct NR_PerRAAttemptInfo_r16, contentionDetected_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_BOOLEAN,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"contentionDetected-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_PerRAAttemptInfo_r16, dlRSRPAboveThreshold_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_BOOLEAN,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dlRSRPAboveThreshold-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PerRAAttemptInfo_r16, ext1),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		0,
		&asn_DEF_NR_ext1_5,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
};
static const int asn_MAP_NR_PerRAAttemptInfo_r16_oms_1[] = { 0, 1, 2 };
static const ber_tlv_tag_t asn_DEF_NR_PerRAAttemptInfo_r16_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_PerRAAttemptInfo_r16_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* contentionDetected-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* dlRSRPAboveThreshold-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* ext1 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_PerRAAttemptInfo_r16_specs_1 = {
	sizeof(struct NR_PerRAAttemptInfo_r16),
	offsetof(struct NR_PerRAAttemptInfo_r16, _asn_ctx),
	asn_MAP_NR_PerRAAttemptInfo_r16_tag2el_1,
	3,	/* Count of tags in the map */
	asn_MAP_NR_PerRAAttemptInfo_r16_oms_1,	/* Optional members */
	2, 1,	/* Root/Additions */
	2,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_PerRAAttemptInfo_r16 = {
	"PerRAAttemptInfo-r16",
	"PerRAAttemptInfo-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_PerRAAttemptInfo_r16_tags_1,
	sizeof(asn_DEF_NR_PerRAAttemptInfo_r16_tags_1)
		/sizeof(asn_DEF_NR_PerRAAttemptInfo_r16_tags_1[0]), /* 1 */
	asn_DEF_NR_PerRAAttemptInfo_r16_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_PerRAAttemptInfo_r16_tags_1)
		/sizeof(asn_DEF_NR_PerRAAttemptInfo_r16_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_PerRAAttemptInfo_r16_1,
	3,	/* Elements count */
	&asn_SPC_NR_PerRAAttemptInfo_r16_specs_1	/* Additional specs */
};

