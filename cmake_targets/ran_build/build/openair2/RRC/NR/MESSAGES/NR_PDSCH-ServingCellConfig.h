/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_PDSCH_ServingCellConfig_H_
#define	_NR_PDSCH_ServingCellConfig_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include "NR_ServCellIndex.h"
#include <NativeInteger.h>
#include <BOOLEAN.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_PDSCH_ServingCellConfig__xOverhead {
	NR_PDSCH_ServingCellConfig__xOverhead_xOh6	= 0,
	NR_PDSCH_ServingCellConfig__xOverhead_xOh12	= 1,
	NR_PDSCH_ServingCellConfig__xOverhead_xOh18	= 2
} e_NR_PDSCH_ServingCellConfig__xOverhead;
typedef enum NR_PDSCH_ServingCellConfig__nrofHARQ_ProcessesForPDSCH {
	NR_PDSCH_ServingCellConfig__nrofHARQ_ProcessesForPDSCH_n2	= 0,
	NR_PDSCH_ServingCellConfig__nrofHARQ_ProcessesForPDSCH_n4	= 1,
	NR_PDSCH_ServingCellConfig__nrofHARQ_ProcessesForPDSCH_n6	= 2,
	NR_PDSCH_ServingCellConfig__nrofHARQ_ProcessesForPDSCH_n10	= 3,
	NR_PDSCH_ServingCellConfig__nrofHARQ_ProcessesForPDSCH_n12	= 4,
	NR_PDSCH_ServingCellConfig__nrofHARQ_ProcessesForPDSCH_n16	= 5
} e_NR_PDSCH_ServingCellConfig__nrofHARQ_ProcessesForPDSCH;
typedef enum NR_PDSCH_ServingCellConfig__ext3__nrofHARQ_ProcessesForPDSCH_v1700 {
	NR_PDSCH_ServingCellConfig__ext3__nrofHARQ_ProcessesForPDSCH_v1700_n32	= 0
} e_NR_PDSCH_ServingCellConfig__ext3__nrofHARQ_ProcessesForPDSCH_v1700;

/* Forward declarations */
struct NR_SetupRelease_PDSCH_CodeBlockGroupTransmission;
struct NR_SetupRelease_PDSCH_CodeBlockGroupTransmissionList_r16;
struct NR_SetupRelease_DownlinkHARQ_FeedbackDisabled_r17;

/* NR_PDSCH-ServingCellConfig */
typedef struct NR_PDSCH_ServingCellConfig {
	struct NR_SetupRelease_PDSCH_CodeBlockGroupTransmission	*codeBlockGroupTransmission;	/* OPTIONAL */
	long	*xOverhead;	/* OPTIONAL */
	long	*nrofHARQ_ProcessesForPDSCH;	/* OPTIONAL */
	NR_ServCellIndex_t	*pucch_Cell;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	struct NR_PDSCH_ServingCellConfig__ext1 {
		long	*maxMIMO_Layers;	/* OPTIONAL */
		BOOLEAN_t	*processingType2Enabled;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext1;
	struct NR_PDSCH_ServingCellConfig__ext2 {
		struct NR_SetupRelease_PDSCH_CodeBlockGroupTransmissionList_r16	*pdsch_CodeBlockGroupTransmissionList_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext2;
	struct NR_PDSCH_ServingCellConfig__ext3 {
		struct NR_SetupRelease_DownlinkHARQ_FeedbackDisabled_r17	*downlinkHARQ_FeedbackDisabled_r17;	/* OPTIONAL */
		long	*nrofHARQ_ProcessesForPDSCH_v1700;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext3;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_PDSCH_ServingCellConfig_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_xOverhead_3;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_nrofHARQ_ProcessesForPDSCH_7;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_nrofHARQ_ProcessesForPDSCH_v1700_23;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_PDSCH_ServingCellConfig;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_PDSCH_ServingCellConfig_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_PDSCH_ServingCellConfig_1[7];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_SetupRelease.h"

#endif	/* _NR_PDSCH_ServingCellConfig_H_ */
#include <asn_internal.h>
