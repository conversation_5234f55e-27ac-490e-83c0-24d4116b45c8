/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_PUSCH_ConfigCommon_H_
#define	_NR_PUSCH_ConfigCommon_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <NativeInteger.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_PUSCH_ConfigCommon__groupHoppingEnabledTransformPrecoding {
	NR_PUSCH_ConfigCommon__groupHoppingEnabledTransformPrecoding_enabled	= 0
} e_NR_PUSCH_ConfigCommon__groupHoppingEnabledTransformPrecoding;

/* Forward declarations */
struct NR_PUSCH_TimeDomainResourceAllocationList;

/* NR_PUSCH-ConfigCommon */
typedef struct NR_PUSCH_ConfigCommon {
	long	*groupHoppingEnabledTransformPrecoding;	/* OPTIONAL */
	struct NR_PUSCH_TimeDomainResourceAllocationList	*pusch_TimeDomainAllocationList;	/* OPTIONAL */
	long	*msg3_DeltaPreamble;	/* OPTIONAL */
	long	*p0_NominalWithGrant;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_PUSCH_ConfigCommon_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_groupHoppingEnabledTransformPrecoding_2;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_PUSCH_ConfigCommon;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_PUSCH_ConfigCommon_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_PUSCH_ConfigCommon_1[4];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_PUSCH-TimeDomainResourceAllocationList.h"

#endif	/* _NR_PUSCH_ConfigCommon_H_ */
#include <asn_internal.h>
