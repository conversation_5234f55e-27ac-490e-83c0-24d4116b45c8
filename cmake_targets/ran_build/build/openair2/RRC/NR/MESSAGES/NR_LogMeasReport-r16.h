/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_LogMeasReport_r16_H_
#define	_NR_LogMeasReport_r16_H_


#include <asn_application.h>

/* Including external dependencies */
#include "NR_AbsoluteTimeInfo-r16.h"
#include "NR_TraceReference-r16.h"
#include <OCTET_STRING.h>
#include "NR_LogMeasInfoList-r16.h"
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_LogMeasReport_r16__logMeasAvailable_r16 {
	NR_LogMeasReport_r16__logMeasAvailable_r16_true	= 0
} e_NR_LogMeasReport_r16__logMeasAvailable_r16;
typedef enum NR_LogMeasReport_r16__logMeasAvailableBT_r16 {
	NR_LogMeasReport_r16__logMeasAvailableBT_r16_true	= 0
} e_NR_LogMeasReport_r16__logMeasAvailableBT_r16;
typedef enum NR_LogMeasReport_r16__logMeasAvailableWLAN_r16 {
	NR_LogMeasReport_r16__logMeasAvailableWLAN_r16_true	= 0
} e_NR_LogMeasReport_r16__logMeasAvailableWLAN_r16;

/* NR_LogMeasReport-r16 */
typedef struct NR_LogMeasReport_r16 {
	NR_AbsoluteTimeInfo_r16_t	 absoluteTimeStamp_r16;
	NR_TraceReference_r16_t	 traceReference_r16;
	OCTET_STRING_t	 traceRecordingSessionRef_r16;
	OCTET_STRING_t	 tce_Id_r16;
	NR_LogMeasInfoList_r16_t	 logMeasInfoList_r16;
	long	*logMeasAvailable_r16;	/* OPTIONAL */
	long	*logMeasAvailableBT_r16;	/* OPTIONAL */
	long	*logMeasAvailableWLAN_r16;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_LogMeasReport_r16_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_logMeasAvailable_r16_7;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_logMeasAvailableBT_r16_9;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_logMeasAvailableWLAN_r16_11;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_LogMeasReport_r16;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_LogMeasReport_r16_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_LogMeasReport_r16_1[8];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_LogMeasReport_r16_H_ */
#include <asn_internal.h>
