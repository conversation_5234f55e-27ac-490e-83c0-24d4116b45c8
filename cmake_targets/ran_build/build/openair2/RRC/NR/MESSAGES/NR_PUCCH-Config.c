/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_PUCCH-Config.h"

static int
memb_NativeInteger_constraint_20(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 15L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_resourceToAddModListExt_v1610_constraint_28(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 128UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_numberOfBitsForPUCCH_ResourceIndicatorDCI_1_2_r16_constraint_28(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 3L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_spatialRelationInfoToAddModListSizeExt_v1610_constraint_28(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 56UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_spatialRelationInfoToReleaseListSizeExt_v1610_constraint_28(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 56UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_spatialRelationInfoToAddModListExt_v1610_constraint_28(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 64UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_spatialRelationInfoToReleaseListExt_v1610_constraint_28(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 64UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_resourceGroupToAddModList_r16_constraint_28(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 4UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_resourceGroupToReleaseList_r16_constraint_28(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 4UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_schedulingRequestResourceToAddModListExt_v1610_constraint_28(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 8UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_powerControlSetInfoToAddModList_r17_constraint_59(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 8UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_powerControlSetInfoToReleaseList_r17_constraint_59(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 8UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_schedulingRequestResourceToAddModListExt_v1700_constraint_59(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 8UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_resourceSetToAddModList_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 4UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_resourceSetToReleaseList_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 4UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_resourceToAddModList_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 128UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_resourceToReleaseList_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 128UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_schedulingRequestResourceToAddModList_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 8UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_schedulingRequestResourceToReleaseList_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 8UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_multi_CSI_PUCCH_ResourceList_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 2UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_dl_DataToUL_ACK_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 8UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_spatialRelationInfoToAddModList_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 8UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_spatialRelationInfoToReleaseList_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 8UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_resourceSetToAddModList_constr_2 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 2,  2,  1,  4 }	/* (SIZE(1..4)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_resourceSetToReleaseList_constr_4 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 2,  2,  1,  4 }	/* (SIZE(1..4)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_resourceToAddModList_constr_6 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 7,  7,  1,  128 }	/* (SIZE(1..128)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_resourceToReleaseList_constr_8 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 7,  7,  1,  128 }	/* (SIZE(1..128)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_schedulingRequestResourceToAddModList_constr_14 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (SIZE(1..8)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_schedulingRequestResourceToReleaseList_constr_16 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (SIZE(1..8)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_multi_CSI_PUCCH_ResourceList_constr_18 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 1,  1,  1,  2 }	/* (SIZE(1..2)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_Member_constr_21 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  15 }	/* (0..15) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dl_DataToUL_ACK_constr_20 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (SIZE(1..8)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_spatialRelationInfoToAddModList_constr_22 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (SIZE(1..8)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_spatialRelationInfoToReleaseList_constr_24 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (SIZE(1..8)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_resourceToAddModListExt_v1610_constr_29 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 7,  7,  1,  128 }	/* (SIZE(1..128)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_normalCP_r16_constr_34 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_extendedCP_r16_constr_37 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_subslotLengthForPUCCH_r16_constr_33 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dmrs_UplinkTransformPrecodingPUCCH_r16_constr_42 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_spatialRelationInfoToAddModListSizeExt_v1610_constr_44 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 6,  6,  1,  56 }	/* (SIZE(1..56)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_spatialRelationInfoToReleaseListSizeExt_v1610_constr_46 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 6,  6,  1,  56 }	/* (SIZE(1..56)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_spatialRelationInfoToAddModListExt_v1610_constr_48 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 6,  6,  1,  64 }	/* (SIZE(1..64)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_spatialRelationInfoToReleaseListExt_v1610_constr_50 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 6,  6,  1,  64 }	/* (SIZE(1..64)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_resourceGroupToAddModList_r16_constr_52 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 2,  2,  1,  4 }	/* (SIZE(1..4)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_resourceGroupToReleaseList_r16_constr_54 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 2,  2,  1,  4 }	/* (SIZE(1..4)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_schedulingRequestResourceToAddModListExt_v1610_constr_57 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (SIZE(1..8)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_resourceToAddModListExt_v1610_constr_29 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 7,  7,  1,  128 }	/* (SIZE(1..128)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_numberOfBitsForPUCCH_ResourceIndicatorDCI_1_2_r16_constr_41 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_spatialRelationInfoToAddModListSizeExt_v1610_constr_44 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 6,  6,  1,  56 }	/* (SIZE(1..56)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_spatialRelationInfoToReleaseListSizeExt_v1610_constr_46 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 6,  6,  1,  56 }	/* (SIZE(1..56)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_spatialRelationInfoToAddModListExt_v1610_constr_48 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 6,  6,  1,  64 }	/* (SIZE(1..64)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_spatialRelationInfoToReleaseListExt_v1610_constr_50 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 6,  6,  1,  64 }	/* (SIZE(1..64)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_resourceGroupToAddModList_r16_constr_52 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 2,  2,  1,  4 }	/* (SIZE(1..4)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_resourceGroupToReleaseList_r16_constr_54 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 2,  2,  1,  4 }	/* (SIZE(1..4)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_schedulingRequestResourceToAddModListExt_v1610_constr_57 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (SIZE(1..8)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_mappingPattern_r17_constr_65 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_powerControlSetInfoToAddModList_r17_constr_68 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (SIZE(1..8)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_powerControlSetInfoToReleaseList_r17_constr_70 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (SIZE(1..8)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_secondTPCFieldDCI_1_1_r17_constr_72 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_secondTPCFieldDCI_1_2_r17_constr_74 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_schedulingRequestResourceToAddModListExt_v1700_constr_79 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (SIZE(1..8)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_powerControlSetInfoToAddModList_r17_constr_68 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (SIZE(1..8)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_powerControlSetInfoToReleaseList_r17_constr_70 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (SIZE(1..8)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_schedulingRequestResourceToAddModListExt_v1700_constr_79 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (SIZE(1..8)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_resourceSetToAddModList_constr_2 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 2,  2,  1,  4 }	/* (SIZE(1..4)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_resourceSetToReleaseList_constr_4 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 2,  2,  1,  4 }	/* (SIZE(1..4)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_resourceToAddModList_constr_6 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 7,  7,  1,  128 }	/* (SIZE(1..128)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_resourceToReleaseList_constr_8 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 7,  7,  1,  128 }	/* (SIZE(1..128)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_schedulingRequestResourceToAddModList_constr_14 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (SIZE(1..8)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_schedulingRequestResourceToReleaseList_constr_16 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (SIZE(1..8)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_multi_CSI_PUCCH_ResourceList_constr_18 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 1,  1,  1,  2 }	/* (SIZE(1..2)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_dl_DataToUL_ACK_constr_20 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (SIZE(1..8)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_spatialRelationInfoToAddModList_constr_22 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (SIZE(1..8)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_spatialRelationInfoToReleaseList_constr_24 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (SIZE(1..8)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static asn_TYPE_member_t asn_MBR_NR_resourceSetToAddModList_2[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_PUCCH_ResourceSet,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_resourceSetToAddModList_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_resourceSetToAddModList_specs_2 = {
	sizeof(struct NR_PUCCH_Config__resourceSetToAddModList),
	offsetof(struct NR_PUCCH_Config__resourceSetToAddModList, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_resourceSetToAddModList_2 = {
	"resourceSetToAddModList",
	"resourceSetToAddModList",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_resourceSetToAddModList_tags_2,
	sizeof(asn_DEF_NR_resourceSetToAddModList_tags_2)
		/sizeof(asn_DEF_NR_resourceSetToAddModList_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_resourceSetToAddModList_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_resourceSetToAddModList_tags_2)
		/sizeof(asn_DEF_NR_resourceSetToAddModList_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_resourceSetToAddModList_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_resourceSetToAddModList_2,
	1,	/* Single element */
	&asn_SPC_NR_resourceSetToAddModList_specs_2	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_resourceSetToReleaseList_4[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (2 << 2)),
		0,
		&asn_DEF_NR_PUCCH_ResourceSetId,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_resourceSetToReleaseList_tags_4[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_resourceSetToReleaseList_specs_4 = {
	sizeof(struct NR_PUCCH_Config__resourceSetToReleaseList),
	offsetof(struct NR_PUCCH_Config__resourceSetToReleaseList, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_resourceSetToReleaseList_4 = {
	"resourceSetToReleaseList",
	"resourceSetToReleaseList",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_resourceSetToReleaseList_tags_4,
	sizeof(asn_DEF_NR_resourceSetToReleaseList_tags_4)
		/sizeof(asn_DEF_NR_resourceSetToReleaseList_tags_4[0]) - 1, /* 1 */
	asn_DEF_NR_resourceSetToReleaseList_tags_4,	/* Same as above */
	sizeof(asn_DEF_NR_resourceSetToReleaseList_tags_4)
		/sizeof(asn_DEF_NR_resourceSetToReleaseList_tags_4[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_resourceSetToReleaseList_constr_4,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_resourceSetToReleaseList_4,
	1,	/* Single element */
	&asn_SPC_NR_resourceSetToReleaseList_specs_4	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_resourceToAddModList_6[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_PUCCH_Resource,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_resourceToAddModList_tags_6[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_resourceToAddModList_specs_6 = {
	sizeof(struct NR_PUCCH_Config__resourceToAddModList),
	offsetof(struct NR_PUCCH_Config__resourceToAddModList, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_resourceToAddModList_6 = {
	"resourceToAddModList",
	"resourceToAddModList",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_resourceToAddModList_tags_6,
	sizeof(asn_DEF_NR_resourceToAddModList_tags_6)
		/sizeof(asn_DEF_NR_resourceToAddModList_tags_6[0]) - 1, /* 1 */
	asn_DEF_NR_resourceToAddModList_tags_6,	/* Same as above */
	sizeof(asn_DEF_NR_resourceToAddModList_tags_6)
		/sizeof(asn_DEF_NR_resourceToAddModList_tags_6[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_resourceToAddModList_constr_6,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_resourceToAddModList_6,
	1,	/* Single element */
	&asn_SPC_NR_resourceToAddModList_specs_6	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_resourceToReleaseList_8[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (2 << 2)),
		0,
		&asn_DEF_NR_PUCCH_ResourceId,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_resourceToReleaseList_tags_8[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_resourceToReleaseList_specs_8 = {
	sizeof(struct NR_PUCCH_Config__resourceToReleaseList),
	offsetof(struct NR_PUCCH_Config__resourceToReleaseList, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_resourceToReleaseList_8 = {
	"resourceToReleaseList",
	"resourceToReleaseList",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_resourceToReleaseList_tags_8,
	sizeof(asn_DEF_NR_resourceToReleaseList_tags_8)
		/sizeof(asn_DEF_NR_resourceToReleaseList_tags_8[0]) - 1, /* 1 */
	asn_DEF_NR_resourceToReleaseList_tags_8,	/* Same as above */
	sizeof(asn_DEF_NR_resourceToReleaseList_tags_8)
		/sizeof(asn_DEF_NR_resourceToReleaseList_tags_8[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_resourceToReleaseList_constr_8,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_resourceToReleaseList_8,
	1,	/* Single element */
	&asn_SPC_NR_resourceToReleaseList_specs_8	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_schedulingRequestResourceToAddModList_14[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_SchedulingRequestResourceConfig,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_schedulingRequestResourceToAddModList_tags_14[] = {
	(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_schedulingRequestResourceToAddModList_specs_14 = {
	sizeof(struct NR_PUCCH_Config__schedulingRequestResourceToAddModList),
	offsetof(struct NR_PUCCH_Config__schedulingRequestResourceToAddModList, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_schedulingRequestResourceToAddModList_14 = {
	"schedulingRequestResourceToAddModList",
	"schedulingRequestResourceToAddModList",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_schedulingRequestResourceToAddModList_tags_14,
	sizeof(asn_DEF_NR_schedulingRequestResourceToAddModList_tags_14)
		/sizeof(asn_DEF_NR_schedulingRequestResourceToAddModList_tags_14[0]) - 1, /* 1 */
	asn_DEF_NR_schedulingRequestResourceToAddModList_tags_14,	/* Same as above */
	sizeof(asn_DEF_NR_schedulingRequestResourceToAddModList_tags_14)
		/sizeof(asn_DEF_NR_schedulingRequestResourceToAddModList_tags_14[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_schedulingRequestResourceToAddModList_constr_14,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_schedulingRequestResourceToAddModList_14,
	1,	/* Single element */
	&asn_SPC_NR_schedulingRequestResourceToAddModList_specs_14	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_schedulingRequestResourceToReleaseList_16[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (2 << 2)),
		0,
		&asn_DEF_NR_SchedulingRequestResourceId,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_schedulingRequestResourceToReleaseList_tags_16[] = {
	(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_schedulingRequestResourceToReleaseList_specs_16 = {
	sizeof(struct NR_PUCCH_Config__schedulingRequestResourceToReleaseList),
	offsetof(struct NR_PUCCH_Config__schedulingRequestResourceToReleaseList, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_schedulingRequestResourceToReleaseList_16 = {
	"schedulingRequestResourceToReleaseList",
	"schedulingRequestResourceToReleaseList",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_schedulingRequestResourceToReleaseList_tags_16,
	sizeof(asn_DEF_NR_schedulingRequestResourceToReleaseList_tags_16)
		/sizeof(asn_DEF_NR_schedulingRequestResourceToReleaseList_tags_16[0]) - 1, /* 1 */
	asn_DEF_NR_schedulingRequestResourceToReleaseList_tags_16,	/* Same as above */
	sizeof(asn_DEF_NR_schedulingRequestResourceToReleaseList_tags_16)
		/sizeof(asn_DEF_NR_schedulingRequestResourceToReleaseList_tags_16[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_schedulingRequestResourceToReleaseList_constr_16,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_schedulingRequestResourceToReleaseList_16,
	1,	/* Single element */
	&asn_SPC_NR_schedulingRequestResourceToReleaseList_specs_16	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_multi_CSI_PUCCH_ResourceList_18[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (2 << 2)),
		0,
		&asn_DEF_NR_PUCCH_ResourceId,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_multi_CSI_PUCCH_ResourceList_tags_18[] = {
	(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_multi_CSI_PUCCH_ResourceList_specs_18 = {
	sizeof(struct NR_PUCCH_Config__multi_CSI_PUCCH_ResourceList),
	offsetof(struct NR_PUCCH_Config__multi_CSI_PUCCH_ResourceList, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_multi_CSI_PUCCH_ResourceList_18 = {
	"multi-CSI-PUCCH-ResourceList",
	"multi-CSI-PUCCH-ResourceList",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_multi_CSI_PUCCH_ResourceList_tags_18,
	sizeof(asn_DEF_NR_multi_CSI_PUCCH_ResourceList_tags_18)
		/sizeof(asn_DEF_NR_multi_CSI_PUCCH_ResourceList_tags_18[0]) - 1, /* 1 */
	asn_DEF_NR_multi_CSI_PUCCH_ResourceList_tags_18,	/* Same as above */
	sizeof(asn_DEF_NR_multi_CSI_PUCCH_ResourceList_tags_18)
		/sizeof(asn_DEF_NR_multi_CSI_PUCCH_ResourceList_tags_18[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_multi_CSI_PUCCH_ResourceList_constr_18,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_multi_CSI_PUCCH_ResourceList_18,
	1,	/* Single element */
	&asn_SPC_NR_multi_CSI_PUCCH_ResourceList_specs_18	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_dl_DataToUL_ACK_20[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (2 << 2)),
		0,
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_Member_constr_21,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NativeInteger_constraint_20
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_dl_DataToUL_ACK_tags_20[] = {
	(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_dl_DataToUL_ACK_specs_20 = {
	sizeof(struct NR_PUCCH_Config__dl_DataToUL_ACK),
	offsetof(struct NR_PUCCH_Config__dl_DataToUL_ACK, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dl_DataToUL_ACK_20 = {
	"dl-DataToUL-ACK",
	"dl-DataToUL-ACK",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_dl_DataToUL_ACK_tags_20,
	sizeof(asn_DEF_NR_dl_DataToUL_ACK_tags_20)
		/sizeof(asn_DEF_NR_dl_DataToUL_ACK_tags_20[0]) - 1, /* 1 */
	asn_DEF_NR_dl_DataToUL_ACK_tags_20,	/* Same as above */
	sizeof(asn_DEF_NR_dl_DataToUL_ACK_tags_20)
		/sizeof(asn_DEF_NR_dl_DataToUL_ACK_tags_20[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dl_DataToUL_ACK_constr_20,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_dl_DataToUL_ACK_20,
	1,	/* Single element */
	&asn_SPC_NR_dl_DataToUL_ACK_specs_20	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_spatialRelationInfoToAddModList_22[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_PUCCH_SpatialRelationInfo,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_spatialRelationInfoToAddModList_tags_22[] = {
	(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_spatialRelationInfoToAddModList_specs_22 = {
	sizeof(struct NR_PUCCH_Config__spatialRelationInfoToAddModList),
	offsetof(struct NR_PUCCH_Config__spatialRelationInfoToAddModList, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_spatialRelationInfoToAddModList_22 = {
	"spatialRelationInfoToAddModList",
	"spatialRelationInfoToAddModList",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_spatialRelationInfoToAddModList_tags_22,
	sizeof(asn_DEF_NR_spatialRelationInfoToAddModList_tags_22)
		/sizeof(asn_DEF_NR_spatialRelationInfoToAddModList_tags_22[0]) - 1, /* 1 */
	asn_DEF_NR_spatialRelationInfoToAddModList_tags_22,	/* Same as above */
	sizeof(asn_DEF_NR_spatialRelationInfoToAddModList_tags_22)
		/sizeof(asn_DEF_NR_spatialRelationInfoToAddModList_tags_22[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_spatialRelationInfoToAddModList_constr_22,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_spatialRelationInfoToAddModList_22,
	1,	/* Single element */
	&asn_SPC_NR_spatialRelationInfoToAddModList_specs_22	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_spatialRelationInfoToReleaseList_24[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (2 << 2)),
		0,
		&asn_DEF_NR_PUCCH_SpatialRelationInfoId,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_spatialRelationInfoToReleaseList_tags_24[] = {
	(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_spatialRelationInfoToReleaseList_specs_24 = {
	sizeof(struct NR_PUCCH_Config__spatialRelationInfoToReleaseList),
	offsetof(struct NR_PUCCH_Config__spatialRelationInfoToReleaseList, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_spatialRelationInfoToReleaseList_24 = {
	"spatialRelationInfoToReleaseList",
	"spatialRelationInfoToReleaseList",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_spatialRelationInfoToReleaseList_tags_24,
	sizeof(asn_DEF_NR_spatialRelationInfoToReleaseList_tags_24)
		/sizeof(asn_DEF_NR_spatialRelationInfoToReleaseList_tags_24[0]) - 1, /* 1 */
	asn_DEF_NR_spatialRelationInfoToReleaseList_tags_24,	/* Same as above */
	sizeof(asn_DEF_NR_spatialRelationInfoToReleaseList_tags_24)
		/sizeof(asn_DEF_NR_spatialRelationInfoToReleaseList_tags_24[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_spatialRelationInfoToReleaseList_constr_24,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_spatialRelationInfoToReleaseList_24,
	1,	/* Single element */
	&asn_SPC_NR_spatialRelationInfoToReleaseList_specs_24	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_resourceToAddModListExt_v1610_29[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_PUCCH_ResourceExt_v1610,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_resourceToAddModListExt_v1610_tags_29[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_resourceToAddModListExt_v1610_specs_29 = {
	sizeof(struct NR_PUCCH_Config__ext1__resourceToAddModListExt_v1610),
	offsetof(struct NR_PUCCH_Config__ext1__resourceToAddModListExt_v1610, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_resourceToAddModListExt_v1610_29 = {
	"resourceToAddModListExt-v1610",
	"resourceToAddModListExt-v1610",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_resourceToAddModListExt_v1610_tags_29,
	sizeof(asn_DEF_NR_resourceToAddModListExt_v1610_tags_29)
		/sizeof(asn_DEF_NR_resourceToAddModListExt_v1610_tags_29[0]) - 1, /* 1 */
	asn_DEF_NR_resourceToAddModListExt_v1610_tags_29,	/* Same as above */
	sizeof(asn_DEF_NR_resourceToAddModListExt_v1610_tags_29)
		/sizeof(asn_DEF_NR_resourceToAddModListExt_v1610_tags_29[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_resourceToAddModListExt_v1610_constr_29,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_resourceToAddModListExt_v1610_29,
	1,	/* Single element */
	&asn_SPC_NR_resourceToAddModListExt_v1610_specs_29	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_normalCP_r16_value2enum_34[] = {
	{ 0,	2,	"n2" },
	{ 1,	2,	"n7" }
};
static const unsigned int asn_MAP_NR_normalCP_r16_enum2value_34[] = {
	0,	/* n2(0) */
	1	/* n7(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_normalCP_r16_specs_34 = {
	asn_MAP_NR_normalCP_r16_value2enum_34,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_normalCP_r16_enum2value_34,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_normalCP_r16_tags_34[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_normalCP_r16_34 = {
	"normalCP-r16",
	"normalCP-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_normalCP_r16_tags_34,
	sizeof(asn_DEF_NR_normalCP_r16_tags_34)
		/sizeof(asn_DEF_NR_normalCP_r16_tags_34[0]) - 1, /* 1 */
	asn_DEF_NR_normalCP_r16_tags_34,	/* Same as above */
	sizeof(asn_DEF_NR_normalCP_r16_tags_34)
		/sizeof(asn_DEF_NR_normalCP_r16_tags_34[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_normalCP_r16_constr_34,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_normalCP_r16_specs_34	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_extendedCP_r16_value2enum_37[] = {
	{ 0,	2,	"n2" },
	{ 1,	2,	"n6" }
};
static const unsigned int asn_MAP_NR_extendedCP_r16_enum2value_37[] = {
	0,	/* n2(0) */
	1	/* n6(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_extendedCP_r16_specs_37 = {
	asn_MAP_NR_extendedCP_r16_value2enum_37,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_extendedCP_r16_enum2value_37,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_extendedCP_r16_tags_37[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_extendedCP_r16_37 = {
	"extendedCP-r16",
	"extendedCP-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_extendedCP_r16_tags_37,
	sizeof(asn_DEF_NR_extendedCP_r16_tags_37)
		/sizeof(asn_DEF_NR_extendedCP_r16_tags_37[0]) - 1, /* 1 */
	asn_DEF_NR_extendedCP_r16_tags_37,	/* Same as above */
	sizeof(asn_DEF_NR_extendedCP_r16_tags_37)
		/sizeof(asn_DEF_NR_extendedCP_r16_tags_37[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_extendedCP_r16_constr_37,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_extendedCP_r16_specs_37	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_subslotLengthForPUCCH_r16_33[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PUCCH_Config__ext1__subslotLengthForPUCCH_r16, choice.normalCP_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_normalCP_r16_34,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"normalCP-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PUCCH_Config__ext1__subslotLengthForPUCCH_r16, choice.extendedCP_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_extendedCP_r16_37,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"extendedCP-r16"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_NR_subslotLengthForPUCCH_r16_tag2el_33[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* normalCP-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* extendedCP-r16 */
};
static asn_CHOICE_specifics_t asn_SPC_NR_subslotLengthForPUCCH_r16_specs_33 = {
	sizeof(struct NR_PUCCH_Config__ext1__subslotLengthForPUCCH_r16),
	offsetof(struct NR_PUCCH_Config__ext1__subslotLengthForPUCCH_r16, _asn_ctx),
	offsetof(struct NR_PUCCH_Config__ext1__subslotLengthForPUCCH_r16, present),
	sizeof(((struct NR_PUCCH_Config__ext1__subslotLengthForPUCCH_r16 *)0)->present),
	asn_MAP_NR_subslotLengthForPUCCH_r16_tag2el_33,
	2,	/* Count of tags in the map */
	0, 0,
	-1	/* Extensions start */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_subslotLengthForPUCCH_r16_33 = {
	"subslotLengthForPUCCH-r16",
	"subslotLengthForPUCCH-r16",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_subslotLengthForPUCCH_r16_constr_33,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		CHOICE_constraint
	},
	asn_MBR_NR_subslotLengthForPUCCH_r16_33,
	2,	/* Elements count */
	&asn_SPC_NR_subslotLengthForPUCCH_r16_specs_33	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dmrs_UplinkTransformPrecodingPUCCH_r16_value2enum_42[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_dmrs_UplinkTransformPrecodingPUCCH_r16_enum2value_42[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dmrs_UplinkTransformPrecodingPUCCH_r16_specs_42 = {
	asn_MAP_NR_dmrs_UplinkTransformPrecodingPUCCH_r16_value2enum_42,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dmrs_UplinkTransformPrecodingPUCCH_r16_enum2value_42,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dmrs_UplinkTransformPrecodingPUCCH_r16_tags_42[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dmrs_UplinkTransformPrecodingPUCCH_r16_42 = {
	"dmrs-UplinkTransformPrecodingPUCCH-r16",
	"dmrs-UplinkTransformPrecodingPUCCH-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dmrs_UplinkTransformPrecodingPUCCH_r16_tags_42,
	sizeof(asn_DEF_NR_dmrs_UplinkTransformPrecodingPUCCH_r16_tags_42)
		/sizeof(asn_DEF_NR_dmrs_UplinkTransformPrecodingPUCCH_r16_tags_42[0]) - 1, /* 1 */
	asn_DEF_NR_dmrs_UplinkTransformPrecodingPUCCH_r16_tags_42,	/* Same as above */
	sizeof(asn_DEF_NR_dmrs_UplinkTransformPrecodingPUCCH_r16_tags_42)
		/sizeof(asn_DEF_NR_dmrs_UplinkTransformPrecodingPUCCH_r16_tags_42[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dmrs_UplinkTransformPrecodingPUCCH_r16_constr_42,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dmrs_UplinkTransformPrecodingPUCCH_r16_specs_42	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_spatialRelationInfoToAddModListSizeExt_v1610_44[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_PUCCH_SpatialRelationInfo,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_spatialRelationInfoToAddModListSizeExt_v1610_tags_44[] = {
	(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_spatialRelationInfoToAddModListSizeExt_v1610_specs_44 = {
	sizeof(struct NR_PUCCH_Config__ext1__spatialRelationInfoToAddModListSizeExt_v1610),
	offsetof(struct NR_PUCCH_Config__ext1__spatialRelationInfoToAddModListSizeExt_v1610, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_spatialRelationInfoToAddModListSizeExt_v1610_44 = {
	"spatialRelationInfoToAddModListSizeExt-v1610",
	"spatialRelationInfoToAddModListSizeExt-v1610",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_spatialRelationInfoToAddModListSizeExt_v1610_tags_44,
	sizeof(asn_DEF_NR_spatialRelationInfoToAddModListSizeExt_v1610_tags_44)
		/sizeof(asn_DEF_NR_spatialRelationInfoToAddModListSizeExt_v1610_tags_44[0]) - 1, /* 1 */
	asn_DEF_NR_spatialRelationInfoToAddModListSizeExt_v1610_tags_44,	/* Same as above */
	sizeof(asn_DEF_NR_spatialRelationInfoToAddModListSizeExt_v1610_tags_44)
		/sizeof(asn_DEF_NR_spatialRelationInfoToAddModListSizeExt_v1610_tags_44[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_spatialRelationInfoToAddModListSizeExt_v1610_constr_44,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_spatialRelationInfoToAddModListSizeExt_v1610_44,
	1,	/* Single element */
	&asn_SPC_NR_spatialRelationInfoToAddModListSizeExt_v1610_specs_44	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_spatialRelationInfoToReleaseListSizeExt_v1610_46[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (2 << 2)),
		0,
		&asn_DEF_NR_PUCCH_SpatialRelationInfoId,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_spatialRelationInfoToReleaseListSizeExt_v1610_tags_46[] = {
	(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_spatialRelationInfoToReleaseListSizeExt_v1610_specs_46 = {
	sizeof(struct NR_PUCCH_Config__ext1__spatialRelationInfoToReleaseListSizeExt_v1610),
	offsetof(struct NR_PUCCH_Config__ext1__spatialRelationInfoToReleaseListSizeExt_v1610, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_spatialRelationInfoToReleaseListSizeExt_v1610_46 = {
	"spatialRelationInfoToReleaseListSizeExt-v1610",
	"spatialRelationInfoToReleaseListSizeExt-v1610",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_spatialRelationInfoToReleaseListSizeExt_v1610_tags_46,
	sizeof(asn_DEF_NR_spatialRelationInfoToReleaseListSizeExt_v1610_tags_46)
		/sizeof(asn_DEF_NR_spatialRelationInfoToReleaseListSizeExt_v1610_tags_46[0]) - 1, /* 1 */
	asn_DEF_NR_spatialRelationInfoToReleaseListSizeExt_v1610_tags_46,	/* Same as above */
	sizeof(asn_DEF_NR_spatialRelationInfoToReleaseListSizeExt_v1610_tags_46)
		/sizeof(asn_DEF_NR_spatialRelationInfoToReleaseListSizeExt_v1610_tags_46[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_spatialRelationInfoToReleaseListSizeExt_v1610_constr_46,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_spatialRelationInfoToReleaseListSizeExt_v1610_46,
	1,	/* Single element */
	&asn_SPC_NR_spatialRelationInfoToReleaseListSizeExt_v1610_specs_46	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_spatialRelationInfoToAddModListExt_v1610_48[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_PUCCH_SpatialRelationInfoExt_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_spatialRelationInfoToAddModListExt_v1610_tags_48[] = {
	(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_spatialRelationInfoToAddModListExt_v1610_specs_48 = {
	sizeof(struct NR_PUCCH_Config__ext1__spatialRelationInfoToAddModListExt_v1610),
	offsetof(struct NR_PUCCH_Config__ext1__spatialRelationInfoToAddModListExt_v1610, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_spatialRelationInfoToAddModListExt_v1610_48 = {
	"spatialRelationInfoToAddModListExt-v1610",
	"spatialRelationInfoToAddModListExt-v1610",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_spatialRelationInfoToAddModListExt_v1610_tags_48,
	sizeof(asn_DEF_NR_spatialRelationInfoToAddModListExt_v1610_tags_48)
		/sizeof(asn_DEF_NR_spatialRelationInfoToAddModListExt_v1610_tags_48[0]) - 1, /* 1 */
	asn_DEF_NR_spatialRelationInfoToAddModListExt_v1610_tags_48,	/* Same as above */
	sizeof(asn_DEF_NR_spatialRelationInfoToAddModListExt_v1610_tags_48)
		/sizeof(asn_DEF_NR_spatialRelationInfoToAddModListExt_v1610_tags_48[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_spatialRelationInfoToAddModListExt_v1610_constr_48,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_spatialRelationInfoToAddModListExt_v1610_48,
	1,	/* Single element */
	&asn_SPC_NR_spatialRelationInfoToAddModListExt_v1610_specs_48	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_spatialRelationInfoToReleaseListExt_v1610_50[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (2 << 2)),
		0,
		&asn_DEF_NR_PUCCH_SpatialRelationInfoId_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_spatialRelationInfoToReleaseListExt_v1610_tags_50[] = {
	(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_spatialRelationInfoToReleaseListExt_v1610_specs_50 = {
	sizeof(struct NR_PUCCH_Config__ext1__spatialRelationInfoToReleaseListExt_v1610),
	offsetof(struct NR_PUCCH_Config__ext1__spatialRelationInfoToReleaseListExt_v1610, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_spatialRelationInfoToReleaseListExt_v1610_50 = {
	"spatialRelationInfoToReleaseListExt-v1610",
	"spatialRelationInfoToReleaseListExt-v1610",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_spatialRelationInfoToReleaseListExt_v1610_tags_50,
	sizeof(asn_DEF_NR_spatialRelationInfoToReleaseListExt_v1610_tags_50)
		/sizeof(asn_DEF_NR_spatialRelationInfoToReleaseListExt_v1610_tags_50[0]) - 1, /* 1 */
	asn_DEF_NR_spatialRelationInfoToReleaseListExt_v1610_tags_50,	/* Same as above */
	sizeof(asn_DEF_NR_spatialRelationInfoToReleaseListExt_v1610_tags_50)
		/sizeof(asn_DEF_NR_spatialRelationInfoToReleaseListExt_v1610_tags_50[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_spatialRelationInfoToReleaseListExt_v1610_constr_50,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_spatialRelationInfoToReleaseListExt_v1610_50,
	1,	/* Single element */
	&asn_SPC_NR_spatialRelationInfoToReleaseListExt_v1610_specs_50	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_resourceGroupToAddModList_r16_52[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_PUCCH_ResourceGroup_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_resourceGroupToAddModList_r16_tags_52[] = {
	(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_resourceGroupToAddModList_r16_specs_52 = {
	sizeof(struct NR_PUCCH_Config__ext1__resourceGroupToAddModList_r16),
	offsetof(struct NR_PUCCH_Config__ext1__resourceGroupToAddModList_r16, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_resourceGroupToAddModList_r16_52 = {
	"resourceGroupToAddModList-r16",
	"resourceGroupToAddModList-r16",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_resourceGroupToAddModList_r16_tags_52,
	sizeof(asn_DEF_NR_resourceGroupToAddModList_r16_tags_52)
		/sizeof(asn_DEF_NR_resourceGroupToAddModList_r16_tags_52[0]) - 1, /* 1 */
	asn_DEF_NR_resourceGroupToAddModList_r16_tags_52,	/* Same as above */
	sizeof(asn_DEF_NR_resourceGroupToAddModList_r16_tags_52)
		/sizeof(asn_DEF_NR_resourceGroupToAddModList_r16_tags_52[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_resourceGroupToAddModList_r16_constr_52,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_resourceGroupToAddModList_r16_52,
	1,	/* Single element */
	&asn_SPC_NR_resourceGroupToAddModList_r16_specs_52	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_resourceGroupToReleaseList_r16_54[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (2 << 2)),
		0,
		&asn_DEF_NR_PUCCH_ResourceGroupId_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_resourceGroupToReleaseList_r16_tags_54[] = {
	(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_resourceGroupToReleaseList_r16_specs_54 = {
	sizeof(struct NR_PUCCH_Config__ext1__resourceGroupToReleaseList_r16),
	offsetof(struct NR_PUCCH_Config__ext1__resourceGroupToReleaseList_r16, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_resourceGroupToReleaseList_r16_54 = {
	"resourceGroupToReleaseList-r16",
	"resourceGroupToReleaseList-r16",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_resourceGroupToReleaseList_r16_tags_54,
	sizeof(asn_DEF_NR_resourceGroupToReleaseList_r16_tags_54)
		/sizeof(asn_DEF_NR_resourceGroupToReleaseList_r16_tags_54[0]) - 1, /* 1 */
	asn_DEF_NR_resourceGroupToReleaseList_r16_tags_54,	/* Same as above */
	sizeof(asn_DEF_NR_resourceGroupToReleaseList_r16_tags_54)
		/sizeof(asn_DEF_NR_resourceGroupToReleaseList_r16_tags_54[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_resourceGroupToReleaseList_r16_constr_54,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_resourceGroupToReleaseList_r16_54,
	1,	/* Single element */
	&asn_SPC_NR_resourceGroupToReleaseList_r16_specs_54	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_schedulingRequestResourceToAddModListExt_v1610_57[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_SchedulingRequestResourceConfigExt_v1610,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_schedulingRequestResourceToAddModListExt_v1610_tags_57[] = {
	(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_schedulingRequestResourceToAddModListExt_v1610_specs_57 = {
	sizeof(struct NR_PUCCH_Config__ext1__schedulingRequestResourceToAddModListExt_v1610),
	offsetof(struct NR_PUCCH_Config__ext1__schedulingRequestResourceToAddModListExt_v1610, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_schedulingRequestResourceToAddModListExt_v1610_57 = {
	"schedulingRequestResourceToAddModListExt-v1610",
	"schedulingRequestResourceToAddModListExt-v1610",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_schedulingRequestResourceToAddModListExt_v1610_tags_57,
	sizeof(asn_DEF_NR_schedulingRequestResourceToAddModListExt_v1610_tags_57)
		/sizeof(asn_DEF_NR_schedulingRequestResourceToAddModListExt_v1610_tags_57[0]) - 1, /* 1 */
	asn_DEF_NR_schedulingRequestResourceToAddModListExt_v1610_tags_57,	/* Same as above */
	sizeof(asn_DEF_NR_schedulingRequestResourceToAddModListExt_v1610_tags_57)
		/sizeof(asn_DEF_NR_schedulingRequestResourceToAddModListExt_v1610_tags_57[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_schedulingRequestResourceToAddModListExt_v1610_constr_57,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_schedulingRequestResourceToAddModListExt_v1610_57,
	1,	/* Single element */
	&asn_SPC_NR_schedulingRequestResourceToAddModListExt_v1610_specs_57	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext1_28[] = {
	{ ATF_POINTER, 15, offsetof(struct NR_PUCCH_Config__ext1, resourceToAddModListExt_v1610),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_resourceToAddModListExt_v1610_29,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_resourceToAddModListExt_v1610_constr_29,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_resourceToAddModListExt_v1610_constraint_28
		},
		0, 0, /* No default value */
		"resourceToAddModListExt-v1610"
		},
	{ ATF_POINTER, 14, offsetof(struct NR_PUCCH_Config__ext1, dl_DataToUL_ACK_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_DL_DataToUL_ACK_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dl-DataToUL-ACK-r16"
		},
	{ ATF_POINTER, 13, offsetof(struct NR_PUCCH_Config__ext1, ul_AccessConfigListDCI_1_1_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_UL_AccessConfigListDCI_1_1_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ul-AccessConfigListDCI-1-1-r16"
		},
	{ ATF_POINTER, 12, offsetof(struct NR_PUCCH_Config__ext1, subslotLengthForPUCCH_r16),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_subslotLengthForPUCCH_r16_33,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"subslotLengthForPUCCH-r16"
		},
	{ ATF_POINTER, 11, offsetof(struct NR_PUCCH_Config__ext1, dl_DataToUL_ACK_DCI_1_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_DL_DataToUL_ACK_DCI_1_2_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dl-DataToUL-ACK-DCI-1-2-r16"
		},
	{ ATF_POINTER, 10, offsetof(struct NR_PUCCH_Config__ext1, numberOfBitsForPUCCH_ResourceIndicatorDCI_1_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_numberOfBitsForPUCCH_ResourceIndicatorDCI_1_2_r16_constr_41,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_numberOfBitsForPUCCH_ResourceIndicatorDCI_1_2_r16_constraint_28
		},
		0, 0, /* No default value */
		"numberOfBitsForPUCCH-ResourceIndicatorDCI-1-2-r16"
		},
	{ ATF_POINTER, 9, offsetof(struct NR_PUCCH_Config__ext1, dmrs_UplinkTransformPrecodingPUCCH_r16),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dmrs_UplinkTransformPrecodingPUCCH_r16_42,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dmrs-UplinkTransformPrecodingPUCCH-r16"
		},
	{ ATF_POINTER, 8, offsetof(struct NR_PUCCH_Config__ext1, spatialRelationInfoToAddModListSizeExt_v1610),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		0,
		&asn_DEF_NR_spatialRelationInfoToAddModListSizeExt_v1610_44,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_spatialRelationInfoToAddModListSizeExt_v1610_constr_44,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_spatialRelationInfoToAddModListSizeExt_v1610_constraint_28
		},
		0, 0, /* No default value */
		"spatialRelationInfoToAddModListSizeExt-v1610"
		},
	{ ATF_POINTER, 7, offsetof(struct NR_PUCCH_Config__ext1, spatialRelationInfoToReleaseListSizeExt_v1610),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		0,
		&asn_DEF_NR_spatialRelationInfoToReleaseListSizeExt_v1610_46,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_spatialRelationInfoToReleaseListSizeExt_v1610_constr_46,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_spatialRelationInfoToReleaseListSizeExt_v1610_constraint_28
		},
		0, 0, /* No default value */
		"spatialRelationInfoToReleaseListSizeExt-v1610"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_PUCCH_Config__ext1, spatialRelationInfoToAddModListExt_v1610),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		0,
		&asn_DEF_NR_spatialRelationInfoToAddModListExt_v1610_48,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_spatialRelationInfoToAddModListExt_v1610_constr_48,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_spatialRelationInfoToAddModListExt_v1610_constraint_28
		},
		0, 0, /* No default value */
		"spatialRelationInfoToAddModListExt-v1610"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_PUCCH_Config__ext1, spatialRelationInfoToReleaseListExt_v1610),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		0,
		&asn_DEF_NR_spatialRelationInfoToReleaseListExt_v1610_50,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_spatialRelationInfoToReleaseListExt_v1610_constr_50,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_spatialRelationInfoToReleaseListExt_v1610_constraint_28
		},
		0, 0, /* No default value */
		"spatialRelationInfoToReleaseListExt-v1610"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_PUCCH_Config__ext1, resourceGroupToAddModList_r16),
		(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
		0,
		&asn_DEF_NR_resourceGroupToAddModList_r16_52,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_resourceGroupToAddModList_r16_constr_52,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_resourceGroupToAddModList_r16_constraint_28
		},
		0, 0, /* No default value */
		"resourceGroupToAddModList-r16"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_PUCCH_Config__ext1, resourceGroupToReleaseList_r16),
		(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
		0,
		&asn_DEF_NR_resourceGroupToReleaseList_r16_54,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_resourceGroupToReleaseList_r16_constr_54,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_resourceGroupToReleaseList_r16_constraint_28
		},
		0, 0, /* No default value */
		"resourceGroupToReleaseList-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_PUCCH_Config__ext1, sps_PUCCH_AN_List_r16),
		(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_SPS_PUCCH_AN_List_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"sps-PUCCH-AN-List-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PUCCH_Config__ext1, schedulingRequestResourceToAddModListExt_v1610),
		(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
		0,
		&asn_DEF_NR_schedulingRequestResourceToAddModListExt_v1610_57,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_schedulingRequestResourceToAddModListExt_v1610_constr_57,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_schedulingRequestResourceToAddModListExt_v1610_constraint_28
		},
		0, 0, /* No default value */
		"schedulingRequestResourceToAddModListExt-v1610"
		},
};
static const int asn_MAP_NR_ext1_oms_28[] = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_28[] = {
	(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_28[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* resourceToAddModListExt-v1610 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* dl-DataToUL-ACK-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* ul-AccessConfigListDCI-1-1-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* subslotLengthForPUCCH-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* dl-DataToUL-ACK-DCI-1-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* numberOfBitsForPUCCH-ResourceIndicatorDCI-1-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* dmrs-UplinkTransformPrecodingPUCCH-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* spatialRelationInfoToAddModListSizeExt-v1610 */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* spatialRelationInfoToReleaseListSizeExt-v1610 */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* spatialRelationInfoToAddModListExt-v1610 */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 }, /* spatialRelationInfoToReleaseListExt-v1610 */
    { (ASN_TAG_CLASS_CONTEXT | (11 << 2)), 11, 0, 0 }, /* resourceGroupToAddModList-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (12 << 2)), 12, 0, 0 }, /* resourceGroupToReleaseList-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (13 << 2)), 13, 0, 0 }, /* sps-PUCCH-AN-List-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (14 << 2)), 14, 0, 0 } /* schedulingRequestResourceToAddModListExt-v1610 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_28 = {
	sizeof(struct NR_PUCCH_Config__ext1),
	offsetof(struct NR_PUCCH_Config__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_28,
	15,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_28,	/* Optional members */
	15, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_28 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_28,
	sizeof(asn_DEF_NR_ext1_tags_28)
		/sizeof(asn_DEF_NR_ext1_tags_28[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_28,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_28)
		/sizeof(asn_DEF_NR_ext1_tags_28[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_28,
	15,	/* Elements count */
	&asn_SPC_NR_ext1_specs_28	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_mappingPattern_r17_value2enum_65[] = {
	{ 0,	13,	"cyclicMapping" },
	{ 1,	17,	"sequentialMapping" }
};
static const unsigned int asn_MAP_NR_mappingPattern_r17_enum2value_65[] = {
	0,	/* cyclicMapping(0) */
	1	/* sequentialMapping(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_mappingPattern_r17_specs_65 = {
	asn_MAP_NR_mappingPattern_r17_value2enum_65,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_mappingPattern_r17_enum2value_65,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_mappingPattern_r17_tags_65[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mappingPattern_r17_65 = {
	"mappingPattern-r17",
	"mappingPattern-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_mappingPattern_r17_tags_65,
	sizeof(asn_DEF_NR_mappingPattern_r17_tags_65)
		/sizeof(asn_DEF_NR_mappingPattern_r17_tags_65[0]) - 1, /* 1 */
	asn_DEF_NR_mappingPattern_r17_tags_65,	/* Same as above */
	sizeof(asn_DEF_NR_mappingPattern_r17_tags_65)
		/sizeof(asn_DEF_NR_mappingPattern_r17_tags_65[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_mappingPattern_r17_constr_65,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_mappingPattern_r17_specs_65	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_powerControlSetInfoToAddModList_r17_68[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_PUCCH_PowerControlSetInfo_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_powerControlSetInfoToAddModList_r17_tags_68[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_powerControlSetInfoToAddModList_r17_specs_68 = {
	sizeof(struct NR_PUCCH_Config__ext2__powerControlSetInfoToAddModList_r17),
	offsetof(struct NR_PUCCH_Config__ext2__powerControlSetInfoToAddModList_r17, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_powerControlSetInfoToAddModList_r17_68 = {
	"powerControlSetInfoToAddModList-r17",
	"powerControlSetInfoToAddModList-r17",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_powerControlSetInfoToAddModList_r17_tags_68,
	sizeof(asn_DEF_NR_powerControlSetInfoToAddModList_r17_tags_68)
		/sizeof(asn_DEF_NR_powerControlSetInfoToAddModList_r17_tags_68[0]) - 1, /* 1 */
	asn_DEF_NR_powerControlSetInfoToAddModList_r17_tags_68,	/* Same as above */
	sizeof(asn_DEF_NR_powerControlSetInfoToAddModList_r17_tags_68)
		/sizeof(asn_DEF_NR_powerControlSetInfoToAddModList_r17_tags_68[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_powerControlSetInfoToAddModList_r17_constr_68,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_powerControlSetInfoToAddModList_r17_68,
	1,	/* Single element */
	&asn_SPC_NR_powerControlSetInfoToAddModList_r17_specs_68	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_powerControlSetInfoToReleaseList_r17_70[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (2 << 2)),
		0,
		&asn_DEF_NR_PUCCH_PowerControlSetInfoId_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_powerControlSetInfoToReleaseList_r17_tags_70[] = {
	(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_powerControlSetInfoToReleaseList_r17_specs_70 = {
	sizeof(struct NR_PUCCH_Config__ext2__powerControlSetInfoToReleaseList_r17),
	offsetof(struct NR_PUCCH_Config__ext2__powerControlSetInfoToReleaseList_r17, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_powerControlSetInfoToReleaseList_r17_70 = {
	"powerControlSetInfoToReleaseList-r17",
	"powerControlSetInfoToReleaseList-r17",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_powerControlSetInfoToReleaseList_r17_tags_70,
	sizeof(asn_DEF_NR_powerControlSetInfoToReleaseList_r17_tags_70)
		/sizeof(asn_DEF_NR_powerControlSetInfoToReleaseList_r17_tags_70[0]) - 1, /* 1 */
	asn_DEF_NR_powerControlSetInfoToReleaseList_r17_tags_70,	/* Same as above */
	sizeof(asn_DEF_NR_powerControlSetInfoToReleaseList_r17_tags_70)
		/sizeof(asn_DEF_NR_powerControlSetInfoToReleaseList_r17_tags_70[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_powerControlSetInfoToReleaseList_r17_constr_70,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_powerControlSetInfoToReleaseList_r17_70,
	1,	/* Single element */
	&asn_SPC_NR_powerControlSetInfoToReleaseList_r17_specs_70	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_secondTPCFieldDCI_1_1_r17_value2enum_72[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_secondTPCFieldDCI_1_1_r17_enum2value_72[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_secondTPCFieldDCI_1_1_r17_specs_72 = {
	asn_MAP_NR_secondTPCFieldDCI_1_1_r17_value2enum_72,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_secondTPCFieldDCI_1_1_r17_enum2value_72,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_secondTPCFieldDCI_1_1_r17_tags_72[] = {
	(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_secondTPCFieldDCI_1_1_r17_72 = {
	"secondTPCFieldDCI-1-1-r17",
	"secondTPCFieldDCI-1-1-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_secondTPCFieldDCI_1_1_r17_tags_72,
	sizeof(asn_DEF_NR_secondTPCFieldDCI_1_1_r17_tags_72)
		/sizeof(asn_DEF_NR_secondTPCFieldDCI_1_1_r17_tags_72[0]) - 1, /* 1 */
	asn_DEF_NR_secondTPCFieldDCI_1_1_r17_tags_72,	/* Same as above */
	sizeof(asn_DEF_NR_secondTPCFieldDCI_1_1_r17_tags_72)
		/sizeof(asn_DEF_NR_secondTPCFieldDCI_1_1_r17_tags_72[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_secondTPCFieldDCI_1_1_r17_constr_72,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_secondTPCFieldDCI_1_1_r17_specs_72	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_secondTPCFieldDCI_1_2_r17_value2enum_74[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_secondTPCFieldDCI_1_2_r17_enum2value_74[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_secondTPCFieldDCI_1_2_r17_specs_74 = {
	asn_MAP_NR_secondTPCFieldDCI_1_2_r17_value2enum_74,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_secondTPCFieldDCI_1_2_r17_enum2value_74,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_secondTPCFieldDCI_1_2_r17_tags_74[] = {
	(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_secondTPCFieldDCI_1_2_r17_74 = {
	"secondTPCFieldDCI-1-2-r17",
	"secondTPCFieldDCI-1-2-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_secondTPCFieldDCI_1_2_r17_tags_74,
	sizeof(asn_DEF_NR_secondTPCFieldDCI_1_2_r17_tags_74)
		/sizeof(asn_DEF_NR_secondTPCFieldDCI_1_2_r17_tags_74[0]) - 1, /* 1 */
	asn_DEF_NR_secondTPCFieldDCI_1_2_r17_tags_74,	/* Same as above */
	sizeof(asn_DEF_NR_secondTPCFieldDCI_1_2_r17_tags_74)
		/sizeof(asn_DEF_NR_secondTPCFieldDCI_1_2_r17_tags_74[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_secondTPCFieldDCI_1_2_r17_constr_74,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_secondTPCFieldDCI_1_2_r17_specs_74	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_schedulingRequestResourceToAddModListExt_v1700_79[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_SchedulingRequestResourceConfigExt_v1700,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_schedulingRequestResourceToAddModListExt_v1700_tags_79[] = {
	(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_schedulingRequestResourceToAddModListExt_v1700_specs_79 = {
	sizeof(struct NR_PUCCH_Config__ext2__schedulingRequestResourceToAddModListExt_v1700),
	offsetof(struct NR_PUCCH_Config__ext2__schedulingRequestResourceToAddModListExt_v1700, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_schedulingRequestResourceToAddModListExt_v1700_79 = {
	"schedulingRequestResourceToAddModListExt-v1700",
	"schedulingRequestResourceToAddModListExt-v1700",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_schedulingRequestResourceToAddModListExt_v1700_tags_79,
	sizeof(asn_DEF_NR_schedulingRequestResourceToAddModListExt_v1700_tags_79)
		/sizeof(asn_DEF_NR_schedulingRequestResourceToAddModListExt_v1700_tags_79[0]) - 1, /* 1 */
	asn_DEF_NR_schedulingRequestResourceToAddModListExt_v1700_tags_79,	/* Same as above */
	sizeof(asn_DEF_NR_schedulingRequestResourceToAddModListExt_v1700_tags_79)
		/sizeof(asn_DEF_NR_schedulingRequestResourceToAddModListExt_v1700_tags_79[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_schedulingRequestResourceToAddModListExt_v1700_constr_79,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_schedulingRequestResourceToAddModListExt_v1700_79,
	1,	/* Single element */
	&asn_SPC_NR_schedulingRequestResourceToAddModListExt_v1700_specs_79	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext2_59[] = {
	{ ATF_POINTER, 18, offsetof(struct NR_PUCCH_Config__ext2, format0_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_PUCCH_FormatConfig,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"format0-r17"
		},
	{ ATF_POINTER, 17, offsetof(struct NR_PUCCH_Config__ext2, format2Ext_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_PUCCH_FormatConfigExt_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"format2Ext-r17"
		},
	{ ATF_POINTER, 16, offsetof(struct NR_PUCCH_Config__ext2, format3Ext_r17),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_PUCCH_FormatConfigExt_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"format3Ext-r17"
		},
	{ ATF_POINTER, 15, offsetof(struct NR_PUCCH_Config__ext2, format4Ext_r17),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_PUCCH_FormatConfigExt_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"format4Ext-r17"
		},
	{ ATF_POINTER, 14, offsetof(struct NR_PUCCH_Config__ext2, ul_AccessConfigListDCI_1_2_r17),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_UL_AccessConfigListDCI_1_2_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ul-AccessConfigListDCI-1-2-r17"
		},
	{ ATF_POINTER, 13, offsetof(struct NR_PUCCH_Config__ext2, mappingPattern_r17),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_mappingPattern_r17_65,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mappingPattern-r17"
		},
	{ ATF_POINTER, 12, offsetof(struct NR_PUCCH_Config__ext2, powerControlSetInfoToAddModList_r17),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		0,
		&asn_DEF_NR_powerControlSetInfoToAddModList_r17_68,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_powerControlSetInfoToAddModList_r17_constr_68,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_powerControlSetInfoToAddModList_r17_constraint_59
		},
		0, 0, /* No default value */
		"powerControlSetInfoToAddModList-r17"
		},
	{ ATF_POINTER, 11, offsetof(struct NR_PUCCH_Config__ext2, powerControlSetInfoToReleaseList_r17),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		0,
		&asn_DEF_NR_powerControlSetInfoToReleaseList_r17_70,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_powerControlSetInfoToReleaseList_r17_constr_70,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_powerControlSetInfoToReleaseList_r17_constraint_59
		},
		0, 0, /* No default value */
		"powerControlSetInfoToReleaseList-r17"
		},
	{ ATF_POINTER, 10, offsetof(struct NR_PUCCH_Config__ext2, secondTPCFieldDCI_1_1_r17),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_secondTPCFieldDCI_1_1_r17_72,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"secondTPCFieldDCI-1-1-r17"
		},
	{ ATF_POINTER, 9, offsetof(struct NR_PUCCH_Config__ext2, secondTPCFieldDCI_1_2_r17),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_secondTPCFieldDCI_1_2_r17_74,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"secondTPCFieldDCI-1-2-r17"
		},
	{ ATF_POINTER, 8, offsetof(struct NR_PUCCH_Config__ext2, dl_DataToUL_ACK_r17),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_DL_DataToUL_ACK_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dl-DataToUL-ACK-r17"
		},
	{ ATF_POINTER, 7, offsetof(struct NR_PUCCH_Config__ext2, dl_DataToUL_ACK_DCI_1_2_r17),
		(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_DL_DataToUL_ACK_DCI_1_2_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dl-DataToUL-ACK-DCI-1-2-r17"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_PUCCH_Config__ext2, ul_AccessConfigListDCI_1_1_r17),
		(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_UL_AccessConfigListDCI_1_1_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ul-AccessConfigListDCI-1-1-r17"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_PUCCH_Config__ext2, schedulingRequestResourceToAddModListExt_v1700),
		(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
		0,
		&asn_DEF_NR_schedulingRequestResourceToAddModListExt_v1700_79,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_schedulingRequestResourceToAddModListExt_v1700_constr_79,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_schedulingRequestResourceToAddModListExt_v1700_constraint_59
		},
		0, 0, /* No default value */
		"schedulingRequestResourceToAddModListExt-v1700"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_PUCCH_Config__ext2, dmrs_BundlingPUCCH_Config_r17),
		(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_DMRS_BundlingPUCCH_Config_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dmrs-BundlingPUCCH-Config-r17"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_PUCCH_Config__ext2, dl_DataToUL_ACK_v1700),
		(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_DL_DataToUL_ACK_v1700,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dl-DataToUL-ACK-v1700"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_PUCCH_Config__ext2, dl_DataToUL_ACK_MulticastDCI_Format4_1_r17),
		(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_DL_DataToUL_ACK_MulticastDCI_Format4_1_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dl-DataToUL-ACK-MulticastDCI-Format4-1-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PUCCH_Config__ext2, sps_PUCCH_AN_ListMulticast_r17),
		(ASN_TAG_CLASS_CONTEXT | (17 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_SPS_PUCCH_AN_List_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"sps-PUCCH-AN-ListMulticast-r17"
		},
};
static const int asn_MAP_NR_ext2_oms_59[] = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17 };
static const ber_tlv_tag_t asn_DEF_NR_ext2_tags_59[] = {
	(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext2_tag2el_59[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* format0-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* format2Ext-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* format3Ext-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* format4Ext-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* ul-AccessConfigListDCI-1-2-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* mappingPattern-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* powerControlSetInfoToAddModList-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* powerControlSetInfoToReleaseList-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* secondTPCFieldDCI-1-1-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* secondTPCFieldDCI-1-2-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 }, /* dl-DataToUL-ACK-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (11 << 2)), 11, 0, 0 }, /* dl-DataToUL-ACK-DCI-1-2-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (12 << 2)), 12, 0, 0 }, /* ul-AccessConfigListDCI-1-1-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (13 << 2)), 13, 0, 0 }, /* schedulingRequestResourceToAddModListExt-v1700 */
    { (ASN_TAG_CLASS_CONTEXT | (14 << 2)), 14, 0, 0 }, /* dmrs-BundlingPUCCH-Config-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (15 << 2)), 15, 0, 0 }, /* dl-DataToUL-ACK-v1700 */
    { (ASN_TAG_CLASS_CONTEXT | (16 << 2)), 16, 0, 0 }, /* dl-DataToUL-ACK-MulticastDCI-Format4-1-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (17 << 2)), 17, 0, 0 } /* sps-PUCCH-AN-ListMulticast-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext2_specs_59 = {
	sizeof(struct NR_PUCCH_Config__ext2),
	offsetof(struct NR_PUCCH_Config__ext2, _asn_ctx),
	asn_MAP_NR_ext2_tag2el_59,
	18,	/* Count of tags in the map */
	asn_MAP_NR_ext2_oms_59,	/* Optional members */
	18, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext2_59 = {
	"ext2",
	"ext2",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext2_tags_59,
	sizeof(asn_DEF_NR_ext2_tags_59)
		/sizeof(asn_DEF_NR_ext2_tags_59[0]) - 1, /* 1 */
	asn_DEF_NR_ext2_tags_59,	/* Same as above */
	sizeof(asn_DEF_NR_ext2_tags_59)
		/sizeof(asn_DEF_NR_ext2_tags_59[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext2_59,
	18,	/* Elements count */
	&asn_SPC_NR_ext2_specs_59	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_PUCCH_Config_1[] = {
	{ ATF_POINTER, 17, offsetof(struct NR_PUCCH_Config, resourceSetToAddModList),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_resourceSetToAddModList_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_resourceSetToAddModList_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_resourceSetToAddModList_constraint_1
		},
		0, 0, /* No default value */
		"resourceSetToAddModList"
		},
	{ ATF_POINTER, 16, offsetof(struct NR_PUCCH_Config, resourceSetToReleaseList),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_resourceSetToReleaseList_4,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_resourceSetToReleaseList_constr_4,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_resourceSetToReleaseList_constraint_1
		},
		0, 0, /* No default value */
		"resourceSetToReleaseList"
		},
	{ ATF_POINTER, 15, offsetof(struct NR_PUCCH_Config, resourceToAddModList),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		0,
		&asn_DEF_NR_resourceToAddModList_6,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_resourceToAddModList_constr_6,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_resourceToAddModList_constraint_1
		},
		0, 0, /* No default value */
		"resourceToAddModList"
		},
	{ ATF_POINTER, 14, offsetof(struct NR_PUCCH_Config, resourceToReleaseList),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		0,
		&asn_DEF_NR_resourceToReleaseList_8,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_resourceToReleaseList_constr_8,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_resourceToReleaseList_constraint_1
		},
		0, 0, /* No default value */
		"resourceToReleaseList"
		},
	{ ATF_POINTER, 13, offsetof(struct NR_PUCCH_Config, format1),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_PUCCH_FormatConfig,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"format1"
		},
	{ ATF_POINTER, 12, offsetof(struct NR_PUCCH_Config, format2),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_PUCCH_FormatConfig,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"format2"
		},
	{ ATF_POINTER, 11, offsetof(struct NR_PUCCH_Config, format3),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_PUCCH_FormatConfig,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"format3"
		},
	{ ATF_POINTER, 10, offsetof(struct NR_PUCCH_Config, format4),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_PUCCH_FormatConfig,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"format4"
		},
	{ ATF_POINTER, 9, offsetof(struct NR_PUCCH_Config, schedulingRequestResourceToAddModList),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		0,
		&asn_DEF_NR_schedulingRequestResourceToAddModList_14,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_schedulingRequestResourceToAddModList_constr_14,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_schedulingRequestResourceToAddModList_constraint_1
		},
		0, 0, /* No default value */
		"schedulingRequestResourceToAddModList"
		},
	{ ATF_POINTER, 8, offsetof(struct NR_PUCCH_Config, schedulingRequestResourceToReleaseList),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		0,
		&asn_DEF_NR_schedulingRequestResourceToReleaseList_16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_schedulingRequestResourceToReleaseList_constr_16,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_schedulingRequestResourceToReleaseList_constraint_1
		},
		0, 0, /* No default value */
		"schedulingRequestResourceToReleaseList"
		},
	{ ATF_POINTER, 7, offsetof(struct NR_PUCCH_Config, multi_CSI_PUCCH_ResourceList),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		0,
		&asn_DEF_NR_multi_CSI_PUCCH_ResourceList_18,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_multi_CSI_PUCCH_ResourceList_constr_18,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_multi_CSI_PUCCH_ResourceList_constraint_1
		},
		0, 0, /* No default value */
		"multi-CSI-PUCCH-ResourceList"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_PUCCH_Config, dl_DataToUL_ACK),
		(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
		0,
		&asn_DEF_NR_dl_DataToUL_ACK_20,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_dl_DataToUL_ACK_constr_20,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_dl_DataToUL_ACK_constraint_1
		},
		0, 0, /* No default value */
		"dl-DataToUL-ACK"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_PUCCH_Config, spatialRelationInfoToAddModList),
		(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
		0,
		&asn_DEF_NR_spatialRelationInfoToAddModList_22,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_spatialRelationInfoToAddModList_constr_22,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_spatialRelationInfoToAddModList_constraint_1
		},
		0, 0, /* No default value */
		"spatialRelationInfoToAddModList"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_PUCCH_Config, spatialRelationInfoToReleaseList),
		(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
		0,
		&asn_DEF_NR_spatialRelationInfoToReleaseList_24,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_spatialRelationInfoToReleaseList_constr_24,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_spatialRelationInfoToReleaseList_constraint_1
		},
		0, 0, /* No default value */
		"spatialRelationInfoToReleaseList"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_PUCCH_Config, pucch_PowerControl),
		(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_PUCCH_PowerControl,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pucch-PowerControl"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_PUCCH_Config, ext1),
		(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
		0,
		&asn_DEF_NR_ext1_28,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PUCCH_Config, ext2),
		(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
		0,
		&asn_DEF_NR_ext2_59,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext2"
		},
};
static const int asn_MAP_NR_PUCCH_Config_oms_1[] = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16 };
static const ber_tlv_tag_t asn_DEF_NR_PUCCH_Config_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_PUCCH_Config_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* resourceSetToAddModList */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* resourceSetToReleaseList */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* resourceToAddModList */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* resourceToReleaseList */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* format1 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* format2 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* format3 */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* format4 */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* schedulingRequestResourceToAddModList */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* schedulingRequestResourceToReleaseList */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 }, /* multi-CSI-PUCCH-ResourceList */
    { (ASN_TAG_CLASS_CONTEXT | (11 << 2)), 11, 0, 0 }, /* dl-DataToUL-ACK */
    { (ASN_TAG_CLASS_CONTEXT | (12 << 2)), 12, 0, 0 }, /* spatialRelationInfoToAddModList */
    { (ASN_TAG_CLASS_CONTEXT | (13 << 2)), 13, 0, 0 }, /* spatialRelationInfoToReleaseList */
    { (ASN_TAG_CLASS_CONTEXT | (14 << 2)), 14, 0, 0 }, /* pucch-PowerControl */
    { (ASN_TAG_CLASS_CONTEXT | (15 << 2)), 15, 0, 0 }, /* ext1 */
    { (ASN_TAG_CLASS_CONTEXT | (16 << 2)), 16, 0, 0 } /* ext2 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_PUCCH_Config_specs_1 = {
	sizeof(struct NR_PUCCH_Config),
	offsetof(struct NR_PUCCH_Config, _asn_ctx),
	asn_MAP_NR_PUCCH_Config_tag2el_1,
	17,	/* Count of tags in the map */
	asn_MAP_NR_PUCCH_Config_oms_1,	/* Optional members */
	15, 2,	/* Root/Additions */
	15,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_PUCCH_Config = {
	"PUCCH-Config",
	"PUCCH-Config",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_PUCCH_Config_tags_1,
	sizeof(asn_DEF_NR_PUCCH_Config_tags_1)
		/sizeof(asn_DEF_NR_PUCCH_Config_tags_1[0]), /* 1 */
	asn_DEF_NR_PUCCH_Config_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_PUCCH_Config_tags_1)
		/sizeof(asn_DEF_NR_PUCCH_Config_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_PUCCH_Config_1,
	17,	/* Elements count */
	&asn_SPC_NR_PUCCH_Config_specs_1	/* Additional specs */
};

