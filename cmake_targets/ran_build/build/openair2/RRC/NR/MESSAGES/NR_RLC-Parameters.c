/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_RLC-Parameters.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_am_WithShortSN_constr_2 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_um_WithShortSN_constr_4 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_um_WithLongSN_constr_6 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_extendedT_PollRetransmit_r16_constr_10 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_extendedT_StatusProhibit_r16_constr_12 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_am_WithLongSN_RedCap_r17_constr_15 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_am_WithShortSN_value2enum_2[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_am_WithShortSN_enum2value_2[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_am_WithShortSN_specs_2 = {
	asn_MAP_NR_am_WithShortSN_value2enum_2,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_am_WithShortSN_enum2value_2,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_am_WithShortSN_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_am_WithShortSN_2 = {
	"am-WithShortSN",
	"am-WithShortSN",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_am_WithShortSN_tags_2,
	sizeof(asn_DEF_NR_am_WithShortSN_tags_2)
		/sizeof(asn_DEF_NR_am_WithShortSN_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_am_WithShortSN_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_am_WithShortSN_tags_2)
		/sizeof(asn_DEF_NR_am_WithShortSN_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_am_WithShortSN_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_am_WithShortSN_specs_2	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_um_WithShortSN_value2enum_4[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_um_WithShortSN_enum2value_4[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_um_WithShortSN_specs_4 = {
	asn_MAP_NR_um_WithShortSN_value2enum_4,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_um_WithShortSN_enum2value_4,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_um_WithShortSN_tags_4[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_um_WithShortSN_4 = {
	"um-WithShortSN",
	"um-WithShortSN",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_um_WithShortSN_tags_4,
	sizeof(asn_DEF_NR_um_WithShortSN_tags_4)
		/sizeof(asn_DEF_NR_um_WithShortSN_tags_4[0]) - 1, /* 1 */
	asn_DEF_NR_um_WithShortSN_tags_4,	/* Same as above */
	sizeof(asn_DEF_NR_um_WithShortSN_tags_4)
		/sizeof(asn_DEF_NR_um_WithShortSN_tags_4[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_um_WithShortSN_constr_4,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_um_WithShortSN_specs_4	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_um_WithLongSN_value2enum_6[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_um_WithLongSN_enum2value_6[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_um_WithLongSN_specs_6 = {
	asn_MAP_NR_um_WithLongSN_value2enum_6,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_um_WithLongSN_enum2value_6,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_um_WithLongSN_tags_6[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_um_WithLongSN_6 = {
	"um-WithLongSN",
	"um-WithLongSN",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_um_WithLongSN_tags_6,
	sizeof(asn_DEF_NR_um_WithLongSN_tags_6)
		/sizeof(asn_DEF_NR_um_WithLongSN_tags_6[0]) - 1, /* 1 */
	asn_DEF_NR_um_WithLongSN_tags_6,	/* Same as above */
	sizeof(asn_DEF_NR_um_WithLongSN_tags_6)
		/sizeof(asn_DEF_NR_um_WithLongSN_tags_6[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_um_WithLongSN_constr_6,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_um_WithLongSN_specs_6	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_extendedT_PollRetransmit_r16_value2enum_10[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_extendedT_PollRetransmit_r16_enum2value_10[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_extendedT_PollRetransmit_r16_specs_10 = {
	asn_MAP_NR_extendedT_PollRetransmit_r16_value2enum_10,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_extendedT_PollRetransmit_r16_enum2value_10,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_extendedT_PollRetransmit_r16_tags_10[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_extendedT_PollRetransmit_r16_10 = {
	"extendedT-PollRetransmit-r16",
	"extendedT-PollRetransmit-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_extendedT_PollRetransmit_r16_tags_10,
	sizeof(asn_DEF_NR_extendedT_PollRetransmit_r16_tags_10)
		/sizeof(asn_DEF_NR_extendedT_PollRetransmit_r16_tags_10[0]) - 1, /* 1 */
	asn_DEF_NR_extendedT_PollRetransmit_r16_tags_10,	/* Same as above */
	sizeof(asn_DEF_NR_extendedT_PollRetransmit_r16_tags_10)
		/sizeof(asn_DEF_NR_extendedT_PollRetransmit_r16_tags_10[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_extendedT_PollRetransmit_r16_constr_10,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_extendedT_PollRetransmit_r16_specs_10	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_extendedT_StatusProhibit_r16_value2enum_12[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_extendedT_StatusProhibit_r16_enum2value_12[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_extendedT_StatusProhibit_r16_specs_12 = {
	asn_MAP_NR_extendedT_StatusProhibit_r16_value2enum_12,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_extendedT_StatusProhibit_r16_enum2value_12,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_extendedT_StatusProhibit_r16_tags_12[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_extendedT_StatusProhibit_r16_12 = {
	"extendedT-StatusProhibit-r16",
	"extendedT-StatusProhibit-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_extendedT_StatusProhibit_r16_tags_12,
	sizeof(asn_DEF_NR_extendedT_StatusProhibit_r16_tags_12)
		/sizeof(asn_DEF_NR_extendedT_StatusProhibit_r16_tags_12[0]) - 1, /* 1 */
	asn_DEF_NR_extendedT_StatusProhibit_r16_tags_12,	/* Same as above */
	sizeof(asn_DEF_NR_extendedT_StatusProhibit_r16_tags_12)
		/sizeof(asn_DEF_NR_extendedT_StatusProhibit_r16_tags_12[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_extendedT_StatusProhibit_r16_constr_12,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_extendedT_StatusProhibit_r16_specs_12	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext1_9[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_RLC_Parameters__ext1, extendedT_PollRetransmit_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_extendedT_PollRetransmit_r16_10,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"extendedT-PollRetransmit-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RLC_Parameters__ext1, extendedT_StatusProhibit_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_extendedT_StatusProhibit_r16_12,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"extendedT-StatusProhibit-r16"
		},
};
static const int asn_MAP_NR_ext1_oms_9[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_9[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_9[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* extendedT-PollRetransmit-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* extendedT-StatusProhibit-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_9 = {
	sizeof(struct NR_RLC_Parameters__ext1),
	offsetof(struct NR_RLC_Parameters__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_9,
	2,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_9,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_9 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_9,
	sizeof(asn_DEF_NR_ext1_tags_9)
		/sizeof(asn_DEF_NR_ext1_tags_9[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_9,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_9)
		/sizeof(asn_DEF_NR_ext1_tags_9[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_9,
	2,	/* Elements count */
	&asn_SPC_NR_ext1_specs_9	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_am_WithLongSN_RedCap_r17_value2enum_15[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_am_WithLongSN_RedCap_r17_enum2value_15[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_am_WithLongSN_RedCap_r17_specs_15 = {
	asn_MAP_NR_am_WithLongSN_RedCap_r17_value2enum_15,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_am_WithLongSN_RedCap_r17_enum2value_15,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_am_WithLongSN_RedCap_r17_tags_15[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_am_WithLongSN_RedCap_r17_15 = {
	"am-WithLongSN-RedCap-r17",
	"am-WithLongSN-RedCap-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_am_WithLongSN_RedCap_r17_tags_15,
	sizeof(asn_DEF_NR_am_WithLongSN_RedCap_r17_tags_15)
		/sizeof(asn_DEF_NR_am_WithLongSN_RedCap_r17_tags_15[0]) - 1, /* 1 */
	asn_DEF_NR_am_WithLongSN_RedCap_r17_tags_15,	/* Same as above */
	sizeof(asn_DEF_NR_am_WithLongSN_RedCap_r17_tags_15)
		/sizeof(asn_DEF_NR_am_WithLongSN_RedCap_r17_tags_15[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_am_WithLongSN_RedCap_r17_constr_15,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_am_WithLongSN_RedCap_r17_specs_15	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext2_14[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_RLC_Parameters__ext2, am_WithLongSN_RedCap_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_am_WithLongSN_RedCap_r17_15,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"am-WithLongSN-RedCap-r17"
		},
};
static const int asn_MAP_NR_ext2_oms_14[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext2_tags_14[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext2_tag2el_14[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* am-WithLongSN-RedCap-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext2_specs_14 = {
	sizeof(struct NR_RLC_Parameters__ext2),
	offsetof(struct NR_RLC_Parameters__ext2, _asn_ctx),
	asn_MAP_NR_ext2_tag2el_14,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext2_oms_14,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext2_14 = {
	"ext2",
	"ext2",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext2_tags_14,
	sizeof(asn_DEF_NR_ext2_tags_14)
		/sizeof(asn_DEF_NR_ext2_tags_14[0]) - 1, /* 1 */
	asn_DEF_NR_ext2_tags_14,	/* Same as above */
	sizeof(asn_DEF_NR_ext2_tags_14)
		/sizeof(asn_DEF_NR_ext2_tags_14[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext2_14,
	1,	/* Elements count */
	&asn_SPC_NR_ext2_specs_14	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_RLC_Parameters_1[] = {
	{ ATF_POINTER, 5, offsetof(struct NR_RLC_Parameters, am_WithShortSN),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_am_WithShortSN_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"am-WithShortSN"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_RLC_Parameters, um_WithShortSN),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_um_WithShortSN_4,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"um-WithShortSN"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_RLC_Parameters, um_WithLongSN),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_um_WithLongSN_6,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"um-WithLongSN"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_RLC_Parameters, ext1),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		0,
		&asn_DEF_NR_ext1_9,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RLC_Parameters, ext2),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		0,
		&asn_DEF_NR_ext2_14,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext2"
		},
};
static const int asn_MAP_NR_RLC_Parameters_oms_1[] = { 0, 1, 2, 3, 4 };
static const ber_tlv_tag_t asn_DEF_NR_RLC_Parameters_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_RLC_Parameters_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* am-WithShortSN */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* um-WithShortSN */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* um-WithLongSN */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* ext1 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 } /* ext2 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_RLC_Parameters_specs_1 = {
	sizeof(struct NR_RLC_Parameters),
	offsetof(struct NR_RLC_Parameters, _asn_ctx),
	asn_MAP_NR_RLC_Parameters_tag2el_1,
	5,	/* Count of tags in the map */
	asn_MAP_NR_RLC_Parameters_oms_1,	/* Optional members */
	3, 2,	/* Root/Additions */
	3,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_RLC_Parameters = {
	"RLC-Parameters",
	"RLC-Parameters",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_RLC_Parameters_tags_1,
	sizeof(asn_DEF_NR_RLC_Parameters_tags_1)
		/sizeof(asn_DEF_NR_RLC_Parameters_tags_1[0]), /* 1 */
	asn_DEF_NR_RLC_Parameters_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_RLC_Parameters_tags_1)
		/sizeof(asn_DEF_NR_RLC_Parameters_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_RLC_Parameters_1,
	5,	/* Elements count */
	&asn_SPC_NR_RLC_Parameters_specs_1	/* Additional specs */
};

