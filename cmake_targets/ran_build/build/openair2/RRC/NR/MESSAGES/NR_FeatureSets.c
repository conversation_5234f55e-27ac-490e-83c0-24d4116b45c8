/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_FeatureSets.h"

static int
memb_NR_featureSetsDownlink_v1540_constraint_11(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 1024UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_featureSetsUplink_v1540_constraint_11(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 1024UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_featureSetsUplinkPerCC_v1540_constraint_11(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 1024UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_featureSetsDownlink_v15a0_constraint_18(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 1024UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_featureSetsDownlink_v1610_constraint_21(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 1024UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_featureSetsUplink_v1610_constraint_21(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 1024UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_featureSetDownlinkPerCC_v1620_constraint_21(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 1024UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_featureSetsUplink_v1630_constraint_28(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 1024UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_featureSetsUplink_v1640_constraint_31(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 1024UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_featureSetsDownlink_v1700_constraint_34(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 1024UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_featureSetsDownlinkPerCC_v1700_constraint_34(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 1024UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_featureSetsUplink_v1710_constraint_34(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 1024UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_featureSetsUplinkPerCC_v1700_constraint_34(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 1024UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_featureSetsDownlink_v1720_constraint_43(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 1024UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_featureSetsDownlinkPerCC_v1720_constraint_43(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 1024UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_featureSetsUplink_v1720_constraint_43(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 1024UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_featureSetsDownlink_v1730_constraint_50(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 1024UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_featureSetsDownlinkPerCC_v1730_constraint_50(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 1024UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_featureSetsDownlink_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 1024UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_featureSetsDownlinkPerCC_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 1024UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_featureSetsUplink_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 1024UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_featureSetsUplinkPerCC_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 1024UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_featureSetsDownlink_constr_2 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_featureSetsDownlinkPerCC_constr_4 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_featureSetsUplink_constr_6 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_featureSetsUplinkPerCC_constr_8 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_featureSetsDownlink_v1540_constr_12 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_featureSetsUplink_v1540_constr_14 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_featureSetsUplinkPerCC_v1540_constr_16 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_featureSetsDownlink_v1540_constr_12 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_featureSetsUplink_v1540_constr_14 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_featureSetsUplinkPerCC_v1540_constr_16 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_featureSetsDownlink_v15a0_constr_19 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_featureSetsDownlink_v15a0_constr_19 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_featureSetsDownlink_v1610_constr_22 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_featureSetsUplink_v1610_constr_24 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_featureSetDownlinkPerCC_v1620_constr_26 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_featureSetsDownlink_v1610_constr_22 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_featureSetsUplink_v1610_constr_24 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_featureSetDownlinkPerCC_v1620_constr_26 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_featureSetsUplink_v1630_constr_29 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_featureSetsUplink_v1630_constr_29 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_featureSetsUplink_v1640_constr_32 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_featureSetsUplink_v1640_constr_32 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_featureSetsDownlink_v1700_constr_35 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_featureSetsDownlinkPerCC_v1700_constr_37 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_featureSetsUplink_v1710_constr_39 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_featureSetsUplinkPerCC_v1700_constr_41 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_featureSetsDownlink_v1700_constr_35 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_featureSetsDownlinkPerCC_v1700_constr_37 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_featureSetsUplink_v1710_constr_39 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_featureSetsUplinkPerCC_v1700_constr_41 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_featureSetsDownlink_v1720_constr_44 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_featureSetsDownlinkPerCC_v1720_constr_46 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_featureSetsUplink_v1720_constr_48 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_featureSetsDownlink_v1720_constr_44 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_featureSetsDownlinkPerCC_v1720_constr_46 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_featureSetsUplink_v1720_constr_48 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_featureSetsDownlink_v1730_constr_51 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_featureSetsDownlinkPerCC_v1730_constr_53 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_featureSetsDownlink_v1730_constr_51 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_featureSetsDownlinkPerCC_v1730_constr_53 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_featureSetsDownlink_constr_2 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_featureSetsDownlinkPerCC_constr_4 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_featureSetsUplink_constr_6 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_featureSetsUplinkPerCC_constr_8 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static asn_TYPE_member_t asn_MBR_NR_featureSetsDownlink_2[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_FeatureSetDownlink,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_featureSetsDownlink_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_featureSetsDownlink_specs_2 = {
	sizeof(struct NR_FeatureSets__featureSetsDownlink),
	offsetof(struct NR_FeatureSets__featureSetsDownlink, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_featureSetsDownlink_2 = {
	"featureSetsDownlink",
	"featureSetsDownlink",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_featureSetsDownlink_tags_2,
	sizeof(asn_DEF_NR_featureSetsDownlink_tags_2)
		/sizeof(asn_DEF_NR_featureSetsDownlink_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_featureSetsDownlink_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_featureSetsDownlink_tags_2)
		/sizeof(asn_DEF_NR_featureSetsDownlink_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_featureSetsDownlink_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_featureSetsDownlink_2,
	1,	/* Single element */
	&asn_SPC_NR_featureSetsDownlink_specs_2	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_featureSetsDownlinkPerCC_4[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_FeatureSetDownlinkPerCC,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_featureSetsDownlinkPerCC_tags_4[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_featureSetsDownlinkPerCC_specs_4 = {
	sizeof(struct NR_FeatureSets__featureSetsDownlinkPerCC),
	offsetof(struct NR_FeatureSets__featureSetsDownlinkPerCC, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_featureSetsDownlinkPerCC_4 = {
	"featureSetsDownlinkPerCC",
	"featureSetsDownlinkPerCC",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_featureSetsDownlinkPerCC_tags_4,
	sizeof(asn_DEF_NR_featureSetsDownlinkPerCC_tags_4)
		/sizeof(asn_DEF_NR_featureSetsDownlinkPerCC_tags_4[0]) - 1, /* 1 */
	asn_DEF_NR_featureSetsDownlinkPerCC_tags_4,	/* Same as above */
	sizeof(asn_DEF_NR_featureSetsDownlinkPerCC_tags_4)
		/sizeof(asn_DEF_NR_featureSetsDownlinkPerCC_tags_4[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_featureSetsDownlinkPerCC_constr_4,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_featureSetsDownlinkPerCC_4,
	1,	/* Single element */
	&asn_SPC_NR_featureSetsDownlinkPerCC_specs_4	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_featureSetsUplink_6[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_FeatureSetUplink,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_featureSetsUplink_tags_6[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_featureSetsUplink_specs_6 = {
	sizeof(struct NR_FeatureSets__featureSetsUplink),
	offsetof(struct NR_FeatureSets__featureSetsUplink, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_featureSetsUplink_6 = {
	"featureSetsUplink",
	"featureSetsUplink",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_featureSetsUplink_tags_6,
	sizeof(asn_DEF_NR_featureSetsUplink_tags_6)
		/sizeof(asn_DEF_NR_featureSetsUplink_tags_6[0]) - 1, /* 1 */
	asn_DEF_NR_featureSetsUplink_tags_6,	/* Same as above */
	sizeof(asn_DEF_NR_featureSetsUplink_tags_6)
		/sizeof(asn_DEF_NR_featureSetsUplink_tags_6[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_featureSetsUplink_constr_6,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_featureSetsUplink_6,
	1,	/* Single element */
	&asn_SPC_NR_featureSetsUplink_specs_6	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_featureSetsUplinkPerCC_8[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_FeatureSetUplinkPerCC,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_featureSetsUplinkPerCC_tags_8[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_featureSetsUplinkPerCC_specs_8 = {
	sizeof(struct NR_FeatureSets__featureSetsUplinkPerCC),
	offsetof(struct NR_FeatureSets__featureSetsUplinkPerCC, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_featureSetsUplinkPerCC_8 = {
	"featureSetsUplinkPerCC",
	"featureSetsUplinkPerCC",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_featureSetsUplinkPerCC_tags_8,
	sizeof(asn_DEF_NR_featureSetsUplinkPerCC_tags_8)
		/sizeof(asn_DEF_NR_featureSetsUplinkPerCC_tags_8[0]) - 1, /* 1 */
	asn_DEF_NR_featureSetsUplinkPerCC_tags_8,	/* Same as above */
	sizeof(asn_DEF_NR_featureSetsUplinkPerCC_tags_8)
		/sizeof(asn_DEF_NR_featureSetsUplinkPerCC_tags_8[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_featureSetsUplinkPerCC_constr_8,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_featureSetsUplinkPerCC_8,
	1,	/* Single element */
	&asn_SPC_NR_featureSetsUplinkPerCC_specs_8	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_featureSetsDownlink_v1540_12[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_FeatureSetDownlink_v1540,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_featureSetsDownlink_v1540_tags_12[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_featureSetsDownlink_v1540_specs_12 = {
	sizeof(struct NR_FeatureSets__ext1__featureSetsDownlink_v1540),
	offsetof(struct NR_FeatureSets__ext1__featureSetsDownlink_v1540, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_featureSetsDownlink_v1540_12 = {
	"featureSetsDownlink-v1540",
	"featureSetsDownlink-v1540",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_featureSetsDownlink_v1540_tags_12,
	sizeof(asn_DEF_NR_featureSetsDownlink_v1540_tags_12)
		/sizeof(asn_DEF_NR_featureSetsDownlink_v1540_tags_12[0]) - 1, /* 1 */
	asn_DEF_NR_featureSetsDownlink_v1540_tags_12,	/* Same as above */
	sizeof(asn_DEF_NR_featureSetsDownlink_v1540_tags_12)
		/sizeof(asn_DEF_NR_featureSetsDownlink_v1540_tags_12[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_featureSetsDownlink_v1540_constr_12,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_featureSetsDownlink_v1540_12,
	1,	/* Single element */
	&asn_SPC_NR_featureSetsDownlink_v1540_specs_12	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_featureSetsUplink_v1540_14[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_FeatureSetUplink_v1540,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_featureSetsUplink_v1540_tags_14[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_featureSetsUplink_v1540_specs_14 = {
	sizeof(struct NR_FeatureSets__ext1__featureSetsUplink_v1540),
	offsetof(struct NR_FeatureSets__ext1__featureSetsUplink_v1540, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_featureSetsUplink_v1540_14 = {
	"featureSetsUplink-v1540",
	"featureSetsUplink-v1540",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_featureSetsUplink_v1540_tags_14,
	sizeof(asn_DEF_NR_featureSetsUplink_v1540_tags_14)
		/sizeof(asn_DEF_NR_featureSetsUplink_v1540_tags_14[0]) - 1, /* 1 */
	asn_DEF_NR_featureSetsUplink_v1540_tags_14,	/* Same as above */
	sizeof(asn_DEF_NR_featureSetsUplink_v1540_tags_14)
		/sizeof(asn_DEF_NR_featureSetsUplink_v1540_tags_14[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_featureSetsUplink_v1540_constr_14,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_featureSetsUplink_v1540_14,
	1,	/* Single element */
	&asn_SPC_NR_featureSetsUplink_v1540_specs_14	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_featureSetsUplinkPerCC_v1540_16[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_FeatureSetUplinkPerCC_v1540,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_featureSetsUplinkPerCC_v1540_tags_16[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_featureSetsUplinkPerCC_v1540_specs_16 = {
	sizeof(struct NR_FeatureSets__ext1__featureSetsUplinkPerCC_v1540),
	offsetof(struct NR_FeatureSets__ext1__featureSetsUplinkPerCC_v1540, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_featureSetsUplinkPerCC_v1540_16 = {
	"featureSetsUplinkPerCC-v1540",
	"featureSetsUplinkPerCC-v1540",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_featureSetsUplinkPerCC_v1540_tags_16,
	sizeof(asn_DEF_NR_featureSetsUplinkPerCC_v1540_tags_16)
		/sizeof(asn_DEF_NR_featureSetsUplinkPerCC_v1540_tags_16[0]) - 1, /* 1 */
	asn_DEF_NR_featureSetsUplinkPerCC_v1540_tags_16,	/* Same as above */
	sizeof(asn_DEF_NR_featureSetsUplinkPerCC_v1540_tags_16)
		/sizeof(asn_DEF_NR_featureSetsUplinkPerCC_v1540_tags_16[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_featureSetsUplinkPerCC_v1540_constr_16,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_featureSetsUplinkPerCC_v1540_16,
	1,	/* Single element */
	&asn_SPC_NR_featureSetsUplinkPerCC_v1540_specs_16	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext1_11[] = {
	{ ATF_POINTER, 3, offsetof(struct NR_FeatureSets__ext1, featureSetsDownlink_v1540),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_featureSetsDownlink_v1540_12,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_featureSetsDownlink_v1540_constr_12,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_featureSetsDownlink_v1540_constraint_11
		},
		0, 0, /* No default value */
		"featureSetsDownlink-v1540"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_FeatureSets__ext1, featureSetsUplink_v1540),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_featureSetsUplink_v1540_14,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_featureSetsUplink_v1540_constr_14,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_featureSetsUplink_v1540_constraint_11
		},
		0, 0, /* No default value */
		"featureSetsUplink-v1540"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_FeatureSets__ext1, featureSetsUplinkPerCC_v1540),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		0,
		&asn_DEF_NR_featureSetsUplinkPerCC_v1540_16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_featureSetsUplinkPerCC_v1540_constr_16,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_featureSetsUplinkPerCC_v1540_constraint_11
		},
		0, 0, /* No default value */
		"featureSetsUplinkPerCC-v1540"
		},
};
static const int asn_MAP_NR_ext1_oms_11[] = { 0, 1, 2 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_11[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_11[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* featureSetsDownlink-v1540 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* featureSetsUplink-v1540 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* featureSetsUplinkPerCC-v1540 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_11 = {
	sizeof(struct NR_FeatureSets__ext1),
	offsetof(struct NR_FeatureSets__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_11,
	3,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_11,	/* Optional members */
	3, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_11 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_11,
	sizeof(asn_DEF_NR_ext1_tags_11)
		/sizeof(asn_DEF_NR_ext1_tags_11[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_11,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_11)
		/sizeof(asn_DEF_NR_ext1_tags_11[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_11,
	3,	/* Elements count */
	&asn_SPC_NR_ext1_specs_11	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_featureSetsDownlink_v15a0_19[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_FeatureSetDownlink_v15a0,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_featureSetsDownlink_v15a0_tags_19[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_featureSetsDownlink_v15a0_specs_19 = {
	sizeof(struct NR_FeatureSets__ext2__featureSetsDownlink_v15a0),
	offsetof(struct NR_FeatureSets__ext2__featureSetsDownlink_v15a0, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_featureSetsDownlink_v15a0_19 = {
	"featureSetsDownlink-v15a0",
	"featureSetsDownlink-v15a0",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_featureSetsDownlink_v15a0_tags_19,
	sizeof(asn_DEF_NR_featureSetsDownlink_v15a0_tags_19)
		/sizeof(asn_DEF_NR_featureSetsDownlink_v15a0_tags_19[0]) - 1, /* 1 */
	asn_DEF_NR_featureSetsDownlink_v15a0_tags_19,	/* Same as above */
	sizeof(asn_DEF_NR_featureSetsDownlink_v15a0_tags_19)
		/sizeof(asn_DEF_NR_featureSetsDownlink_v15a0_tags_19[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_featureSetsDownlink_v15a0_constr_19,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_featureSetsDownlink_v15a0_19,
	1,	/* Single element */
	&asn_SPC_NR_featureSetsDownlink_v15a0_specs_19	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext2_18[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_FeatureSets__ext2, featureSetsDownlink_v15a0),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_featureSetsDownlink_v15a0_19,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_featureSetsDownlink_v15a0_constr_19,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_featureSetsDownlink_v15a0_constraint_18
		},
		0, 0, /* No default value */
		"featureSetsDownlink-v15a0"
		},
};
static const int asn_MAP_NR_ext2_oms_18[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext2_tags_18[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext2_tag2el_18[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* featureSetsDownlink-v15a0 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext2_specs_18 = {
	sizeof(struct NR_FeatureSets__ext2),
	offsetof(struct NR_FeatureSets__ext2, _asn_ctx),
	asn_MAP_NR_ext2_tag2el_18,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext2_oms_18,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext2_18 = {
	"ext2",
	"ext2",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext2_tags_18,
	sizeof(asn_DEF_NR_ext2_tags_18)
		/sizeof(asn_DEF_NR_ext2_tags_18[0]) - 1, /* 1 */
	asn_DEF_NR_ext2_tags_18,	/* Same as above */
	sizeof(asn_DEF_NR_ext2_tags_18)
		/sizeof(asn_DEF_NR_ext2_tags_18[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext2_18,
	1,	/* Elements count */
	&asn_SPC_NR_ext2_specs_18	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_featureSetsDownlink_v1610_22[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_FeatureSetDownlink_v1610,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_featureSetsDownlink_v1610_tags_22[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_featureSetsDownlink_v1610_specs_22 = {
	sizeof(struct NR_FeatureSets__ext3__featureSetsDownlink_v1610),
	offsetof(struct NR_FeatureSets__ext3__featureSetsDownlink_v1610, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_featureSetsDownlink_v1610_22 = {
	"featureSetsDownlink-v1610",
	"featureSetsDownlink-v1610",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_featureSetsDownlink_v1610_tags_22,
	sizeof(asn_DEF_NR_featureSetsDownlink_v1610_tags_22)
		/sizeof(asn_DEF_NR_featureSetsDownlink_v1610_tags_22[0]) - 1, /* 1 */
	asn_DEF_NR_featureSetsDownlink_v1610_tags_22,	/* Same as above */
	sizeof(asn_DEF_NR_featureSetsDownlink_v1610_tags_22)
		/sizeof(asn_DEF_NR_featureSetsDownlink_v1610_tags_22[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_featureSetsDownlink_v1610_constr_22,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_featureSetsDownlink_v1610_22,
	1,	/* Single element */
	&asn_SPC_NR_featureSetsDownlink_v1610_specs_22	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_featureSetsUplink_v1610_24[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_FeatureSetUplink_v1610,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_featureSetsUplink_v1610_tags_24[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_featureSetsUplink_v1610_specs_24 = {
	sizeof(struct NR_FeatureSets__ext3__featureSetsUplink_v1610),
	offsetof(struct NR_FeatureSets__ext3__featureSetsUplink_v1610, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_featureSetsUplink_v1610_24 = {
	"featureSetsUplink-v1610",
	"featureSetsUplink-v1610",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_featureSetsUplink_v1610_tags_24,
	sizeof(asn_DEF_NR_featureSetsUplink_v1610_tags_24)
		/sizeof(asn_DEF_NR_featureSetsUplink_v1610_tags_24[0]) - 1, /* 1 */
	asn_DEF_NR_featureSetsUplink_v1610_tags_24,	/* Same as above */
	sizeof(asn_DEF_NR_featureSetsUplink_v1610_tags_24)
		/sizeof(asn_DEF_NR_featureSetsUplink_v1610_tags_24[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_featureSetsUplink_v1610_constr_24,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_featureSetsUplink_v1610_24,
	1,	/* Single element */
	&asn_SPC_NR_featureSetsUplink_v1610_specs_24	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_featureSetDownlinkPerCC_v1620_26[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_FeatureSetDownlinkPerCC_v1620,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_featureSetDownlinkPerCC_v1620_tags_26[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_featureSetDownlinkPerCC_v1620_specs_26 = {
	sizeof(struct NR_FeatureSets__ext3__featureSetDownlinkPerCC_v1620),
	offsetof(struct NR_FeatureSets__ext3__featureSetDownlinkPerCC_v1620, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_featureSetDownlinkPerCC_v1620_26 = {
	"featureSetDownlinkPerCC-v1620",
	"featureSetDownlinkPerCC-v1620",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_featureSetDownlinkPerCC_v1620_tags_26,
	sizeof(asn_DEF_NR_featureSetDownlinkPerCC_v1620_tags_26)
		/sizeof(asn_DEF_NR_featureSetDownlinkPerCC_v1620_tags_26[0]) - 1, /* 1 */
	asn_DEF_NR_featureSetDownlinkPerCC_v1620_tags_26,	/* Same as above */
	sizeof(asn_DEF_NR_featureSetDownlinkPerCC_v1620_tags_26)
		/sizeof(asn_DEF_NR_featureSetDownlinkPerCC_v1620_tags_26[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_featureSetDownlinkPerCC_v1620_constr_26,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_featureSetDownlinkPerCC_v1620_26,
	1,	/* Single element */
	&asn_SPC_NR_featureSetDownlinkPerCC_v1620_specs_26	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext3_21[] = {
	{ ATF_POINTER, 3, offsetof(struct NR_FeatureSets__ext3, featureSetsDownlink_v1610),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_featureSetsDownlink_v1610_22,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_featureSetsDownlink_v1610_constr_22,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_featureSetsDownlink_v1610_constraint_21
		},
		0, 0, /* No default value */
		"featureSetsDownlink-v1610"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_FeatureSets__ext3, featureSetsUplink_v1610),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_featureSetsUplink_v1610_24,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_featureSetsUplink_v1610_constr_24,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_featureSetsUplink_v1610_constraint_21
		},
		0, 0, /* No default value */
		"featureSetsUplink-v1610"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_FeatureSets__ext3, featureSetDownlinkPerCC_v1620),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		0,
		&asn_DEF_NR_featureSetDownlinkPerCC_v1620_26,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_featureSetDownlinkPerCC_v1620_constr_26,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_featureSetDownlinkPerCC_v1620_constraint_21
		},
		0, 0, /* No default value */
		"featureSetDownlinkPerCC-v1620"
		},
};
static const int asn_MAP_NR_ext3_oms_21[] = { 0, 1, 2 };
static const ber_tlv_tag_t asn_DEF_NR_ext3_tags_21[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext3_tag2el_21[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* featureSetsDownlink-v1610 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* featureSetsUplink-v1610 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* featureSetDownlinkPerCC-v1620 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext3_specs_21 = {
	sizeof(struct NR_FeatureSets__ext3),
	offsetof(struct NR_FeatureSets__ext3, _asn_ctx),
	asn_MAP_NR_ext3_tag2el_21,
	3,	/* Count of tags in the map */
	asn_MAP_NR_ext3_oms_21,	/* Optional members */
	3, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext3_21 = {
	"ext3",
	"ext3",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext3_tags_21,
	sizeof(asn_DEF_NR_ext3_tags_21)
		/sizeof(asn_DEF_NR_ext3_tags_21[0]) - 1, /* 1 */
	asn_DEF_NR_ext3_tags_21,	/* Same as above */
	sizeof(asn_DEF_NR_ext3_tags_21)
		/sizeof(asn_DEF_NR_ext3_tags_21[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext3_21,
	3,	/* Elements count */
	&asn_SPC_NR_ext3_specs_21	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_featureSetsUplink_v1630_29[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_FeatureSetUplink_v1630,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_featureSetsUplink_v1630_tags_29[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_featureSetsUplink_v1630_specs_29 = {
	sizeof(struct NR_FeatureSets__ext4__featureSetsUplink_v1630),
	offsetof(struct NR_FeatureSets__ext4__featureSetsUplink_v1630, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_featureSetsUplink_v1630_29 = {
	"featureSetsUplink-v1630",
	"featureSetsUplink-v1630",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_featureSetsUplink_v1630_tags_29,
	sizeof(asn_DEF_NR_featureSetsUplink_v1630_tags_29)
		/sizeof(asn_DEF_NR_featureSetsUplink_v1630_tags_29[0]) - 1, /* 1 */
	asn_DEF_NR_featureSetsUplink_v1630_tags_29,	/* Same as above */
	sizeof(asn_DEF_NR_featureSetsUplink_v1630_tags_29)
		/sizeof(asn_DEF_NR_featureSetsUplink_v1630_tags_29[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_featureSetsUplink_v1630_constr_29,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_featureSetsUplink_v1630_29,
	1,	/* Single element */
	&asn_SPC_NR_featureSetsUplink_v1630_specs_29	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext4_28[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_FeatureSets__ext4, featureSetsUplink_v1630),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_featureSetsUplink_v1630_29,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_featureSetsUplink_v1630_constr_29,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_featureSetsUplink_v1630_constraint_28
		},
		0, 0, /* No default value */
		"featureSetsUplink-v1630"
		},
};
static const int asn_MAP_NR_ext4_oms_28[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext4_tags_28[] = {
	(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext4_tag2el_28[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* featureSetsUplink-v1630 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext4_specs_28 = {
	sizeof(struct NR_FeatureSets__ext4),
	offsetof(struct NR_FeatureSets__ext4, _asn_ctx),
	asn_MAP_NR_ext4_tag2el_28,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext4_oms_28,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext4_28 = {
	"ext4",
	"ext4",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext4_tags_28,
	sizeof(asn_DEF_NR_ext4_tags_28)
		/sizeof(asn_DEF_NR_ext4_tags_28[0]) - 1, /* 1 */
	asn_DEF_NR_ext4_tags_28,	/* Same as above */
	sizeof(asn_DEF_NR_ext4_tags_28)
		/sizeof(asn_DEF_NR_ext4_tags_28[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext4_28,
	1,	/* Elements count */
	&asn_SPC_NR_ext4_specs_28	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_featureSetsUplink_v1640_32[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_FeatureSetUplink_v1640,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_featureSetsUplink_v1640_tags_32[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_featureSetsUplink_v1640_specs_32 = {
	sizeof(struct NR_FeatureSets__ext5__featureSetsUplink_v1640),
	offsetof(struct NR_FeatureSets__ext5__featureSetsUplink_v1640, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_featureSetsUplink_v1640_32 = {
	"featureSetsUplink-v1640",
	"featureSetsUplink-v1640",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_featureSetsUplink_v1640_tags_32,
	sizeof(asn_DEF_NR_featureSetsUplink_v1640_tags_32)
		/sizeof(asn_DEF_NR_featureSetsUplink_v1640_tags_32[0]) - 1, /* 1 */
	asn_DEF_NR_featureSetsUplink_v1640_tags_32,	/* Same as above */
	sizeof(asn_DEF_NR_featureSetsUplink_v1640_tags_32)
		/sizeof(asn_DEF_NR_featureSetsUplink_v1640_tags_32[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_featureSetsUplink_v1640_constr_32,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_featureSetsUplink_v1640_32,
	1,	/* Single element */
	&asn_SPC_NR_featureSetsUplink_v1640_specs_32	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext5_31[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_FeatureSets__ext5, featureSetsUplink_v1640),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_featureSetsUplink_v1640_32,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_featureSetsUplink_v1640_constr_32,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_featureSetsUplink_v1640_constraint_31
		},
		0, 0, /* No default value */
		"featureSetsUplink-v1640"
		},
};
static const int asn_MAP_NR_ext5_oms_31[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext5_tags_31[] = {
	(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext5_tag2el_31[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* featureSetsUplink-v1640 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext5_specs_31 = {
	sizeof(struct NR_FeatureSets__ext5),
	offsetof(struct NR_FeatureSets__ext5, _asn_ctx),
	asn_MAP_NR_ext5_tag2el_31,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext5_oms_31,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext5_31 = {
	"ext5",
	"ext5",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext5_tags_31,
	sizeof(asn_DEF_NR_ext5_tags_31)
		/sizeof(asn_DEF_NR_ext5_tags_31[0]) - 1, /* 1 */
	asn_DEF_NR_ext5_tags_31,	/* Same as above */
	sizeof(asn_DEF_NR_ext5_tags_31)
		/sizeof(asn_DEF_NR_ext5_tags_31[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext5_31,
	1,	/* Elements count */
	&asn_SPC_NR_ext5_specs_31	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_featureSetsDownlink_v1700_35[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_FeatureSetDownlink_v1700,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_featureSetsDownlink_v1700_tags_35[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_featureSetsDownlink_v1700_specs_35 = {
	sizeof(struct NR_FeatureSets__ext6__featureSetsDownlink_v1700),
	offsetof(struct NR_FeatureSets__ext6__featureSetsDownlink_v1700, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_featureSetsDownlink_v1700_35 = {
	"featureSetsDownlink-v1700",
	"featureSetsDownlink-v1700",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_featureSetsDownlink_v1700_tags_35,
	sizeof(asn_DEF_NR_featureSetsDownlink_v1700_tags_35)
		/sizeof(asn_DEF_NR_featureSetsDownlink_v1700_tags_35[0]) - 1, /* 1 */
	asn_DEF_NR_featureSetsDownlink_v1700_tags_35,	/* Same as above */
	sizeof(asn_DEF_NR_featureSetsDownlink_v1700_tags_35)
		/sizeof(asn_DEF_NR_featureSetsDownlink_v1700_tags_35[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_featureSetsDownlink_v1700_constr_35,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_featureSetsDownlink_v1700_35,
	1,	/* Single element */
	&asn_SPC_NR_featureSetsDownlink_v1700_specs_35	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_featureSetsDownlinkPerCC_v1700_37[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_FeatureSetDownlinkPerCC_v1700,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_featureSetsDownlinkPerCC_v1700_tags_37[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_featureSetsDownlinkPerCC_v1700_specs_37 = {
	sizeof(struct NR_FeatureSets__ext6__featureSetsDownlinkPerCC_v1700),
	offsetof(struct NR_FeatureSets__ext6__featureSetsDownlinkPerCC_v1700, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_featureSetsDownlinkPerCC_v1700_37 = {
	"featureSetsDownlinkPerCC-v1700",
	"featureSetsDownlinkPerCC-v1700",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_featureSetsDownlinkPerCC_v1700_tags_37,
	sizeof(asn_DEF_NR_featureSetsDownlinkPerCC_v1700_tags_37)
		/sizeof(asn_DEF_NR_featureSetsDownlinkPerCC_v1700_tags_37[0]) - 1, /* 1 */
	asn_DEF_NR_featureSetsDownlinkPerCC_v1700_tags_37,	/* Same as above */
	sizeof(asn_DEF_NR_featureSetsDownlinkPerCC_v1700_tags_37)
		/sizeof(asn_DEF_NR_featureSetsDownlinkPerCC_v1700_tags_37[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_featureSetsDownlinkPerCC_v1700_constr_37,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_featureSetsDownlinkPerCC_v1700_37,
	1,	/* Single element */
	&asn_SPC_NR_featureSetsDownlinkPerCC_v1700_specs_37	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_featureSetsUplink_v1710_39[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_FeatureSetUplink_v1710,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_featureSetsUplink_v1710_tags_39[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_featureSetsUplink_v1710_specs_39 = {
	sizeof(struct NR_FeatureSets__ext6__featureSetsUplink_v1710),
	offsetof(struct NR_FeatureSets__ext6__featureSetsUplink_v1710, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_featureSetsUplink_v1710_39 = {
	"featureSetsUplink-v1710",
	"featureSetsUplink-v1710",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_featureSetsUplink_v1710_tags_39,
	sizeof(asn_DEF_NR_featureSetsUplink_v1710_tags_39)
		/sizeof(asn_DEF_NR_featureSetsUplink_v1710_tags_39[0]) - 1, /* 1 */
	asn_DEF_NR_featureSetsUplink_v1710_tags_39,	/* Same as above */
	sizeof(asn_DEF_NR_featureSetsUplink_v1710_tags_39)
		/sizeof(asn_DEF_NR_featureSetsUplink_v1710_tags_39[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_featureSetsUplink_v1710_constr_39,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_featureSetsUplink_v1710_39,
	1,	/* Single element */
	&asn_SPC_NR_featureSetsUplink_v1710_specs_39	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_featureSetsUplinkPerCC_v1700_41[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_FeatureSetUplinkPerCC_v1700,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_featureSetsUplinkPerCC_v1700_tags_41[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_featureSetsUplinkPerCC_v1700_specs_41 = {
	sizeof(struct NR_FeatureSets__ext6__featureSetsUplinkPerCC_v1700),
	offsetof(struct NR_FeatureSets__ext6__featureSetsUplinkPerCC_v1700, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_featureSetsUplinkPerCC_v1700_41 = {
	"featureSetsUplinkPerCC-v1700",
	"featureSetsUplinkPerCC-v1700",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_featureSetsUplinkPerCC_v1700_tags_41,
	sizeof(asn_DEF_NR_featureSetsUplinkPerCC_v1700_tags_41)
		/sizeof(asn_DEF_NR_featureSetsUplinkPerCC_v1700_tags_41[0]) - 1, /* 1 */
	asn_DEF_NR_featureSetsUplinkPerCC_v1700_tags_41,	/* Same as above */
	sizeof(asn_DEF_NR_featureSetsUplinkPerCC_v1700_tags_41)
		/sizeof(asn_DEF_NR_featureSetsUplinkPerCC_v1700_tags_41[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_featureSetsUplinkPerCC_v1700_constr_41,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_featureSetsUplinkPerCC_v1700_41,
	1,	/* Single element */
	&asn_SPC_NR_featureSetsUplinkPerCC_v1700_specs_41	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext6_34[] = {
	{ ATF_POINTER, 4, offsetof(struct NR_FeatureSets__ext6, featureSetsDownlink_v1700),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_featureSetsDownlink_v1700_35,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_featureSetsDownlink_v1700_constr_35,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_featureSetsDownlink_v1700_constraint_34
		},
		0, 0, /* No default value */
		"featureSetsDownlink-v1700"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_FeatureSets__ext6, featureSetsDownlinkPerCC_v1700),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_featureSetsDownlinkPerCC_v1700_37,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_featureSetsDownlinkPerCC_v1700_constr_37,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_featureSetsDownlinkPerCC_v1700_constraint_34
		},
		0, 0, /* No default value */
		"featureSetsDownlinkPerCC-v1700"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_FeatureSets__ext6, featureSetsUplink_v1710),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		0,
		&asn_DEF_NR_featureSetsUplink_v1710_39,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_featureSetsUplink_v1710_constr_39,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_featureSetsUplink_v1710_constraint_34
		},
		0, 0, /* No default value */
		"featureSetsUplink-v1710"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_FeatureSets__ext6, featureSetsUplinkPerCC_v1700),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		0,
		&asn_DEF_NR_featureSetsUplinkPerCC_v1700_41,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_featureSetsUplinkPerCC_v1700_constr_41,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_featureSetsUplinkPerCC_v1700_constraint_34
		},
		0, 0, /* No default value */
		"featureSetsUplinkPerCC-v1700"
		},
};
static const int asn_MAP_NR_ext6_oms_34[] = { 0, 1, 2, 3 };
static const ber_tlv_tag_t asn_DEF_NR_ext6_tags_34[] = {
	(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext6_tag2el_34[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* featureSetsDownlink-v1700 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* featureSetsDownlinkPerCC-v1700 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* featureSetsUplink-v1710 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 } /* featureSetsUplinkPerCC-v1700 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext6_specs_34 = {
	sizeof(struct NR_FeatureSets__ext6),
	offsetof(struct NR_FeatureSets__ext6, _asn_ctx),
	asn_MAP_NR_ext6_tag2el_34,
	4,	/* Count of tags in the map */
	asn_MAP_NR_ext6_oms_34,	/* Optional members */
	4, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext6_34 = {
	"ext6",
	"ext6",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext6_tags_34,
	sizeof(asn_DEF_NR_ext6_tags_34)
		/sizeof(asn_DEF_NR_ext6_tags_34[0]) - 1, /* 1 */
	asn_DEF_NR_ext6_tags_34,	/* Same as above */
	sizeof(asn_DEF_NR_ext6_tags_34)
		/sizeof(asn_DEF_NR_ext6_tags_34[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext6_34,
	4,	/* Elements count */
	&asn_SPC_NR_ext6_specs_34	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_featureSetsDownlink_v1720_44[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_FeatureSetDownlink_v1720,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_featureSetsDownlink_v1720_tags_44[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_featureSetsDownlink_v1720_specs_44 = {
	sizeof(struct NR_FeatureSets__ext7__featureSetsDownlink_v1720),
	offsetof(struct NR_FeatureSets__ext7__featureSetsDownlink_v1720, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_featureSetsDownlink_v1720_44 = {
	"featureSetsDownlink-v1720",
	"featureSetsDownlink-v1720",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_featureSetsDownlink_v1720_tags_44,
	sizeof(asn_DEF_NR_featureSetsDownlink_v1720_tags_44)
		/sizeof(asn_DEF_NR_featureSetsDownlink_v1720_tags_44[0]) - 1, /* 1 */
	asn_DEF_NR_featureSetsDownlink_v1720_tags_44,	/* Same as above */
	sizeof(asn_DEF_NR_featureSetsDownlink_v1720_tags_44)
		/sizeof(asn_DEF_NR_featureSetsDownlink_v1720_tags_44[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_featureSetsDownlink_v1720_constr_44,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_featureSetsDownlink_v1720_44,
	1,	/* Single element */
	&asn_SPC_NR_featureSetsDownlink_v1720_specs_44	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_featureSetsDownlinkPerCC_v1720_46[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_FeatureSetDownlinkPerCC_v1720,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_featureSetsDownlinkPerCC_v1720_tags_46[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_featureSetsDownlinkPerCC_v1720_specs_46 = {
	sizeof(struct NR_FeatureSets__ext7__featureSetsDownlinkPerCC_v1720),
	offsetof(struct NR_FeatureSets__ext7__featureSetsDownlinkPerCC_v1720, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_featureSetsDownlinkPerCC_v1720_46 = {
	"featureSetsDownlinkPerCC-v1720",
	"featureSetsDownlinkPerCC-v1720",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_featureSetsDownlinkPerCC_v1720_tags_46,
	sizeof(asn_DEF_NR_featureSetsDownlinkPerCC_v1720_tags_46)
		/sizeof(asn_DEF_NR_featureSetsDownlinkPerCC_v1720_tags_46[0]) - 1, /* 1 */
	asn_DEF_NR_featureSetsDownlinkPerCC_v1720_tags_46,	/* Same as above */
	sizeof(asn_DEF_NR_featureSetsDownlinkPerCC_v1720_tags_46)
		/sizeof(asn_DEF_NR_featureSetsDownlinkPerCC_v1720_tags_46[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_featureSetsDownlinkPerCC_v1720_constr_46,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_featureSetsDownlinkPerCC_v1720_46,
	1,	/* Single element */
	&asn_SPC_NR_featureSetsDownlinkPerCC_v1720_specs_46	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_featureSetsUplink_v1720_48[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_FeatureSetUplink_v1720,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_featureSetsUplink_v1720_tags_48[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_featureSetsUplink_v1720_specs_48 = {
	sizeof(struct NR_FeatureSets__ext7__featureSetsUplink_v1720),
	offsetof(struct NR_FeatureSets__ext7__featureSetsUplink_v1720, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_featureSetsUplink_v1720_48 = {
	"featureSetsUplink-v1720",
	"featureSetsUplink-v1720",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_featureSetsUplink_v1720_tags_48,
	sizeof(asn_DEF_NR_featureSetsUplink_v1720_tags_48)
		/sizeof(asn_DEF_NR_featureSetsUplink_v1720_tags_48[0]) - 1, /* 1 */
	asn_DEF_NR_featureSetsUplink_v1720_tags_48,	/* Same as above */
	sizeof(asn_DEF_NR_featureSetsUplink_v1720_tags_48)
		/sizeof(asn_DEF_NR_featureSetsUplink_v1720_tags_48[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_featureSetsUplink_v1720_constr_48,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_featureSetsUplink_v1720_48,
	1,	/* Single element */
	&asn_SPC_NR_featureSetsUplink_v1720_specs_48	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext7_43[] = {
	{ ATF_POINTER, 3, offsetof(struct NR_FeatureSets__ext7, featureSetsDownlink_v1720),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_featureSetsDownlink_v1720_44,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_featureSetsDownlink_v1720_constr_44,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_featureSetsDownlink_v1720_constraint_43
		},
		0, 0, /* No default value */
		"featureSetsDownlink-v1720"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_FeatureSets__ext7, featureSetsDownlinkPerCC_v1720),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_featureSetsDownlinkPerCC_v1720_46,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_featureSetsDownlinkPerCC_v1720_constr_46,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_featureSetsDownlinkPerCC_v1720_constraint_43
		},
		0, 0, /* No default value */
		"featureSetsDownlinkPerCC-v1720"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_FeatureSets__ext7, featureSetsUplink_v1720),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		0,
		&asn_DEF_NR_featureSetsUplink_v1720_48,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_featureSetsUplink_v1720_constr_48,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_featureSetsUplink_v1720_constraint_43
		},
		0, 0, /* No default value */
		"featureSetsUplink-v1720"
		},
};
static const int asn_MAP_NR_ext7_oms_43[] = { 0, 1, 2 };
static const ber_tlv_tag_t asn_DEF_NR_ext7_tags_43[] = {
	(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext7_tag2el_43[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* featureSetsDownlink-v1720 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* featureSetsDownlinkPerCC-v1720 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* featureSetsUplink-v1720 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext7_specs_43 = {
	sizeof(struct NR_FeatureSets__ext7),
	offsetof(struct NR_FeatureSets__ext7, _asn_ctx),
	asn_MAP_NR_ext7_tag2el_43,
	3,	/* Count of tags in the map */
	asn_MAP_NR_ext7_oms_43,	/* Optional members */
	3, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext7_43 = {
	"ext7",
	"ext7",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext7_tags_43,
	sizeof(asn_DEF_NR_ext7_tags_43)
		/sizeof(asn_DEF_NR_ext7_tags_43[0]) - 1, /* 1 */
	asn_DEF_NR_ext7_tags_43,	/* Same as above */
	sizeof(asn_DEF_NR_ext7_tags_43)
		/sizeof(asn_DEF_NR_ext7_tags_43[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext7_43,
	3,	/* Elements count */
	&asn_SPC_NR_ext7_specs_43	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_featureSetsDownlink_v1730_51[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_FeatureSetDownlink_v1730,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_featureSetsDownlink_v1730_tags_51[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_featureSetsDownlink_v1730_specs_51 = {
	sizeof(struct NR_FeatureSets__ext8__featureSetsDownlink_v1730),
	offsetof(struct NR_FeatureSets__ext8__featureSetsDownlink_v1730, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_featureSetsDownlink_v1730_51 = {
	"featureSetsDownlink-v1730",
	"featureSetsDownlink-v1730",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_featureSetsDownlink_v1730_tags_51,
	sizeof(asn_DEF_NR_featureSetsDownlink_v1730_tags_51)
		/sizeof(asn_DEF_NR_featureSetsDownlink_v1730_tags_51[0]) - 1, /* 1 */
	asn_DEF_NR_featureSetsDownlink_v1730_tags_51,	/* Same as above */
	sizeof(asn_DEF_NR_featureSetsDownlink_v1730_tags_51)
		/sizeof(asn_DEF_NR_featureSetsDownlink_v1730_tags_51[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_featureSetsDownlink_v1730_constr_51,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_featureSetsDownlink_v1730_51,
	1,	/* Single element */
	&asn_SPC_NR_featureSetsDownlink_v1730_specs_51	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_featureSetsDownlinkPerCC_v1730_53[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_FeatureSetDownlinkPerCC_v1730,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_featureSetsDownlinkPerCC_v1730_tags_53[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_featureSetsDownlinkPerCC_v1730_specs_53 = {
	sizeof(struct NR_FeatureSets__ext8__featureSetsDownlinkPerCC_v1730),
	offsetof(struct NR_FeatureSets__ext8__featureSetsDownlinkPerCC_v1730, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_featureSetsDownlinkPerCC_v1730_53 = {
	"featureSetsDownlinkPerCC-v1730",
	"featureSetsDownlinkPerCC-v1730",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_featureSetsDownlinkPerCC_v1730_tags_53,
	sizeof(asn_DEF_NR_featureSetsDownlinkPerCC_v1730_tags_53)
		/sizeof(asn_DEF_NR_featureSetsDownlinkPerCC_v1730_tags_53[0]) - 1, /* 1 */
	asn_DEF_NR_featureSetsDownlinkPerCC_v1730_tags_53,	/* Same as above */
	sizeof(asn_DEF_NR_featureSetsDownlinkPerCC_v1730_tags_53)
		/sizeof(asn_DEF_NR_featureSetsDownlinkPerCC_v1730_tags_53[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_featureSetsDownlinkPerCC_v1730_constr_53,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_featureSetsDownlinkPerCC_v1730_53,
	1,	/* Single element */
	&asn_SPC_NR_featureSetsDownlinkPerCC_v1730_specs_53	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext8_50[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_FeatureSets__ext8, featureSetsDownlink_v1730),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_featureSetsDownlink_v1730_51,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_featureSetsDownlink_v1730_constr_51,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_featureSetsDownlink_v1730_constraint_50
		},
		0, 0, /* No default value */
		"featureSetsDownlink-v1730"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_FeatureSets__ext8, featureSetsDownlinkPerCC_v1730),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_featureSetsDownlinkPerCC_v1730_53,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_featureSetsDownlinkPerCC_v1730_constr_53,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_featureSetsDownlinkPerCC_v1730_constraint_50
		},
		0, 0, /* No default value */
		"featureSetsDownlinkPerCC-v1730"
		},
};
static const int asn_MAP_NR_ext8_oms_50[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_ext8_tags_50[] = {
	(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext8_tag2el_50[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* featureSetsDownlink-v1730 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* featureSetsDownlinkPerCC-v1730 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext8_specs_50 = {
	sizeof(struct NR_FeatureSets__ext8),
	offsetof(struct NR_FeatureSets__ext8, _asn_ctx),
	asn_MAP_NR_ext8_tag2el_50,
	2,	/* Count of tags in the map */
	asn_MAP_NR_ext8_oms_50,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext8_50 = {
	"ext8",
	"ext8",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext8_tags_50,
	sizeof(asn_DEF_NR_ext8_tags_50)
		/sizeof(asn_DEF_NR_ext8_tags_50[0]) - 1, /* 1 */
	asn_DEF_NR_ext8_tags_50,	/* Same as above */
	sizeof(asn_DEF_NR_ext8_tags_50)
		/sizeof(asn_DEF_NR_ext8_tags_50[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext8_50,
	2,	/* Elements count */
	&asn_SPC_NR_ext8_specs_50	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_FeatureSets_1[] = {
	{ ATF_POINTER, 12, offsetof(struct NR_FeatureSets, featureSetsDownlink),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_featureSetsDownlink_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_featureSetsDownlink_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_featureSetsDownlink_constraint_1
		},
		0, 0, /* No default value */
		"featureSetsDownlink"
		},
	{ ATF_POINTER, 11, offsetof(struct NR_FeatureSets, featureSetsDownlinkPerCC),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_featureSetsDownlinkPerCC_4,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_featureSetsDownlinkPerCC_constr_4,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_featureSetsDownlinkPerCC_constraint_1
		},
		0, 0, /* No default value */
		"featureSetsDownlinkPerCC"
		},
	{ ATF_POINTER, 10, offsetof(struct NR_FeatureSets, featureSetsUplink),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		0,
		&asn_DEF_NR_featureSetsUplink_6,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_featureSetsUplink_constr_6,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_featureSetsUplink_constraint_1
		},
		0, 0, /* No default value */
		"featureSetsUplink"
		},
	{ ATF_POINTER, 9, offsetof(struct NR_FeatureSets, featureSetsUplinkPerCC),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		0,
		&asn_DEF_NR_featureSetsUplinkPerCC_8,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_featureSetsUplinkPerCC_constr_8,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_featureSetsUplinkPerCC_constraint_1
		},
		0, 0, /* No default value */
		"featureSetsUplinkPerCC"
		},
	{ ATF_POINTER, 8, offsetof(struct NR_FeatureSets, ext1),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		0,
		&asn_DEF_NR_ext1_11,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
	{ ATF_POINTER, 7, offsetof(struct NR_FeatureSets, ext2),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		0,
		&asn_DEF_NR_ext2_18,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext2"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_FeatureSets, ext3),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		0,
		&asn_DEF_NR_ext3_21,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext3"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_FeatureSets, ext4),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		0,
		&asn_DEF_NR_ext4_28,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext4"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_FeatureSets, ext5),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		0,
		&asn_DEF_NR_ext5_31,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext5"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_FeatureSets, ext6),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		0,
		&asn_DEF_NR_ext6_34,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext6"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_FeatureSets, ext7),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		0,
		&asn_DEF_NR_ext7_43,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext7"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_FeatureSets, ext8),
		(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
		0,
		&asn_DEF_NR_ext8_50,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext8"
		},
};
static const int asn_MAP_NR_FeatureSets_oms_1[] = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11 };
static const ber_tlv_tag_t asn_DEF_NR_FeatureSets_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_FeatureSets_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* featureSetsDownlink */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* featureSetsDownlinkPerCC */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* featureSetsUplink */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* featureSetsUplinkPerCC */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* ext1 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* ext2 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* ext3 */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* ext4 */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* ext5 */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* ext6 */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 }, /* ext7 */
    { (ASN_TAG_CLASS_CONTEXT | (11 << 2)), 11, 0, 0 } /* ext8 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_FeatureSets_specs_1 = {
	sizeof(struct NR_FeatureSets),
	offsetof(struct NR_FeatureSets, _asn_ctx),
	asn_MAP_NR_FeatureSets_tag2el_1,
	12,	/* Count of tags in the map */
	asn_MAP_NR_FeatureSets_oms_1,	/* Optional members */
	4, 8,	/* Root/Additions */
	4,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_FeatureSets = {
	"FeatureSets",
	"FeatureSets",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_FeatureSets_tags_1,
	sizeof(asn_DEF_NR_FeatureSets_tags_1)
		/sizeof(asn_DEF_NR_FeatureSets_tags_1[0]), /* 1 */
	asn_DEF_NR_FeatureSets_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_FeatureSets_tags_1)
		/sizeof(asn_DEF_NR_FeatureSets_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_FeatureSets_1,
	12,	/* Elements count */
	&asn_SPC_NR_FeatureSets_specs_1	/* Additional specs */
};

