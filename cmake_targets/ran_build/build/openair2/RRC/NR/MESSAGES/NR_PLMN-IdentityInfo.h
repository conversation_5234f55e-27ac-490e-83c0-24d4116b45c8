/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_PLMN_IdentityInfo_H_
#define	_NR_PLMN_IdentityInfo_H_


#include <asn_application.h>

/* Including external dependencies */
#include "NR_TrackingAreaCode.h"
#include "NR_RAN-AreaCode.h"
#include "NR_CellIdentity.h"
#include <NativeEnumerated.h>
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include <constr_SEQUENCE.h>
#include <NativeInteger.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_PLMN_IdentityInfo__cellReservedForOperatorUse {
	NR_PLMN_IdentityInfo__cellReservedForOperatorUse_reserved	= 0,
	NR_PLMN_IdentityInfo__cellReservedForOperatorUse_notReserved	= 1
} e_NR_PLMN_IdentityInfo__cellReservedForOperatorUse;
typedef enum NR_PLMN_IdentityInfo__ext1__iab_Support_r16 {
	NR_PLMN_IdentityInfo__ext1__iab_Support_r16_true	= 0
} e_NR_PLMN_IdentityInfo__ext1__iab_Support_r16;

/* Forward declarations */
struct NR_PLMN_Identity;

/* NR_PLMN-IdentityInfo */
typedef struct NR_PLMN_IdentityInfo {
	struct NR_PLMN_IdentityInfo__plmn_IdentityList {
		A_SEQUENCE_OF(struct NR_PLMN_Identity) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} plmn_IdentityList;
	NR_TrackingAreaCode_t	*trackingAreaCode;	/* OPTIONAL */
	NR_RAN_AreaCode_t	*ranac;	/* OPTIONAL */
	NR_CellIdentity_t	 cellIdentity;
	long	 cellReservedForOperatorUse;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	struct NR_PLMN_IdentityInfo__ext1 {
		long	*iab_Support_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext1;
	struct NR_PLMN_IdentityInfo__ext2 {
		struct NR_PLMN_IdentityInfo__ext2__trackingAreaList_r17 {
			A_SEQUENCE_OF(NR_TrackingAreaCode_t) list;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *trackingAreaList_r17;
		long	*gNB_ID_Length_r17;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext2;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_PLMN_IdentityInfo_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_cellReservedForOperatorUse_7;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_iab_Support_r16_12;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_PLMN_IdentityInfo;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_PLMN_IdentityInfo_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_PLMN_IdentityInfo_1[7];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_PLMN-Identity.h"

#endif	/* _NR_PLMN_IdentityInfo_H_ */
#include <asn_internal.h>
