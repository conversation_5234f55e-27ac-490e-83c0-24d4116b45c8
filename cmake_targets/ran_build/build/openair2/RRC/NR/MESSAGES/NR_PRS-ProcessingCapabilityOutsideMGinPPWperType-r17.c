/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_PRS-ProcessingCapabilityOutsideMGinPPWperType-r17.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_prsProcessingType_r17_constr_2 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ppw_dl_PRS_BufferType_r17_constr_6 CC_NOTUSED = {
	{ APC_CONSTRAINED | APC_EXTENSIBLE,  1,  1,  0,  1 }	/* (0..1,...) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ppw_durationOfPRS_ProcessingSymbolsN_r17_constr_12 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 5,  5,  0,  17 }	/* (0..17) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ppw_durationOfPRS_ProcessingSymbolsT_r17_constr_31 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  12 }	/* (0..12) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ppw_durationOfPRS_ProcessingSymbolsN2_r17_constr_46 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  10 }	/* (0..10) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ppw_durationOfPRS_ProcessingSymbolsT2_r17_constr_58 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ppw_durationOfPRS_Processing_r17_constr_10 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_scs15_r17_constr_64 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  10 }	/* (0..10) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_scs30_r17_constr_76 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  10 }	/* (0..10) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_scs60_r17_constr_88 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  10 }	/* (0..10) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_scs120_r17_constr_100 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  10 }	/* (0..10) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_fr1_r17_constr_114 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  6 }	/* (0..6) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_fr2_r17_constr_122 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ppw_maxNumOfDL_Bandwidth_r17_constr_113 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_prsProcessingType_r17_value2enum_2[] = {
	{ 0,	6,	"type1A" },
	{ 1,	6,	"type1B" },
	{ 2,	5,	"type2" }
};
static const unsigned int asn_MAP_NR_prsProcessingType_r17_enum2value_2[] = {
	0,	/* type1A(0) */
	1,	/* type1B(1) */
	2	/* type2(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_prsProcessingType_r17_specs_2 = {
	asn_MAP_NR_prsProcessingType_r17_value2enum_2,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_prsProcessingType_r17_enum2value_2,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_prsProcessingType_r17_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_prsProcessingType_r17_2 = {
	"prsProcessingType-r17",
	"prsProcessingType-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_prsProcessingType_r17_tags_2,
	sizeof(asn_DEF_NR_prsProcessingType_r17_tags_2)
		/sizeof(asn_DEF_NR_prsProcessingType_r17_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_prsProcessingType_r17_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_prsProcessingType_r17_tags_2)
		/sizeof(asn_DEF_NR_prsProcessingType_r17_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_prsProcessingType_r17_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_prsProcessingType_r17_specs_2	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_ppw_dl_PRS_BufferType_r17_value2enum_6[] = {
	{ 0,	5,	"type1" },
	{ 1,	5,	"type2" }
	/* This list is extensible */
};
static const unsigned int asn_MAP_NR_ppw_dl_PRS_BufferType_r17_enum2value_6[] = {
	0,	/* type1(0) */
	1	/* type2(1) */
	/* This list is extensible */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ppw_dl_PRS_BufferType_r17_specs_6 = {
	asn_MAP_NR_ppw_dl_PRS_BufferType_r17_value2enum_6,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ppw_dl_PRS_BufferType_r17_enum2value_6,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	3,	/* Extensions before this member */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ppw_dl_PRS_BufferType_r17_tags_6[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ppw_dl_PRS_BufferType_r17_6 = {
	"ppw-dl-PRS-BufferType-r17",
	"ppw-dl-PRS-BufferType-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ppw_dl_PRS_BufferType_r17_tags_6,
	sizeof(asn_DEF_NR_ppw_dl_PRS_BufferType_r17_tags_6)
		/sizeof(asn_DEF_NR_ppw_dl_PRS_BufferType_r17_tags_6[0]) - 1, /* 1 */
	asn_DEF_NR_ppw_dl_PRS_BufferType_r17_tags_6,	/* Same as above */
	sizeof(asn_DEF_NR_ppw_dl_PRS_BufferType_r17_tags_6)
		/sizeof(asn_DEF_NR_ppw_dl_PRS_BufferType_r17_tags_6[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ppw_dl_PRS_BufferType_r17_constr_6,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ppw_dl_PRS_BufferType_r17_specs_6	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_ppw_durationOfPRS_ProcessingSymbolsN_r17_value2enum_12[] = {
	{ 0,	8,	"msDot125" },
	{ 1,	7,	"msDot25" },
	{ 2,	6,	"msDot5" },
	{ 3,	3,	"ms1" },
	{ 4,	3,	"ms2" },
	{ 5,	3,	"ms4" },
	{ 6,	3,	"ms6" },
	{ 7,	3,	"ms8" },
	{ 8,	4,	"ms12" },
	{ 9,	4,	"ms16" },
	{ 10,	4,	"ms20" },
	{ 11,	4,	"ms25" },
	{ 12,	4,	"ms30" },
	{ 13,	4,	"ms32" },
	{ 14,	4,	"ms35" },
	{ 15,	4,	"ms40" },
	{ 16,	4,	"ms45" },
	{ 17,	4,	"ms50" }
};
static const unsigned int asn_MAP_NR_ppw_durationOfPRS_ProcessingSymbolsN_r17_enum2value_12[] = {
	3,	/* ms1(3) */
	8,	/* ms12(8) */
	9,	/* ms16(9) */
	4,	/* ms2(4) */
	10,	/* ms20(10) */
	11,	/* ms25(11) */
	12,	/* ms30(12) */
	13,	/* ms32(13) */
	14,	/* ms35(14) */
	5,	/* ms4(5) */
	15,	/* ms40(15) */
	16,	/* ms45(16) */
	17,	/* ms50(17) */
	6,	/* ms6(6) */
	7,	/* ms8(7) */
	0,	/* msDot125(0) */
	1,	/* msDot25(1) */
	2	/* msDot5(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ppw_durationOfPRS_ProcessingSymbolsN_r17_specs_12 = {
	asn_MAP_NR_ppw_durationOfPRS_ProcessingSymbolsN_r17_value2enum_12,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ppw_durationOfPRS_ProcessingSymbolsN_r17_enum2value_12,	/* N => "tag"; sorted by N */
	18,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsN_r17_tags_12[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsN_r17_12 = {
	"ppw-durationOfPRS-ProcessingSymbolsN-r17",
	"ppw-durationOfPRS-ProcessingSymbolsN-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsN_r17_tags_12,
	sizeof(asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsN_r17_tags_12)
		/sizeof(asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsN_r17_tags_12[0]) - 1, /* 1 */
	asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsN_r17_tags_12,	/* Same as above */
	sizeof(asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsN_r17_tags_12)
		/sizeof(asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsN_r17_tags_12[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ppw_durationOfPRS_ProcessingSymbolsN_r17_constr_12,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ppw_durationOfPRS_ProcessingSymbolsN_r17_specs_12	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_ppw_durationOfPRS_ProcessingSymbolsT_r17_value2enum_31[] = {
	{ 0,	3,	"ms1" },
	{ 1,	3,	"ms2" },
	{ 2,	3,	"ms4" },
	{ 3,	3,	"ms8" },
	{ 4,	4,	"ms16" },
	{ 5,	4,	"ms20" },
	{ 6,	4,	"ms30" },
	{ 7,	4,	"ms40" },
	{ 8,	4,	"ms80" },
	{ 9,	5,	"ms160" },
	{ 10,	5,	"ms320" },
	{ 11,	5,	"ms640" },
	{ 12,	6,	"ms1280" }
};
static const unsigned int asn_MAP_NR_ppw_durationOfPRS_ProcessingSymbolsT_r17_enum2value_31[] = {
	0,	/* ms1(0) */
	12,	/* ms1280(12) */
	4,	/* ms16(4) */
	9,	/* ms160(9) */
	1,	/* ms2(1) */
	5,	/* ms20(5) */
	6,	/* ms30(6) */
	10,	/* ms320(10) */
	2,	/* ms4(2) */
	7,	/* ms40(7) */
	11,	/* ms640(11) */
	3,	/* ms8(3) */
	8	/* ms80(8) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ppw_durationOfPRS_ProcessingSymbolsT_r17_specs_31 = {
	asn_MAP_NR_ppw_durationOfPRS_ProcessingSymbolsT_r17_value2enum_31,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ppw_durationOfPRS_ProcessingSymbolsT_r17_enum2value_31,	/* N => "tag"; sorted by N */
	13,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsT_r17_tags_31[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsT_r17_31 = {
	"ppw-durationOfPRS-ProcessingSymbolsT-r17",
	"ppw-durationOfPRS-ProcessingSymbolsT-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsT_r17_tags_31,
	sizeof(asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsT_r17_tags_31)
		/sizeof(asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsT_r17_tags_31[0]) - 1, /* 1 */
	asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsT_r17_tags_31,	/* Same as above */
	sizeof(asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsT_r17_tags_31)
		/sizeof(asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsT_r17_tags_31[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ppw_durationOfPRS_ProcessingSymbolsT_r17_constr_31,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ppw_durationOfPRS_ProcessingSymbolsT_r17_specs_31	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ppw_durationOfPRS_Processing1_r17_11[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17__ppw_durationOfPRS_Processing_r17__ppw_durationOfPRS_Processing1_r17, ppw_durationOfPRS_ProcessingSymbolsN_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsN_r17_12,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ppw-durationOfPRS-ProcessingSymbolsN-r17"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17__ppw_durationOfPRS_Processing_r17__ppw_durationOfPRS_Processing1_r17, ppw_durationOfPRS_ProcessingSymbolsT_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsT_r17_31,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ppw-durationOfPRS-ProcessingSymbolsT-r17"
		},
};
static const ber_tlv_tag_t asn_DEF_NR_ppw_durationOfPRS_Processing1_r17_tags_11[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ppw_durationOfPRS_Processing1_r17_tag2el_11[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* ppw-durationOfPRS-ProcessingSymbolsN-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* ppw-durationOfPRS-ProcessingSymbolsT-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ppw_durationOfPRS_Processing1_r17_specs_11 = {
	sizeof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17__ppw_durationOfPRS_Processing_r17__ppw_durationOfPRS_Processing1_r17),
	offsetof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17__ppw_durationOfPRS_Processing_r17__ppw_durationOfPRS_Processing1_r17, _asn_ctx),
	asn_MAP_NR_ppw_durationOfPRS_Processing1_r17_tag2el_11,
	2,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ppw_durationOfPRS_Processing1_r17_11 = {
	"ppw-durationOfPRS-Processing1-r17",
	"ppw-durationOfPRS-Processing1-r17",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ppw_durationOfPRS_Processing1_r17_tags_11,
	sizeof(asn_DEF_NR_ppw_durationOfPRS_Processing1_r17_tags_11)
		/sizeof(asn_DEF_NR_ppw_durationOfPRS_Processing1_r17_tags_11[0]) - 1, /* 1 */
	asn_DEF_NR_ppw_durationOfPRS_Processing1_r17_tags_11,	/* Same as above */
	sizeof(asn_DEF_NR_ppw_durationOfPRS_Processing1_r17_tags_11)
		/sizeof(asn_DEF_NR_ppw_durationOfPRS_Processing1_r17_tags_11[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ppw_durationOfPRS_Processing1_r17_11,
	2,	/* Elements count */
	&asn_SPC_NR_ppw_durationOfPRS_Processing1_r17_specs_11	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_ppw_durationOfPRS_ProcessingSymbolsN2_r17_value2enum_46[] = {
	{ 0,	8,	"msDot125" },
	{ 1,	7,	"msDot25" },
	{ 2,	6,	"msDot5" },
	{ 3,	3,	"ms1" },
	{ 4,	3,	"ms2" },
	{ 5,	3,	"ms3" },
	{ 6,	3,	"ms4" },
	{ 7,	3,	"ms5" },
	{ 8,	3,	"ms6" },
	{ 9,	3,	"ms8" },
	{ 10,	4,	"ms12" }
};
static const unsigned int asn_MAP_NR_ppw_durationOfPRS_ProcessingSymbolsN2_r17_enum2value_46[] = {
	3,	/* ms1(3) */
	10,	/* ms12(10) */
	4,	/* ms2(4) */
	5,	/* ms3(5) */
	6,	/* ms4(6) */
	7,	/* ms5(7) */
	8,	/* ms6(8) */
	9,	/* ms8(9) */
	0,	/* msDot125(0) */
	1,	/* msDot25(1) */
	2	/* msDot5(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ppw_durationOfPRS_ProcessingSymbolsN2_r17_specs_46 = {
	asn_MAP_NR_ppw_durationOfPRS_ProcessingSymbolsN2_r17_value2enum_46,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ppw_durationOfPRS_ProcessingSymbolsN2_r17_enum2value_46,	/* N => "tag"; sorted by N */
	11,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsN2_r17_tags_46[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsN2_r17_46 = {
	"ppw-durationOfPRS-ProcessingSymbolsN2-r17",
	"ppw-durationOfPRS-ProcessingSymbolsN2-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsN2_r17_tags_46,
	sizeof(asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsN2_r17_tags_46)
		/sizeof(asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsN2_r17_tags_46[0]) - 1, /* 1 */
	asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsN2_r17_tags_46,	/* Same as above */
	sizeof(asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsN2_r17_tags_46)
		/sizeof(asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsN2_r17_tags_46[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ppw_durationOfPRS_ProcessingSymbolsN2_r17_constr_46,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ppw_durationOfPRS_ProcessingSymbolsN2_r17_specs_46	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_ppw_durationOfPRS_ProcessingSymbolsT2_r17_value2enum_58[] = {
	{ 0,	3,	"ms4" },
	{ 1,	3,	"ms5" },
	{ 2,	3,	"ms6" },
	{ 3,	3,	"ms8" }
};
static const unsigned int asn_MAP_NR_ppw_durationOfPRS_ProcessingSymbolsT2_r17_enum2value_58[] = {
	0,	/* ms4(0) */
	1,	/* ms5(1) */
	2,	/* ms6(2) */
	3	/* ms8(3) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ppw_durationOfPRS_ProcessingSymbolsT2_r17_specs_58 = {
	asn_MAP_NR_ppw_durationOfPRS_ProcessingSymbolsT2_r17_value2enum_58,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ppw_durationOfPRS_ProcessingSymbolsT2_r17_enum2value_58,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsT2_r17_tags_58[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsT2_r17_58 = {
	"ppw-durationOfPRS-ProcessingSymbolsT2-r17",
	"ppw-durationOfPRS-ProcessingSymbolsT2-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsT2_r17_tags_58,
	sizeof(asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsT2_r17_tags_58)
		/sizeof(asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsT2_r17_tags_58[0]) - 1, /* 1 */
	asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsT2_r17_tags_58,	/* Same as above */
	sizeof(asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsT2_r17_tags_58)
		/sizeof(asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsT2_r17_tags_58[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ppw_durationOfPRS_ProcessingSymbolsT2_r17_constr_58,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ppw_durationOfPRS_ProcessingSymbolsT2_r17_specs_58	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ppw_durationOfPRS_Processing2_r17_45[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17__ppw_durationOfPRS_Processing_r17__ppw_durationOfPRS_Processing2_r17, ppw_durationOfPRS_ProcessingSymbolsN2_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsN2_r17_46,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ppw-durationOfPRS-ProcessingSymbolsN2-r17"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17__ppw_durationOfPRS_Processing_r17__ppw_durationOfPRS_Processing2_r17, ppw_durationOfPRS_ProcessingSymbolsT2_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ppw_durationOfPRS_ProcessingSymbolsT2_r17_58,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ppw-durationOfPRS-ProcessingSymbolsT2-r17"
		},
};
static const ber_tlv_tag_t asn_DEF_NR_ppw_durationOfPRS_Processing2_r17_tags_45[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ppw_durationOfPRS_Processing2_r17_tag2el_45[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* ppw-durationOfPRS-ProcessingSymbolsN2-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* ppw-durationOfPRS-ProcessingSymbolsT2-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ppw_durationOfPRS_Processing2_r17_specs_45 = {
	sizeof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17__ppw_durationOfPRS_Processing_r17__ppw_durationOfPRS_Processing2_r17),
	offsetof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17__ppw_durationOfPRS_Processing_r17__ppw_durationOfPRS_Processing2_r17, _asn_ctx),
	asn_MAP_NR_ppw_durationOfPRS_Processing2_r17_tag2el_45,
	2,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ppw_durationOfPRS_Processing2_r17_45 = {
	"ppw-durationOfPRS-Processing2-r17",
	"ppw-durationOfPRS-Processing2-r17",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ppw_durationOfPRS_Processing2_r17_tags_45,
	sizeof(asn_DEF_NR_ppw_durationOfPRS_Processing2_r17_tags_45)
		/sizeof(asn_DEF_NR_ppw_durationOfPRS_Processing2_r17_tags_45[0]) - 1, /* 1 */
	asn_DEF_NR_ppw_durationOfPRS_Processing2_r17_tags_45,	/* Same as above */
	sizeof(asn_DEF_NR_ppw_durationOfPRS_Processing2_r17_tags_45)
		/sizeof(asn_DEF_NR_ppw_durationOfPRS_Processing2_r17_tags_45[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ppw_durationOfPRS_Processing2_r17_45,
	2,	/* Elements count */
	&asn_SPC_NR_ppw_durationOfPRS_Processing2_r17_specs_45	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ppw_durationOfPRS_Processing_r17_10[] = {
	{ ATF_POINTER, 0, offsetof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17__ppw_durationOfPRS_Processing_r17, choice.ppw_durationOfPRS_Processing1_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_ppw_durationOfPRS_Processing1_r17_11,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ppw-durationOfPRS-Processing1-r17"
		},
	{ ATF_POINTER, 0, offsetof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17__ppw_durationOfPRS_Processing_r17, choice.ppw_durationOfPRS_Processing2_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_ppw_durationOfPRS_Processing2_r17_45,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ppw-durationOfPRS-Processing2-r17"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ppw_durationOfPRS_Processing_r17_tag2el_10[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* ppw-durationOfPRS-Processing1-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* ppw-durationOfPRS-Processing2-r17 */
};
static asn_CHOICE_specifics_t asn_SPC_NR_ppw_durationOfPRS_Processing_r17_specs_10 = {
	sizeof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17__ppw_durationOfPRS_Processing_r17),
	offsetof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17__ppw_durationOfPRS_Processing_r17, _asn_ctx),
	offsetof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17__ppw_durationOfPRS_Processing_r17, present),
	sizeof(((struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17__ppw_durationOfPRS_Processing_r17 *)0)->present),
	asn_MAP_NR_ppw_durationOfPRS_Processing_r17_tag2el_10,
	2,	/* Count of tags in the map */
	0, 0,
	-1	/* Extensions start */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ppw_durationOfPRS_Processing_r17_10 = {
	"ppw-durationOfPRS-Processing-r17",
	"ppw-durationOfPRS-Processing-r17",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ppw_durationOfPRS_Processing_r17_constr_10,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		CHOICE_constraint
	},
	asn_MBR_NR_ppw_durationOfPRS_Processing_r17_10,
	2,	/* Elements count */
	&asn_SPC_NR_ppw_durationOfPRS_Processing_r17_specs_10	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_scs15_r17_value2enum_64[] = {
	{ 0,	2,	"n1" },
	{ 1,	2,	"n2" },
	{ 2,	2,	"n4" },
	{ 3,	2,	"n6" },
	{ 4,	2,	"n8" },
	{ 5,	3,	"n12" },
	{ 6,	3,	"n16" },
	{ 7,	3,	"n24" },
	{ 8,	3,	"n32" },
	{ 9,	3,	"n48" },
	{ 10,	3,	"n64" }
};
static const unsigned int asn_MAP_NR_scs15_r17_enum2value_64[] = {
	0,	/* n1(0) */
	5,	/* n12(5) */
	6,	/* n16(6) */
	1,	/* n2(1) */
	7,	/* n24(7) */
	8,	/* n32(8) */
	2,	/* n4(2) */
	9,	/* n48(9) */
	3,	/* n6(3) */
	10,	/* n64(10) */
	4	/* n8(4) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_scs15_r17_specs_64 = {
	asn_MAP_NR_scs15_r17_value2enum_64,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_scs15_r17_enum2value_64,	/* N => "tag"; sorted by N */
	11,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_scs15_r17_tags_64[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_scs15_r17_64 = {
	"scs15-r17",
	"scs15-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_scs15_r17_tags_64,
	sizeof(asn_DEF_NR_scs15_r17_tags_64)
		/sizeof(asn_DEF_NR_scs15_r17_tags_64[0]) - 1, /* 1 */
	asn_DEF_NR_scs15_r17_tags_64,	/* Same as above */
	sizeof(asn_DEF_NR_scs15_r17_tags_64)
		/sizeof(asn_DEF_NR_scs15_r17_tags_64[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_scs15_r17_constr_64,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_scs15_r17_specs_64	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_scs30_r17_value2enum_76[] = {
	{ 0,	2,	"n1" },
	{ 1,	2,	"n2" },
	{ 2,	2,	"n4" },
	{ 3,	2,	"n6" },
	{ 4,	2,	"n8" },
	{ 5,	3,	"n12" },
	{ 6,	3,	"n16" },
	{ 7,	3,	"n24" },
	{ 8,	3,	"n32" },
	{ 9,	3,	"n48" },
	{ 10,	3,	"n64" }
};
static const unsigned int asn_MAP_NR_scs30_r17_enum2value_76[] = {
	0,	/* n1(0) */
	5,	/* n12(5) */
	6,	/* n16(6) */
	1,	/* n2(1) */
	7,	/* n24(7) */
	8,	/* n32(8) */
	2,	/* n4(2) */
	9,	/* n48(9) */
	3,	/* n6(3) */
	10,	/* n64(10) */
	4	/* n8(4) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_scs30_r17_specs_76 = {
	asn_MAP_NR_scs30_r17_value2enum_76,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_scs30_r17_enum2value_76,	/* N => "tag"; sorted by N */
	11,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_scs30_r17_tags_76[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_scs30_r17_76 = {
	"scs30-r17",
	"scs30-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_scs30_r17_tags_76,
	sizeof(asn_DEF_NR_scs30_r17_tags_76)
		/sizeof(asn_DEF_NR_scs30_r17_tags_76[0]) - 1, /* 1 */
	asn_DEF_NR_scs30_r17_tags_76,	/* Same as above */
	sizeof(asn_DEF_NR_scs30_r17_tags_76)
		/sizeof(asn_DEF_NR_scs30_r17_tags_76[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_scs30_r17_constr_76,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_scs30_r17_specs_76	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_scs60_r17_value2enum_88[] = {
	{ 0,	2,	"n1" },
	{ 1,	2,	"n2" },
	{ 2,	2,	"n4" },
	{ 3,	2,	"n6" },
	{ 4,	2,	"n8" },
	{ 5,	3,	"n12" },
	{ 6,	3,	"n16" },
	{ 7,	3,	"n24" },
	{ 8,	3,	"n32" },
	{ 9,	3,	"n48" },
	{ 10,	3,	"n64" }
};
static const unsigned int asn_MAP_NR_scs60_r17_enum2value_88[] = {
	0,	/* n1(0) */
	5,	/* n12(5) */
	6,	/* n16(6) */
	1,	/* n2(1) */
	7,	/* n24(7) */
	8,	/* n32(8) */
	2,	/* n4(2) */
	9,	/* n48(9) */
	3,	/* n6(3) */
	10,	/* n64(10) */
	4	/* n8(4) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_scs60_r17_specs_88 = {
	asn_MAP_NR_scs60_r17_value2enum_88,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_scs60_r17_enum2value_88,	/* N => "tag"; sorted by N */
	11,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_scs60_r17_tags_88[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_scs60_r17_88 = {
	"scs60-r17",
	"scs60-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_scs60_r17_tags_88,
	sizeof(asn_DEF_NR_scs60_r17_tags_88)
		/sizeof(asn_DEF_NR_scs60_r17_tags_88[0]) - 1, /* 1 */
	asn_DEF_NR_scs60_r17_tags_88,	/* Same as above */
	sizeof(asn_DEF_NR_scs60_r17_tags_88)
		/sizeof(asn_DEF_NR_scs60_r17_tags_88[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_scs60_r17_constr_88,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_scs60_r17_specs_88	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_scs120_r17_value2enum_100[] = {
	{ 0,	2,	"n1" },
	{ 1,	2,	"n2" },
	{ 2,	2,	"n4" },
	{ 3,	2,	"n6" },
	{ 4,	2,	"n8" },
	{ 5,	3,	"n12" },
	{ 6,	3,	"n16" },
	{ 7,	3,	"n24" },
	{ 8,	3,	"n32" },
	{ 9,	3,	"n48" },
	{ 10,	3,	"n64" }
};
static const unsigned int asn_MAP_NR_scs120_r17_enum2value_100[] = {
	0,	/* n1(0) */
	5,	/* n12(5) */
	6,	/* n16(6) */
	1,	/* n2(1) */
	7,	/* n24(7) */
	8,	/* n32(8) */
	2,	/* n4(2) */
	9,	/* n48(9) */
	3,	/* n6(3) */
	10,	/* n64(10) */
	4	/* n8(4) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_scs120_r17_specs_100 = {
	asn_MAP_NR_scs120_r17_value2enum_100,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_scs120_r17_enum2value_100,	/* N => "tag"; sorted by N */
	11,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_scs120_r17_tags_100[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_scs120_r17_100 = {
	"scs120-r17",
	"scs120-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_scs120_r17_tags_100,
	sizeof(asn_DEF_NR_scs120_r17_tags_100)
		/sizeof(asn_DEF_NR_scs120_r17_tags_100[0]) - 1, /* 1 */
	asn_DEF_NR_scs120_r17_tags_100,	/* Same as above */
	sizeof(asn_DEF_NR_scs120_r17_tags_100)
		/sizeof(asn_DEF_NR_scs120_r17_tags_100[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_scs120_r17_constr_100,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_scs120_r17_specs_100	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ppw_maxNumOfDL_PRS_ResProcessedPerSlot_r17_63[] = {
	{ ATF_POINTER, 4, offsetof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17__ppw_maxNumOfDL_PRS_ResProcessedPerSlot_r17, scs15_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_scs15_r17_64,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs15-r17"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17__ppw_maxNumOfDL_PRS_ResProcessedPerSlot_r17, scs30_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_scs30_r17_76,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs30-r17"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17__ppw_maxNumOfDL_PRS_ResProcessedPerSlot_r17, scs60_r17),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_scs60_r17_88,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs60-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17__ppw_maxNumOfDL_PRS_ResProcessedPerSlot_r17, scs120_r17),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_scs120_r17_100,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs120-r17"
		},
};
static const int asn_MAP_NR_ppw_maxNumOfDL_PRS_ResProcessedPerSlot_r17_oms_63[] = { 0, 1, 2, 3 };
static const ber_tlv_tag_t asn_DEF_NR_ppw_maxNumOfDL_PRS_ResProcessedPerSlot_r17_tags_63[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ppw_maxNumOfDL_PRS_ResProcessedPerSlot_r17_tag2el_63[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* scs15-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* scs30-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* scs60-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 } /* scs120-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ppw_maxNumOfDL_PRS_ResProcessedPerSlot_r17_specs_63 = {
	sizeof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17__ppw_maxNumOfDL_PRS_ResProcessedPerSlot_r17),
	offsetof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17__ppw_maxNumOfDL_PRS_ResProcessedPerSlot_r17, _asn_ctx),
	asn_MAP_NR_ppw_maxNumOfDL_PRS_ResProcessedPerSlot_r17_tag2el_63,
	4,	/* Count of tags in the map */
	asn_MAP_NR_ppw_maxNumOfDL_PRS_ResProcessedPerSlot_r17_oms_63,	/* Optional members */
	4, 0,	/* Root/Additions */
	4,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ppw_maxNumOfDL_PRS_ResProcessedPerSlot_r17_63 = {
	"ppw-maxNumOfDL-PRS-ResProcessedPerSlot-r17",
	"ppw-maxNumOfDL-PRS-ResProcessedPerSlot-r17",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ppw_maxNumOfDL_PRS_ResProcessedPerSlot_r17_tags_63,
	sizeof(asn_DEF_NR_ppw_maxNumOfDL_PRS_ResProcessedPerSlot_r17_tags_63)
		/sizeof(asn_DEF_NR_ppw_maxNumOfDL_PRS_ResProcessedPerSlot_r17_tags_63[0]) - 1, /* 1 */
	asn_DEF_NR_ppw_maxNumOfDL_PRS_ResProcessedPerSlot_r17_tags_63,	/* Same as above */
	sizeof(asn_DEF_NR_ppw_maxNumOfDL_PRS_ResProcessedPerSlot_r17_tags_63)
		/sizeof(asn_DEF_NR_ppw_maxNumOfDL_PRS_ResProcessedPerSlot_r17_tags_63[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ppw_maxNumOfDL_PRS_ResProcessedPerSlot_r17_63,
	4,	/* Elements count */
	&asn_SPC_NR_ppw_maxNumOfDL_PRS_ResProcessedPerSlot_r17_specs_63	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_fr1_r17_value2enum_114[] = {
	{ 0,	4,	"mhz5" },
	{ 1,	5,	"mhz10" },
	{ 2,	5,	"mhz20" },
	{ 3,	5,	"mhz40" },
	{ 4,	5,	"mhz50" },
	{ 5,	5,	"mhz80" },
	{ 6,	6,	"mhz100" }
};
static const unsigned int asn_MAP_NR_fr1_r17_enum2value_114[] = {
	1,	/* mhz10(1) */
	6,	/* mhz100(6) */
	2,	/* mhz20(2) */
	3,	/* mhz40(3) */
	0,	/* mhz5(0) */
	4,	/* mhz50(4) */
	5	/* mhz80(5) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_fr1_r17_specs_114 = {
	asn_MAP_NR_fr1_r17_value2enum_114,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_fr1_r17_enum2value_114,	/* N => "tag"; sorted by N */
	7,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_fr1_r17_tags_114[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_fr1_r17_114 = {
	"fr1-r17",
	"fr1-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_fr1_r17_tags_114,
	sizeof(asn_DEF_NR_fr1_r17_tags_114)
		/sizeof(asn_DEF_NR_fr1_r17_tags_114[0]) - 1, /* 1 */
	asn_DEF_NR_fr1_r17_tags_114,	/* Same as above */
	sizeof(asn_DEF_NR_fr1_r17_tags_114)
		/sizeof(asn_DEF_NR_fr1_r17_tags_114[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_fr1_r17_constr_114,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_fr1_r17_specs_114	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_fr2_r17_value2enum_122[] = {
	{ 0,	5,	"mhz50" },
	{ 1,	6,	"mhz100" },
	{ 2,	6,	"mhz200" },
	{ 3,	6,	"mhz400" }
};
static const unsigned int asn_MAP_NR_fr2_r17_enum2value_122[] = {
	1,	/* mhz100(1) */
	2,	/* mhz200(2) */
	3,	/* mhz400(3) */
	0	/* mhz50(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_fr2_r17_specs_122 = {
	asn_MAP_NR_fr2_r17_value2enum_122,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_fr2_r17_enum2value_122,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_fr2_r17_tags_122[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_fr2_r17_122 = {
	"fr2-r17",
	"fr2-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_fr2_r17_tags_122,
	sizeof(asn_DEF_NR_fr2_r17_tags_122)
		/sizeof(asn_DEF_NR_fr2_r17_tags_122[0]) - 1, /* 1 */
	asn_DEF_NR_fr2_r17_tags_122,	/* Same as above */
	sizeof(asn_DEF_NR_fr2_r17_tags_122)
		/sizeof(asn_DEF_NR_fr2_r17_tags_122[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_fr2_r17_constr_122,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_fr2_r17_specs_122	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ppw_maxNumOfDL_Bandwidth_r17_113[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17__ppw_maxNumOfDL_Bandwidth_r17, choice.fr1_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_fr1_r17_114,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"fr1-r17"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17__ppw_maxNumOfDL_Bandwidth_r17, choice.fr2_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_fr2_r17_122,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"fr2-r17"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ppw_maxNumOfDL_Bandwidth_r17_tag2el_113[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* fr1-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* fr2-r17 */
};
static asn_CHOICE_specifics_t asn_SPC_NR_ppw_maxNumOfDL_Bandwidth_r17_specs_113 = {
	sizeof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17__ppw_maxNumOfDL_Bandwidth_r17),
	offsetof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17__ppw_maxNumOfDL_Bandwidth_r17, _asn_ctx),
	offsetof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17__ppw_maxNumOfDL_Bandwidth_r17, present),
	sizeof(((struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17__ppw_maxNumOfDL_Bandwidth_r17 *)0)->present),
	asn_MAP_NR_ppw_maxNumOfDL_Bandwidth_r17_tag2el_113,
	2,	/* Count of tags in the map */
	0, 0,
	-1	/* Extensions start */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ppw_maxNumOfDL_Bandwidth_r17_113 = {
	"ppw-maxNumOfDL-Bandwidth-r17",
	"ppw-maxNumOfDL-Bandwidth-r17",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ppw_maxNumOfDL_Bandwidth_r17_constr_113,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		CHOICE_constraint
	},
	asn_MBR_NR_ppw_maxNumOfDL_Bandwidth_r17_113,
	2,	/* Elements count */
	&asn_SPC_NR_ppw_maxNumOfDL_Bandwidth_r17_specs_113	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17, prsProcessingType_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_prsProcessingType_r17_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"prsProcessingType-r17"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17, ppw_dl_PRS_BufferType_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ppw_dl_PRS_BufferType_r17_6,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ppw-dl-PRS-BufferType-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17, ppw_durationOfPRS_Processing_r17),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_ppw_durationOfPRS_Processing_r17_10,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ppw-durationOfPRS-Processing-r17"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17, ppw_maxNumOfDL_PRS_ResProcessedPerSlot_r17),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		0,
		&asn_DEF_NR_ppw_maxNumOfDL_PRS_ResProcessedPerSlot_r17_63,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ppw-maxNumOfDL-PRS-ResProcessedPerSlot-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17, ppw_maxNumOfDL_Bandwidth_r17),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_ppw_maxNumOfDL_Bandwidth_r17_113,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ppw-maxNumOfDL-Bandwidth-r17"
		},
};
static const int asn_MAP_NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17_oms_1[] = { 2, 4 };
static const ber_tlv_tag_t asn_DEF_NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* prsProcessingType-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* ppw-dl-PRS-BufferType-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* ppw-durationOfPRS-Processing-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* ppw-maxNumOfDL-PRS-ResProcessedPerSlot-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 } /* ppw-maxNumOfDL-Bandwidth-r17 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17_specs_1 = {
	sizeof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17),
	offsetof(struct NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17, _asn_ctx),
	asn_MAP_NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17_tag2el_1,
	5,	/* Count of tags in the map */
	asn_MAP_NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17_oms_1,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17 = {
	"PRS-ProcessingCapabilityOutsideMGinPPWperType-r17",
	"PRS-ProcessingCapabilityOutsideMGinPPWperType-r17",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17_tags_1,
	sizeof(asn_DEF_NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17_tags_1)
		/sizeof(asn_DEF_NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17_tags_1[0]), /* 1 */
	asn_DEF_NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17_tags_1)
		/sizeof(asn_DEF_NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17_1,
	5,	/* Elements count */
	&asn_SPC_NR_PRS_ProcessingCapabilityOutsideMGinPPWperType_r17_specs_1	/* Additional specs */
};

