/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_RF-Parameters.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_supportedBandListNR_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 1024UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_supportedBandListNR_constr_2 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_srs_SwitchingTimeRequested_constr_9 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_extendedBand_n77_r16_constr_30 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_sidelinkRequested_r17_constr_45 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_extendedBand_n77_2_r17_constr_47 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_supportedBandListNR_constr_2 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 10,  10,  1,  1024 }	/* (SIZE(1..1024)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static asn_TYPE_member_t asn_MBR_NR_supportedBandListNR_2[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_BandNR,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_supportedBandListNR_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_supportedBandListNR_specs_2 = {
	sizeof(struct NR_RF_Parameters__supportedBandListNR),
	offsetof(struct NR_RF_Parameters__supportedBandListNR, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_supportedBandListNR_2 = {
	"supportedBandListNR",
	"supportedBandListNR",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_supportedBandListNR_tags_2,
	sizeof(asn_DEF_NR_supportedBandListNR_tags_2)
		/sizeof(asn_DEF_NR_supportedBandListNR_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_supportedBandListNR_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_supportedBandListNR_tags_2)
		/sizeof(asn_DEF_NR_supportedBandListNR_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_supportedBandListNR_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_supportedBandListNR_2,
	1,	/* Single element */
	&asn_SPC_NR_supportedBandListNR_specs_2	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_srs_SwitchingTimeRequested_value2enum_9[] = {
	{ 0,	4,	"true" }
};
static const unsigned int asn_MAP_NR_srs_SwitchingTimeRequested_enum2value_9[] = {
	0	/* true(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_srs_SwitchingTimeRequested_specs_9 = {
	asn_MAP_NR_srs_SwitchingTimeRequested_value2enum_9,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_srs_SwitchingTimeRequested_enum2value_9,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_srs_SwitchingTimeRequested_tags_9[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_srs_SwitchingTimeRequested_9 = {
	"srs-SwitchingTimeRequested",
	"srs-SwitchingTimeRequested",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_srs_SwitchingTimeRequested_tags_9,
	sizeof(asn_DEF_NR_srs_SwitchingTimeRequested_tags_9)
		/sizeof(asn_DEF_NR_srs_SwitchingTimeRequested_tags_9[0]) - 1, /* 1 */
	asn_DEF_NR_srs_SwitchingTimeRequested_tags_9,	/* Same as above */
	sizeof(asn_DEF_NR_srs_SwitchingTimeRequested_tags_9)
		/sizeof(asn_DEF_NR_srs_SwitchingTimeRequested_tags_9[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_srs_SwitchingTimeRequested_constr_9,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_srs_SwitchingTimeRequested_specs_9	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext1_7[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_RF_Parameters__ext1, supportedBandCombinationList_v1540),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1540,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1540"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RF_Parameters__ext1, srs_SwitchingTimeRequested),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_srs_SwitchingTimeRequested_9,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"srs-SwitchingTimeRequested"
		},
};
static const int asn_MAP_NR_ext1_oms_7[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_7[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_7[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* supportedBandCombinationList-v1540 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* srs-SwitchingTimeRequested */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_7 = {
	sizeof(struct NR_RF_Parameters__ext1),
	offsetof(struct NR_RF_Parameters__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_7,
	2,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_7,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_7 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_7,
	sizeof(asn_DEF_NR_ext1_tags_7)
		/sizeof(asn_DEF_NR_ext1_tags_7[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_7,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_7)
		/sizeof(asn_DEF_NR_ext1_tags_7[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_7,
	2,	/* Elements count */
	&asn_SPC_NR_ext1_specs_7	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext2_11[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_RF_Parameters__ext2, supportedBandCombinationList_v1550),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1550,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1550"
		},
};
static const int asn_MAP_NR_ext2_oms_11[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext2_tags_11[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext2_tag2el_11[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* supportedBandCombinationList-v1550 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext2_specs_11 = {
	sizeof(struct NR_RF_Parameters__ext2),
	offsetof(struct NR_RF_Parameters__ext2, _asn_ctx),
	asn_MAP_NR_ext2_tag2el_11,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext2_oms_11,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext2_11 = {
	"ext2",
	"ext2",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext2_tags_11,
	sizeof(asn_DEF_NR_ext2_tags_11)
		/sizeof(asn_DEF_NR_ext2_tags_11[0]) - 1, /* 1 */
	asn_DEF_NR_ext2_tags_11,	/* Same as above */
	sizeof(asn_DEF_NR_ext2_tags_11)
		/sizeof(asn_DEF_NR_ext2_tags_11[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext2_11,
	1,	/* Elements count */
	&asn_SPC_NR_ext2_specs_11	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext3_13[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_RF_Parameters__ext3, supportedBandCombinationList_v1560),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1560,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1560"
		},
};
static const int asn_MAP_NR_ext3_oms_13[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext3_tags_13[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext3_tag2el_13[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* supportedBandCombinationList-v1560 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext3_specs_13 = {
	sizeof(struct NR_RF_Parameters__ext3),
	offsetof(struct NR_RF_Parameters__ext3, _asn_ctx),
	asn_MAP_NR_ext3_tag2el_13,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext3_oms_13,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext3_13 = {
	"ext3",
	"ext3",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext3_tags_13,
	sizeof(asn_DEF_NR_ext3_tags_13)
		/sizeof(asn_DEF_NR_ext3_tags_13[0]) - 1, /* 1 */
	asn_DEF_NR_ext3_tags_13,	/* Same as above */
	sizeof(asn_DEF_NR_ext3_tags_13)
		/sizeof(asn_DEF_NR_ext3_tags_13[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext3_13,
	1,	/* Elements count */
	&asn_SPC_NR_ext3_specs_13	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext4_15[] = {
	{ ATF_POINTER, 3, offsetof(struct NR_RF_Parameters__ext4, supportedBandCombinationList_v1610),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1610,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1610"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_RF_Parameters__ext4, supportedBandCombinationListSidelinkEUTRA_NR_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationListSidelinkEUTRA_NR_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationListSidelinkEUTRA-NR-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RF_Parameters__ext4, supportedBandCombinationList_UplinkTxSwitch_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_UplinkTxSwitch_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-UplinkTxSwitch-r16"
		},
};
static const int asn_MAP_NR_ext4_oms_15[] = { 0, 1, 2 };
static const ber_tlv_tag_t asn_DEF_NR_ext4_tags_15[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext4_tag2el_15[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* supportedBandCombinationList-v1610 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* supportedBandCombinationListSidelinkEUTRA-NR-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* supportedBandCombinationList-UplinkTxSwitch-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext4_specs_15 = {
	sizeof(struct NR_RF_Parameters__ext4),
	offsetof(struct NR_RF_Parameters__ext4, _asn_ctx),
	asn_MAP_NR_ext4_tag2el_15,
	3,	/* Count of tags in the map */
	asn_MAP_NR_ext4_oms_15,	/* Optional members */
	3, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext4_15 = {
	"ext4",
	"ext4",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext4_tags_15,
	sizeof(asn_DEF_NR_ext4_tags_15)
		/sizeof(asn_DEF_NR_ext4_tags_15[0]) - 1, /* 1 */
	asn_DEF_NR_ext4_tags_15,	/* Same as above */
	sizeof(asn_DEF_NR_ext4_tags_15)
		/sizeof(asn_DEF_NR_ext4_tags_15[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext4_15,
	3,	/* Elements count */
	&asn_SPC_NR_ext4_specs_15	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext5_19[] = {
	{ ATF_POINTER, 3, offsetof(struct NR_RF_Parameters__ext5, supportedBandCombinationList_v1630),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1630,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1630"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_RF_Parameters__ext5, supportedBandCombinationListSidelinkEUTRA_NR_v1630),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationListSidelinkEUTRA_NR_v1630,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationListSidelinkEUTRA-NR-v1630"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RF_Parameters__ext5, supportedBandCombinationList_UplinkTxSwitch_v1630),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_UplinkTxSwitch_v1630,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-UplinkTxSwitch-v1630"
		},
};
static const int asn_MAP_NR_ext5_oms_19[] = { 0, 1, 2 };
static const ber_tlv_tag_t asn_DEF_NR_ext5_tags_19[] = {
	(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext5_tag2el_19[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* supportedBandCombinationList-v1630 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* supportedBandCombinationListSidelinkEUTRA-NR-v1630 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* supportedBandCombinationList-UplinkTxSwitch-v1630 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext5_specs_19 = {
	sizeof(struct NR_RF_Parameters__ext5),
	offsetof(struct NR_RF_Parameters__ext5, _asn_ctx),
	asn_MAP_NR_ext5_tag2el_19,
	3,	/* Count of tags in the map */
	asn_MAP_NR_ext5_oms_19,	/* Optional members */
	3, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext5_19 = {
	"ext5",
	"ext5",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext5_tags_19,
	sizeof(asn_DEF_NR_ext5_tags_19)
		/sizeof(asn_DEF_NR_ext5_tags_19[0]) - 1, /* 1 */
	asn_DEF_NR_ext5_tags_19,	/* Same as above */
	sizeof(asn_DEF_NR_ext5_tags_19)
		/sizeof(asn_DEF_NR_ext5_tags_19[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext5_19,
	3,	/* Elements count */
	&asn_SPC_NR_ext5_specs_19	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext6_23[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_RF_Parameters__ext6, supportedBandCombinationList_v1640),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1640,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1640"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RF_Parameters__ext6, supportedBandCombinationList_UplinkTxSwitch_v1640),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_UplinkTxSwitch_v1640,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-UplinkTxSwitch-v1640"
		},
};
static const int asn_MAP_NR_ext6_oms_23[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_ext6_tags_23[] = {
	(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext6_tag2el_23[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* supportedBandCombinationList-v1640 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* supportedBandCombinationList-UplinkTxSwitch-v1640 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext6_specs_23 = {
	sizeof(struct NR_RF_Parameters__ext6),
	offsetof(struct NR_RF_Parameters__ext6, _asn_ctx),
	asn_MAP_NR_ext6_tag2el_23,
	2,	/* Count of tags in the map */
	asn_MAP_NR_ext6_oms_23,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext6_23 = {
	"ext6",
	"ext6",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext6_tags_23,
	sizeof(asn_DEF_NR_ext6_tags_23)
		/sizeof(asn_DEF_NR_ext6_tags_23[0]) - 1, /* 1 */
	asn_DEF_NR_ext6_tags_23,	/* Same as above */
	sizeof(asn_DEF_NR_ext6_tags_23)
		/sizeof(asn_DEF_NR_ext6_tags_23[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext6_23,
	2,	/* Elements count */
	&asn_SPC_NR_ext6_specs_23	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext7_26[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_RF_Parameters__ext7, supportedBandCombinationList_v1650),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1650,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1650"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RF_Parameters__ext7, supportedBandCombinationList_UplinkTxSwitch_v1650),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_UplinkTxSwitch_v1650,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-UplinkTxSwitch-v1650"
		},
};
static const int asn_MAP_NR_ext7_oms_26[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_ext7_tags_26[] = {
	(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext7_tag2el_26[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* supportedBandCombinationList-v1650 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* supportedBandCombinationList-UplinkTxSwitch-v1650 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext7_specs_26 = {
	sizeof(struct NR_RF_Parameters__ext7),
	offsetof(struct NR_RF_Parameters__ext7, _asn_ctx),
	asn_MAP_NR_ext7_tag2el_26,
	2,	/* Count of tags in the map */
	asn_MAP_NR_ext7_oms_26,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext7_26 = {
	"ext7",
	"ext7",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext7_tags_26,
	sizeof(asn_DEF_NR_ext7_tags_26)
		/sizeof(asn_DEF_NR_ext7_tags_26[0]) - 1, /* 1 */
	asn_DEF_NR_ext7_tags_26,	/* Same as above */
	sizeof(asn_DEF_NR_ext7_tags_26)
		/sizeof(asn_DEF_NR_ext7_tags_26[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext7_26,
	2,	/* Elements count */
	&asn_SPC_NR_ext7_specs_26	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_extendedBand_n77_r16_value2enum_30[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_extendedBand_n77_r16_enum2value_30[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_extendedBand_n77_r16_specs_30 = {
	asn_MAP_NR_extendedBand_n77_r16_value2enum_30,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_extendedBand_n77_r16_enum2value_30,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_extendedBand_n77_r16_tags_30[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_extendedBand_n77_r16_30 = {
	"extendedBand-n77-r16",
	"extendedBand-n77-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_extendedBand_n77_r16_tags_30,
	sizeof(asn_DEF_NR_extendedBand_n77_r16_tags_30)
		/sizeof(asn_DEF_NR_extendedBand_n77_r16_tags_30[0]) - 1, /* 1 */
	asn_DEF_NR_extendedBand_n77_r16_tags_30,	/* Same as above */
	sizeof(asn_DEF_NR_extendedBand_n77_r16_tags_30)
		/sizeof(asn_DEF_NR_extendedBand_n77_r16_tags_30[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_extendedBand_n77_r16_constr_30,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_extendedBand_n77_r16_specs_30	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext8_29[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_RF_Parameters__ext8, extendedBand_n77_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_extendedBand_n77_r16_30,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"extendedBand-n77-r16"
		},
};
static const int asn_MAP_NR_ext8_oms_29[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext8_tags_29[] = {
	(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext8_tag2el_29[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* extendedBand-n77-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext8_specs_29 = {
	sizeof(struct NR_RF_Parameters__ext8),
	offsetof(struct NR_RF_Parameters__ext8, _asn_ctx),
	asn_MAP_NR_ext8_tag2el_29,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext8_oms_29,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext8_29 = {
	"ext8",
	"ext8",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext8_tags_29,
	sizeof(asn_DEF_NR_ext8_tags_29)
		/sizeof(asn_DEF_NR_ext8_tags_29[0]) - 1, /* 1 */
	asn_DEF_NR_ext8_tags_29,	/* Same as above */
	sizeof(asn_DEF_NR_ext8_tags_29)
		/sizeof(asn_DEF_NR_ext8_tags_29[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext8_29,
	1,	/* Elements count */
	&asn_SPC_NR_ext8_specs_29	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext9_32[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_RF_Parameters__ext9, supportedBandCombinationList_UplinkTxSwitch_v1670),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_UplinkTxSwitch_v1670,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-UplinkTxSwitch-v1670"
		},
};
static const int asn_MAP_NR_ext9_oms_32[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext9_tags_32[] = {
	(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext9_tag2el_32[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* supportedBandCombinationList-UplinkTxSwitch-v1670 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext9_specs_32 = {
	sizeof(struct NR_RF_Parameters__ext9),
	offsetof(struct NR_RF_Parameters__ext9, _asn_ctx),
	asn_MAP_NR_ext9_tag2el_32,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext9_oms_32,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext9_32 = {
	"ext9",
	"ext9",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext9_tags_32,
	sizeof(asn_DEF_NR_ext9_tags_32)
		/sizeof(asn_DEF_NR_ext9_tags_32[0]) - 1, /* 1 */
	asn_DEF_NR_ext9_tags_32,	/* Same as above */
	sizeof(asn_DEF_NR_ext9_tags_32)
		/sizeof(asn_DEF_NR_ext9_tags_32[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext9_32,
	1,	/* Elements count */
	&asn_SPC_NR_ext9_specs_32	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext10_34[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_RF_Parameters__ext10, supportedBandCombinationList_v1680),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1680,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1680"
		},
};
static const int asn_MAP_NR_ext10_oms_34[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext10_tags_34[] = {
	(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext10_tag2el_34[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* supportedBandCombinationList-v1680 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext10_specs_34 = {
	sizeof(struct NR_RF_Parameters__ext10),
	offsetof(struct NR_RF_Parameters__ext10, _asn_ctx),
	asn_MAP_NR_ext10_tag2el_34,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext10_oms_34,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext10_34 = {
	"ext10",
	"ext10",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext10_tags_34,
	sizeof(asn_DEF_NR_ext10_tags_34)
		/sizeof(asn_DEF_NR_ext10_tags_34[0]) - 1, /* 1 */
	asn_DEF_NR_ext10_tags_34,	/* Same as above */
	sizeof(asn_DEF_NR_ext10_tags_34)
		/sizeof(asn_DEF_NR_ext10_tags_34[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext10_34,
	1,	/* Elements count */
	&asn_SPC_NR_ext10_specs_34	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext11_36[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_RF_Parameters__ext11, supportedBandCombinationList_v1690),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1690,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1690"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RF_Parameters__ext11, supportedBandCombinationList_UplinkTxSwitch_v1690),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_UplinkTxSwitch_v1690,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-UplinkTxSwitch-v1690"
		},
};
static const int asn_MAP_NR_ext11_oms_36[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_ext11_tags_36[] = {
	(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext11_tag2el_36[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* supportedBandCombinationList-v1690 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* supportedBandCombinationList-UplinkTxSwitch-v1690 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext11_specs_36 = {
	sizeof(struct NR_RF_Parameters__ext11),
	offsetof(struct NR_RF_Parameters__ext11, _asn_ctx),
	asn_MAP_NR_ext11_tag2el_36,
	2,	/* Count of tags in the map */
	asn_MAP_NR_ext11_oms_36,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext11_36 = {
	"ext11",
	"ext11",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext11_tags_36,
	sizeof(asn_DEF_NR_ext11_tags_36)
		/sizeof(asn_DEF_NR_ext11_tags_36[0]) - 1, /* 1 */
	asn_DEF_NR_ext11_tags_36,	/* Same as above */
	sizeof(asn_DEF_NR_ext11_tags_36)
		/sizeof(asn_DEF_NR_ext11_tags_36[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext11_36,
	2,	/* Elements count */
	&asn_SPC_NR_ext11_specs_36	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_sidelinkRequested_r17_value2enum_45[] = {
	{ 0,	4,	"true" }
};
static const unsigned int asn_MAP_NR_sidelinkRequested_r17_enum2value_45[] = {
	0	/* true(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_sidelinkRequested_r17_specs_45 = {
	asn_MAP_NR_sidelinkRequested_r17_value2enum_45,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_sidelinkRequested_r17_enum2value_45,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_sidelinkRequested_r17_tags_45[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_sidelinkRequested_r17_45 = {
	"sidelinkRequested-r17",
	"sidelinkRequested-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_sidelinkRequested_r17_tags_45,
	sizeof(asn_DEF_NR_sidelinkRequested_r17_tags_45)
		/sizeof(asn_DEF_NR_sidelinkRequested_r17_tags_45[0]) - 1, /* 1 */
	asn_DEF_NR_sidelinkRequested_r17_tags_45,	/* Same as above */
	sizeof(asn_DEF_NR_sidelinkRequested_r17_tags_45)
		/sizeof(asn_DEF_NR_sidelinkRequested_r17_tags_45[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_sidelinkRequested_r17_constr_45,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_sidelinkRequested_r17_specs_45	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_extendedBand_n77_2_r17_value2enum_47[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_extendedBand_n77_2_r17_enum2value_47[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_extendedBand_n77_2_r17_specs_47 = {
	asn_MAP_NR_extendedBand_n77_2_r17_value2enum_47,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_extendedBand_n77_2_r17_enum2value_47,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_extendedBand_n77_2_r17_tags_47[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_extendedBand_n77_2_r17_47 = {
	"extendedBand-n77-2-r17",
	"extendedBand-n77-2-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_extendedBand_n77_2_r17_tags_47,
	sizeof(asn_DEF_NR_extendedBand_n77_2_r17_tags_47)
		/sizeof(asn_DEF_NR_extendedBand_n77_2_r17_tags_47[0]) - 1, /* 1 */
	asn_DEF_NR_extendedBand_n77_2_r17_tags_47,	/* Same as above */
	sizeof(asn_DEF_NR_extendedBand_n77_2_r17_tags_47)
		/sizeof(asn_DEF_NR_extendedBand_n77_2_r17_tags_47[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_extendedBand_n77_2_r17_constr_47,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_extendedBand_n77_2_r17_specs_47	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext12_39[] = {
	{ ATF_POINTER, 7, offsetof(struct NR_RF_Parameters__ext12, supportedBandCombinationList_v1700),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1700,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1700"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_RF_Parameters__ext12, supportedBandCombinationList_UplinkTxSwitch_v1700),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_UplinkTxSwitch_v1700,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-UplinkTxSwitch-v1700"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_RF_Parameters__ext12, supportedBandCombinationListSL_RelayDiscovery_r17),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_OCTET_STRING,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationListSL-RelayDiscovery-r17"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_RF_Parameters__ext12, supportedBandCombinationListSL_NonRelayDiscovery_r17),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_OCTET_STRING,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationListSL-NonRelayDiscovery-r17"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_RF_Parameters__ext12, supportedBandCombinationListSidelinkEUTRA_NR_v1710),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationListSidelinkEUTRA_NR_v1710,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationListSidelinkEUTRA-NR-v1710"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_RF_Parameters__ext12, sidelinkRequested_r17),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_sidelinkRequested_r17_45,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"sidelinkRequested-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RF_Parameters__ext12, extendedBand_n77_2_r17),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_extendedBand_n77_2_r17_47,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"extendedBand-n77-2-r17"
		},
};
static const int asn_MAP_NR_ext12_oms_39[] = { 0, 1, 2, 3, 4, 5, 6 };
static const ber_tlv_tag_t asn_DEF_NR_ext12_tags_39[] = {
	(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext12_tag2el_39[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* supportedBandCombinationList-v1700 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* supportedBandCombinationList-UplinkTxSwitch-v1700 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* supportedBandCombinationListSL-RelayDiscovery-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* supportedBandCombinationListSL-NonRelayDiscovery-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* supportedBandCombinationListSidelinkEUTRA-NR-v1710 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* sidelinkRequested-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 } /* extendedBand-n77-2-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext12_specs_39 = {
	sizeof(struct NR_RF_Parameters__ext12),
	offsetof(struct NR_RF_Parameters__ext12, _asn_ctx),
	asn_MAP_NR_ext12_tag2el_39,
	7,	/* Count of tags in the map */
	asn_MAP_NR_ext12_oms_39,	/* Optional members */
	7, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext12_39 = {
	"ext12",
	"ext12",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext12_tags_39,
	sizeof(asn_DEF_NR_ext12_tags_39)
		/sizeof(asn_DEF_NR_ext12_tags_39[0]) - 1, /* 1 */
	asn_DEF_NR_ext12_tags_39,	/* Same as above */
	sizeof(asn_DEF_NR_ext12_tags_39)
		/sizeof(asn_DEF_NR_ext12_tags_39[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext12_39,
	7,	/* Elements count */
	&asn_SPC_NR_ext12_specs_39	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext13_49[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_RF_Parameters__ext13, supportedBandCombinationList_v1720),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1720,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1720"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RF_Parameters__ext13, supportedBandCombinationList_UplinkTxSwitch_v1720),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_UplinkTxSwitch_v1720,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-UplinkTxSwitch-v1720"
		},
};
static const int asn_MAP_NR_ext13_oms_49[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_ext13_tags_49[] = {
	(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext13_tag2el_49[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* supportedBandCombinationList-v1720 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* supportedBandCombinationList-UplinkTxSwitch-v1720 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext13_specs_49 = {
	sizeof(struct NR_RF_Parameters__ext13),
	offsetof(struct NR_RF_Parameters__ext13, _asn_ctx),
	asn_MAP_NR_ext13_tag2el_49,
	2,	/* Count of tags in the map */
	asn_MAP_NR_ext13_oms_49,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext13_49 = {
	"ext13",
	"ext13",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext13_tags_49,
	sizeof(asn_DEF_NR_ext13_tags_49)
		/sizeof(asn_DEF_NR_ext13_tags_49[0]) - 1, /* 1 */
	asn_DEF_NR_ext13_tags_49,	/* Same as above */
	sizeof(asn_DEF_NR_ext13_tags_49)
		/sizeof(asn_DEF_NR_ext13_tags_49[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext13_49,
	2,	/* Elements count */
	&asn_SPC_NR_ext13_specs_49	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext14_52[] = {
	{ ATF_POINTER, 4, offsetof(struct NR_RF_Parameters__ext14, supportedBandCombinationList_v1730),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1730,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1730"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_RF_Parameters__ext14, supportedBandCombinationList_UplinkTxSwitch_v1730),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_UplinkTxSwitch_v1730,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-UplinkTxSwitch-v1730"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_RF_Parameters__ext14, supportedBandCombinationListSL_RelayDiscovery_v1730),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationListSL_Discovery_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationListSL-RelayDiscovery-v1730"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RF_Parameters__ext14, supportedBandCombinationListSL_NonRelayDiscovery_v1730),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationListSL_Discovery_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationListSL-NonRelayDiscovery-v1730"
		},
};
static const int asn_MAP_NR_ext14_oms_52[] = { 0, 1, 2, 3 };
static const ber_tlv_tag_t asn_DEF_NR_ext14_tags_52[] = {
	(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext14_tag2el_52[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* supportedBandCombinationList-v1730 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* supportedBandCombinationList-UplinkTxSwitch-v1730 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* supportedBandCombinationListSL-RelayDiscovery-v1730 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 } /* supportedBandCombinationListSL-NonRelayDiscovery-v1730 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext14_specs_52 = {
	sizeof(struct NR_RF_Parameters__ext14),
	offsetof(struct NR_RF_Parameters__ext14, _asn_ctx),
	asn_MAP_NR_ext14_tag2el_52,
	4,	/* Count of tags in the map */
	asn_MAP_NR_ext14_oms_52,	/* Optional members */
	4, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext14_52 = {
	"ext14",
	"ext14",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext14_tags_52,
	sizeof(asn_DEF_NR_ext14_tags_52)
		/sizeof(asn_DEF_NR_ext14_tags_52[0]) - 1, /* 1 */
	asn_DEF_NR_ext14_tags_52,	/* Same as above */
	sizeof(asn_DEF_NR_ext14_tags_52)
		/sizeof(asn_DEF_NR_ext14_tags_52[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext14_52,
	4,	/* Elements count */
	&asn_SPC_NR_ext14_specs_52	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_RF_Parameters_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RF_Parameters, supportedBandListNR),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_supportedBandListNR_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_supportedBandListNR_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_supportedBandListNR_constraint_1
		},
		0, 0, /* No default value */
		"supportedBandListNR"
		},
	{ ATF_POINTER, 16, offsetof(struct NR_RF_Parameters, supportedBandCombinationList),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList"
		},
	{ ATF_POINTER, 15, offsetof(struct NR_RF_Parameters, appliedFreqBandListFilter),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_FreqBandList,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"appliedFreqBandListFilter"
		},
	{ ATF_POINTER, 14, offsetof(struct NR_RF_Parameters, ext1),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		0,
		&asn_DEF_NR_ext1_7,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
	{ ATF_POINTER, 13, offsetof(struct NR_RF_Parameters, ext2),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		0,
		&asn_DEF_NR_ext2_11,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext2"
		},
	{ ATF_POINTER, 12, offsetof(struct NR_RF_Parameters, ext3),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		0,
		&asn_DEF_NR_ext3_13,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext3"
		},
	{ ATF_POINTER, 11, offsetof(struct NR_RF_Parameters, ext4),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		0,
		&asn_DEF_NR_ext4_15,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext4"
		},
	{ ATF_POINTER, 10, offsetof(struct NR_RF_Parameters, ext5),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		0,
		&asn_DEF_NR_ext5_19,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext5"
		},
	{ ATF_POINTER, 9, offsetof(struct NR_RF_Parameters, ext6),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		0,
		&asn_DEF_NR_ext6_23,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext6"
		},
	{ ATF_POINTER, 8, offsetof(struct NR_RF_Parameters, ext7),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		0,
		&asn_DEF_NR_ext7_26,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext7"
		},
	{ ATF_POINTER, 7, offsetof(struct NR_RF_Parameters, ext8),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		0,
		&asn_DEF_NR_ext8_29,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext8"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_RF_Parameters, ext9),
		(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
		0,
		&asn_DEF_NR_ext9_32,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext9"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_RF_Parameters, ext10),
		(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
		0,
		&asn_DEF_NR_ext10_34,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext10"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_RF_Parameters, ext11),
		(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
		0,
		&asn_DEF_NR_ext11_36,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext11"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_RF_Parameters, ext12),
		(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
		0,
		&asn_DEF_NR_ext12_39,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext12"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_RF_Parameters, ext13),
		(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
		0,
		&asn_DEF_NR_ext13_49,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext13"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RF_Parameters, ext14),
		(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
		0,
		&asn_DEF_NR_ext14_52,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext14"
		},
};
static const int asn_MAP_NR_RF_Parameters_oms_1[] = { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16 };
static const ber_tlv_tag_t asn_DEF_NR_RF_Parameters_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_RF_Parameters_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* supportedBandListNR */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* supportedBandCombinationList */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* appliedFreqBandListFilter */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* ext1 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* ext2 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* ext3 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* ext4 */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* ext5 */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* ext6 */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* ext7 */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 }, /* ext8 */
    { (ASN_TAG_CLASS_CONTEXT | (11 << 2)), 11, 0, 0 }, /* ext9 */
    { (ASN_TAG_CLASS_CONTEXT | (12 << 2)), 12, 0, 0 }, /* ext10 */
    { (ASN_TAG_CLASS_CONTEXT | (13 << 2)), 13, 0, 0 }, /* ext11 */
    { (ASN_TAG_CLASS_CONTEXT | (14 << 2)), 14, 0, 0 }, /* ext12 */
    { (ASN_TAG_CLASS_CONTEXT | (15 << 2)), 15, 0, 0 }, /* ext13 */
    { (ASN_TAG_CLASS_CONTEXT | (16 << 2)), 16, 0, 0 } /* ext14 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_RF_Parameters_specs_1 = {
	sizeof(struct NR_RF_Parameters),
	offsetof(struct NR_RF_Parameters, _asn_ctx),
	asn_MAP_NR_RF_Parameters_tag2el_1,
	17,	/* Count of tags in the map */
	asn_MAP_NR_RF_Parameters_oms_1,	/* Optional members */
	2, 14,	/* Root/Additions */
	3,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_RF_Parameters = {
	"RF-Parameters",
	"RF-Parameters",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_RF_Parameters_tags_1,
	sizeof(asn_DEF_NR_RF_Parameters_tags_1)
		/sizeof(asn_DEF_NR_RF_Parameters_tags_1[0]), /* 1 */
	asn_DEF_NR_RF_Parameters_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_RF_Parameters_tags_1)
		/sizeof(asn_DEF_NR_RF_Parameters_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_RF_Parameters_1,
	17,	/* Elements count */
	&asn_SPC_NR_RF_Parameters_specs_1	/* Additional specs */
};

