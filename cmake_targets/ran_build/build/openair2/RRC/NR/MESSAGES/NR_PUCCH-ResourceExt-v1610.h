/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_PUCCH_ResourceExt_v1610_H_
#define	_NR_PUCCH_ResourceExt_v1610_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include <constr_CHOICE.h>
#include <constr_SEQUENCE.h>
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_PUCCH_ResourceExt_v1610__interlaceAllocation_r16__interlace0_r16_PR {
	NR_PUCCH_ResourceExt_v1610__interlaceAllocation_r16__interlace0_r16_PR_NOTHING,	/* No components present */
	NR_PUCCH_ResourceExt_v1610__interlaceAllocation_r16__interlace0_r16_PR_scs15,
	NR_PUCCH_ResourceExt_v1610__interlaceAllocation_r16__interlace0_r16_PR_scs30
} NR_PUCCH_ResourceExt_v1610__interlaceAllocation_r16__interlace0_r16_PR;
typedef enum NR_PUCCH_ResourceExt_v1610__format_v1610_PR {
	NR_PUCCH_ResourceExt_v1610__format_v1610_PR_NOTHING,	/* No components present */
	NR_PUCCH_ResourceExt_v1610__format_v1610_PR_interlace1_v1610,
	NR_PUCCH_ResourceExt_v1610__format_v1610_PR_occ_v1610
} NR_PUCCH_ResourceExt_v1610__format_v1610_PR;
typedef enum NR_PUCCH_ResourceExt_v1610__format_v1610__occ_v1610__occ_Length_v1610 {
	NR_PUCCH_ResourceExt_v1610__format_v1610__occ_v1610__occ_Length_v1610_n2	= 0,
	NR_PUCCH_ResourceExt_v1610__format_v1610__occ_v1610__occ_Length_v1610_n4	= 1
} e_NR_PUCCH_ResourceExt_v1610__format_v1610__occ_v1610__occ_Length_v1610;
typedef enum NR_PUCCH_ResourceExt_v1610__format_v1610__occ_v1610__occ_Index_v1610 {
	NR_PUCCH_ResourceExt_v1610__format_v1610__occ_v1610__occ_Index_v1610_n0	= 0,
	NR_PUCCH_ResourceExt_v1610__format_v1610__occ_v1610__occ_Index_v1610_n1	= 1,
	NR_PUCCH_ResourceExt_v1610__format_v1610__occ_v1610__occ_Index_v1610_n2	= 2,
	NR_PUCCH_ResourceExt_v1610__format_v1610__occ_v1610__occ_Index_v1610_n3	= 3
} e_NR_PUCCH_ResourceExt_v1610__format_v1610__occ_v1610__occ_Index_v1610;
typedef enum NR_PUCCH_ResourceExt_v1610__ext1__pucch_RepetitionNrofSlots_r17 {
	NR_PUCCH_ResourceExt_v1610__ext1__pucch_RepetitionNrofSlots_r17_n1	= 0,
	NR_PUCCH_ResourceExt_v1610__ext1__pucch_RepetitionNrofSlots_r17_n2	= 1,
	NR_PUCCH_ResourceExt_v1610__ext1__pucch_RepetitionNrofSlots_r17_n4	= 2,
	NR_PUCCH_ResourceExt_v1610__ext1__pucch_RepetitionNrofSlots_r17_n8	= 3
} e_NR_PUCCH_ResourceExt_v1610__ext1__pucch_RepetitionNrofSlots_r17;

/* NR_PUCCH-ResourceExt-v1610 */
typedef struct NR_PUCCH_ResourceExt_v1610 {
	struct NR_PUCCH_ResourceExt_v1610__interlaceAllocation_r16 {
		long	 rb_SetIndex_r16;
		struct NR_PUCCH_ResourceExt_v1610__interlaceAllocation_r16__interlace0_r16 {
			NR_PUCCH_ResourceExt_v1610__interlaceAllocation_r16__interlace0_r16_PR present;
			union NR_PUCCH_ResourceExt_v1610__NR_interlaceAllocation_r16__NR_interlace0_r16_u {
				long	 scs15;
				long	 scs30;
			} choice;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} interlace0_r16;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *interlaceAllocation_r16;
	struct NR_PUCCH_ResourceExt_v1610__format_v1610 {
		NR_PUCCH_ResourceExt_v1610__format_v1610_PR present;
		union NR_PUCCH_ResourceExt_v1610__NR_format_v1610_u {
			long	 interlace1_v1610;
			struct NR_PUCCH_ResourceExt_v1610__format_v1610__occ_v1610 {
				long	*occ_Length_v1610;	/* OPTIONAL */
				long	*occ_Index_v1610;	/* OPTIONAL */
				
				/* Context for parsing across buffer boundaries */
				asn_struct_ctx_t _asn_ctx;
			} *occ_v1610;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *format_v1610;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	struct NR_PUCCH_ResourceExt_v1610__ext1 {
		struct NR_PUCCH_ResourceExt_v1610__ext1__formatExt_v1700 {
			long	 nrofPRBs_r17;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *formatExt_v1700;
		long	*pucch_RepetitionNrofSlots_r17;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext1;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_PUCCH_ResourceExt_v1610_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_occ_Length_v1610_10;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_occ_Index_v1610_13;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pucch_RepetitionNrofSlots_r17_22;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_PUCCH_ResourceExt_v1610;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_PUCCH_ResourceExt_v1610_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_PUCCH_ResourceExt_v1610_1[3];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_PUCCH_ResourceExt_v1610_H_ */
#include <asn_internal.h>
