/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_RF-ParametersMRDC.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_srs_SwitchingTimeRequested_constr_6 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_srs_SwitchingTimeRequested_value2enum_6[] = {
	{ 0,	4,	"true" }
};
static const unsigned int asn_MAP_NR_srs_SwitchingTimeRequested_enum2value_6[] = {
	0	/* true(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_srs_SwitchingTimeRequested_specs_6 = {
	asn_MAP_NR_srs_SwitchingTimeRequested_value2enum_6,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_srs_SwitchingTimeRequested_enum2value_6,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_srs_SwitchingTimeRequested_tags_6[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_srs_SwitchingTimeRequested_6 = {
	"srs-SwitchingTimeRequested",
	"srs-SwitchingTimeRequested",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_srs_SwitchingTimeRequested_tags_6,
	sizeof(asn_DEF_NR_srs_SwitchingTimeRequested_tags_6)
		/sizeof(asn_DEF_NR_srs_SwitchingTimeRequested_tags_6[0]) - 1, /* 1 */
	asn_DEF_NR_srs_SwitchingTimeRequested_tags_6,	/* Same as above */
	sizeof(asn_DEF_NR_srs_SwitchingTimeRequested_tags_6)
		/sizeof(asn_DEF_NR_srs_SwitchingTimeRequested_tags_6[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_srs_SwitchingTimeRequested_constr_6,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_srs_SwitchingTimeRequested_specs_6	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext1_5[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_RF_ParametersMRDC__ext1, srs_SwitchingTimeRequested),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_srs_SwitchingTimeRequested_6,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"srs-SwitchingTimeRequested"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RF_ParametersMRDC__ext1, supportedBandCombinationList_v1540),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1540,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1540"
		},
};
static const int asn_MAP_NR_ext1_oms_5[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_5[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_5[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* srs-SwitchingTimeRequested */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* supportedBandCombinationList-v1540 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_5 = {
	sizeof(struct NR_RF_ParametersMRDC__ext1),
	offsetof(struct NR_RF_ParametersMRDC__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_5,
	2,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_5,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_5 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_5,
	sizeof(asn_DEF_NR_ext1_tags_5)
		/sizeof(asn_DEF_NR_ext1_tags_5[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_5,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_5)
		/sizeof(asn_DEF_NR_ext1_tags_5[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_5,
	2,	/* Elements count */
	&asn_SPC_NR_ext1_specs_5	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext2_9[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_RF_ParametersMRDC__ext2, supportedBandCombinationList_v1550),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1550,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1550"
		},
};
static const int asn_MAP_NR_ext2_oms_9[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext2_tags_9[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext2_tag2el_9[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* supportedBandCombinationList-v1550 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext2_specs_9 = {
	sizeof(struct NR_RF_ParametersMRDC__ext2),
	offsetof(struct NR_RF_ParametersMRDC__ext2, _asn_ctx),
	asn_MAP_NR_ext2_tag2el_9,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext2_oms_9,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext2_9 = {
	"ext2",
	"ext2",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext2_tags_9,
	sizeof(asn_DEF_NR_ext2_tags_9)
		/sizeof(asn_DEF_NR_ext2_tags_9[0]) - 1, /* 1 */
	asn_DEF_NR_ext2_tags_9,	/* Same as above */
	sizeof(asn_DEF_NR_ext2_tags_9)
		/sizeof(asn_DEF_NR_ext2_tags_9[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext2_9,
	1,	/* Elements count */
	&asn_SPC_NR_ext2_specs_9	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext3_11[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_RF_ParametersMRDC__ext3, supportedBandCombinationList_v1560),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1560,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1560"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RF_ParametersMRDC__ext3, supportedBandCombinationListNEDC_Only),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationListNEDC-Only"
		},
};
static const int asn_MAP_NR_ext3_oms_11[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_ext3_tags_11[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext3_tag2el_11[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* supportedBandCombinationList-v1560 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* supportedBandCombinationListNEDC-Only */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext3_specs_11 = {
	sizeof(struct NR_RF_ParametersMRDC__ext3),
	offsetof(struct NR_RF_ParametersMRDC__ext3, _asn_ctx),
	asn_MAP_NR_ext3_tag2el_11,
	2,	/* Count of tags in the map */
	asn_MAP_NR_ext3_oms_11,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext3_11 = {
	"ext3",
	"ext3",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext3_tags_11,
	sizeof(asn_DEF_NR_ext3_tags_11)
		/sizeof(asn_DEF_NR_ext3_tags_11[0]) - 1, /* 1 */
	asn_DEF_NR_ext3_tags_11,	/* Same as above */
	sizeof(asn_DEF_NR_ext3_tags_11)
		/sizeof(asn_DEF_NR_ext3_tags_11[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext3_11,
	2,	/* Elements count */
	&asn_SPC_NR_ext3_specs_11	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext4_14[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_RF_ParametersMRDC__ext4, supportedBandCombinationList_v1570),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1570,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1570"
		},
};
static const int asn_MAP_NR_ext4_oms_14[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext4_tags_14[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext4_tag2el_14[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* supportedBandCombinationList-v1570 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext4_specs_14 = {
	sizeof(struct NR_RF_ParametersMRDC__ext4),
	offsetof(struct NR_RF_ParametersMRDC__ext4, _asn_ctx),
	asn_MAP_NR_ext4_tag2el_14,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext4_oms_14,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext4_14 = {
	"ext4",
	"ext4",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext4_tags_14,
	sizeof(asn_DEF_NR_ext4_tags_14)
		/sizeof(asn_DEF_NR_ext4_tags_14[0]) - 1, /* 1 */
	asn_DEF_NR_ext4_tags_14,	/* Same as above */
	sizeof(asn_DEF_NR_ext4_tags_14)
		/sizeof(asn_DEF_NR_ext4_tags_14[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext4_14,
	1,	/* Elements count */
	&asn_SPC_NR_ext4_specs_14	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext5_16[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_RF_ParametersMRDC__ext5, supportedBandCombinationList_v1580),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1580,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1580"
		},
};
static const int asn_MAP_NR_ext5_oms_16[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext5_tags_16[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext5_tag2el_16[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* supportedBandCombinationList-v1580 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext5_specs_16 = {
	sizeof(struct NR_RF_ParametersMRDC__ext5),
	offsetof(struct NR_RF_ParametersMRDC__ext5, _asn_ctx),
	asn_MAP_NR_ext5_tag2el_16,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext5_oms_16,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext5_16 = {
	"ext5",
	"ext5",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext5_tags_16,
	sizeof(asn_DEF_NR_ext5_tags_16)
		/sizeof(asn_DEF_NR_ext5_tags_16[0]) - 1, /* 1 */
	asn_DEF_NR_ext5_tags_16,	/* Same as above */
	sizeof(asn_DEF_NR_ext5_tags_16)
		/sizeof(asn_DEF_NR_ext5_tags_16[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext5_16,
	1,	/* Elements count */
	&asn_SPC_NR_ext5_specs_16	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext6_18[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_RF_ParametersMRDC__ext6, supportedBandCombinationList_v1590),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1590,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1590"
		},
};
static const int asn_MAP_NR_ext6_oms_18[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext6_tags_18[] = {
	(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext6_tag2el_18[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* supportedBandCombinationList-v1590 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext6_specs_18 = {
	sizeof(struct NR_RF_ParametersMRDC__ext6),
	offsetof(struct NR_RF_ParametersMRDC__ext6, _asn_ctx),
	asn_MAP_NR_ext6_tag2el_18,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext6_oms_18,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext6_18 = {
	"ext6",
	"ext6",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext6_tags_18,
	sizeof(asn_DEF_NR_ext6_tags_18)
		/sizeof(asn_DEF_NR_ext6_tags_18[0]) - 1, /* 1 */
	asn_DEF_NR_ext6_tags_18,	/* Same as above */
	sizeof(asn_DEF_NR_ext6_tags_18)
		/sizeof(asn_DEF_NR_ext6_tags_18[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext6_18,
	1,	/* Elements count */
	&asn_SPC_NR_ext6_specs_18	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_supportedBandCombinationListNEDC_Only_v15a0_21[] = {
	{ ATF_POINTER, 5, offsetof(struct NR_RF_ParametersMRDC__ext7__supportedBandCombinationListNEDC_Only_v15a0, supportedBandCombinationList_v1540),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1540,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1540"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_RF_ParametersMRDC__ext7__supportedBandCombinationListNEDC_Only_v15a0, supportedBandCombinationList_v1560),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1560,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1560"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_RF_ParametersMRDC__ext7__supportedBandCombinationListNEDC_Only_v15a0, supportedBandCombinationList_v1570),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1570,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1570"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_RF_ParametersMRDC__ext7__supportedBandCombinationListNEDC_Only_v15a0, supportedBandCombinationList_v1580),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1580,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1580"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RF_ParametersMRDC__ext7__supportedBandCombinationListNEDC_Only_v15a0, supportedBandCombinationList_v1590),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1590,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1590"
		},
};
static const int asn_MAP_NR_supportedBandCombinationListNEDC_Only_v15a0_oms_21[] = { 0, 1, 2, 3, 4 };
static const ber_tlv_tag_t asn_DEF_NR_supportedBandCombinationListNEDC_Only_v15a0_tags_21[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_supportedBandCombinationListNEDC_Only_v15a0_tag2el_21[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* supportedBandCombinationList-v1540 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* supportedBandCombinationList-v1560 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* supportedBandCombinationList-v1570 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* supportedBandCombinationList-v1580 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 } /* supportedBandCombinationList-v1590 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_supportedBandCombinationListNEDC_Only_v15a0_specs_21 = {
	sizeof(struct NR_RF_ParametersMRDC__ext7__supportedBandCombinationListNEDC_Only_v15a0),
	offsetof(struct NR_RF_ParametersMRDC__ext7__supportedBandCombinationListNEDC_Only_v15a0, _asn_ctx),
	asn_MAP_NR_supportedBandCombinationListNEDC_Only_v15a0_tag2el_21,
	5,	/* Count of tags in the map */
	asn_MAP_NR_supportedBandCombinationListNEDC_Only_v15a0_oms_21,	/* Optional members */
	5, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_supportedBandCombinationListNEDC_Only_v15a0_21 = {
	"supportedBandCombinationListNEDC-Only-v15a0",
	"supportedBandCombinationListNEDC-Only-v15a0",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_supportedBandCombinationListNEDC_Only_v15a0_tags_21,
	sizeof(asn_DEF_NR_supportedBandCombinationListNEDC_Only_v15a0_tags_21)
		/sizeof(asn_DEF_NR_supportedBandCombinationListNEDC_Only_v15a0_tags_21[0]) - 1, /* 1 */
	asn_DEF_NR_supportedBandCombinationListNEDC_Only_v15a0_tags_21,	/* Same as above */
	sizeof(asn_DEF_NR_supportedBandCombinationListNEDC_Only_v15a0_tags_21)
		/sizeof(asn_DEF_NR_supportedBandCombinationListNEDC_Only_v15a0_tags_21[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_supportedBandCombinationListNEDC_Only_v15a0_21,
	5,	/* Elements count */
	&asn_SPC_NR_supportedBandCombinationListNEDC_Only_v15a0_specs_21	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext7_20[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_RF_ParametersMRDC__ext7, supportedBandCombinationListNEDC_Only_v15a0),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_supportedBandCombinationListNEDC_Only_v15a0_21,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationListNEDC-Only-v15a0"
		},
};
static const int asn_MAP_NR_ext7_oms_20[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext7_tags_20[] = {
	(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext7_tag2el_20[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* supportedBandCombinationListNEDC-Only-v15a0 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext7_specs_20 = {
	sizeof(struct NR_RF_ParametersMRDC__ext7),
	offsetof(struct NR_RF_ParametersMRDC__ext7, _asn_ctx),
	asn_MAP_NR_ext7_tag2el_20,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext7_oms_20,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext7_20 = {
	"ext7",
	"ext7",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext7_tags_20,
	sizeof(asn_DEF_NR_ext7_tags_20)
		/sizeof(asn_DEF_NR_ext7_tags_20[0]) - 1, /* 1 */
	asn_DEF_NR_ext7_tags_20,	/* Same as above */
	sizeof(asn_DEF_NR_ext7_tags_20)
		/sizeof(asn_DEF_NR_ext7_tags_20[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext7_20,
	1,	/* Elements count */
	&asn_SPC_NR_ext7_specs_20	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext8_27[] = {
	{ ATF_POINTER, 3, offsetof(struct NR_RF_ParametersMRDC__ext8, supportedBandCombinationList_v1610),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1610,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1610"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_RF_ParametersMRDC__ext8, supportedBandCombinationListNEDC_Only_v1610),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1610,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationListNEDC-Only-v1610"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RF_ParametersMRDC__ext8, supportedBandCombinationList_UplinkTxSwitch_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_UplinkTxSwitch_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-UplinkTxSwitch-r16"
		},
};
static const int asn_MAP_NR_ext8_oms_27[] = { 0, 1, 2 };
static const ber_tlv_tag_t asn_DEF_NR_ext8_tags_27[] = {
	(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext8_tag2el_27[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* supportedBandCombinationList-v1610 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* supportedBandCombinationListNEDC-Only-v1610 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* supportedBandCombinationList-UplinkTxSwitch-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext8_specs_27 = {
	sizeof(struct NR_RF_ParametersMRDC__ext8),
	offsetof(struct NR_RF_ParametersMRDC__ext8, _asn_ctx),
	asn_MAP_NR_ext8_tag2el_27,
	3,	/* Count of tags in the map */
	asn_MAP_NR_ext8_oms_27,	/* Optional members */
	3, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext8_27 = {
	"ext8",
	"ext8",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext8_tags_27,
	sizeof(asn_DEF_NR_ext8_tags_27)
		/sizeof(asn_DEF_NR_ext8_tags_27[0]) - 1, /* 1 */
	asn_DEF_NR_ext8_tags_27,	/* Same as above */
	sizeof(asn_DEF_NR_ext8_tags_27)
		/sizeof(asn_DEF_NR_ext8_tags_27[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext8_27,
	3,	/* Elements count */
	&asn_SPC_NR_ext8_specs_27	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext9_31[] = {
	{ ATF_POINTER, 3, offsetof(struct NR_RF_ParametersMRDC__ext9, supportedBandCombinationList_v1630),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1630,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1630"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_RF_ParametersMRDC__ext9, supportedBandCombinationListNEDC_Only_v1630),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1630,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationListNEDC-Only-v1630"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RF_ParametersMRDC__ext9, supportedBandCombinationList_UplinkTxSwitch_v1630),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_UplinkTxSwitch_v1630,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-UplinkTxSwitch-v1630"
		},
};
static const int asn_MAP_NR_ext9_oms_31[] = { 0, 1, 2 };
static const ber_tlv_tag_t asn_DEF_NR_ext9_tags_31[] = {
	(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext9_tag2el_31[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* supportedBandCombinationList-v1630 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* supportedBandCombinationListNEDC-Only-v1630 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* supportedBandCombinationList-UplinkTxSwitch-v1630 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext9_specs_31 = {
	sizeof(struct NR_RF_ParametersMRDC__ext9),
	offsetof(struct NR_RF_ParametersMRDC__ext9, _asn_ctx),
	asn_MAP_NR_ext9_tag2el_31,
	3,	/* Count of tags in the map */
	asn_MAP_NR_ext9_oms_31,	/* Optional members */
	3, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext9_31 = {
	"ext9",
	"ext9",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext9_tags_31,
	sizeof(asn_DEF_NR_ext9_tags_31)
		/sizeof(asn_DEF_NR_ext9_tags_31[0]) - 1, /* 1 */
	asn_DEF_NR_ext9_tags_31,	/* Same as above */
	sizeof(asn_DEF_NR_ext9_tags_31)
		/sizeof(asn_DEF_NR_ext9_tags_31[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext9_31,
	3,	/* Elements count */
	&asn_SPC_NR_ext9_specs_31	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext10_35[] = {
	{ ATF_POINTER, 3, offsetof(struct NR_RF_ParametersMRDC__ext10, supportedBandCombinationList_v1640),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1640,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1640"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_RF_ParametersMRDC__ext10, supportedBandCombinationListNEDC_Only_v1640),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1640,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationListNEDC-Only-v1640"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RF_ParametersMRDC__ext10, supportedBandCombinationList_UplinkTxSwitch_v1640),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_UplinkTxSwitch_v1640,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-UplinkTxSwitch-v1640"
		},
};
static const int asn_MAP_NR_ext10_oms_35[] = { 0, 1, 2 };
static const ber_tlv_tag_t asn_DEF_NR_ext10_tags_35[] = {
	(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext10_tag2el_35[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* supportedBandCombinationList-v1640 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* supportedBandCombinationListNEDC-Only-v1640 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* supportedBandCombinationList-UplinkTxSwitch-v1640 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext10_specs_35 = {
	sizeof(struct NR_RF_ParametersMRDC__ext10),
	offsetof(struct NR_RF_ParametersMRDC__ext10, _asn_ctx),
	asn_MAP_NR_ext10_tag2el_35,
	3,	/* Count of tags in the map */
	asn_MAP_NR_ext10_oms_35,	/* Optional members */
	3, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext10_35 = {
	"ext10",
	"ext10",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext10_tags_35,
	sizeof(asn_DEF_NR_ext10_tags_35)
		/sizeof(asn_DEF_NR_ext10_tags_35[0]) - 1, /* 1 */
	asn_DEF_NR_ext10_tags_35,	/* Same as above */
	sizeof(asn_DEF_NR_ext10_tags_35)
		/sizeof(asn_DEF_NR_ext10_tags_35[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext10_35,
	3,	/* Elements count */
	&asn_SPC_NR_ext10_specs_35	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext11_39[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_RF_ParametersMRDC__ext11, supportedBandCombinationList_UplinkTxSwitch_v1670),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_UplinkTxSwitch_v1670,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-UplinkTxSwitch-v1670"
		},
};
static const int asn_MAP_NR_ext11_oms_39[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext11_tags_39[] = {
	(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext11_tag2el_39[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* supportedBandCombinationList-UplinkTxSwitch-v1670 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext11_specs_39 = {
	sizeof(struct NR_RF_ParametersMRDC__ext11),
	offsetof(struct NR_RF_ParametersMRDC__ext11, _asn_ctx),
	asn_MAP_NR_ext11_tag2el_39,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext11_oms_39,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext11_39 = {
	"ext11",
	"ext11",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext11_tags_39,
	sizeof(asn_DEF_NR_ext11_tags_39)
		/sizeof(asn_DEF_NR_ext11_tags_39[0]) - 1, /* 1 */
	asn_DEF_NR_ext11_tags_39,	/* Same as above */
	sizeof(asn_DEF_NR_ext11_tags_39)
		/sizeof(asn_DEF_NR_ext11_tags_39[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext11_39,
	1,	/* Elements count */
	&asn_SPC_NR_ext11_specs_39	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext12_41[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_RF_ParametersMRDC__ext12, supportedBandCombinationList_v1700),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1700,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1700"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RF_ParametersMRDC__ext12, supportedBandCombinationList_UplinkTxSwitch_v1700),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_UplinkTxSwitch_v1700,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-UplinkTxSwitch-v1700"
		},
};
static const int asn_MAP_NR_ext12_oms_41[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_ext12_tags_41[] = {
	(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext12_tag2el_41[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* supportedBandCombinationList-v1700 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* supportedBandCombinationList-UplinkTxSwitch-v1700 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext12_specs_41 = {
	sizeof(struct NR_RF_ParametersMRDC__ext12),
	offsetof(struct NR_RF_ParametersMRDC__ext12, _asn_ctx),
	asn_MAP_NR_ext12_tag2el_41,
	2,	/* Count of tags in the map */
	asn_MAP_NR_ext12_oms_41,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext12_41 = {
	"ext12",
	"ext12",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext12_tags_41,
	sizeof(asn_DEF_NR_ext12_tags_41)
		/sizeof(asn_DEF_NR_ext12_tags_41[0]) - 1, /* 1 */
	asn_DEF_NR_ext12_tags_41,	/* Same as above */
	sizeof(asn_DEF_NR_ext12_tags_41)
		/sizeof(asn_DEF_NR_ext12_tags_41[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext12_41,
	2,	/* Elements count */
	&asn_SPC_NR_ext12_specs_41	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_supportedBandCombinationListNEDC_Only_v1720_46[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_RF_ParametersMRDC__ext13__supportedBandCombinationListNEDC_Only_v1720, supportedBandCombinationList_v1700),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1700,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1700"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RF_ParametersMRDC__ext13__supportedBandCombinationListNEDC_Only_v1720, supportedBandCombinationList_v1720),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1720,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1720"
		},
};
static const int asn_MAP_NR_supportedBandCombinationListNEDC_Only_v1720_oms_46[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_supportedBandCombinationListNEDC_Only_v1720_tags_46[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_supportedBandCombinationListNEDC_Only_v1720_tag2el_46[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* supportedBandCombinationList-v1700 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* supportedBandCombinationList-v1720 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_supportedBandCombinationListNEDC_Only_v1720_specs_46 = {
	sizeof(struct NR_RF_ParametersMRDC__ext13__supportedBandCombinationListNEDC_Only_v1720),
	offsetof(struct NR_RF_ParametersMRDC__ext13__supportedBandCombinationListNEDC_Only_v1720, _asn_ctx),
	asn_MAP_NR_supportedBandCombinationListNEDC_Only_v1720_tag2el_46,
	2,	/* Count of tags in the map */
	asn_MAP_NR_supportedBandCombinationListNEDC_Only_v1720_oms_46,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_supportedBandCombinationListNEDC_Only_v1720_46 = {
	"supportedBandCombinationListNEDC-Only-v1720",
	"supportedBandCombinationListNEDC-Only-v1720",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_supportedBandCombinationListNEDC_Only_v1720_tags_46,
	sizeof(asn_DEF_NR_supportedBandCombinationListNEDC_Only_v1720_tags_46)
		/sizeof(asn_DEF_NR_supportedBandCombinationListNEDC_Only_v1720_tags_46[0]) - 1, /* 1 */
	asn_DEF_NR_supportedBandCombinationListNEDC_Only_v1720_tags_46,	/* Same as above */
	sizeof(asn_DEF_NR_supportedBandCombinationListNEDC_Only_v1720_tags_46)
		/sizeof(asn_DEF_NR_supportedBandCombinationListNEDC_Only_v1720_tags_46[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_supportedBandCombinationListNEDC_Only_v1720_46,
	2,	/* Elements count */
	&asn_SPC_NR_supportedBandCombinationListNEDC_Only_v1720_specs_46	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext13_44[] = {
	{ ATF_POINTER, 3, offsetof(struct NR_RF_ParametersMRDC__ext13, supportedBandCombinationList_v1720),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1720,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1720"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_RF_ParametersMRDC__ext13, supportedBandCombinationListNEDC_Only_v1720),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_supportedBandCombinationListNEDC_Only_v1720_46,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationListNEDC-Only-v1720"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RF_ParametersMRDC__ext13, supportedBandCombinationList_UplinkTxSwitch_v1720),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_UplinkTxSwitch_v1720,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-UplinkTxSwitch-v1720"
		},
};
static const int asn_MAP_NR_ext13_oms_44[] = { 0, 1, 2 };
static const ber_tlv_tag_t asn_DEF_NR_ext13_tags_44[] = {
	(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext13_tag2el_44[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* supportedBandCombinationList-v1720 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* supportedBandCombinationListNEDC-Only-v1720 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* supportedBandCombinationList-UplinkTxSwitch-v1720 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext13_specs_44 = {
	sizeof(struct NR_RF_ParametersMRDC__ext13),
	offsetof(struct NR_RF_ParametersMRDC__ext13, _asn_ctx),
	asn_MAP_NR_ext13_tag2el_44,
	3,	/* Count of tags in the map */
	asn_MAP_NR_ext13_oms_44,	/* Optional members */
	3, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext13_44 = {
	"ext13",
	"ext13",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext13_tags_44,
	sizeof(asn_DEF_NR_ext13_tags_44)
		/sizeof(asn_DEF_NR_ext13_tags_44[0]) - 1, /* 1 */
	asn_DEF_NR_ext13_tags_44,	/* Same as above */
	sizeof(asn_DEF_NR_ext13_tags_44)
		/sizeof(asn_DEF_NR_ext13_tags_44[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext13_44,
	3,	/* Elements count */
	&asn_SPC_NR_ext13_specs_44	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext14_50[] = {
	{ ATF_POINTER, 3, offsetof(struct NR_RF_ParametersMRDC__ext14, supportedBandCombinationList_v1730),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1730,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-v1730"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_RF_ParametersMRDC__ext14, supportedBandCombinationListNEDC_Only_v1730),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_v1730,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationListNEDC-Only-v1730"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RF_ParametersMRDC__ext14, supportedBandCombinationList_UplinkTxSwitch_v1730),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList_UplinkTxSwitch_v1730,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList-UplinkTxSwitch-v1730"
		},
};
static const int asn_MAP_NR_ext14_oms_50[] = { 0, 1, 2 };
static const ber_tlv_tag_t asn_DEF_NR_ext14_tags_50[] = {
	(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext14_tag2el_50[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* supportedBandCombinationList-v1730 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* supportedBandCombinationListNEDC-Only-v1730 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* supportedBandCombinationList-UplinkTxSwitch-v1730 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext14_specs_50 = {
	sizeof(struct NR_RF_ParametersMRDC__ext14),
	offsetof(struct NR_RF_ParametersMRDC__ext14, _asn_ctx),
	asn_MAP_NR_ext14_tag2el_50,
	3,	/* Count of tags in the map */
	asn_MAP_NR_ext14_oms_50,	/* Optional members */
	3, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext14_50 = {
	"ext14",
	"ext14",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext14_tags_50,
	sizeof(asn_DEF_NR_ext14_tags_50)
		/sizeof(asn_DEF_NR_ext14_tags_50[0]) - 1, /* 1 */
	asn_DEF_NR_ext14_tags_50,	/* Same as above */
	sizeof(asn_DEF_NR_ext14_tags_50)
		/sizeof(asn_DEF_NR_ext14_tags_50[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext14_50,
	3,	/* Elements count */
	&asn_SPC_NR_ext14_specs_50	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_RF_ParametersMRDC_1[] = {
	{ ATF_POINTER, 16, offsetof(struct NR_RF_ParametersMRDC, supportedBandCombinationList),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BandCombinationList,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedBandCombinationList"
		},
	{ ATF_POINTER, 15, offsetof(struct NR_RF_ParametersMRDC, appliedFreqBandListFilter),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_FreqBandList,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"appliedFreqBandListFilter"
		},
	{ ATF_POINTER, 14, offsetof(struct NR_RF_ParametersMRDC, ext1),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		0,
		&asn_DEF_NR_ext1_5,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
	{ ATF_POINTER, 13, offsetof(struct NR_RF_ParametersMRDC, ext2),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		0,
		&asn_DEF_NR_ext2_9,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext2"
		},
	{ ATF_POINTER, 12, offsetof(struct NR_RF_ParametersMRDC, ext3),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		0,
		&asn_DEF_NR_ext3_11,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext3"
		},
	{ ATF_POINTER, 11, offsetof(struct NR_RF_ParametersMRDC, ext4),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		0,
		&asn_DEF_NR_ext4_14,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext4"
		},
	{ ATF_POINTER, 10, offsetof(struct NR_RF_ParametersMRDC, ext5),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		0,
		&asn_DEF_NR_ext5_16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext5"
		},
	{ ATF_POINTER, 9, offsetof(struct NR_RF_ParametersMRDC, ext6),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		0,
		&asn_DEF_NR_ext6_18,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext6"
		},
	{ ATF_POINTER, 8, offsetof(struct NR_RF_ParametersMRDC, ext7),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		0,
		&asn_DEF_NR_ext7_20,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext7"
		},
	{ ATF_POINTER, 7, offsetof(struct NR_RF_ParametersMRDC, ext8),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		0,
		&asn_DEF_NR_ext8_27,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext8"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_RF_ParametersMRDC, ext9),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		0,
		&asn_DEF_NR_ext9_31,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext9"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_RF_ParametersMRDC, ext10),
		(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
		0,
		&asn_DEF_NR_ext10_35,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext10"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_RF_ParametersMRDC, ext11),
		(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
		0,
		&asn_DEF_NR_ext11_39,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext11"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_RF_ParametersMRDC, ext12),
		(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
		0,
		&asn_DEF_NR_ext12_41,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext12"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_RF_ParametersMRDC, ext13),
		(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
		0,
		&asn_DEF_NR_ext13_44,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext13"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RF_ParametersMRDC, ext14),
		(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
		0,
		&asn_DEF_NR_ext14_50,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext14"
		},
};
static const int asn_MAP_NR_RF_ParametersMRDC_oms_1[] = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15 };
static const ber_tlv_tag_t asn_DEF_NR_RF_ParametersMRDC_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_RF_ParametersMRDC_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* supportedBandCombinationList */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* appliedFreqBandListFilter */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* ext1 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* ext2 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* ext3 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* ext4 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* ext5 */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* ext6 */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* ext7 */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* ext8 */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 }, /* ext9 */
    { (ASN_TAG_CLASS_CONTEXT | (11 << 2)), 11, 0, 0 }, /* ext10 */
    { (ASN_TAG_CLASS_CONTEXT | (12 << 2)), 12, 0, 0 }, /* ext11 */
    { (ASN_TAG_CLASS_CONTEXT | (13 << 2)), 13, 0, 0 }, /* ext12 */
    { (ASN_TAG_CLASS_CONTEXT | (14 << 2)), 14, 0, 0 }, /* ext13 */
    { (ASN_TAG_CLASS_CONTEXT | (15 << 2)), 15, 0, 0 } /* ext14 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_RF_ParametersMRDC_specs_1 = {
	sizeof(struct NR_RF_ParametersMRDC),
	offsetof(struct NR_RF_ParametersMRDC, _asn_ctx),
	asn_MAP_NR_RF_ParametersMRDC_tag2el_1,
	16,	/* Count of tags in the map */
	asn_MAP_NR_RF_ParametersMRDC_oms_1,	/* Optional members */
	2, 14,	/* Root/Additions */
	2,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_RF_ParametersMRDC = {
	"RF-ParametersMRDC",
	"RF-ParametersMRDC",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_RF_ParametersMRDC_tags_1,
	sizeof(asn_DEF_NR_RF_ParametersMRDC_tags_1)
		/sizeof(asn_DEF_NR_RF_ParametersMRDC_tags_1[0]), /* 1 */
	asn_DEF_NR_RF_ParametersMRDC_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_RF_ParametersMRDC_tags_1)
		/sizeof(asn_DEF_NR_RF_ParametersMRDC_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_RF_ParametersMRDC_1,
	16,	/* Elements count */
	&asn_SPC_NR_RF_ParametersMRDC_specs_1	/* Additional specs */
};

