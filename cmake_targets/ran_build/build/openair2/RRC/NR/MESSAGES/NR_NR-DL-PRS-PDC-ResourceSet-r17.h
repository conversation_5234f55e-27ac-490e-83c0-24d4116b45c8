/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_NR_DL_PRS_PDC_ResourceSet_r17_H_
#define	_NR_NR_DL_PRS_PDC_ResourceSet_r17_H_


#include <asn_application.h>

/* Including external dependencies */
#include "NR_NR-DL-PRS-Periodicity-and-ResourceSetSlotOffset-r17.h"
#include <NativeEnumerated.h>
#include <NativeInteger.h>
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_NR_DL_PRS_PDC_ResourceSet_r17__numSymbols_r17 {
	NR_NR_DL_PRS_PDC_ResourceSet_r17__numSymbols_r17_n2	= 0,
	NR_NR_DL_PRS_PDC_ResourceSet_r17__numSymbols_r17_n4	= 1,
	NR_NR_DL_PRS_PDC_ResourceSet_r17__numSymbols_r17_n6	= 2,
	NR_NR_DL_PRS_PDC_ResourceSet_r17__numSymbols_r17_n12	= 3,
	NR_NR_DL_PRS_PDC_ResourceSet_r17__numSymbols_r17_spare4	= 4,
	NR_NR_DL_PRS_PDC_ResourceSet_r17__numSymbols_r17_spare3	= 5,
	NR_NR_DL_PRS_PDC_ResourceSet_r17__numSymbols_r17_spare2	= 6,
	NR_NR_DL_PRS_PDC_ResourceSet_r17__numSymbols_r17_spare1	= 7
} e_NR_NR_DL_PRS_PDC_ResourceSet_r17__numSymbols_r17;

/* Forward declarations */
struct NR_RepFactorAndTimeGap_r17;
struct NR_NR_DL_PRS_Resource_r17;

/* NR_NR-DL-PRS-PDC-ResourceSet-r17 */
typedef struct NR_NR_DL_PRS_PDC_ResourceSet_r17 {
	NR_NR_DL_PRS_Periodicity_and_ResourceSetSlotOffset_r17_t	 periodicityAndOffset_r17;
	long	 numSymbols_r17;
	long	 dl_PRS_ResourceBandwidth_r17;
	long	 dl_PRS_StartPRB_r17;
	struct NR_NR_DL_PRS_PDC_ResourceSet_r17__resourceList_r17 {
		A_SEQUENCE_OF(struct NR_NR_DL_PRS_Resource_r17) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} resourceList_r17;
	struct NR_RepFactorAndTimeGap_r17	*repFactorAndTimeGap_r17;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_NR_DL_PRS_PDC_ResourceSet_r17_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_numSymbols_r17_3;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_NR_DL_PRS_PDC_ResourceSet_r17;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_NR_DL_PRS_PDC_ResourceSet_r17_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_NR_DL_PRS_PDC_ResourceSet_r17_1[6];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_RepFactorAndTimeGap-r17.h"
#include "NR_NR-DL-PRS-Resource-r17.h"

#endif	/* _NR_NR_DL_PRS_PDC_ResourceSet_r17_H_ */
#include <asn_internal.h>
