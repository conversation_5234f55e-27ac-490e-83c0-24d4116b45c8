/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_PUCCH_ResourceSet_H_
#define	_NR_PUCCH_ResourceSet_H_


#include <asn_application.h>

/* Including external dependencies */
#include "NR_PUCCH-ResourceSetId.h"
#include <NativeInteger.h>
#include "NR_PUCCH-ResourceId.h"
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* NR_PUCCH-ResourceSet */
typedef struct NR_PUCCH_ResourceSet {
	NR_PUCCH_ResourceSetId_t	 pucch_ResourceSetId;
	struct NR_PUCCH_ResourceSet__resourceList {
		A_SEQUENCE_OF(NR_PUCCH_ResourceId_t) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} resourceList;
	long	*maxPayloadSize;	/* OPTIONAL */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_PUCCH_ResourceSet_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_NR_PUCCH_ResourceSet;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_PUCCH_ResourceSet_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_PUCCH_ResourceSet_1[3];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_PUCCH_ResourceSet_H_ */
#include <asn_internal.h>
