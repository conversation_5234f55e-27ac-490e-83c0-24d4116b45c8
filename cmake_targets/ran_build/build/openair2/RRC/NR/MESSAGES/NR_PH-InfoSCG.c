/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-InterNodeDefinitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_PH-InfoSCG.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_twoSRS_PUSCH_Repetition_r17_constr_7 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_twoSRS_PUSCH_Repetition_r17_value2enum_7[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_twoSRS_PUSCH_Repetition_r17_enum2value_7[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_twoSRS_PUSCH_Repetition_r17_specs_7 = {
	asn_MAP_NR_twoSRS_PUSCH_Repetition_r17_value2enum_7,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_twoSRS_PUSCH_Repetition_r17_enum2value_7,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_twoSRS_PUSCH_Repetition_r17_tags_7[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_twoSRS_PUSCH_Repetition_r17_7 = {
	"twoSRS-PUSCH-Repetition-r17",
	"twoSRS-PUSCH-Repetition-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_twoSRS_PUSCH_Repetition_r17_tags_7,
	sizeof(asn_DEF_NR_twoSRS_PUSCH_Repetition_r17_tags_7)
		/sizeof(asn_DEF_NR_twoSRS_PUSCH_Repetition_r17_tags_7[0]) - 1, /* 1 */
	asn_DEF_NR_twoSRS_PUSCH_Repetition_r17_tags_7,	/* Same as above */
	sizeof(asn_DEF_NR_twoSRS_PUSCH_Repetition_r17_tags_7)
		/sizeof(asn_DEF_NR_twoSRS_PUSCH_Repetition_r17_tags_7[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_twoSRS_PUSCH_Repetition_r17_constr_7,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_twoSRS_PUSCH_Repetition_r17_specs_7	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext1_6[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_PH_InfoSCG__ext1, twoSRS_PUSCH_Repetition_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_twoSRS_PUSCH_Repetition_r17_7,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"twoSRS-PUSCH-Repetition-r17"
		},
};
static const int asn_MAP_NR_ext1_oms_6[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_6[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_6[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* twoSRS-PUSCH-Repetition-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_6 = {
	sizeof(struct NR_PH_InfoSCG__ext1),
	offsetof(struct NR_PH_InfoSCG__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_6,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_6,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_6 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_6,
	sizeof(asn_DEF_NR_ext1_tags_6)
		/sizeof(asn_DEF_NR_ext1_tags_6[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_6,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_6)
		/sizeof(asn_DEF_NR_ext1_tags_6[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_6,
	1,	/* Elements count */
	&asn_SPC_NR_ext1_specs_6	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_PH_InfoSCG_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PH_InfoSCG, servCellIndex),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ServCellIndex,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"servCellIndex"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PH_InfoSCG, ph_Uplink),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_PH_UplinkCarrierSCG,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ph-Uplink"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_PH_InfoSCG, ph_SupplementaryUplink),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_PH_UplinkCarrierSCG,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ph-SupplementaryUplink"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PH_InfoSCG, ext1),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		0,
		&asn_DEF_NR_ext1_6,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
};
static const int asn_MAP_NR_PH_InfoSCG_oms_1[] = { 2, 3 };
static const ber_tlv_tag_t asn_DEF_NR_PH_InfoSCG_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_PH_InfoSCG_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* servCellIndex */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* ph-Uplink */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* ph-SupplementaryUplink */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 } /* ext1 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_PH_InfoSCG_specs_1 = {
	sizeof(struct NR_PH_InfoSCG),
	offsetof(struct NR_PH_InfoSCG, _asn_ctx),
	asn_MAP_NR_PH_InfoSCG_tag2el_1,
	4,	/* Count of tags in the map */
	asn_MAP_NR_PH_InfoSCG_oms_1,	/* Optional members */
	1, 1,	/* Root/Additions */
	3,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_PH_InfoSCG = {
	"PH-InfoSCG",
	"PH-InfoSCG",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_PH_InfoSCG_tags_1,
	sizeof(asn_DEF_NR_PH_InfoSCG_tags_1)
		/sizeof(asn_DEF_NR_PH_InfoSCG_tags_1[0]), /* 1 */
	asn_DEF_NR_PH_InfoSCG_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_PH_InfoSCG_tags_1)
		/sizeof(asn_DEF_NR_PH_InfoSCG_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_PH_InfoSCG_1,
	4,	/* Elements count */
	&asn_SPC_NR_PH_InfoSCG_specs_1	/* Additional specs */
};

