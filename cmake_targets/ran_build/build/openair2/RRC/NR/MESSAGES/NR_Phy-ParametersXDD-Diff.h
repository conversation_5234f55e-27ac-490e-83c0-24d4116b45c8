/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_Phy_ParametersXDD_Diff_H_
#define	_NR_Phy_ParametersXDD_Diff_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_Phy_ParametersXDD_Diff__dynamicSFI {
	NR_Phy_ParametersXDD_Diff__dynamicSFI_supported	= 0
} e_NR_Phy_ParametersXDD_Diff__dynamicSFI;
typedef enum NR_Phy_ParametersXDD_Diff__twoPUCCH_F0_2_ConsecSymbols {
	NR_Phy_ParametersXDD_Diff__twoPUCCH_F0_2_ConsecSymbols_supported	= 0
} e_NR_Phy_ParametersXDD_Diff__twoPUCCH_F0_2_ConsecSymbols;
typedef enum NR_Phy_ParametersXDD_Diff__twoDifferentTPC_Loop_PUSCH {
	NR_Phy_ParametersXDD_Diff__twoDifferentTPC_Loop_PUSCH_supported	= 0
} e_NR_Phy_ParametersXDD_Diff__twoDifferentTPC_Loop_PUSCH;
typedef enum NR_Phy_ParametersXDD_Diff__twoDifferentTPC_Loop_PUCCH {
	NR_Phy_ParametersXDD_Diff__twoDifferentTPC_Loop_PUCCH_supported	= 0
} e_NR_Phy_ParametersXDD_Diff__twoDifferentTPC_Loop_PUCCH;
typedef enum NR_Phy_ParametersXDD_Diff__ext1__dl_SchedulingOffset_PDSCH_TypeA {
	NR_Phy_ParametersXDD_Diff__ext1__dl_SchedulingOffset_PDSCH_TypeA_supported	= 0
} e_NR_Phy_ParametersXDD_Diff__ext1__dl_SchedulingOffset_PDSCH_TypeA;
typedef enum NR_Phy_ParametersXDD_Diff__ext1__dl_SchedulingOffset_PDSCH_TypeB {
	NR_Phy_ParametersXDD_Diff__ext1__dl_SchedulingOffset_PDSCH_TypeB_supported	= 0
} e_NR_Phy_ParametersXDD_Diff__ext1__dl_SchedulingOffset_PDSCH_TypeB;
typedef enum NR_Phy_ParametersXDD_Diff__ext1__ul_SchedulingOffset {
	NR_Phy_ParametersXDD_Diff__ext1__ul_SchedulingOffset_supported	= 0
} e_NR_Phy_ParametersXDD_Diff__ext1__ul_SchedulingOffset;

/* NR_Phy-ParametersXDD-Diff */
typedef struct NR_Phy_ParametersXDD_Diff {
	long	*dynamicSFI;	/* OPTIONAL */
	long	*twoPUCCH_F0_2_ConsecSymbols;	/* OPTIONAL */
	long	*twoDifferentTPC_Loop_PUSCH;	/* OPTIONAL */
	long	*twoDifferentTPC_Loop_PUCCH;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	struct NR_Phy_ParametersXDD_Diff__ext1 {
		long	*dl_SchedulingOffset_PDSCH_TypeA;	/* OPTIONAL */
		long	*dl_SchedulingOffset_PDSCH_TypeB;	/* OPTIONAL */
		long	*ul_SchedulingOffset;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext1;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_Phy_ParametersXDD_Diff_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dynamicSFI_2;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_twoPUCCH_F0_2_ConsecSymbols_4;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_twoDifferentTPC_Loop_PUSCH_6;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_twoDifferentTPC_Loop_PUCCH_8;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeA_12;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeB_14;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_ul_SchedulingOffset_16;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_Phy_ParametersXDD_Diff;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_Phy_ParametersXDD_Diff_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_Phy_ParametersXDD_Diff_1[5];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_Phy_ParametersXDD_Diff_H_ */
#include <asn_internal.h>
