/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_MeasResultsPerCellIdleNR_r16_H_
#define	_NR_MeasResultsPerCellIdleNR_r16_H_


#include <asn_application.h>

/* Including external dependencies */
#include "NR_PhysCellId.h"
#include "NR_RSRP-Range.h"
#include "NR_RSRQ-Range.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct NR_ResultsPerSSB_IndexList_r16;

/* NR_MeasResultsPerCellIdleNR-r16 */
typedef struct NR_MeasResultsPerCellIdleNR_r16 {
	NR_PhysCellId_t	 physCellId_r16;
	struct NR_MeasResultsPerCellIdleNR_r16__measIdleResultNR_r16 {
		NR_RSRP_Range_t	*rsrp_Result_r16;	/* OPTIONAL */
		NR_RSRQ_Range_t	*rsrq_Result_r16;	/* OPTIONAL */
		struct NR_ResultsPerSSB_IndexList_r16	*resultsSSB_Indexes_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} measIdleResultNR_r16;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_MeasResultsPerCellIdleNR_r16_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_NR_MeasResultsPerCellIdleNR_r16;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_MeasResultsPerCellIdleNR_r16_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_MeasResultsPerCellIdleNR_r16_1[2];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_ResultsPerSSB-IndexList-r16.h"

#endif	/* _NR_MeasResultsPerCellIdleNR_r16_H_ */
#include <asn_internal.h>
