/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_DownlinkConfigCommonSIB_H_
#define	_NR_DownlinkConfigCommonSIB_H_


#include <asn_application.h>

/* Including external dependencies */
#include "NR_FrequencyInfoDL-SIB.h"
#include "NR_BWP-DownlinkCommon.h"
#include "NR_BCCH-Config.h"
#include "NR_PCCH-Config.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct NR_PEI_Config_r17;
struct NR_BWP_DownlinkCommon;

/* NR_DownlinkConfigCommonSIB */
typedef struct NR_DownlinkConfigCommonSIB {
	NR_FrequencyInfoDL_SIB_t	 frequencyInfoDL;
	NR_BWP_DownlinkCommon_t	 initialDownlinkBWP;
	NR_BCCH_Config_t	 bcch_Config;
	NR_PCCH_Config_t	 pcch_Config;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	struct NR_DownlinkConfigCommonSIB__ext1 {
		struct NR_PEI_Config_r17	*pei_Config_r17;	/* OPTIONAL */
		struct NR_BWP_DownlinkCommon	*initialDownlinkBWP_RedCap_r17;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext1;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_DownlinkConfigCommonSIB_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_NR_DownlinkConfigCommonSIB;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_DownlinkConfigCommonSIB_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_DownlinkConfigCommonSIB_1[5];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_PEI-Config-r17.h"
#include "NR_BWP-DownlinkCommon.h"

#endif	/* _NR_DownlinkConfigCommonSIB_H_ */
#include <asn_internal.h>
