/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_PDSCH_ConfigBroadcast_r17_H_
#define	_NR_PDSCH_ConfigBroadcast_r17_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_PDSCH_ConfigBroadcast_r17__mcs_Table_r17 {
	NR_PDSCH_ConfigBroadcast_r17__mcs_Table_r17_qam256	= 0,
	NR_PDSCH_ConfigBroadcast_r17__mcs_Table_r17_qam64LowSE	= 1
} e_NR_PDSCH_ConfigBroadcast_r17__mcs_Table_r17;
typedef enum NR_PDSCH_ConfigBroadcast_r17__xOverhead_r17 {
	NR_PDSCH_ConfigBroadcast_r17__xOverhead_r17_xOh6	= 0,
	NR_PDSCH_ConfigBroadcast_r17__xOverhead_r17_xOh12	= 1,
	NR_PDSCH_ConfigBroadcast_r17__xOverhead_r17_xOh18	= 2
} e_NR_PDSCH_ConfigBroadcast_r17__xOverhead_r17;

/* Forward declarations */
struct NR_PDSCH_TimeDomainResourceAllocationList_r16;
struct NR_RateMatchPatternLTE_CRS;
struct NR_PDSCH_ConfigPTM_r17;
struct NR_RateMatchPattern;

/* NR_PDSCH-ConfigBroadcast-r17 */
typedef struct NR_PDSCH_ConfigBroadcast_r17 {
	struct NR_PDSCH_ConfigBroadcast_r17__pdschConfigList_r17 {
		A_SEQUENCE_OF(struct NR_PDSCH_ConfigPTM_r17) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} pdschConfigList_r17;
	struct NR_PDSCH_TimeDomainResourceAllocationList_r16	*pdsch_TimeDomainAllocationList_r17;	/* OPTIONAL */
	struct NR_PDSCH_ConfigBroadcast_r17__rateMatchPatternToAddModList_r17 {
		A_SEQUENCE_OF(struct NR_RateMatchPattern) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *rateMatchPatternToAddModList_r17;
	struct NR_RateMatchPatternLTE_CRS	*lte_CRS_ToMatchAround_r17;	/* OPTIONAL */
	long	*mcs_Table_r17;	/* OPTIONAL */
	long	*xOverhead_r17;	/* OPTIONAL */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_PDSCH_ConfigBroadcast_r17_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mcs_Table_r17_8;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_xOverhead_r17_11;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_PDSCH_ConfigBroadcast_r17;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_PDSCH_ConfigBroadcast_r17_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_PDSCH_ConfigBroadcast_r17_1[6];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_PDSCH-TimeDomainResourceAllocationList-r16.h"
#include "NR_RateMatchPatternLTE-CRS.h"
#include "NR_PDSCH-ConfigPTM-r17.h"
#include "NR_RateMatchPattern.h"

#endif	/* _NR_PDSCH_ConfigBroadcast_r17_H_ */
#include <asn_internal.h>
