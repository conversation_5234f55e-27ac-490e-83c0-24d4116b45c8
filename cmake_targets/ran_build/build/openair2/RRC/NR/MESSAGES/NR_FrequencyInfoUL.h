/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_FrequencyInfoUL_H_
#define	_NR_FrequencyInfoUL_H_


#include <asn_application.h>

/* Including external dependencies */
#include "NR_ARFCN-ValueNR.h"
#include "NR_AdditionalSpectrumEmission.h"
#include "NR_P-Max.h"
#include <NativeEnumerated.h>
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_FrequencyInfoUL__frequencyShift7p5khz {
	NR_FrequencyInfoUL__frequencyShift7p5khz_true	= 0
} e_NR_FrequencyInfoUL__frequencyShift7p5khz;

/* Forward declarations */
struct NR_MultiFrequencyBandListNR;
struct NR_SCS_SpecificCarrier;

/* NR_FrequencyInfoUL */
typedef struct NR_FrequencyInfoUL {
	struct NR_MultiFrequencyBandListNR	*frequencyBandList;	/* OPTIONAL */
	NR_ARFCN_ValueNR_t	*absoluteFrequencyPointA;	/* OPTIONAL */
	struct NR_FrequencyInfoUL__scs_SpecificCarrierList {
		A_SEQUENCE_OF(struct NR_SCS_SpecificCarrier) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} scs_SpecificCarrierList;
	NR_AdditionalSpectrumEmission_t	*additionalSpectrumEmission;	/* OPTIONAL */
	NR_P_Max_t	*p_Max;	/* OPTIONAL */
	long	*frequencyShift7p5khz;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_FrequencyInfoUL_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_frequencyShift7p5khz_8;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_FrequencyInfoUL;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_FrequencyInfoUL_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_FrequencyInfoUL_1[6];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_MultiFrequencyBandListNR.h"
#include "NR_SCS-SpecificCarrier.h"

#endif	/* _NR_FrequencyInfoUL_H_ */
#include <asn_internal.h>
