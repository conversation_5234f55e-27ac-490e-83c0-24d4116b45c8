/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_PUCCH_CSI_Resource_H_
#define	_NR_PUCCH_CSI_Resource_H_


#include <asn_application.h>

/* Including external dependencies */
#include "NR_BWP-Id.h"
#include "NR_PUCCH-ResourceId.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* NR_PUCCH-CSI-Resource */
typedef struct NR_PUCCH_CSI_Resource {
	NR_BWP_Id_t	 uplinkBandwidthPartId;
	NR_PUCCH_ResourceId_t	 pucch_Resource;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_PUCCH_CSI_Resource_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_NR_PUCCH_CSI_Resource;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_PUCCH_CSI_Resource_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_PUCCH_CSI_Resource_1[2];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_PUCCH_CSI_Resource_H_ */
#include <asn_internal.h>
