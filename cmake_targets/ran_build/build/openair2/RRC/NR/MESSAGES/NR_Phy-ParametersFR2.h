/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_Phy_ParametersFR2_H_
#define	_NR_Phy_ParametersFR2_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_Phy_ParametersFR2__dummy {
	NR_Phy_ParametersFR2__dummy_supported	= 0
} e_NR_Phy_ParametersFR2__dummy;
typedef enum NR_Phy_ParametersFR2__pdsch_RE_MappingFR2_PerSymbol {
	NR_Phy_ParametersFR2__pdsch_RE_MappingFR2_PerSymbol_n6	= 0,
	NR_Phy_ParametersFR2__pdsch_RE_MappingFR2_PerSymbol_n20	= 1
} e_NR_Phy_ParametersFR2__pdsch_RE_MappingFR2_PerSymbol;
typedef enum NR_Phy_ParametersFR2__ext1__pCell_FR2 {
	NR_Phy_ParametersFR2__ext1__pCell_FR2_supported	= 0
} e_NR_Phy_ParametersFR2__ext1__pCell_FR2;
typedef enum NR_Phy_ParametersFR2__ext1__pdsch_RE_MappingFR2_PerSlot {
	NR_Phy_ParametersFR2__ext1__pdsch_RE_MappingFR2_PerSlot_n16	= 0,
	NR_Phy_ParametersFR2__ext1__pdsch_RE_MappingFR2_PerSlot_n32	= 1,
	NR_Phy_ParametersFR2__ext1__pdsch_RE_MappingFR2_PerSlot_n48	= 2,
	NR_Phy_ParametersFR2__ext1__pdsch_RE_MappingFR2_PerSlot_n64	= 3,
	NR_Phy_ParametersFR2__ext1__pdsch_RE_MappingFR2_PerSlot_n80	= 4,
	NR_Phy_ParametersFR2__ext1__pdsch_RE_MappingFR2_PerSlot_n96	= 5,
	NR_Phy_ParametersFR2__ext1__pdsch_RE_MappingFR2_PerSlot_n112	= 6,
	NR_Phy_ParametersFR2__ext1__pdsch_RE_MappingFR2_PerSlot_n128	= 7,
	NR_Phy_ParametersFR2__ext1__pdsch_RE_MappingFR2_PerSlot_n144	= 8,
	NR_Phy_ParametersFR2__ext1__pdsch_RE_MappingFR2_PerSlot_n160	= 9,
	NR_Phy_ParametersFR2__ext1__pdsch_RE_MappingFR2_PerSlot_n176	= 10,
	NR_Phy_ParametersFR2__ext1__pdsch_RE_MappingFR2_PerSlot_n192	= 11,
	NR_Phy_ParametersFR2__ext1__pdsch_RE_MappingFR2_PerSlot_n208	= 12,
	NR_Phy_ParametersFR2__ext1__pdsch_RE_MappingFR2_PerSlot_n224	= 13,
	NR_Phy_ParametersFR2__ext1__pdsch_RE_MappingFR2_PerSlot_n240	= 14,
	NR_Phy_ParametersFR2__ext1__pdsch_RE_MappingFR2_PerSlot_n256	= 15
} e_NR_Phy_ParametersFR2__ext1__pdsch_RE_MappingFR2_PerSlot;
typedef enum NR_Phy_ParametersFR2__ext2__defaultSpatialRelationPathlossRS_r16 {
	NR_Phy_ParametersFR2__ext2__defaultSpatialRelationPathlossRS_r16_supported	= 0
} e_NR_Phy_ParametersFR2__ext2__defaultSpatialRelationPathlossRS_r16;
typedef enum NR_Phy_ParametersFR2__ext2__spatialRelationUpdateAP_SRS_r16 {
	NR_Phy_ParametersFR2__ext2__spatialRelationUpdateAP_SRS_r16_supported	= 0
} e_NR_Phy_ParametersFR2__ext2__spatialRelationUpdateAP_SRS_r16;
typedef enum NR_Phy_ParametersFR2__ext2__maxNumberSRS_PosSpatialRelationsAllServingCells_r16 {
	NR_Phy_ParametersFR2__ext2__maxNumberSRS_PosSpatialRelationsAllServingCells_r16_n0	= 0,
	NR_Phy_ParametersFR2__ext2__maxNumberSRS_PosSpatialRelationsAllServingCells_r16_n1	= 1,
	NR_Phy_ParametersFR2__ext2__maxNumberSRS_PosSpatialRelationsAllServingCells_r16_n2	= 2,
	NR_Phy_ParametersFR2__ext2__maxNumberSRS_PosSpatialRelationsAllServingCells_r16_n4	= 3,
	NR_Phy_ParametersFR2__ext2__maxNumberSRS_PosSpatialRelationsAllServingCells_r16_n8	= 4,
	NR_Phy_ParametersFR2__ext2__maxNumberSRS_PosSpatialRelationsAllServingCells_r16_n16	= 5
} e_NR_Phy_ParametersFR2__ext2__maxNumberSRS_PosSpatialRelationsAllServingCells_r16;

/* NR_Phy-ParametersFR2 */
typedef struct NR_Phy_ParametersFR2 {
	long	*dummy;	/* OPTIONAL */
	long	*pdsch_RE_MappingFR2_PerSymbol;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	struct NR_Phy_ParametersFR2__ext1 {
		long	*pCell_FR2;	/* OPTIONAL */
		long	*pdsch_RE_MappingFR2_PerSlot;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext1;
	struct NR_Phy_ParametersFR2__ext2 {
		long	*defaultSpatialRelationPathlossRS_r16;	/* OPTIONAL */
		long	*spatialRelationUpdateAP_SRS_r16;	/* OPTIONAL */
		long	*maxNumberSRS_PosSpatialRelationsAllServingCells_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext2;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_Phy_ParametersFR2_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dummy_2;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pdsch_RE_MappingFR2_PerSymbol_4;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pCell_FR2_9;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pdsch_RE_MappingFR2_PerSlot_11;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_defaultSpatialRelationPathlossRS_r16_29;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_spatialRelationUpdateAP_SRS_r16_31;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumberSRS_PosSpatialRelationsAllServingCells_r16_33;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_Phy_ParametersFR2;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_Phy_ParametersFR2_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_Phy_ParametersFR2_1[4];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_Phy_ParametersFR2_H_ */
#include <asn_internal.h>
