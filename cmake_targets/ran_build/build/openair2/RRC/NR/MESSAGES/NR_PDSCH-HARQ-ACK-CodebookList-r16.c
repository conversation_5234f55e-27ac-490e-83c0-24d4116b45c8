/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_PDSCH-HARQ-ACK-CodebookList-r16.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_Member_constr_2 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_NR_PDSCH_HARQ_ACK_CodebookList_r16_constr_1 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 1,  1,  1,  2 }	/* (SIZE(1..2)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_Member_value2enum_2[] = {
	{ 0,	10,	"semiStatic" },
	{ 1,	7,	"dynamic" }
};
static const unsigned int asn_MAP_NR_Member_enum2value_2[] = {
	1,	/* dynamic(1) */
	0	/* semiStatic(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_Member_specs_2 = {
	asn_MAP_NR_Member_value2enum_2,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_Member_enum2value_2,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_Member_tags_2[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_Member_2 = {
	"ENUMERATED",
	"ENUMERATED",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_Member_tags_2,
	sizeof(asn_DEF_NR_Member_tags_2)
		/sizeof(asn_DEF_NR_Member_tags_2[0]), /* 1 */
	asn_DEF_NR_Member_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_Member_tags_2)
		/sizeof(asn_DEF_NR_Member_tags_2[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_Member_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_Member_specs_2	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_PDSCH_HARQ_ACK_CodebookList_r16_1[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (10 << 2)),
		0,
		&asn_DEF_NR_Member_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_PDSCH_HARQ_ACK_CodebookList_r16_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_NR_PDSCH_HARQ_ACK_CodebookList_r16_specs_1 = {
	sizeof(struct NR_PDSCH_HARQ_ACK_CodebookList_r16),
	offsetof(struct NR_PDSCH_HARQ_ACK_CodebookList_r16, _asn_ctx),
	1,	/* XER encoding is XMLValueList */
};
asn_TYPE_descriptor_t asn_DEF_NR_PDSCH_HARQ_ACK_CodebookList_r16 = {
	"PDSCH-HARQ-ACK-CodebookList-r16",
	"PDSCH-HARQ-ACK-CodebookList-r16",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_PDSCH_HARQ_ACK_CodebookList_r16_tags_1,
	sizeof(asn_DEF_NR_PDSCH_HARQ_ACK_CodebookList_r16_tags_1)
		/sizeof(asn_DEF_NR_PDSCH_HARQ_ACK_CodebookList_r16_tags_1[0]), /* 1 */
	asn_DEF_NR_PDSCH_HARQ_ACK_CodebookList_r16_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_PDSCH_HARQ_ACK_CodebookList_r16_tags_1)
		/sizeof(asn_DEF_NR_PDSCH_HARQ_ACK_CodebookList_r16_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_PDSCH_HARQ_ACK_CodebookList_r16_constr_1,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_PDSCH_HARQ_ACK_CodebookList_r16_1,
	1,	/* Single element */
	&asn_SPC_NR_PDSCH_HARQ_ACK_CodebookList_r16_specs_1	/* Additional specs */
};

