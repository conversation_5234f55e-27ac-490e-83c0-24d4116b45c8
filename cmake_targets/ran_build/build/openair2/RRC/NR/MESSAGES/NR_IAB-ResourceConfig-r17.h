/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_IAB_ResourceConfig_r17_H_
#define	_NR_IAB_ResourceConfig_r17_H_


#include <asn_application.h>

/* Including external dependencies */
#include "NR_IAB-ResourceConfigID-r17.h"
#include <NativeEnumerated.h>
#include "NR_SubcarrierSpacing.h"
#include <NativeInteger.h>
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_IAB_ResourceConfig_r17__periodicitySlotList_r17 {
	NR_IAB_ResourceConfig_r17__periodicitySlotList_r17_ms0p5	= 0,
	NR_IAB_ResourceConfig_r17__periodicitySlotList_r17_ms0p625	= 1,
	NR_IAB_ResourceConfig_r17__periodicitySlotList_r17_ms1	= 2,
	NR_IAB_ResourceConfig_r17__periodicitySlotList_r17_ms1p25	= 3,
	NR_IAB_ResourceConfig_r17__periodicitySlotList_r17_ms2	= 4,
	NR_IAB_ResourceConfig_r17__periodicitySlotList_r17_ms2p5	= 5,
	NR_IAB_ResourceConfig_r17__periodicitySlotList_r17_ms5	= 6,
	NR_IAB_ResourceConfig_r17__periodicitySlotList_r17_ms10	= 7,
	NR_IAB_ResourceConfig_r17__periodicitySlotList_r17_ms20	= 8,
	NR_IAB_ResourceConfig_r17__periodicitySlotList_r17_ms40	= 9,
	NR_IAB_ResourceConfig_r17__periodicitySlotList_r17_ms80	= 10,
	NR_IAB_ResourceConfig_r17__periodicitySlotList_r17_ms160	= 11
} e_NR_IAB_ResourceConfig_r17__periodicitySlotList_r17;

/* NR_IAB-ResourceConfig-r17 */
typedef struct NR_IAB_ResourceConfig_r17 {
	NR_IAB_ResourceConfigID_r17_t	 iab_ResourceConfigID_r17;
	struct NR_IAB_ResourceConfig_r17__slotList_r17 {
		A_SEQUENCE_OF(long) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *slotList_r17;
	long	*periodicitySlotList_r17;	/* OPTIONAL */
	NR_SubcarrierSpacing_t	*slotListSubcarrierSpacing_r17;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_IAB_ResourceConfig_r17_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_periodicitySlotList_r17_5;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_IAB_ResourceConfig_r17;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_IAB_ResourceConfig_r17_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_IAB_ResourceConfig_r17_1[4];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_IAB_ResourceConfig_r17_H_ */
#include <asn_internal.h>
