/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_PDSCH-ServingCellConfig.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_maxMIMO_Layers_constraint_16(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 8L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_xOverhead_constr_3 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_nrofHARQ_ProcessesForPDSCH_constr_7 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  5 }	/* (0..5) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_maxMIMO_Layers_constr_17 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (1..8) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_nrofHARQ_ProcessesForPDSCH_v1700_constr_23 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_xOverhead_value2enum_3[] = {
	{ 0,	4,	"xOh6" },
	{ 1,	5,	"xOh12" },
	{ 2,	5,	"xOh18" }
};
static const unsigned int asn_MAP_NR_xOverhead_enum2value_3[] = {
	1,	/* xOh12(1) */
	2,	/* xOh18(2) */
	0	/* xOh6(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_xOverhead_specs_3 = {
	asn_MAP_NR_xOverhead_value2enum_3,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_xOverhead_enum2value_3,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_xOverhead_tags_3[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_xOverhead_3 = {
	"xOverhead",
	"xOverhead",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_xOverhead_tags_3,
	sizeof(asn_DEF_NR_xOverhead_tags_3)
		/sizeof(asn_DEF_NR_xOverhead_tags_3[0]) - 1, /* 1 */
	asn_DEF_NR_xOverhead_tags_3,	/* Same as above */
	sizeof(asn_DEF_NR_xOverhead_tags_3)
		/sizeof(asn_DEF_NR_xOverhead_tags_3[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_xOverhead_constr_3,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_xOverhead_specs_3	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_nrofHARQ_ProcessesForPDSCH_value2enum_7[] = {
	{ 0,	2,	"n2" },
	{ 1,	2,	"n4" },
	{ 2,	2,	"n6" },
	{ 3,	3,	"n10" },
	{ 4,	3,	"n12" },
	{ 5,	3,	"n16" }
};
static const unsigned int asn_MAP_NR_nrofHARQ_ProcessesForPDSCH_enum2value_7[] = {
	3,	/* n10(3) */
	4,	/* n12(4) */
	5,	/* n16(5) */
	0,	/* n2(0) */
	1,	/* n4(1) */
	2	/* n6(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_nrofHARQ_ProcessesForPDSCH_specs_7 = {
	asn_MAP_NR_nrofHARQ_ProcessesForPDSCH_value2enum_7,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_nrofHARQ_ProcessesForPDSCH_enum2value_7,	/* N => "tag"; sorted by N */
	6,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_nrofHARQ_ProcessesForPDSCH_tags_7[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_nrofHARQ_ProcessesForPDSCH_7 = {
	"nrofHARQ-ProcessesForPDSCH",
	"nrofHARQ-ProcessesForPDSCH",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_nrofHARQ_ProcessesForPDSCH_tags_7,
	sizeof(asn_DEF_NR_nrofHARQ_ProcessesForPDSCH_tags_7)
		/sizeof(asn_DEF_NR_nrofHARQ_ProcessesForPDSCH_tags_7[0]) - 1, /* 1 */
	asn_DEF_NR_nrofHARQ_ProcessesForPDSCH_tags_7,	/* Same as above */
	sizeof(asn_DEF_NR_nrofHARQ_ProcessesForPDSCH_tags_7)
		/sizeof(asn_DEF_NR_nrofHARQ_ProcessesForPDSCH_tags_7[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_nrofHARQ_ProcessesForPDSCH_constr_7,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_nrofHARQ_ProcessesForPDSCH_specs_7	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext1_16[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_PDSCH_ServingCellConfig__ext1, maxMIMO_Layers),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_maxMIMO_Layers_constr_17,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_maxMIMO_Layers_constraint_16
		},
		0, 0, /* No default value */
		"maxMIMO-Layers"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PDSCH_ServingCellConfig__ext1, processingType2Enabled),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_BOOLEAN,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"processingType2Enabled"
		},
};
static const int asn_MAP_NR_ext1_oms_16[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_16[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_16[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* maxMIMO-Layers */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* processingType2Enabled */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_16 = {
	sizeof(struct NR_PDSCH_ServingCellConfig__ext1),
	offsetof(struct NR_PDSCH_ServingCellConfig__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_16,
	2,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_16,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_16 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_16,
	sizeof(asn_DEF_NR_ext1_tags_16)
		/sizeof(asn_DEF_NR_ext1_tags_16[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_16,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_16)
		/sizeof(asn_DEF_NR_ext1_tags_16[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_16,
	2,	/* Elements count */
	&asn_SPC_NR_ext1_specs_16	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext2_19[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_PDSCH_ServingCellConfig__ext2, pdsch_CodeBlockGroupTransmissionList_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_PDSCH_CodeBlockGroupTransmissionList_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pdsch-CodeBlockGroupTransmissionList-r16"
		},
};
static const int asn_MAP_NR_ext2_oms_19[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext2_tags_19[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext2_tag2el_19[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* pdsch-CodeBlockGroupTransmissionList-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext2_specs_19 = {
	sizeof(struct NR_PDSCH_ServingCellConfig__ext2),
	offsetof(struct NR_PDSCH_ServingCellConfig__ext2, _asn_ctx),
	asn_MAP_NR_ext2_tag2el_19,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext2_oms_19,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext2_19 = {
	"ext2",
	"ext2",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext2_tags_19,
	sizeof(asn_DEF_NR_ext2_tags_19)
		/sizeof(asn_DEF_NR_ext2_tags_19[0]) - 1, /* 1 */
	asn_DEF_NR_ext2_tags_19,	/* Same as above */
	sizeof(asn_DEF_NR_ext2_tags_19)
		/sizeof(asn_DEF_NR_ext2_tags_19[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext2_19,
	1,	/* Elements count */
	&asn_SPC_NR_ext2_specs_19	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_nrofHARQ_ProcessesForPDSCH_v1700_value2enum_23[] = {
	{ 0,	3,	"n32" }
};
static const unsigned int asn_MAP_NR_nrofHARQ_ProcessesForPDSCH_v1700_enum2value_23[] = {
	0	/* n32(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_nrofHARQ_ProcessesForPDSCH_v1700_specs_23 = {
	asn_MAP_NR_nrofHARQ_ProcessesForPDSCH_v1700_value2enum_23,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_nrofHARQ_ProcessesForPDSCH_v1700_enum2value_23,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_nrofHARQ_ProcessesForPDSCH_v1700_tags_23[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_nrofHARQ_ProcessesForPDSCH_v1700_23 = {
	"nrofHARQ-ProcessesForPDSCH-v1700",
	"nrofHARQ-ProcessesForPDSCH-v1700",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_nrofHARQ_ProcessesForPDSCH_v1700_tags_23,
	sizeof(asn_DEF_NR_nrofHARQ_ProcessesForPDSCH_v1700_tags_23)
		/sizeof(asn_DEF_NR_nrofHARQ_ProcessesForPDSCH_v1700_tags_23[0]) - 1, /* 1 */
	asn_DEF_NR_nrofHARQ_ProcessesForPDSCH_v1700_tags_23,	/* Same as above */
	sizeof(asn_DEF_NR_nrofHARQ_ProcessesForPDSCH_v1700_tags_23)
		/sizeof(asn_DEF_NR_nrofHARQ_ProcessesForPDSCH_v1700_tags_23[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_nrofHARQ_ProcessesForPDSCH_v1700_constr_23,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_nrofHARQ_ProcessesForPDSCH_v1700_specs_23	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext3_21[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_PDSCH_ServingCellConfig__ext3, downlinkHARQ_FeedbackDisabled_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_DownlinkHARQ_FeedbackDisabled_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"downlinkHARQ-FeedbackDisabled-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PDSCH_ServingCellConfig__ext3, nrofHARQ_ProcessesForPDSCH_v1700),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_nrofHARQ_ProcessesForPDSCH_v1700_23,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"nrofHARQ-ProcessesForPDSCH-v1700"
		},
};
static const int asn_MAP_NR_ext3_oms_21[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_ext3_tags_21[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext3_tag2el_21[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* downlinkHARQ-FeedbackDisabled-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* nrofHARQ-ProcessesForPDSCH-v1700 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext3_specs_21 = {
	sizeof(struct NR_PDSCH_ServingCellConfig__ext3),
	offsetof(struct NR_PDSCH_ServingCellConfig__ext3, _asn_ctx),
	asn_MAP_NR_ext3_tag2el_21,
	2,	/* Count of tags in the map */
	asn_MAP_NR_ext3_oms_21,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext3_21 = {
	"ext3",
	"ext3",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext3_tags_21,
	sizeof(asn_DEF_NR_ext3_tags_21)
		/sizeof(asn_DEF_NR_ext3_tags_21[0]) - 1, /* 1 */
	asn_DEF_NR_ext3_tags_21,	/* Same as above */
	sizeof(asn_DEF_NR_ext3_tags_21)
		/sizeof(asn_DEF_NR_ext3_tags_21[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext3_21,
	2,	/* Elements count */
	&asn_SPC_NR_ext3_specs_21	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_PDSCH_ServingCellConfig_1[] = {
	{ ATF_POINTER, 7, offsetof(struct NR_PDSCH_ServingCellConfig, codeBlockGroupTransmission),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_PDSCH_CodeBlockGroupTransmission,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"codeBlockGroupTransmission"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_PDSCH_ServingCellConfig, xOverhead),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_xOverhead_3,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"xOverhead"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_PDSCH_ServingCellConfig, nrofHARQ_ProcessesForPDSCH),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_nrofHARQ_ProcessesForPDSCH_7,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"nrofHARQ-ProcessesForPDSCH"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_PDSCH_ServingCellConfig, pucch_Cell),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ServCellIndex,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pucch-Cell"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_PDSCH_ServingCellConfig, ext1),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		0,
		&asn_DEF_NR_ext1_16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_PDSCH_ServingCellConfig, ext2),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		0,
		&asn_DEF_NR_ext2_19,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext2"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PDSCH_ServingCellConfig, ext3),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		0,
		&asn_DEF_NR_ext3_21,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext3"
		},
};
static const int asn_MAP_NR_PDSCH_ServingCellConfig_oms_1[] = { 0, 1, 2, 3, 4, 5, 6 };
static const ber_tlv_tag_t asn_DEF_NR_PDSCH_ServingCellConfig_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_PDSCH_ServingCellConfig_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* codeBlockGroupTransmission */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* xOverhead */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* nrofHARQ-ProcessesForPDSCH */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* pucch-Cell */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* ext1 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* ext2 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 } /* ext3 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_PDSCH_ServingCellConfig_specs_1 = {
	sizeof(struct NR_PDSCH_ServingCellConfig),
	offsetof(struct NR_PDSCH_ServingCellConfig, _asn_ctx),
	asn_MAP_NR_PDSCH_ServingCellConfig_tag2el_1,
	7,	/* Count of tags in the map */
	asn_MAP_NR_PDSCH_ServingCellConfig_oms_1,	/* Optional members */
	4, 3,	/* Root/Additions */
	4,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_PDSCH_ServingCellConfig = {
	"PDSCH-ServingCellConfig",
	"PDSCH-ServingCellConfig",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_PDSCH_ServingCellConfig_tags_1,
	sizeof(asn_DEF_NR_PDSCH_ServingCellConfig_tags_1)
		/sizeof(asn_DEF_NR_PDSCH_ServingCellConfig_tags_1[0]), /* 1 */
	asn_DEF_NR_PDSCH_ServingCellConfig_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_PDSCH_ServingCellConfig_tags_1)
		/sizeof(asn_DEF_NR_PDSCH_ServingCellConfig_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_PDSCH_ServingCellConfig_1,
	7,	/* Elements count */
	&asn_SPC_NR_PDSCH_ServingCellConfig_specs_1	/* Additional specs */
};

