/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_LocationMeasurementIndication_H_
#define	_NR_LocationMeasurementIndication_H_


#include <asn_application.h>

/* Including external dependencies */
#include <constr_SEQUENCE.h>
#include <constr_CHOICE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_LocationMeasurementIndication__criticalExtensions_PR {
	NR_LocationMeasurementIndication__criticalExtensions_PR_NOTHING,	/* No components present */
	NR_LocationMeasurementIndication__criticalExtensions_PR_locationMeasurementIndication,
	NR_LocationMeasurementIndication__criticalExtensions_PR_criticalExtensionsFuture
} NR_LocationMeasurementIndication__criticalExtensions_PR;

/* Forward declarations */
struct NR_LocationMeasurementIndication_IEs;

/* NR_LocationMeasurementIndication */
typedef struct NR_LocationMeasurementIndication {
	struct NR_LocationMeasurementIndication__criticalExtensions {
		NR_LocationMeasurementIndication__criticalExtensions_PR present;
		union NR_LocationMeasurementIndication__NR_criticalExtensions_u {
			struct NR_LocationMeasurementIndication_IEs	*locationMeasurementIndication;
			struct NR_LocationMeasurementIndication__criticalExtensions__criticalExtensionsFuture {
				
				/* Context for parsing across buffer boundaries */
				asn_struct_ctx_t _asn_ctx;
			} *criticalExtensionsFuture;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} criticalExtensions;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_LocationMeasurementIndication_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_NR_LocationMeasurementIndication;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_LocationMeasurementIndication_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_LocationMeasurementIndication_1[1];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_LocationMeasurementIndication-IEs.h"

#endif	/* _NR_LocationMeasurementIndication_H_ */
#include <asn_internal.h>
