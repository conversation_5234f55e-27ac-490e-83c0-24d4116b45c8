/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_RRCReestablishmentRequest_IEs_H_
#define	_NR_RRCReestablishmentRequest_IEs_H_


#include <asn_application.h>

/* Including external dependencies */
#include "NR_ReestabUE-Identity.h"
#include "NR_ReestablishmentCause.h"
#include <BIT_STRING.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* NR_RRCReestablishmentRequest-IEs */
typedef struct NR_RRCReestablishmentRequest_IEs {
	NR_ReestabUE_Identity_t	 ue_Identity;
	NR_ReestablishmentCause_t	 reestablishmentCause;
	BIT_STRING_t	 spare;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_RRCReestablishmentRequest_IEs_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_NR_RRCReestablishmentRequest_IEs;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_RRCReestablishmentRequest_IEs_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_RRCReestablishmentRequest_IEs_1[3];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_RRCReestablishmentRequest_IEs_H_ */
#include <asn_internal.h>
