/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_LTE-NeighCellsCRS-AssistInfo-r17.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_neighCarrierFreqDL_r17_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 16383L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_neighCarrierBandwidthDL_r17_constr_2 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  7 }	/* (0..7) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_neighCRS_muting_r17_constr_13 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_neighNrofCRS_Ports_r17_constr_16 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_neighV_Shift_r17_constr_20 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  5 }	/* (0..5) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_neighCarrierFreqDL_r17_constr_11 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 14,  14,  0,  16383 }	/* (0..16383) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_neighCarrierBandwidthDL_r17_value2enum_2[] = {
	{ 0,	2,	"n6" },
	{ 1,	3,	"n15" },
	{ 2,	3,	"n25" },
	{ 3,	3,	"n50" },
	{ 4,	3,	"n75" },
	{ 5,	4,	"n100" },
	{ 6,	6,	"spare2" },
	{ 7,	6,	"spare1" }
};
static const unsigned int asn_MAP_NR_neighCarrierBandwidthDL_r17_enum2value_2[] = {
	5,	/* n100(5) */
	1,	/* n15(1) */
	2,	/* n25(2) */
	3,	/* n50(3) */
	0,	/* n6(0) */
	4,	/* n75(4) */
	7,	/* spare1(7) */
	6	/* spare2(6) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_neighCarrierBandwidthDL_r17_specs_2 = {
	asn_MAP_NR_neighCarrierBandwidthDL_r17_value2enum_2,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_neighCarrierBandwidthDL_r17_enum2value_2,	/* N => "tag"; sorted by N */
	8,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_neighCarrierBandwidthDL_r17_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_neighCarrierBandwidthDL_r17_2 = {
	"neighCarrierBandwidthDL-r17",
	"neighCarrierBandwidthDL-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_neighCarrierBandwidthDL_r17_tags_2,
	sizeof(asn_DEF_NR_neighCarrierBandwidthDL_r17_tags_2)
		/sizeof(asn_DEF_NR_neighCarrierBandwidthDL_r17_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_neighCarrierBandwidthDL_r17_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_neighCarrierBandwidthDL_r17_tags_2)
		/sizeof(asn_DEF_NR_neighCarrierBandwidthDL_r17_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_neighCarrierBandwidthDL_r17_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_neighCarrierBandwidthDL_r17_specs_2	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_neighCRS_muting_r17_value2enum_13[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_neighCRS_muting_r17_enum2value_13[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_neighCRS_muting_r17_specs_13 = {
	asn_MAP_NR_neighCRS_muting_r17_value2enum_13,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_neighCRS_muting_r17_enum2value_13,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_neighCRS_muting_r17_tags_13[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_neighCRS_muting_r17_13 = {
	"neighCRS-muting-r17",
	"neighCRS-muting-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_neighCRS_muting_r17_tags_13,
	sizeof(asn_DEF_NR_neighCRS_muting_r17_tags_13)
		/sizeof(asn_DEF_NR_neighCRS_muting_r17_tags_13[0]) - 1, /* 1 */
	asn_DEF_NR_neighCRS_muting_r17_tags_13,	/* Same as above */
	sizeof(asn_DEF_NR_neighCRS_muting_r17_tags_13)
		/sizeof(asn_DEF_NR_neighCRS_muting_r17_tags_13[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_neighCRS_muting_r17_constr_13,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_neighCRS_muting_r17_specs_13	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_neighNrofCRS_Ports_r17_value2enum_16[] = {
	{ 0,	2,	"n1" },
	{ 1,	2,	"n2" },
	{ 2,	2,	"n4" }
};
static const unsigned int asn_MAP_NR_neighNrofCRS_Ports_r17_enum2value_16[] = {
	0,	/* n1(0) */
	1,	/* n2(1) */
	2	/* n4(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_neighNrofCRS_Ports_r17_specs_16 = {
	asn_MAP_NR_neighNrofCRS_Ports_r17_value2enum_16,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_neighNrofCRS_Ports_r17_enum2value_16,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_neighNrofCRS_Ports_r17_tags_16[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_neighNrofCRS_Ports_r17_16 = {
	"neighNrofCRS-Ports-r17",
	"neighNrofCRS-Ports-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_neighNrofCRS_Ports_r17_tags_16,
	sizeof(asn_DEF_NR_neighNrofCRS_Ports_r17_tags_16)
		/sizeof(asn_DEF_NR_neighNrofCRS_Ports_r17_tags_16[0]) - 1, /* 1 */
	asn_DEF_NR_neighNrofCRS_Ports_r17_tags_16,	/* Same as above */
	sizeof(asn_DEF_NR_neighNrofCRS_Ports_r17_tags_16)
		/sizeof(asn_DEF_NR_neighNrofCRS_Ports_r17_tags_16[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_neighNrofCRS_Ports_r17_constr_16,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_neighNrofCRS_Ports_r17_specs_16	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_neighV_Shift_r17_value2enum_20[] = {
	{ 0,	2,	"n0" },
	{ 1,	2,	"n1" },
	{ 2,	2,	"n2" },
	{ 3,	2,	"n3" },
	{ 4,	2,	"n4" },
	{ 5,	2,	"n5" }
};
static const unsigned int asn_MAP_NR_neighV_Shift_r17_enum2value_20[] = {
	0,	/* n0(0) */
	1,	/* n1(1) */
	2,	/* n2(2) */
	3,	/* n3(3) */
	4,	/* n4(4) */
	5	/* n5(5) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_neighV_Shift_r17_specs_20 = {
	asn_MAP_NR_neighV_Shift_r17_value2enum_20,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_neighV_Shift_r17_enum2value_20,	/* N => "tag"; sorted by N */
	6,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_neighV_Shift_r17_tags_20[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_neighV_Shift_r17_20 = {
	"neighV-Shift-r17",
	"neighV-Shift-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_neighV_Shift_r17_tags_20,
	sizeof(asn_DEF_NR_neighV_Shift_r17_tags_20)
		/sizeof(asn_DEF_NR_neighV_Shift_r17_tags_20[0]) - 1, /* 1 */
	asn_DEF_NR_neighV_Shift_r17_tags_20,	/* Same as above */
	sizeof(asn_DEF_NR_neighV_Shift_r17_tags_20)
		/sizeof(asn_DEF_NR_neighV_Shift_r17_tags_20[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_neighV_Shift_r17_constr_20,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_neighV_Shift_r17_specs_20	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_LTE_NeighCellsCRS_AssistInfo_r17_1[] = {
	{ ATF_POINTER, 7, offsetof(struct NR_LTE_NeighCellsCRS_AssistInfo_r17, neighCarrierBandwidthDL_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_neighCarrierBandwidthDL_r17_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"neighCarrierBandwidthDL-r17"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_LTE_NeighCellsCRS_AssistInfo_r17, neighCarrierFreqDL_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_neighCarrierFreqDL_r17_constr_11,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_neighCarrierFreqDL_r17_constraint_1
		},
		0, 0, /* No default value */
		"neighCarrierFreqDL-r17"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_LTE_NeighCellsCRS_AssistInfo_r17, neighCellId_r17),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_EUTRA_PhysCellId,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"neighCellId-r17"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_LTE_NeighCellsCRS_AssistInfo_r17, neighCRS_muting_r17),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_neighCRS_muting_r17_13,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"neighCRS-muting-r17"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_LTE_NeighCellsCRS_AssistInfo_r17, neighMBSFN_SubframeConfigList_r17),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_EUTRA_MBSFN_SubframeConfigList,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"neighMBSFN-SubframeConfigList-r17"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_LTE_NeighCellsCRS_AssistInfo_r17, neighNrofCRS_Ports_r17),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_neighNrofCRS_Ports_r17_16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"neighNrofCRS-Ports-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_LTE_NeighCellsCRS_AssistInfo_r17, neighV_Shift_r17),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_neighV_Shift_r17_20,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"neighV-Shift-r17"
		},
};
static const int asn_MAP_NR_LTE_NeighCellsCRS_AssistInfo_r17_oms_1[] = { 0, 1, 2, 3, 4, 5, 6 };
static const ber_tlv_tag_t asn_DEF_NR_LTE_NeighCellsCRS_AssistInfo_r17_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_LTE_NeighCellsCRS_AssistInfo_r17_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* neighCarrierBandwidthDL-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* neighCarrierFreqDL-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* neighCellId-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* neighCRS-muting-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* neighMBSFN-SubframeConfigList-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* neighNrofCRS-Ports-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 } /* neighV-Shift-r17 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_LTE_NeighCellsCRS_AssistInfo_r17_specs_1 = {
	sizeof(struct NR_LTE_NeighCellsCRS_AssistInfo_r17),
	offsetof(struct NR_LTE_NeighCellsCRS_AssistInfo_r17, _asn_ctx),
	asn_MAP_NR_LTE_NeighCellsCRS_AssistInfo_r17_tag2el_1,
	7,	/* Count of tags in the map */
	asn_MAP_NR_LTE_NeighCellsCRS_AssistInfo_r17_oms_1,	/* Optional members */
	7, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_LTE_NeighCellsCRS_AssistInfo_r17 = {
	"LTE-NeighCellsCRS-AssistInfo-r17",
	"LTE-NeighCellsCRS-AssistInfo-r17",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_LTE_NeighCellsCRS_AssistInfo_r17_tags_1,
	sizeof(asn_DEF_NR_LTE_NeighCellsCRS_AssistInfo_r17_tags_1)
		/sizeof(asn_DEF_NR_LTE_NeighCellsCRS_AssistInfo_r17_tags_1[0]), /* 1 */
	asn_DEF_NR_LTE_NeighCellsCRS_AssistInfo_r17_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_LTE_NeighCellsCRS_AssistInfo_r17_tags_1)
		/sizeof(asn_DEF_NR_LTE_NeighCellsCRS_AssistInfo_r17_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_LTE_NeighCellsCRS_AssistInfo_r17_1,
	7,	/* Elements count */
	&asn_SPC_NR_LTE_NeighCellsCRS_AssistInfo_r17_specs_1	/* Additional specs */
};

