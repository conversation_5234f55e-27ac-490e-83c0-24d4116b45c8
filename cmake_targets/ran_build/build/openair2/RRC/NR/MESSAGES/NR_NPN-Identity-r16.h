/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_NPN_Identity_r16_H_
#define	_NR_NPN_Identity_r16_H_


#include <asn_application.h>

/* Including external dependencies */
#include "NR_PLMN-Identity.h"
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include <constr_SEQUENCE.h>
#include "NR_NID-r16.h"
#include <constr_CHOICE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_NPN_Identity_r16_PR {
	NR_NPN_Identity_r16_PR_NOTHING,	/* No components present */
	NR_NPN_Identity_r16_PR_pni_npn_r16,
	NR_NPN_Identity_r16_PR_snpn_r16
} NR_NPN_Identity_r16_PR;

/* Forward declarations */
struct NR_CAG_IdentityInfo_r16;

/* NR_NPN-Identity-r16 */
typedef struct NR_NPN_Identity_r16 {
	NR_NPN_Identity_r16_PR present;
	union NR_NPN_Identity_r16_u {
		struct NR_NPN_Identity_r16__pni_npn_r16 {
			NR_PLMN_Identity_t	 plmn_Identity_r16;
			struct NR_NPN_Identity_r16__pni_npn_r16__cag_IdentityList_r16 {
				A_SEQUENCE_OF(struct NR_CAG_IdentityInfo_r16) list;
				
				/* Context for parsing across buffer boundaries */
				asn_struct_ctx_t _asn_ctx;
			} cag_IdentityList_r16;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *pni_npn_r16;
		struct NR_NPN_Identity_r16__snpn_r16 {
			NR_PLMN_Identity_t	 plmn_Identity_r16;
			struct NR_NPN_Identity_r16__snpn_r16__nid_List_r16 {
				A_SEQUENCE_OF(NR_NID_r16_t) list;
				
				/* Context for parsing across buffer boundaries */
				asn_struct_ctx_t _asn_ctx;
			} nid_List_r16;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *snpn_r16;
	} choice;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_NPN_Identity_r16_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_NR_NPN_Identity_r16;
extern asn_CHOICE_specifics_t asn_SPC_NR_NPN_Identity_r16_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_NPN_Identity_r16_1[2];
extern asn_per_constraints_t asn_PER_type_NR_NPN_Identity_r16_constr_1;

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_CAG-IdentityInfo-r16.h"

#endif	/* _NR_NPN_Identity_r16_H_ */
#include <asn_internal.h>
