/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_MAC_ParametersXDD_Diff_H_
#define	_NR_MAC_ParametersXDD_Diff_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_MAC_ParametersXDD_Diff__skipUplinkTxDynamic {
	NR_MAC_ParametersXDD_Diff__skipUplinkTxDynamic_supported	= 0
} e_NR_MAC_ParametersXDD_Diff__skipUplinkTxDynamic;
typedef enum NR_MAC_ParametersXDD_Diff__logicalChannelSR_DelayTimer {
	NR_MAC_ParametersXDD_Diff__logicalChannelSR_DelayTimer_supported	= 0
} e_NR_MAC_ParametersXDD_Diff__logicalChannelSR_DelayTimer;
typedef enum NR_MAC_ParametersXDD_Diff__longDRX_Cycle {
	NR_MAC_ParametersXDD_Diff__longDRX_Cycle_supported	= 0
} e_NR_MAC_ParametersXDD_Diff__longDRX_Cycle;
typedef enum NR_MAC_ParametersXDD_Diff__shortDRX_Cycle {
	NR_MAC_ParametersXDD_Diff__shortDRX_Cycle_supported	= 0
} e_NR_MAC_ParametersXDD_Diff__shortDRX_Cycle;
typedef enum NR_MAC_ParametersXDD_Diff__multipleSR_Configurations {
	NR_MAC_ParametersXDD_Diff__multipleSR_Configurations_supported	= 0
} e_NR_MAC_ParametersXDD_Diff__multipleSR_Configurations;
typedef enum NR_MAC_ParametersXDD_Diff__multipleConfiguredGrants {
	NR_MAC_ParametersXDD_Diff__multipleConfiguredGrants_supported	= 0
} e_NR_MAC_ParametersXDD_Diff__multipleConfiguredGrants;
typedef enum NR_MAC_ParametersXDD_Diff__ext1__secondaryDRX_Group_r16 {
	NR_MAC_ParametersXDD_Diff__ext1__secondaryDRX_Group_r16_supported	= 0
} e_NR_MAC_ParametersXDD_Diff__ext1__secondaryDRX_Group_r16;
typedef enum NR_MAC_ParametersXDD_Diff__ext2__enhancedSkipUplinkTxDynamic_r16 {
	NR_MAC_ParametersXDD_Diff__ext2__enhancedSkipUplinkTxDynamic_r16_supported	= 0
} e_NR_MAC_ParametersXDD_Diff__ext2__enhancedSkipUplinkTxDynamic_r16;
typedef enum NR_MAC_ParametersXDD_Diff__ext2__enhancedSkipUplinkTxConfigured_r16 {
	NR_MAC_ParametersXDD_Diff__ext2__enhancedSkipUplinkTxConfigured_r16_supported	= 0
} e_NR_MAC_ParametersXDD_Diff__ext2__enhancedSkipUplinkTxConfigured_r16;

/* NR_MAC-ParametersXDD-Diff */
typedef struct NR_MAC_ParametersXDD_Diff {
	long	*skipUplinkTxDynamic;	/* OPTIONAL */
	long	*logicalChannelSR_DelayTimer;	/* OPTIONAL */
	long	*longDRX_Cycle;	/* OPTIONAL */
	long	*shortDRX_Cycle;	/* OPTIONAL */
	long	*multipleSR_Configurations;	/* OPTIONAL */
	long	*multipleConfiguredGrants;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	struct NR_MAC_ParametersXDD_Diff__ext1 {
		long	*secondaryDRX_Group_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext1;
	struct NR_MAC_ParametersXDD_Diff__ext2 {
		long	*enhancedSkipUplinkTxDynamic_r16;	/* OPTIONAL */
		long	*enhancedSkipUplinkTxConfigured_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext2;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_MAC_ParametersXDD_Diff_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_skipUplinkTxDynamic_2;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_logicalChannelSR_DelayTimer_4;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_longDRX_Cycle_6;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_shortDRX_Cycle_8;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_multipleSR_Configurations_10;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_multipleConfiguredGrants_12;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_secondaryDRX_Group_r16_16;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_enhancedSkipUplinkTxDynamic_r16_19;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_enhancedSkipUplinkTxConfigured_r16_21;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_MAC_ParametersXDD_Diff;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_MAC_ParametersXDD_Diff_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_MAC_ParametersXDD_Diff_1[8];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_MAC_ParametersXDD_Diff_H_ */
#include <asn_internal.h>
