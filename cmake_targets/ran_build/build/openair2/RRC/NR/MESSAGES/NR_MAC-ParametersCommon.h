/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_MAC_ParametersCommon_H_
#define	_NR_MAC_ParametersCommon_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_MAC_ParametersCommon__lcp_Restriction {
	NR_MAC_ParametersCommon__lcp_Restriction_supported	= 0
} e_NR_MAC_ParametersCommon__lcp_Restriction;
typedef enum NR_MAC_ParametersCommon__dummy {
	NR_MAC_ParametersCommon__dummy_supported	= 0
} e_NR_MAC_ParametersCommon__dummy;
typedef enum NR_MAC_ParametersCommon__lch_ToSCellRestriction {
	NR_MAC_ParametersCommon__lch_ToSCellRestriction_supported	= 0
} e_NR_MAC_ParametersCommon__lch_ToSCellRestriction;
typedef enum NR_MAC_ParametersCommon__ext1__recommendedBitRate {
	NR_MAC_ParametersCommon__ext1__recommendedBitRate_supported	= 0
} e_NR_MAC_ParametersCommon__ext1__recommendedBitRate;
typedef enum NR_MAC_ParametersCommon__ext1__recommendedBitRateQuery {
	NR_MAC_ParametersCommon__ext1__recommendedBitRateQuery_supported	= 0
} e_NR_MAC_ParametersCommon__ext1__recommendedBitRateQuery;
typedef enum NR_MAC_ParametersCommon__ext2__recommendedBitRateMultiplier_r16 {
	NR_MAC_ParametersCommon__ext2__recommendedBitRateMultiplier_r16_supported	= 0
} e_NR_MAC_ParametersCommon__ext2__recommendedBitRateMultiplier_r16;
typedef enum NR_MAC_ParametersCommon__ext2__preEmptiveBSR_r16 {
	NR_MAC_ParametersCommon__ext2__preEmptiveBSR_r16_supported	= 0
} e_NR_MAC_ParametersCommon__ext2__preEmptiveBSR_r16;
typedef enum NR_MAC_ParametersCommon__ext2__autonomousTransmission_r16 {
	NR_MAC_ParametersCommon__ext2__autonomousTransmission_r16_supported	= 0
} e_NR_MAC_ParametersCommon__ext2__autonomousTransmission_r16;
typedef enum NR_MAC_ParametersCommon__ext2__lch_PriorityBasedPrioritization_r16 {
	NR_MAC_ParametersCommon__ext2__lch_PriorityBasedPrioritization_r16_supported	= 0
} e_NR_MAC_ParametersCommon__ext2__lch_PriorityBasedPrioritization_r16;
typedef enum NR_MAC_ParametersCommon__ext2__lch_ToConfiguredGrantMapping_r16 {
	NR_MAC_ParametersCommon__ext2__lch_ToConfiguredGrantMapping_r16_supported	= 0
} e_NR_MAC_ParametersCommon__ext2__lch_ToConfiguredGrantMapping_r16;
typedef enum NR_MAC_ParametersCommon__ext2__lch_ToGrantPriorityRestriction_r16 {
	NR_MAC_ParametersCommon__ext2__lch_ToGrantPriorityRestriction_r16_supported	= 0
} e_NR_MAC_ParametersCommon__ext2__lch_ToGrantPriorityRestriction_r16;
typedef enum NR_MAC_ParametersCommon__ext2__singlePHR_P_r16 {
	NR_MAC_ParametersCommon__ext2__singlePHR_P_r16_supported	= 0
} e_NR_MAC_ParametersCommon__ext2__singlePHR_P_r16;
typedef enum NR_MAC_ParametersCommon__ext2__ul_LBT_FailureDetectionRecovery_r16 {
	NR_MAC_ParametersCommon__ext2__ul_LBT_FailureDetectionRecovery_r16_supported	= 0
} e_NR_MAC_ParametersCommon__ext2__ul_LBT_FailureDetectionRecovery_r16;
typedef enum NR_MAC_ParametersCommon__ext2__tdd_MPE_P_MPR_Reporting_r16 {
	NR_MAC_ParametersCommon__ext2__tdd_MPE_P_MPR_Reporting_r16_supported	= 0
} e_NR_MAC_ParametersCommon__ext2__tdd_MPE_P_MPR_Reporting_r16;
typedef enum NR_MAC_ParametersCommon__ext2__lcid_ExtensionIAB_r16 {
	NR_MAC_ParametersCommon__ext2__lcid_ExtensionIAB_r16_supported	= 0
} e_NR_MAC_ParametersCommon__ext2__lcid_ExtensionIAB_r16;
typedef enum NR_MAC_ParametersCommon__ext3__spCell_BFR_CBRA_r16 {
	NR_MAC_ParametersCommon__ext3__spCell_BFR_CBRA_r16_supported	= 0
} e_NR_MAC_ParametersCommon__ext3__spCell_BFR_CBRA_r16;
typedef enum NR_MAC_ParametersCommon__ext4__srs_ResourceId_Ext_r16 {
	NR_MAC_ParametersCommon__ext4__srs_ResourceId_Ext_r16_supported	= 0
} e_NR_MAC_ParametersCommon__ext4__srs_ResourceId_Ext_r16;
typedef enum NR_MAC_ParametersCommon__ext5__enhancedUuDRX_forSidelink_r17 {
	NR_MAC_ParametersCommon__ext5__enhancedUuDRX_forSidelink_r17_supported	= 0
} e_NR_MAC_ParametersCommon__ext5__enhancedUuDRX_forSidelink_r17;
typedef enum NR_MAC_ParametersCommon__ext5__mg_ActivationRequestPRS_Meas_r17 {
	NR_MAC_ParametersCommon__ext5__mg_ActivationRequestPRS_Meas_r17_supported	= 0
} e_NR_MAC_ParametersCommon__ext5__mg_ActivationRequestPRS_Meas_r17;
typedef enum NR_MAC_ParametersCommon__ext5__mg_ActivationCommPRS_Meas_r17 {
	NR_MAC_ParametersCommon__ext5__mg_ActivationCommPRS_Meas_r17_supported	= 0
} e_NR_MAC_ParametersCommon__ext5__mg_ActivationCommPRS_Meas_r17;
typedef enum NR_MAC_ParametersCommon__ext5__intraCG_Prioritization_r17 {
	NR_MAC_ParametersCommon__ext5__intraCG_Prioritization_r17_supported	= 0
} e_NR_MAC_ParametersCommon__ext5__intraCG_Prioritization_r17;
typedef enum NR_MAC_ParametersCommon__ext5__jointPrioritizationCG_Retx_Timer_r17 {
	NR_MAC_ParametersCommon__ext5__jointPrioritizationCG_Retx_Timer_r17_supported	= 0
} e_NR_MAC_ParametersCommon__ext5__jointPrioritizationCG_Retx_Timer_r17;
typedef enum NR_MAC_ParametersCommon__ext5__survivalTime_r17 {
	NR_MAC_ParametersCommon__ext5__survivalTime_r17_supported	= 0
} e_NR_MAC_ParametersCommon__ext5__survivalTime_r17;
typedef enum NR_MAC_ParametersCommon__ext5__lcg_ExtensionIAB_r17 {
	NR_MAC_ParametersCommon__ext5__lcg_ExtensionIAB_r17_supported	= 0
} e_NR_MAC_ParametersCommon__ext5__lcg_ExtensionIAB_r17;
typedef enum NR_MAC_ParametersCommon__ext5__harq_FeedbackDisabled_r17 {
	NR_MAC_ParametersCommon__ext5__harq_FeedbackDisabled_r17_supported	= 0
} e_NR_MAC_ParametersCommon__ext5__harq_FeedbackDisabled_r17;
typedef enum NR_MAC_ParametersCommon__ext5__uplink_Harq_ModeB_r17 {
	NR_MAC_ParametersCommon__ext5__uplink_Harq_ModeB_r17_supported	= 0
} e_NR_MAC_ParametersCommon__ext5__uplink_Harq_ModeB_r17;
typedef enum NR_MAC_ParametersCommon__ext5__sr_TriggeredBy_TA_Report_r17 {
	NR_MAC_ParametersCommon__ext5__sr_TriggeredBy_TA_Report_r17_supported	= 0
} e_NR_MAC_ParametersCommon__ext5__sr_TriggeredBy_TA_Report_r17;
typedef enum NR_MAC_ParametersCommon__ext5__extendedDRX_CycleInactive_r17 {
	NR_MAC_ParametersCommon__ext5__extendedDRX_CycleInactive_r17_supported	= 0
} e_NR_MAC_ParametersCommon__ext5__extendedDRX_CycleInactive_r17;
typedef enum NR_MAC_ParametersCommon__ext5__simultaneousSR_PUSCH_DiffPUCCH_groups_r17 {
	NR_MAC_ParametersCommon__ext5__simultaneousSR_PUSCH_DiffPUCCH_groups_r17_supported	= 0
} e_NR_MAC_ParametersCommon__ext5__simultaneousSR_PUSCH_DiffPUCCH_groups_r17;
typedef enum NR_MAC_ParametersCommon__ext5__lastTransmissionUL_r17 {
	NR_MAC_ParametersCommon__ext5__lastTransmissionUL_r17_supported	= 0
} e_NR_MAC_ParametersCommon__ext5__lastTransmissionUL_r17;

/* NR_MAC-ParametersCommon */
typedef struct NR_MAC_ParametersCommon {
	long	*lcp_Restriction;	/* OPTIONAL */
	long	*dummy;	/* OPTIONAL */
	long	*lch_ToSCellRestriction;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	struct NR_MAC_ParametersCommon__ext1 {
		long	*recommendedBitRate;	/* OPTIONAL */
		long	*recommendedBitRateQuery;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext1;
	struct NR_MAC_ParametersCommon__ext2 {
		long	*recommendedBitRateMultiplier_r16;	/* OPTIONAL */
		long	*preEmptiveBSR_r16;	/* OPTIONAL */
		long	*autonomousTransmission_r16;	/* OPTIONAL */
		long	*lch_PriorityBasedPrioritization_r16;	/* OPTIONAL */
		long	*lch_ToConfiguredGrantMapping_r16;	/* OPTIONAL */
		long	*lch_ToGrantPriorityRestriction_r16;	/* OPTIONAL */
		long	*singlePHR_P_r16;	/* OPTIONAL */
		long	*ul_LBT_FailureDetectionRecovery_r16;	/* OPTIONAL */
		long	*tdd_MPE_P_MPR_Reporting_r16;	/* OPTIONAL */
		long	*lcid_ExtensionIAB_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext2;
	struct NR_MAC_ParametersCommon__ext3 {
		long	*spCell_BFR_CBRA_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext3;
	struct NR_MAC_ParametersCommon__ext4 {
		long	*srs_ResourceId_Ext_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext4;
	struct NR_MAC_ParametersCommon__ext5 {
		long	*enhancedUuDRX_forSidelink_r17;	/* OPTIONAL */
		long	*mg_ActivationRequestPRS_Meas_r17;	/* OPTIONAL */
		long	*mg_ActivationCommPRS_Meas_r17;	/* OPTIONAL */
		long	*intraCG_Prioritization_r17;	/* OPTIONAL */
		long	*jointPrioritizationCG_Retx_Timer_r17;	/* OPTIONAL */
		long	*survivalTime_r17;	/* OPTIONAL */
		long	*lcg_ExtensionIAB_r17;	/* OPTIONAL */
		long	*harq_FeedbackDisabled_r17;	/* OPTIONAL */
		long	*uplink_Harq_ModeB_r17;	/* OPTIONAL */
		long	*sr_TriggeredBy_TA_Report_r17;	/* OPTIONAL */
		long	*extendedDRX_CycleInactive_r17;	/* OPTIONAL */
		long	*simultaneousSR_PUSCH_DiffPUCCH_groups_r17;	/* OPTIONAL */
		long	*lastTransmissionUL_r17;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext5;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_MAC_ParametersCommon_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_lcp_Restriction_2;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dummy_4;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_lch_ToSCellRestriction_6;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_recommendedBitRate_10;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_recommendedBitRateQuery_12;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_recommendedBitRateMultiplier_r16_15;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_preEmptiveBSR_r16_17;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_autonomousTransmission_r16_19;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_lch_PriorityBasedPrioritization_r16_21;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_lch_ToConfiguredGrantMapping_r16_23;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_lch_ToGrantPriorityRestriction_r16_25;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_singlePHR_P_r16_27;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_ul_LBT_FailureDetectionRecovery_r16_29;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_tdd_MPE_P_MPR_Reporting_r16_31;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_lcid_ExtensionIAB_r16_33;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_spCell_BFR_CBRA_r16_36;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_srs_ResourceId_Ext_r16_39;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_enhancedUuDRX_forSidelink_r17_42;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mg_ActivationRequestPRS_Meas_r17_44;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mg_ActivationCommPRS_Meas_r17_46;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_intraCG_Prioritization_r17_48;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_jointPrioritizationCG_Retx_Timer_r17_50;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_survivalTime_r17_52;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_lcg_ExtensionIAB_r17_54;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_harq_FeedbackDisabled_r17_56;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_uplink_Harq_ModeB_r17_58;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_sr_TriggeredBy_TA_Report_r17_60;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_extendedDRX_CycleInactive_r17_62;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_simultaneousSR_PUSCH_DiffPUCCH_groups_r17_64;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_lastTransmissionUL_r17_66;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_MAC_ParametersCommon;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_MAC_ParametersCommon_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_MAC_ParametersCommon_1[8];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_MAC_ParametersCommon_H_ */
#include <asn_internal.h>
