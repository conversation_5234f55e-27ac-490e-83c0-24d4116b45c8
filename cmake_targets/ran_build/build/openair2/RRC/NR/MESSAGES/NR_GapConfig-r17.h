/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_GapConfig_r17_H_
#define	_NR_GapConfig_r17_H_


#include <asn_application.h>

/* Including external dependencies */
#include "NR_MeasGapId-r17.h"
#include <NativeEnumerated.h>
#include <NativeInteger.h>
#include "NR_ServCellIndex.h"
#include "NR_MeasGapSharingScheme.h"
#include "NR_GapPriority-r17.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_GapConfig_r17__gapType_r17 {
	NR_GapConfig_r17__gapType_r17_perUE	= 0,
	NR_GapConfig_r17__gapType_r17_perFR1	= 1,
	NR_GapConfig_r17__gapType_r17_perFR2	= 2
} e_NR_GapConfig_r17__gapType_r17;
typedef enum NR_GapConfig_r17__mgl_r17 {
	NR_GapConfig_r17__mgl_r17_ms1	= 0,
	NR_GapConfig_r17__mgl_r17_ms1dot5	= 1,
	NR_GapConfig_r17__mgl_r17_ms2	= 2,
	NR_GapConfig_r17__mgl_r17_ms3	= 3,
	NR_GapConfig_r17__mgl_r17_ms3dot5	= 4,
	NR_GapConfig_r17__mgl_r17_ms4	= 5,
	NR_GapConfig_r17__mgl_r17_ms5	= 6,
	NR_GapConfig_r17__mgl_r17_ms5dot5	= 7,
	NR_GapConfig_r17__mgl_r17_ms6	= 8,
	NR_GapConfig_r17__mgl_r17_ms10	= 9,
	NR_GapConfig_r17__mgl_r17_ms20	= 10
} e_NR_GapConfig_r17__mgl_r17;
typedef enum NR_GapConfig_r17__mgrp_r17 {
	NR_GapConfig_r17__mgrp_r17_ms20	= 0,
	NR_GapConfig_r17__mgrp_r17_ms40	= 1,
	NR_GapConfig_r17__mgrp_r17_ms80	= 2,
	NR_GapConfig_r17__mgrp_r17_ms160	= 3
} e_NR_GapConfig_r17__mgrp_r17;
typedef enum NR_GapConfig_r17__mgta_r17 {
	NR_GapConfig_r17__mgta_r17_ms0	= 0,
	NR_GapConfig_r17__mgta_r17_ms0dot25	= 1,
	NR_GapConfig_r17__mgta_r17_ms0dot5	= 2,
	NR_GapConfig_r17__mgta_r17_ms0dot75	= 3
} e_NR_GapConfig_r17__mgta_r17;
typedef enum NR_GapConfig_r17__refServCellIndicator_r17 {
	NR_GapConfig_r17__refServCellIndicator_r17_pCell	= 0,
	NR_GapConfig_r17__refServCellIndicator_r17_pSCell	= 1,
	NR_GapConfig_r17__refServCellIndicator_r17_mcg_FR2	= 2
} e_NR_GapConfig_r17__refServCellIndicator_r17;
typedef enum NR_GapConfig_r17__preConfigInd_r17 {
	NR_GapConfig_r17__preConfigInd_r17_true	= 0
} e_NR_GapConfig_r17__preConfigInd_r17;
typedef enum NR_GapConfig_r17__ncsgInd_r17 {
	NR_GapConfig_r17__ncsgInd_r17_true	= 0
} e_NR_GapConfig_r17__ncsgInd_r17;
typedef enum NR_GapConfig_r17__gapAssociationPRS_r17 {
	NR_GapConfig_r17__gapAssociationPRS_r17_true	= 0
} e_NR_GapConfig_r17__gapAssociationPRS_r17;

/* NR_GapConfig-r17 */
typedef struct NR_GapConfig_r17 {
	NR_MeasGapId_r17_t	 measGapId_r17;
	long	 gapType_r17;
	long	 gapOffset_r17;
	long	 mgl_r17;
	long	 mgrp_r17;
	long	 mgta_r17;
	long	*refServCellIndicator_r17;	/* OPTIONAL */
	NR_ServCellIndex_t	*refFR2_ServCellAsyncCA_r17;	/* OPTIONAL */
	long	*preConfigInd_r17;	/* OPTIONAL */
	long	*ncsgInd_r17;	/* OPTIONAL */
	long	*gapAssociationPRS_r17;	/* OPTIONAL */
	NR_MeasGapSharingScheme_t	*gapSharing_r17;	/* OPTIONAL */
	NR_GapPriority_r17_t	*gapPriority_r17;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_GapConfig_r17_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_gapType_r17_3;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mgl_r17_8;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mgrp_r17_20;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mgta_r17_25;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_refServCellIndicator_r17_30;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_preConfigInd_r17_35;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_ncsgInd_r17_37;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_gapAssociationPRS_r17_39;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_GapConfig_r17;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_GapConfig_r17_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_GapConfig_r17_1[13];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_GapConfig_r17_H_ */
#include <asn_internal.h>
