/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_MeasResults_H_
#define	_NR_MeasResults_H_


#include <asn_application.h>

/* Including external dependencies */
#include "NR_MeasId.h"
#include "NR_MeasResultServMOList.h"
#include <OCTET_STRING.h>
#include <constr_CHOICE.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_MeasResults__measResultNeighCells_PR {
	NR_MeasResults__measResultNeighCells_PR_NOTHING,	/* No components present */
	NR_MeasResults__measResultNeighCells_PR_measResultListNR,
	/* Extensions may appear below */
	NR_MeasResults__measResultNeighCells_PR_measResultListEUTRA,
	NR_MeasResults__measResultNeighCells_PR_measResultListUTRA_FDD_r16,
	NR_MeasResults__measResultNeighCells_PR_sl_MeasResultsCandRelay_r17
} NR_MeasResults__measResultNeighCells_PR;

/* Forward declarations */
struct NR_MeasResultListNR;
struct NR_MeasResultListEUTRA;
struct NR_MeasResultListUTRA_FDD_r16;
struct NR_MeasResultServFreqListEUTRA_SCG;
struct NR_MeasResultServFreqListNR_SCG;
struct NR_MeasResultSFTD_EUTRA;
struct NR_MeasResultCellSFTD_NR;
struct NR_MeasResultCellListSFTD_NR;
struct NR_MeasResultForRSSI_r16;
struct NR_LocationInfo_r16;
struct NR_UL_PDCP_DelayValueResultList_r16;
struct NR_MeasResultsSL_r16;
struct NR_MeasResultCLI_r16;
struct NR_MeasResultRxTxTimeDiff_r17;
struct NR_UL_PDCP_ExcessDelayResultList_r17;

/* NR_MeasResults */
typedef struct NR_MeasResults {
	NR_MeasId_t	 measId;
	NR_MeasResultServMOList_t	 measResultServingMOList;
	struct NR_MeasResults__measResultNeighCells {
		NR_MeasResults__measResultNeighCells_PR present;
		union NR_MeasResults__NR_measResultNeighCells_u {
			struct NR_MeasResultListNR	*measResultListNR;
			/*
			 * This type is extensible,
			 * possible extensions are below.
			 */
			struct NR_MeasResultListEUTRA	*measResultListEUTRA;
			struct NR_MeasResultListUTRA_FDD_r16	*measResultListUTRA_FDD_r16;
			OCTET_STRING_t	 sl_MeasResultsCandRelay_r17;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *measResultNeighCells;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	struct NR_MeasResults__ext1 {
		struct NR_MeasResultServFreqListEUTRA_SCG	*measResultServFreqListEUTRA_SCG;	/* OPTIONAL */
		struct NR_MeasResultServFreqListNR_SCG	*measResultServFreqListNR_SCG;	/* OPTIONAL */
		struct NR_MeasResultSFTD_EUTRA	*measResultSFTD_EUTRA;	/* OPTIONAL */
		struct NR_MeasResultCellSFTD_NR	*measResultSFTD_NR;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext1;
	struct NR_MeasResults__ext2 {
		struct NR_MeasResultCellListSFTD_NR	*measResultCellListSFTD_NR;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext2;
	struct NR_MeasResults__ext3 {
		struct NR_MeasResultForRSSI_r16	*measResultForRSSI_r16;	/* OPTIONAL */
		struct NR_LocationInfo_r16	*locationInfo_r16;	/* OPTIONAL */
		struct NR_UL_PDCP_DelayValueResultList_r16	*ul_PDCP_DelayValueResultList_r16;	/* OPTIONAL */
		struct NR_MeasResultsSL_r16	*measResultsSL_r16;	/* OPTIONAL */
		struct NR_MeasResultCLI_r16	*measResultCLI_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext3;
	struct NR_MeasResults__ext4 {
		struct NR_MeasResultRxTxTimeDiff_r17	*measResultRxTxTimeDiff_r17;	/* OPTIONAL */
		OCTET_STRING_t	*sl_MeasResultServingRelay_r17;	/* OPTIONAL */
		struct NR_UL_PDCP_ExcessDelayResultList_r17	*ul_PDCP_ExcessDelayResultList_r17;	/* OPTIONAL */
		OCTET_STRING_t	*coarseLocationInfo_r17;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext4;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_MeasResults_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_NR_MeasResults;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_MeasResults_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_MeasResults_1[7];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_MeasResultListNR.h"
#include "NR_MeasResultListEUTRA.h"
#include "NR_MeasResultListUTRA-FDD-r16.h"
#include "NR_MeasResultServFreqListEUTRA-SCG.h"
#include "NR_MeasResultServFreqListNR-SCG.h"
#include "NR_MeasResultSFTD-EUTRA.h"
#include "NR_MeasResultCellSFTD-NR.h"
#include "NR_MeasResultCellListSFTD-NR.h"
#include "NR_MeasResultForRSSI-r16.h"
#include "NR_LocationInfo-r16.h"
#include "NR_UL-PDCP-DelayValueResultList-r16.h"
#include "NR_MeasResultsSL-r16.h"
#include "NR_MeasResultCLI-r16.h"
#include "NR_MeasResultRxTxTimeDiff-r17.h"
#include "NR_UL-PDCP-ExcessDelayResultList-r17.h"

#endif	/* _NR_MeasResults_H_ */
#include <asn_internal.h>
