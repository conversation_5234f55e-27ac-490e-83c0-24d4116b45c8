/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_RRCReconfiguration_v1560_IEs_H_
#define	_NR_RRCReconfiguration_v1560_IEs_H_


#include <asn_application.h>

/* Including external dependencies */
#include <OCTET_STRING.h>
#include "NR_SK-Counter.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct NR_SetupRelease_MRDC_SecondaryCellGroupConfig;
struct NR_RRCReconfiguration_v1610_IEs;

/* NR_RRCReconfiguration-v1560-IEs */
typedef struct NR_RRCReconfiguration_v1560_IEs {
	struct NR_SetupRelease_MRDC_SecondaryCellGroupConfig	*mrdc_SecondaryCellGroupConfig;	/* OPTIONAL */
	OCTET_STRING_t	*radioBearerConfig2;	/* OPTIONAL */
	NR_SK_Counter_t	*sk_Counter;	/* OPTIONAL */
	struct NR_RRCReconfiguration_v1610_IEs	*nonCriticalExtension;	/* OPTIONAL */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_RRCReconfiguration_v1560_IEs_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_NR_RRCReconfiguration_v1560_IEs;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_RRCReconfiguration_v1560_IEs_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_RRCReconfiguration_v1560_IEs_1[4];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_SetupRelease.h"
#include "NR_RRCReconfiguration-v1610-IEs.h"

#endif	/* _NR_RRCReconfiguration_v1560_IEs_H_ */
#include <asn_internal.h>
