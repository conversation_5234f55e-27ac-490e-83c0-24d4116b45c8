/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_PUCCH_PowerControl_H_
#define	_NR_PUCCH_PowerControl_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include <NativeEnumerated.h>
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_PUCCH_PowerControl__twoPUCCH_PC_AdjustmentStates {
	NR_PUCCH_PowerControl__twoPUCCH_PC_AdjustmentStates_twoStates	= 0
} e_NR_PUCCH_PowerControl__twoPUCCH_PC_AdjustmentStates;

/* Forward declarations */
struct NR_P0_PUCCH;
struct NR_PUCCH_PathlossReferenceRS;
struct NR_SetupRelease_PathlossReferenceRSs_v1610;

/* NR_PUCCH-PowerControl */
typedef struct NR_PUCCH_PowerControl {
	long	*deltaF_PUCCH_f0;	/* OPTIONAL */
	long	*deltaF_PUCCH_f1;	/* OPTIONAL */
	long	*deltaF_PUCCH_f2;	/* OPTIONAL */
	long	*deltaF_PUCCH_f3;	/* OPTIONAL */
	long	*deltaF_PUCCH_f4;	/* OPTIONAL */
	struct NR_PUCCH_PowerControl__p0_Set {
		A_SEQUENCE_OF(struct NR_P0_PUCCH) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *p0_Set;
	struct NR_PUCCH_PowerControl__pathlossReferenceRSs {
		A_SEQUENCE_OF(struct NR_PUCCH_PathlossReferenceRS) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *pathlossReferenceRSs;
	long	*twoPUCCH_PC_AdjustmentStates;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	struct NR_PUCCH_PowerControl__ext1 {
		struct NR_SetupRelease_PathlossReferenceRSs_v1610	*pathlossReferenceRSs_v1610;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext1;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_PUCCH_PowerControl_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_twoPUCCH_PC_AdjustmentStates_11;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_PUCCH_PowerControl;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_PUCCH_PowerControl_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_PUCCH_PowerControl_1[9];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_P0-PUCCH.h"
#include "NR_PUCCH-PathlossReferenceRS.h"
#include "NR_SetupRelease.h"

#endif	/* _NR_PUCCH_PowerControl_H_ */
#include <asn_internal.h>
