/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_RF_Parameters_H_
#define	_NR_RF_Parameters_H_


#include <asn_application.h>

/* Including external dependencies */
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>
#include <OCTET_STRING.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_RF_Parameters__ext1__srs_SwitchingTimeRequested {
	NR_RF_Parameters__ext1__srs_SwitchingTimeRequested_true	= 0
} e_NR_RF_Parameters__ext1__srs_SwitchingTimeRequested;
typedef enum NR_RF_Parameters__ext8__extendedBand_n77_r16 {
	NR_RF_Parameters__ext8__extendedBand_n77_r16_supported	= 0
} e_NR_RF_Parameters__ext8__extendedBand_n77_r16;
typedef enum NR_RF_Parameters__ext12__sidelinkRequested_r17 {
	NR_RF_Parameters__ext12__sidelinkRequested_r17_true	= 0
} e_NR_RF_Parameters__ext12__sidelinkRequested_r17;
typedef enum NR_RF_Parameters__ext12__extendedBand_n77_2_r17 {
	NR_RF_Parameters__ext12__extendedBand_n77_2_r17_supported	= 0
} e_NR_RF_Parameters__ext12__extendedBand_n77_2_r17;

/* Forward declarations */
struct NR_BandCombinationList;
struct NR_FreqBandList;
struct NR_BandNR;
struct NR_BandCombinationList_v1540;
struct NR_BandCombinationList_v1550;
struct NR_BandCombinationList_v1560;
struct NR_BandCombinationList_v1610;
struct NR_BandCombinationListSidelinkEUTRA_NR_r16;
struct NR_BandCombinationList_UplinkTxSwitch_r16;
struct NR_BandCombinationList_v1630;
struct NR_BandCombinationListSidelinkEUTRA_NR_v1630;
struct NR_BandCombinationList_UplinkTxSwitch_v1630;
struct NR_BandCombinationList_v1640;
struct NR_BandCombinationList_UplinkTxSwitch_v1640;
struct NR_BandCombinationList_v1650;
struct NR_BandCombinationList_UplinkTxSwitch_v1650;
struct NR_BandCombinationList_UplinkTxSwitch_v1670;
struct NR_BandCombinationList_v1680;
struct NR_BandCombinationList_v1690;
struct NR_BandCombinationList_UplinkTxSwitch_v1690;
struct NR_BandCombinationList_v1700;
struct NR_BandCombinationList_UplinkTxSwitch_v1700;
struct NR_BandCombinationListSidelinkEUTRA_NR_v1710;
struct NR_BandCombinationList_v1720;
struct NR_BandCombinationList_UplinkTxSwitch_v1720;
struct NR_BandCombinationList_v1730;
struct NR_BandCombinationList_UplinkTxSwitch_v1730;
struct NR_BandCombinationListSL_Discovery_r17;

/* NR_RF-Parameters */
typedef struct NR_RF_Parameters {
	struct NR_RF_Parameters__supportedBandListNR {
		A_SEQUENCE_OF(struct NR_BandNR) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} supportedBandListNR;
	struct NR_BandCombinationList	*supportedBandCombinationList;	/* OPTIONAL */
	struct NR_FreqBandList	*appliedFreqBandListFilter;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	struct NR_RF_Parameters__ext1 {
		struct NR_BandCombinationList_v1540	*supportedBandCombinationList_v1540;	/* OPTIONAL */
		long	*srs_SwitchingTimeRequested;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext1;
	struct NR_RF_Parameters__ext2 {
		struct NR_BandCombinationList_v1550	*supportedBandCombinationList_v1550;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext2;
	struct NR_RF_Parameters__ext3 {
		struct NR_BandCombinationList_v1560	*supportedBandCombinationList_v1560;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext3;
	struct NR_RF_Parameters__ext4 {
		struct NR_BandCombinationList_v1610	*supportedBandCombinationList_v1610;	/* OPTIONAL */
		struct NR_BandCombinationListSidelinkEUTRA_NR_r16	*supportedBandCombinationListSidelinkEUTRA_NR_r16;	/* OPTIONAL */
		struct NR_BandCombinationList_UplinkTxSwitch_r16	*supportedBandCombinationList_UplinkTxSwitch_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext4;
	struct NR_RF_Parameters__ext5 {
		struct NR_BandCombinationList_v1630	*supportedBandCombinationList_v1630;	/* OPTIONAL */
		struct NR_BandCombinationListSidelinkEUTRA_NR_v1630	*supportedBandCombinationListSidelinkEUTRA_NR_v1630;	/* OPTIONAL */
		struct NR_BandCombinationList_UplinkTxSwitch_v1630	*supportedBandCombinationList_UplinkTxSwitch_v1630;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext5;
	struct NR_RF_Parameters__ext6 {
		struct NR_BandCombinationList_v1640	*supportedBandCombinationList_v1640;	/* OPTIONAL */
		struct NR_BandCombinationList_UplinkTxSwitch_v1640	*supportedBandCombinationList_UplinkTxSwitch_v1640;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext6;
	struct NR_RF_Parameters__ext7 {
		struct NR_BandCombinationList_v1650	*supportedBandCombinationList_v1650;	/* OPTIONAL */
		struct NR_BandCombinationList_UplinkTxSwitch_v1650	*supportedBandCombinationList_UplinkTxSwitch_v1650;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext7;
	struct NR_RF_Parameters__ext8 {
		long	*extendedBand_n77_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext8;
	struct NR_RF_Parameters__ext9 {
		struct NR_BandCombinationList_UplinkTxSwitch_v1670	*supportedBandCombinationList_UplinkTxSwitch_v1670;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext9;
	struct NR_RF_Parameters__ext10 {
		struct NR_BandCombinationList_v1680	*supportedBandCombinationList_v1680;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext10;
	struct NR_RF_Parameters__ext11 {
		struct NR_BandCombinationList_v1690	*supportedBandCombinationList_v1690;	/* OPTIONAL */
		struct NR_BandCombinationList_UplinkTxSwitch_v1690	*supportedBandCombinationList_UplinkTxSwitch_v1690;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext11;
	struct NR_RF_Parameters__ext12 {
		struct NR_BandCombinationList_v1700	*supportedBandCombinationList_v1700;	/* OPTIONAL */
		struct NR_BandCombinationList_UplinkTxSwitch_v1700	*supportedBandCombinationList_UplinkTxSwitch_v1700;	/* OPTIONAL */
		OCTET_STRING_t	*supportedBandCombinationListSL_RelayDiscovery_r17;	/* OPTIONAL */
		OCTET_STRING_t	*supportedBandCombinationListSL_NonRelayDiscovery_r17;	/* OPTIONAL */
		struct NR_BandCombinationListSidelinkEUTRA_NR_v1710	*supportedBandCombinationListSidelinkEUTRA_NR_v1710;	/* OPTIONAL */
		long	*sidelinkRequested_r17;	/* OPTIONAL */
		long	*extendedBand_n77_2_r17;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext12;
	struct NR_RF_Parameters__ext13 {
		struct NR_BandCombinationList_v1720	*supportedBandCombinationList_v1720;	/* OPTIONAL */
		struct NR_BandCombinationList_UplinkTxSwitch_v1720	*supportedBandCombinationList_UplinkTxSwitch_v1720;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext13;
	struct NR_RF_Parameters__ext14 {
		struct NR_BandCombinationList_v1730	*supportedBandCombinationList_v1730;	/* OPTIONAL */
		struct NR_BandCombinationList_UplinkTxSwitch_v1730	*supportedBandCombinationList_UplinkTxSwitch_v1730;	/* OPTIONAL */
		struct NR_BandCombinationListSL_Discovery_r17	*supportedBandCombinationListSL_RelayDiscovery_v1730;	/* OPTIONAL */
		struct NR_BandCombinationListSL_Discovery_r17	*supportedBandCombinationListSL_NonRelayDiscovery_v1730;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext14;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_RF_Parameters_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_srs_SwitchingTimeRequested_9;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_extendedBand_n77_r16_30;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_sidelinkRequested_r17_45;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_extendedBand_n77_2_r17_47;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_RF_Parameters;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_RF_Parameters_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_RF_Parameters_1[17];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_BandCombinationList.h"
#include "NR_FreqBandList.h"
#include "NR_BandNR.h"
#include "NR_BandCombinationList-v1540.h"
#include "NR_BandCombinationList-v1550.h"
#include "NR_BandCombinationList-v1560.h"
#include "NR_BandCombinationList-v1610.h"
#include "NR_BandCombinationListSidelinkEUTRA-NR-r16.h"
#include "NR_BandCombinationList-UplinkTxSwitch-r16.h"
#include "NR_BandCombinationList-v1630.h"
#include "NR_BandCombinationListSidelinkEUTRA-NR-v1630.h"
#include "NR_BandCombinationList-UplinkTxSwitch-v1630.h"
#include "NR_BandCombinationList-v1640.h"
#include "NR_BandCombinationList-UplinkTxSwitch-v1640.h"
#include "NR_BandCombinationList-v1650.h"
#include "NR_BandCombinationList-UplinkTxSwitch-v1650.h"
#include "NR_BandCombinationList-UplinkTxSwitch-v1670.h"
#include "NR_BandCombinationList-v1680.h"
#include "NR_BandCombinationList-v1690.h"
#include "NR_BandCombinationList-UplinkTxSwitch-v1690.h"
#include "NR_BandCombinationList-v1700.h"
#include "NR_BandCombinationList-UplinkTxSwitch-v1700.h"
#include "NR_BandCombinationListSidelinkEUTRA-NR-v1710.h"
#include "NR_BandCombinationList-v1720.h"
#include "NR_BandCombinationList-UplinkTxSwitch-v1720.h"
#include "NR_BandCombinationList-v1730.h"
#include "NR_BandCombinationList-UplinkTxSwitch-v1730.h"
#include "NR_BandCombinationListSL-Discovery-r17.h"

#endif	/* _NR_RF_Parameters_H_ */
#include <asn_internal.h>
