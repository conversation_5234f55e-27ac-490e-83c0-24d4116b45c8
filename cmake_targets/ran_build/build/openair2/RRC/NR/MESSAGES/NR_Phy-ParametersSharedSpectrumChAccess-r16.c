/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_Phy-ParametersSharedSpectrumChAccess-r16.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ss_SINR_Meas_r16_constr_2 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_sp_CSI_ReportPUCCH_r16_constr_4 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_sp_CSI_ReportPUSCH_r16_constr_6 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dynamicSFI_r16_constr_8 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_sameSymbol_r16_constr_11 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_diffSymbol_r16_constr_13 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_mux_SR_HARQ_ACK_PUCCH_r16_constr_15 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_r16_constr_17 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_r16_constr_19 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pucch_Repetition_F1_3_4_r16_constr_21 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_type1_PUSCH_RepetitionMultiSlots_r16_constr_23 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_type2_PUSCH_RepetitionMultiSlots_r16_constr_25 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pusch_RepetitionMultiSlots_r16_constr_27 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pdsch_RepetitionMultiSlots_r16_constr_29 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_downlinkSPS_r16_constr_31 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_configuredUL_GrantType1_r16_constr_33 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_configuredUL_GrantType2_r16_constr_35 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pre_EmptIndication_DL_r16_constr_37 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_ss_SINR_Meas_r16_value2enum_2[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_ss_SINR_Meas_r16_enum2value_2[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ss_SINR_Meas_r16_specs_2 = {
	asn_MAP_NR_ss_SINR_Meas_r16_value2enum_2,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ss_SINR_Meas_r16_enum2value_2,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ss_SINR_Meas_r16_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ss_SINR_Meas_r16_2 = {
	"ss-SINR-Meas-r16",
	"ss-SINR-Meas-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ss_SINR_Meas_r16_tags_2,
	sizeof(asn_DEF_NR_ss_SINR_Meas_r16_tags_2)
		/sizeof(asn_DEF_NR_ss_SINR_Meas_r16_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_ss_SINR_Meas_r16_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_ss_SINR_Meas_r16_tags_2)
		/sizeof(asn_DEF_NR_ss_SINR_Meas_r16_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ss_SINR_Meas_r16_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ss_SINR_Meas_r16_specs_2	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_sp_CSI_ReportPUCCH_r16_value2enum_4[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_sp_CSI_ReportPUCCH_r16_enum2value_4[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_sp_CSI_ReportPUCCH_r16_specs_4 = {
	asn_MAP_NR_sp_CSI_ReportPUCCH_r16_value2enum_4,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_sp_CSI_ReportPUCCH_r16_enum2value_4,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_sp_CSI_ReportPUCCH_r16_tags_4[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_sp_CSI_ReportPUCCH_r16_4 = {
	"sp-CSI-ReportPUCCH-r16",
	"sp-CSI-ReportPUCCH-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_sp_CSI_ReportPUCCH_r16_tags_4,
	sizeof(asn_DEF_NR_sp_CSI_ReportPUCCH_r16_tags_4)
		/sizeof(asn_DEF_NR_sp_CSI_ReportPUCCH_r16_tags_4[0]) - 1, /* 1 */
	asn_DEF_NR_sp_CSI_ReportPUCCH_r16_tags_4,	/* Same as above */
	sizeof(asn_DEF_NR_sp_CSI_ReportPUCCH_r16_tags_4)
		/sizeof(asn_DEF_NR_sp_CSI_ReportPUCCH_r16_tags_4[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_sp_CSI_ReportPUCCH_r16_constr_4,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_sp_CSI_ReportPUCCH_r16_specs_4	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_sp_CSI_ReportPUSCH_r16_value2enum_6[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_sp_CSI_ReportPUSCH_r16_enum2value_6[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_sp_CSI_ReportPUSCH_r16_specs_6 = {
	asn_MAP_NR_sp_CSI_ReportPUSCH_r16_value2enum_6,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_sp_CSI_ReportPUSCH_r16_enum2value_6,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_sp_CSI_ReportPUSCH_r16_tags_6[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_sp_CSI_ReportPUSCH_r16_6 = {
	"sp-CSI-ReportPUSCH-r16",
	"sp-CSI-ReportPUSCH-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_sp_CSI_ReportPUSCH_r16_tags_6,
	sizeof(asn_DEF_NR_sp_CSI_ReportPUSCH_r16_tags_6)
		/sizeof(asn_DEF_NR_sp_CSI_ReportPUSCH_r16_tags_6[0]) - 1, /* 1 */
	asn_DEF_NR_sp_CSI_ReportPUSCH_r16_tags_6,	/* Same as above */
	sizeof(asn_DEF_NR_sp_CSI_ReportPUSCH_r16_tags_6)
		/sizeof(asn_DEF_NR_sp_CSI_ReportPUSCH_r16_tags_6[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_sp_CSI_ReportPUSCH_r16_constr_6,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_sp_CSI_ReportPUSCH_r16_specs_6	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dynamicSFI_r16_value2enum_8[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_dynamicSFI_r16_enum2value_8[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dynamicSFI_r16_specs_8 = {
	asn_MAP_NR_dynamicSFI_r16_value2enum_8,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dynamicSFI_r16_enum2value_8,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dynamicSFI_r16_tags_8[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dynamicSFI_r16_8 = {
	"dynamicSFI-r16",
	"dynamicSFI-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dynamicSFI_r16_tags_8,
	sizeof(asn_DEF_NR_dynamicSFI_r16_tags_8)
		/sizeof(asn_DEF_NR_dynamicSFI_r16_tags_8[0]) - 1, /* 1 */
	asn_DEF_NR_dynamicSFI_r16_tags_8,	/* Same as above */
	sizeof(asn_DEF_NR_dynamicSFI_r16_tags_8)
		/sizeof(asn_DEF_NR_dynamicSFI_r16_tags_8[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dynamicSFI_r16_constr_8,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dynamicSFI_r16_specs_8	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_sameSymbol_r16_value2enum_11[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_sameSymbol_r16_enum2value_11[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_sameSymbol_r16_specs_11 = {
	asn_MAP_NR_sameSymbol_r16_value2enum_11,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_sameSymbol_r16_enum2value_11,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_sameSymbol_r16_tags_11[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_sameSymbol_r16_11 = {
	"sameSymbol-r16",
	"sameSymbol-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_sameSymbol_r16_tags_11,
	sizeof(asn_DEF_NR_sameSymbol_r16_tags_11)
		/sizeof(asn_DEF_NR_sameSymbol_r16_tags_11[0]) - 1, /* 1 */
	asn_DEF_NR_sameSymbol_r16_tags_11,	/* Same as above */
	sizeof(asn_DEF_NR_sameSymbol_r16_tags_11)
		/sizeof(asn_DEF_NR_sameSymbol_r16_tags_11[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_sameSymbol_r16_constr_11,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_sameSymbol_r16_specs_11	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_diffSymbol_r16_value2enum_13[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_diffSymbol_r16_enum2value_13[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_diffSymbol_r16_specs_13 = {
	asn_MAP_NR_diffSymbol_r16_value2enum_13,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_diffSymbol_r16_enum2value_13,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_diffSymbol_r16_tags_13[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_diffSymbol_r16_13 = {
	"diffSymbol-r16",
	"diffSymbol-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_diffSymbol_r16_tags_13,
	sizeof(asn_DEF_NR_diffSymbol_r16_tags_13)
		/sizeof(asn_DEF_NR_diffSymbol_r16_tags_13[0]) - 1, /* 1 */
	asn_DEF_NR_diffSymbol_r16_tags_13,	/* Same as above */
	sizeof(asn_DEF_NR_diffSymbol_r16_tags_13)
		/sizeof(asn_DEF_NR_diffSymbol_r16_tags_13[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_diffSymbol_r16_constr_13,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_diffSymbol_r16_specs_13	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16_10[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_Phy_ParametersSharedSpectrumChAccess_r16__mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16, sameSymbol_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_sameSymbol_r16_11,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"sameSymbol-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersSharedSpectrumChAccess_r16__mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16, diffSymbol_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_diffSymbol_r16_13,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"diffSymbol-r16"
		},
};
static const int asn_MAP_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16_oms_10[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16_tags_10[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16_tag2el_10[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* sameSymbol-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* diffSymbol-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16_specs_10 = {
	sizeof(struct NR_Phy_ParametersSharedSpectrumChAccess_r16__mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16),
	offsetof(struct NR_Phy_ParametersSharedSpectrumChAccess_r16__mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16, _asn_ctx),
	asn_MAP_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16_tag2el_10,
	2,	/* Count of tags in the map */
	asn_MAP_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16_oms_10,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16_10 = {
	"mux-SR-HARQ-ACK-CSI-PUCCH-OncePerSlot-r16",
	"mux-SR-HARQ-ACK-CSI-PUCCH-OncePerSlot-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16_tags_10,
	sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16_tags_10)
		/sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16_tags_10[0]) - 1, /* 1 */
	asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16_tags_10,	/* Same as above */
	sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16_tags_10)
		/sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16_tags_10[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16_10,
	2,	/* Elements count */
	&asn_SPC_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16_specs_10	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_mux_SR_HARQ_ACK_PUCCH_r16_value2enum_15[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_mux_SR_HARQ_ACK_PUCCH_r16_enum2value_15[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_mux_SR_HARQ_ACK_PUCCH_r16_specs_15 = {
	asn_MAP_NR_mux_SR_HARQ_ACK_PUCCH_r16_value2enum_15,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_mux_SR_HARQ_ACK_PUCCH_r16_enum2value_15,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_mux_SR_HARQ_ACK_PUCCH_r16_tags_15[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mux_SR_HARQ_ACK_PUCCH_r16_15 = {
	"mux-SR-HARQ-ACK-PUCCH-r16",
	"mux-SR-HARQ-ACK-PUCCH-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_mux_SR_HARQ_ACK_PUCCH_r16_tags_15,
	sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_PUCCH_r16_tags_15)
		/sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_PUCCH_r16_tags_15[0]) - 1, /* 1 */
	asn_DEF_NR_mux_SR_HARQ_ACK_PUCCH_r16_tags_15,	/* Same as above */
	sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_PUCCH_r16_tags_15)
		/sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_PUCCH_r16_tags_15[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_mux_SR_HARQ_ACK_PUCCH_r16_constr_15,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_mux_SR_HARQ_ACK_PUCCH_r16_specs_15	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_r16_value2enum_17[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_r16_enum2value_17[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_r16_specs_17 = {
	asn_MAP_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_r16_value2enum_17,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_r16_enum2value_17,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_r16_tags_17[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_r16_17 = {
	"mux-SR-HARQ-ACK-CSI-PUCCH-MultiPerSlot-r16",
	"mux-SR-HARQ-ACK-CSI-PUCCH-MultiPerSlot-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_r16_tags_17,
	sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_r16_tags_17)
		/sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_r16_tags_17[0]) - 1, /* 1 */
	asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_r16_tags_17,	/* Same as above */
	sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_r16_tags_17)
		/sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_r16_tags_17[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_r16_constr_17,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_r16_specs_17	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_r16_value2enum_19[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_r16_enum2value_19[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_r16_specs_19 = {
	asn_MAP_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_r16_value2enum_19,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_r16_enum2value_19,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_r16_tags_19[] = {
	(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_r16_19 = {
	"mux-HARQ-ACK-PUSCH-DiffSymbol-r16",
	"mux-HARQ-ACK-PUSCH-DiffSymbol-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_r16_tags_19,
	sizeof(asn_DEF_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_r16_tags_19)
		/sizeof(asn_DEF_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_r16_tags_19[0]) - 1, /* 1 */
	asn_DEF_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_r16_tags_19,	/* Same as above */
	sizeof(asn_DEF_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_r16_tags_19)
		/sizeof(asn_DEF_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_r16_tags_19[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_r16_constr_19,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_r16_specs_19	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pucch_Repetition_F1_3_4_r16_value2enum_21[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_pucch_Repetition_F1_3_4_r16_enum2value_21[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pucch_Repetition_F1_3_4_r16_specs_21 = {
	asn_MAP_NR_pucch_Repetition_F1_3_4_r16_value2enum_21,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pucch_Repetition_F1_3_4_r16_enum2value_21,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pucch_Repetition_F1_3_4_r16_tags_21[] = {
	(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pucch_Repetition_F1_3_4_r16_21 = {
	"pucch-Repetition-F1-3-4-r16",
	"pucch-Repetition-F1-3-4-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pucch_Repetition_F1_3_4_r16_tags_21,
	sizeof(asn_DEF_NR_pucch_Repetition_F1_3_4_r16_tags_21)
		/sizeof(asn_DEF_NR_pucch_Repetition_F1_3_4_r16_tags_21[0]) - 1, /* 1 */
	asn_DEF_NR_pucch_Repetition_F1_3_4_r16_tags_21,	/* Same as above */
	sizeof(asn_DEF_NR_pucch_Repetition_F1_3_4_r16_tags_21)
		/sizeof(asn_DEF_NR_pucch_Repetition_F1_3_4_r16_tags_21[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pucch_Repetition_F1_3_4_r16_constr_21,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pucch_Repetition_F1_3_4_r16_specs_21	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_type1_PUSCH_RepetitionMultiSlots_r16_value2enum_23[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_type1_PUSCH_RepetitionMultiSlots_r16_enum2value_23[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_type1_PUSCH_RepetitionMultiSlots_r16_specs_23 = {
	asn_MAP_NR_type1_PUSCH_RepetitionMultiSlots_r16_value2enum_23,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_type1_PUSCH_RepetitionMultiSlots_r16_enum2value_23,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_type1_PUSCH_RepetitionMultiSlots_r16_tags_23[] = {
	(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_type1_PUSCH_RepetitionMultiSlots_r16_23 = {
	"type1-PUSCH-RepetitionMultiSlots-r16",
	"type1-PUSCH-RepetitionMultiSlots-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_type1_PUSCH_RepetitionMultiSlots_r16_tags_23,
	sizeof(asn_DEF_NR_type1_PUSCH_RepetitionMultiSlots_r16_tags_23)
		/sizeof(asn_DEF_NR_type1_PUSCH_RepetitionMultiSlots_r16_tags_23[0]) - 1, /* 1 */
	asn_DEF_NR_type1_PUSCH_RepetitionMultiSlots_r16_tags_23,	/* Same as above */
	sizeof(asn_DEF_NR_type1_PUSCH_RepetitionMultiSlots_r16_tags_23)
		/sizeof(asn_DEF_NR_type1_PUSCH_RepetitionMultiSlots_r16_tags_23[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_type1_PUSCH_RepetitionMultiSlots_r16_constr_23,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_type1_PUSCH_RepetitionMultiSlots_r16_specs_23	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_type2_PUSCH_RepetitionMultiSlots_r16_value2enum_25[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_type2_PUSCH_RepetitionMultiSlots_r16_enum2value_25[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_type2_PUSCH_RepetitionMultiSlots_r16_specs_25 = {
	asn_MAP_NR_type2_PUSCH_RepetitionMultiSlots_r16_value2enum_25,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_type2_PUSCH_RepetitionMultiSlots_r16_enum2value_25,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_type2_PUSCH_RepetitionMultiSlots_r16_tags_25[] = {
	(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_type2_PUSCH_RepetitionMultiSlots_r16_25 = {
	"type2-PUSCH-RepetitionMultiSlots-r16",
	"type2-PUSCH-RepetitionMultiSlots-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_type2_PUSCH_RepetitionMultiSlots_r16_tags_25,
	sizeof(asn_DEF_NR_type2_PUSCH_RepetitionMultiSlots_r16_tags_25)
		/sizeof(asn_DEF_NR_type2_PUSCH_RepetitionMultiSlots_r16_tags_25[0]) - 1, /* 1 */
	asn_DEF_NR_type2_PUSCH_RepetitionMultiSlots_r16_tags_25,	/* Same as above */
	sizeof(asn_DEF_NR_type2_PUSCH_RepetitionMultiSlots_r16_tags_25)
		/sizeof(asn_DEF_NR_type2_PUSCH_RepetitionMultiSlots_r16_tags_25[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_type2_PUSCH_RepetitionMultiSlots_r16_constr_25,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_type2_PUSCH_RepetitionMultiSlots_r16_specs_25	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pusch_RepetitionMultiSlots_r16_value2enum_27[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_pusch_RepetitionMultiSlots_r16_enum2value_27[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pusch_RepetitionMultiSlots_r16_specs_27 = {
	asn_MAP_NR_pusch_RepetitionMultiSlots_r16_value2enum_27,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pusch_RepetitionMultiSlots_r16_enum2value_27,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pusch_RepetitionMultiSlots_r16_tags_27[] = {
	(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pusch_RepetitionMultiSlots_r16_27 = {
	"pusch-RepetitionMultiSlots-r16",
	"pusch-RepetitionMultiSlots-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pusch_RepetitionMultiSlots_r16_tags_27,
	sizeof(asn_DEF_NR_pusch_RepetitionMultiSlots_r16_tags_27)
		/sizeof(asn_DEF_NR_pusch_RepetitionMultiSlots_r16_tags_27[0]) - 1, /* 1 */
	asn_DEF_NR_pusch_RepetitionMultiSlots_r16_tags_27,	/* Same as above */
	sizeof(asn_DEF_NR_pusch_RepetitionMultiSlots_r16_tags_27)
		/sizeof(asn_DEF_NR_pusch_RepetitionMultiSlots_r16_tags_27[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pusch_RepetitionMultiSlots_r16_constr_27,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pusch_RepetitionMultiSlots_r16_specs_27	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pdsch_RepetitionMultiSlots_r16_value2enum_29[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_pdsch_RepetitionMultiSlots_r16_enum2value_29[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pdsch_RepetitionMultiSlots_r16_specs_29 = {
	asn_MAP_NR_pdsch_RepetitionMultiSlots_r16_value2enum_29,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pdsch_RepetitionMultiSlots_r16_enum2value_29,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pdsch_RepetitionMultiSlots_r16_tags_29[] = {
	(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pdsch_RepetitionMultiSlots_r16_29 = {
	"pdsch-RepetitionMultiSlots-r16",
	"pdsch-RepetitionMultiSlots-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pdsch_RepetitionMultiSlots_r16_tags_29,
	sizeof(asn_DEF_NR_pdsch_RepetitionMultiSlots_r16_tags_29)
		/sizeof(asn_DEF_NR_pdsch_RepetitionMultiSlots_r16_tags_29[0]) - 1, /* 1 */
	asn_DEF_NR_pdsch_RepetitionMultiSlots_r16_tags_29,	/* Same as above */
	sizeof(asn_DEF_NR_pdsch_RepetitionMultiSlots_r16_tags_29)
		/sizeof(asn_DEF_NR_pdsch_RepetitionMultiSlots_r16_tags_29[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pdsch_RepetitionMultiSlots_r16_constr_29,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pdsch_RepetitionMultiSlots_r16_specs_29	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_downlinkSPS_r16_value2enum_31[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_downlinkSPS_r16_enum2value_31[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_downlinkSPS_r16_specs_31 = {
	asn_MAP_NR_downlinkSPS_r16_value2enum_31,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_downlinkSPS_r16_enum2value_31,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_downlinkSPS_r16_tags_31[] = {
	(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_downlinkSPS_r16_31 = {
	"downlinkSPS-r16",
	"downlinkSPS-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_downlinkSPS_r16_tags_31,
	sizeof(asn_DEF_NR_downlinkSPS_r16_tags_31)
		/sizeof(asn_DEF_NR_downlinkSPS_r16_tags_31[0]) - 1, /* 1 */
	asn_DEF_NR_downlinkSPS_r16_tags_31,	/* Same as above */
	sizeof(asn_DEF_NR_downlinkSPS_r16_tags_31)
		/sizeof(asn_DEF_NR_downlinkSPS_r16_tags_31[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_downlinkSPS_r16_constr_31,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_downlinkSPS_r16_specs_31	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_configuredUL_GrantType1_r16_value2enum_33[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_configuredUL_GrantType1_r16_enum2value_33[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_configuredUL_GrantType1_r16_specs_33 = {
	asn_MAP_NR_configuredUL_GrantType1_r16_value2enum_33,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_configuredUL_GrantType1_r16_enum2value_33,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_configuredUL_GrantType1_r16_tags_33[] = {
	(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_configuredUL_GrantType1_r16_33 = {
	"configuredUL-GrantType1-r16",
	"configuredUL-GrantType1-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_configuredUL_GrantType1_r16_tags_33,
	sizeof(asn_DEF_NR_configuredUL_GrantType1_r16_tags_33)
		/sizeof(asn_DEF_NR_configuredUL_GrantType1_r16_tags_33[0]) - 1, /* 1 */
	asn_DEF_NR_configuredUL_GrantType1_r16_tags_33,	/* Same as above */
	sizeof(asn_DEF_NR_configuredUL_GrantType1_r16_tags_33)
		/sizeof(asn_DEF_NR_configuredUL_GrantType1_r16_tags_33[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_configuredUL_GrantType1_r16_constr_33,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_configuredUL_GrantType1_r16_specs_33	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_configuredUL_GrantType2_r16_value2enum_35[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_configuredUL_GrantType2_r16_enum2value_35[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_configuredUL_GrantType2_r16_specs_35 = {
	asn_MAP_NR_configuredUL_GrantType2_r16_value2enum_35,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_configuredUL_GrantType2_r16_enum2value_35,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_configuredUL_GrantType2_r16_tags_35[] = {
	(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_configuredUL_GrantType2_r16_35 = {
	"configuredUL-GrantType2-r16",
	"configuredUL-GrantType2-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_configuredUL_GrantType2_r16_tags_35,
	sizeof(asn_DEF_NR_configuredUL_GrantType2_r16_tags_35)
		/sizeof(asn_DEF_NR_configuredUL_GrantType2_r16_tags_35[0]) - 1, /* 1 */
	asn_DEF_NR_configuredUL_GrantType2_r16_tags_35,	/* Same as above */
	sizeof(asn_DEF_NR_configuredUL_GrantType2_r16_tags_35)
		/sizeof(asn_DEF_NR_configuredUL_GrantType2_r16_tags_35[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_configuredUL_GrantType2_r16_constr_35,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_configuredUL_GrantType2_r16_specs_35	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pre_EmptIndication_DL_r16_value2enum_37[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_pre_EmptIndication_DL_r16_enum2value_37[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pre_EmptIndication_DL_r16_specs_37 = {
	asn_MAP_NR_pre_EmptIndication_DL_r16_value2enum_37,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pre_EmptIndication_DL_r16_enum2value_37,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pre_EmptIndication_DL_r16_tags_37[] = {
	(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pre_EmptIndication_DL_r16_37 = {
	"pre-EmptIndication-DL-r16",
	"pre-EmptIndication-DL-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pre_EmptIndication_DL_r16_tags_37,
	sizeof(asn_DEF_NR_pre_EmptIndication_DL_r16_tags_37)
		/sizeof(asn_DEF_NR_pre_EmptIndication_DL_r16_tags_37[0]) - 1, /* 1 */
	asn_DEF_NR_pre_EmptIndication_DL_r16_tags_37,	/* Same as above */
	sizeof(asn_DEF_NR_pre_EmptIndication_DL_r16_tags_37)
		/sizeof(asn_DEF_NR_pre_EmptIndication_DL_r16_tags_37[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pre_EmptIndication_DL_r16_constr_37,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pre_EmptIndication_DL_r16_specs_37	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_Phy_ParametersSharedSpectrumChAccess_r16_1[] = {
	{ ATF_POINTER, 17, offsetof(struct NR_Phy_ParametersSharedSpectrumChAccess_r16, ss_SINR_Meas_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ss_SINR_Meas_r16_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ss-SINR-Meas-r16"
		},
	{ ATF_POINTER, 16, offsetof(struct NR_Phy_ParametersSharedSpectrumChAccess_r16, sp_CSI_ReportPUCCH_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_sp_CSI_ReportPUCCH_r16_4,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"sp-CSI-ReportPUCCH-r16"
		},
	{ ATF_POINTER, 15, offsetof(struct NR_Phy_ParametersSharedSpectrumChAccess_r16, sp_CSI_ReportPUSCH_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_sp_CSI_ReportPUSCH_r16_6,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"sp-CSI-ReportPUSCH-r16"
		},
	{ ATF_POINTER, 14, offsetof(struct NR_Phy_ParametersSharedSpectrumChAccess_r16, dynamicSFI_r16),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dynamicSFI_r16_8,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dynamicSFI-r16"
		},
	{ ATF_POINTER, 13, offsetof(struct NR_Phy_ParametersSharedSpectrumChAccess_r16, mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		0,
		&asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_r16_10,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mux-SR-HARQ-ACK-CSI-PUCCH-OncePerSlot-r16"
		},
	{ ATF_POINTER, 12, offsetof(struct NR_Phy_ParametersSharedSpectrumChAccess_r16, mux_SR_HARQ_ACK_PUCCH_r16),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_mux_SR_HARQ_ACK_PUCCH_r16_15,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mux-SR-HARQ-ACK-PUCCH-r16"
		},
	{ ATF_POINTER, 11, offsetof(struct NR_Phy_ParametersSharedSpectrumChAccess_r16, mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_r16),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_r16_17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mux-SR-HARQ-ACK-CSI-PUCCH-MultiPerSlot-r16"
		},
	{ ATF_POINTER, 10, offsetof(struct NR_Phy_ParametersSharedSpectrumChAccess_r16, mux_HARQ_ACK_PUSCH_DiffSymbol_r16),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_r16_19,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mux-HARQ-ACK-PUSCH-DiffSymbol-r16"
		},
	{ ATF_POINTER, 9, offsetof(struct NR_Phy_ParametersSharedSpectrumChAccess_r16, pucch_Repetition_F1_3_4_r16),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pucch_Repetition_F1_3_4_r16_21,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pucch-Repetition-F1-3-4-r16"
		},
	{ ATF_POINTER, 8, offsetof(struct NR_Phy_ParametersSharedSpectrumChAccess_r16, type1_PUSCH_RepetitionMultiSlots_r16),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_type1_PUSCH_RepetitionMultiSlots_r16_23,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"type1-PUSCH-RepetitionMultiSlots-r16"
		},
	{ ATF_POINTER, 7, offsetof(struct NR_Phy_ParametersSharedSpectrumChAccess_r16, type2_PUSCH_RepetitionMultiSlots_r16),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_type2_PUSCH_RepetitionMultiSlots_r16_25,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"type2-PUSCH-RepetitionMultiSlots-r16"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_Phy_ParametersSharedSpectrumChAccess_r16, pusch_RepetitionMultiSlots_r16),
		(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pusch_RepetitionMultiSlots_r16_27,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pusch-RepetitionMultiSlots-r16"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_Phy_ParametersSharedSpectrumChAccess_r16, pdsch_RepetitionMultiSlots_r16),
		(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pdsch_RepetitionMultiSlots_r16_29,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pdsch-RepetitionMultiSlots-r16"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_Phy_ParametersSharedSpectrumChAccess_r16, downlinkSPS_r16),
		(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_downlinkSPS_r16_31,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"downlinkSPS-r16"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_Phy_ParametersSharedSpectrumChAccess_r16, configuredUL_GrantType1_r16),
		(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_configuredUL_GrantType1_r16_33,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"configuredUL-GrantType1-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_Phy_ParametersSharedSpectrumChAccess_r16, configuredUL_GrantType2_r16),
		(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_configuredUL_GrantType2_r16_35,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"configuredUL-GrantType2-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersSharedSpectrumChAccess_r16, pre_EmptIndication_DL_r16),
		(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pre_EmptIndication_DL_r16_37,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pre-EmptIndication-DL-r16"
		},
};
static const int asn_MAP_NR_Phy_ParametersSharedSpectrumChAccess_r16_oms_1[] = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16 };
static const ber_tlv_tag_t asn_DEF_NR_Phy_ParametersSharedSpectrumChAccess_r16_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_Phy_ParametersSharedSpectrumChAccess_r16_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* ss-SINR-Meas-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* sp-CSI-ReportPUCCH-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* sp-CSI-ReportPUSCH-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* dynamicSFI-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* mux-SR-HARQ-ACK-CSI-PUCCH-OncePerSlot-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* mux-SR-HARQ-ACK-PUCCH-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* mux-SR-HARQ-ACK-CSI-PUCCH-MultiPerSlot-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* mux-HARQ-ACK-PUSCH-DiffSymbol-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* pucch-Repetition-F1-3-4-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* type1-PUSCH-RepetitionMultiSlots-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 }, /* type2-PUSCH-RepetitionMultiSlots-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (11 << 2)), 11, 0, 0 }, /* pusch-RepetitionMultiSlots-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (12 << 2)), 12, 0, 0 }, /* pdsch-RepetitionMultiSlots-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (13 << 2)), 13, 0, 0 }, /* downlinkSPS-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (14 << 2)), 14, 0, 0 }, /* configuredUL-GrantType1-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (15 << 2)), 15, 0, 0 }, /* configuredUL-GrantType2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (16 << 2)), 16, 0, 0 } /* pre-EmptIndication-DL-r16 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_Phy_ParametersSharedSpectrumChAccess_r16_specs_1 = {
	sizeof(struct NR_Phy_ParametersSharedSpectrumChAccess_r16),
	offsetof(struct NR_Phy_ParametersSharedSpectrumChAccess_r16, _asn_ctx),
	asn_MAP_NR_Phy_ParametersSharedSpectrumChAccess_r16_tag2el_1,
	17,	/* Count of tags in the map */
	asn_MAP_NR_Phy_ParametersSharedSpectrumChAccess_r16_oms_1,	/* Optional members */
	17, 0,	/* Root/Additions */
	17,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_Phy_ParametersSharedSpectrumChAccess_r16 = {
	"Phy-ParametersSharedSpectrumChAccess-r16",
	"Phy-ParametersSharedSpectrumChAccess-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_Phy_ParametersSharedSpectrumChAccess_r16_tags_1,
	sizeof(asn_DEF_NR_Phy_ParametersSharedSpectrumChAccess_r16_tags_1)
		/sizeof(asn_DEF_NR_Phy_ParametersSharedSpectrumChAccess_r16_tags_1[0]), /* 1 */
	asn_DEF_NR_Phy_ParametersSharedSpectrumChAccess_r16_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_Phy_ParametersSharedSpectrumChAccess_r16_tags_1)
		/sizeof(asn_DEF_NR_Phy_ParametersSharedSpectrumChAccess_r16_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_Phy_ParametersSharedSpectrumChAccess_r16_1,
	17,	/* Elements count */
	&asn_SPC_NR_Phy_ParametersSharedSpectrumChAccess_r16_specs_1	/* Additional specs */
};

