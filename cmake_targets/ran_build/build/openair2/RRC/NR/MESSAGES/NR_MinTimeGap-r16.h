/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_MinTimeGap_r16_H_
#define	_NR_MinTimeGap_r16_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_MinTimeGap_r16__scs_15kHz_r16 {
	NR_MinTimeGap_r16__scs_15kHz_r16_sl1	= 0,
	NR_MinTimeGap_r16__scs_15kHz_r16_sl3	= 1
} e_NR_MinTimeGap_r16__scs_15kHz_r16;
typedef enum NR_MinTimeGap_r16__scs_30kHz_r16 {
	NR_MinTimeGap_r16__scs_30kHz_r16_sl1	= 0,
	NR_MinTimeGap_r16__scs_30kHz_r16_sl6	= 1
} e_NR_MinTimeGap_r16__scs_30kHz_r16;
typedef enum NR_MinTimeGap_r16__scs_60kHz_r16 {
	NR_MinTimeGap_r16__scs_60kHz_r16_sl1	= 0,
	NR_MinTimeGap_r16__scs_60kHz_r16_sl12	= 1
} e_NR_MinTimeGap_r16__scs_60kHz_r16;
typedef enum NR_MinTimeGap_r16__scs_120kHz_r16 {
	NR_MinTimeGap_r16__scs_120kHz_r16_sl2	= 0,
	NR_MinTimeGap_r16__scs_120kHz_r16_sl24	= 1
} e_NR_MinTimeGap_r16__scs_120kHz_r16;

/* NR_MinTimeGap-r16 */
typedef struct NR_MinTimeGap_r16 {
	long	*scs_15kHz_r16;	/* OPTIONAL */
	long	*scs_30kHz_r16;	/* OPTIONAL */
	long	*scs_60kHz_r16;	/* OPTIONAL */
	long	*scs_120kHz_r16;	/* OPTIONAL */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_MinTimeGap_r16_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_15kHz_r16_2;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_30kHz_r16_5;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_60kHz_r16_8;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_120kHz_r16_11;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_MinTimeGap_r16;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_MinTimeGap_r16_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_MinTimeGap_r16_1[4];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_MinTimeGap_r16_H_ */
#include <asn_internal.h>
