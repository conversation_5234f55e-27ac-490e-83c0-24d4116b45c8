/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_FeatureSetUplink_v1630_H_
#define	_NR_FeatureSetUplink_v1630_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_FeatureSetUplink_v1630__offsetSRS_CB_PUSCH_Ant_Switch_fr1_r16 {
	NR_FeatureSetUplink_v1630__offsetSRS_CB_PUSCH_Ant_Switch_fr1_r16_supported	= 0
} e_NR_FeatureSetUplink_v1630__offsetSRS_CB_PUSCH_Ant_Switch_fr1_r16;
typedef enum NR_FeatureSetUplink_v1630__offsetSRS_CB_PUSCH_PDCCH_MonitorSingleOcc_fr1_r16 {
	NR_FeatureSetUplink_v1630__offsetSRS_CB_PUSCH_PDCCH_MonitorSingleOcc_fr1_r16_supported	= 0
} e_NR_FeatureSetUplink_v1630__offsetSRS_CB_PUSCH_PDCCH_MonitorSingleOcc_fr1_r16;
typedef enum NR_FeatureSetUplink_v1630__offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithoutGap_fr1_r16 {
	NR_FeatureSetUplink_v1630__offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithoutGap_fr1_r16_supported	= 0
} e_NR_FeatureSetUplink_v1630__offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithoutGap_fr1_r16;
typedef enum NR_FeatureSetUplink_v1630__offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithGap_fr1_r16 {
	NR_FeatureSetUplink_v1630__offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithGap_fr1_r16_supported	= 0
} e_NR_FeatureSetUplink_v1630__offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithGap_fr1_r16;
typedef enum NR_FeatureSetUplink_v1630__dummy {
	NR_FeatureSetUplink_v1630__dummy_supported	= 0
} e_NR_FeatureSetUplink_v1630__dummy;
typedef enum NR_FeatureSetUplink_v1630__partialCancellationPUCCH_PUSCH_PRACH_TX_r16 {
	NR_FeatureSetUplink_v1630__partialCancellationPUCCH_PUSCH_PRACH_TX_r16_supported	= 0
} e_NR_FeatureSetUplink_v1630__partialCancellationPUCCH_PUSCH_PRACH_TX_r16;

/* NR_FeatureSetUplink-v1630 */
typedef struct NR_FeatureSetUplink_v1630 {
	long	*offsetSRS_CB_PUSCH_Ant_Switch_fr1_r16;	/* OPTIONAL */
	long	*offsetSRS_CB_PUSCH_PDCCH_MonitorSingleOcc_fr1_r16;	/* OPTIONAL */
	long	*offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithoutGap_fr1_r16;	/* OPTIONAL */
	long	*offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithGap_fr1_r16;	/* OPTIONAL */
	long	*dummy;	/* OPTIONAL */
	long	*partialCancellationPUCCH_PUSCH_PRACH_TX_r16;	/* OPTIONAL */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_FeatureSetUplink_v1630_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_offsetSRS_CB_PUSCH_Ant_Switch_fr1_r16_2;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_offsetSRS_CB_PUSCH_PDCCH_MonitorSingleOcc_fr1_r16_4;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithoutGap_fr1_r16_6;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithGap_fr1_r16_8;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dummy_10;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_partialCancellationPUCCH_PUSCH_PRACH_TX_r16_12;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_FeatureSetUplink_v1630;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_FeatureSetUplink_v1630_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_FeatureSetUplink_v1630_1[6];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_FeatureSetUplink_v1630_H_ */
#include <asn_internal.h>
