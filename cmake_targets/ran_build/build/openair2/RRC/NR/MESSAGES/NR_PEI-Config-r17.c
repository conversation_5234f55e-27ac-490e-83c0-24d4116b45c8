/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_PEI-Config-r17.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_payloadSizeDCI_2_7_r17_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 43L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_pei_FrameOffset_r17_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 16L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_po_NumPerPEI_r17_constr_2 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_lastUsedCellOnly_r17_constr_10 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_payloadSizeDCI_2_7_r17_constr_7 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 6,  6,  1,  43 }	/* (1..43) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_pei_FrameOffset_r17_constr_8 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 5,  5,  0,  16 }	/* (0..16) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_po_NumPerPEI_r17_value2enum_2[] = {
	{ 0,	3,	"po1" },
	{ 1,	3,	"po2" },
	{ 2,	3,	"po4" },
	{ 3,	3,	"po8" }
};
static const unsigned int asn_MAP_NR_po_NumPerPEI_r17_enum2value_2[] = {
	0,	/* po1(0) */
	1,	/* po2(1) */
	2,	/* po4(2) */
	3	/* po8(3) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_po_NumPerPEI_r17_specs_2 = {
	asn_MAP_NR_po_NumPerPEI_r17_value2enum_2,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_po_NumPerPEI_r17_enum2value_2,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_po_NumPerPEI_r17_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_po_NumPerPEI_r17_2 = {
	"po-NumPerPEI-r17",
	"po-NumPerPEI-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_po_NumPerPEI_r17_tags_2,
	sizeof(asn_DEF_NR_po_NumPerPEI_r17_tags_2)
		/sizeof(asn_DEF_NR_po_NumPerPEI_r17_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_po_NumPerPEI_r17_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_po_NumPerPEI_r17_tags_2)
		/sizeof(asn_DEF_NR_po_NumPerPEI_r17_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_po_NumPerPEI_r17_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_po_NumPerPEI_r17_specs_2	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_lastUsedCellOnly_r17_value2enum_10[] = {
	{ 0,	4,	"true" }
};
static const unsigned int asn_MAP_NR_lastUsedCellOnly_r17_enum2value_10[] = {
	0	/* true(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_lastUsedCellOnly_r17_specs_10 = {
	asn_MAP_NR_lastUsedCellOnly_r17_value2enum_10,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_lastUsedCellOnly_r17_enum2value_10,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_lastUsedCellOnly_r17_tags_10[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_lastUsedCellOnly_r17_10 = {
	"lastUsedCellOnly-r17",
	"lastUsedCellOnly-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_lastUsedCellOnly_r17_tags_10,
	sizeof(asn_DEF_NR_lastUsedCellOnly_r17_tags_10)
		/sizeof(asn_DEF_NR_lastUsedCellOnly_r17_tags_10[0]) - 1, /* 1 */
	asn_DEF_NR_lastUsedCellOnly_r17_tags_10,	/* Same as above */
	sizeof(asn_DEF_NR_lastUsedCellOnly_r17_tags_10)
		/sizeof(asn_DEF_NR_lastUsedCellOnly_r17_tags_10[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_lastUsedCellOnly_r17_constr_10,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_lastUsedCellOnly_r17_specs_10	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_PEI_Config_r17_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PEI_Config_r17, po_NumPerPEI_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_po_NumPerPEI_r17_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"po-NumPerPEI-r17"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PEI_Config_r17, payloadSizeDCI_2_7_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_payloadSizeDCI_2_7_r17_constr_7,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_payloadSizeDCI_2_7_r17_constraint_1
		},
		0, 0, /* No default value */
		"payloadSizeDCI-2-7-r17"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PEI_Config_r17, pei_FrameOffset_r17),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_pei_FrameOffset_r17_constr_8,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_pei_FrameOffset_r17_constraint_1
		},
		0, 0, /* No default value */
		"pei-FrameOffset-r17"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PEI_Config_r17, subgroupConfig_r17),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_SubgroupConfig_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"subgroupConfig-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PEI_Config_r17, lastUsedCellOnly_r17),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_lastUsedCellOnly_r17_10,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"lastUsedCellOnly-r17"
		},
};
static const int asn_MAP_NR_PEI_Config_r17_oms_1[] = { 4 };
static const ber_tlv_tag_t asn_DEF_NR_PEI_Config_r17_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_PEI_Config_r17_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* po-NumPerPEI-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* payloadSizeDCI-2-7-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* pei-FrameOffset-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* subgroupConfig-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 } /* lastUsedCellOnly-r17 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_PEI_Config_r17_specs_1 = {
	sizeof(struct NR_PEI_Config_r17),
	offsetof(struct NR_PEI_Config_r17, _asn_ctx),
	asn_MAP_NR_PEI_Config_r17_tag2el_1,
	5,	/* Count of tags in the map */
	asn_MAP_NR_PEI_Config_r17_oms_1,	/* Optional members */
	1, 0,	/* Root/Additions */
	5,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_PEI_Config_r17 = {
	"PEI-Config-r17",
	"PEI-Config-r17",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_PEI_Config_r17_tags_1,
	sizeof(asn_DEF_NR_PEI_Config_r17_tags_1)
		/sizeof(asn_DEF_NR_PEI_Config_r17_tags_1[0]), /* 1 */
	asn_DEF_NR_PEI_Config_r17_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_PEI_Config_r17_tags_1)
		/sizeof(asn_DEF_NR_PEI_Config_r17_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_PEI_Config_r17_1,
	5,	/* Elements count */
	&asn_SPC_NR_PEI_Config_r17_specs_1	/* Additional specs */
};

