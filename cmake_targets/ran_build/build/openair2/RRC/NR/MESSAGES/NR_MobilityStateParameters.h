/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_MobilityStateParameters_H_
#define	_NR_MobilityStateParameters_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <NativeInteger.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_MobilityStateParameters__t_Evaluation {
	NR_MobilityStateParameters__t_Evaluation_s30	= 0,
	NR_MobilityStateParameters__t_Evaluation_s60	= 1,
	NR_MobilityStateParameters__t_Evaluation_s120	= 2,
	NR_MobilityStateParameters__t_Evaluation_s180	= 3,
	NR_MobilityStateParameters__t_Evaluation_s240	= 4,
	NR_MobilityStateParameters__t_Evaluation_spare3	= 5,
	NR_MobilityStateParameters__t_Evaluation_spare2	= 6,
	NR_MobilityStateParameters__t_Evaluation_spare1	= 7
} e_NR_MobilityStateParameters__t_Evaluation;
typedef enum NR_MobilityStateParameters__t_HystNormal {
	NR_MobilityStateParameters__t_HystNormal_s30	= 0,
	NR_MobilityStateParameters__t_HystNormal_s60	= 1,
	NR_MobilityStateParameters__t_HystNormal_s120	= 2,
	NR_MobilityStateParameters__t_HystNormal_s180	= 3,
	NR_MobilityStateParameters__t_HystNormal_s240	= 4,
	NR_MobilityStateParameters__t_HystNormal_spare3	= 5,
	NR_MobilityStateParameters__t_HystNormal_spare2	= 6,
	NR_MobilityStateParameters__t_HystNormal_spare1	= 7
} e_NR_MobilityStateParameters__t_HystNormal;

/* NR_MobilityStateParameters */
typedef struct NR_MobilityStateParameters {
	long	 t_Evaluation;
	long	 t_HystNormal;
	long	 n_CellChangeMedium;
	long	 n_CellChangeHigh;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_MobilityStateParameters_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_t_Evaluation_2;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_t_HystNormal_11;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_MobilityStateParameters;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_MobilityStateParameters_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_MobilityStateParameters_1[4];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_MobilityStateParameters_H_ */
#include <asn_internal.h>
