/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_RMTC-Config-r16.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_rmtc_SubframeOffset_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 639L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_rmtc_Periodicity_r16_constr_2 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  4 }	/* (0..4) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_measDurationSymbols_r16_constr_9 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  4 }	/* (0..4) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ref_SCS_CP_r16_constr_16 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_rmtc_Bandwidth_r17_constr_23 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  4 }	/* (0..4) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_measDurationSymbols_v1700_constr_29 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ref_SCS_CP_v1700_constr_33 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_rmtc_SubframeOffset_r16_constr_8 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 10,  10,  0,  639 }	/* (0..639) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_rmtc_Periodicity_r16_value2enum_2[] = {
	{ 0,	4,	"ms40" },
	{ 1,	4,	"ms80" },
	{ 2,	5,	"ms160" },
	{ 3,	5,	"ms320" },
	{ 4,	5,	"ms640" }
};
static const unsigned int asn_MAP_NR_rmtc_Periodicity_r16_enum2value_2[] = {
	2,	/* ms160(2) */
	3,	/* ms320(3) */
	0,	/* ms40(0) */
	4,	/* ms640(4) */
	1	/* ms80(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_rmtc_Periodicity_r16_specs_2 = {
	asn_MAP_NR_rmtc_Periodicity_r16_value2enum_2,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_rmtc_Periodicity_r16_enum2value_2,	/* N => "tag"; sorted by N */
	5,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_rmtc_Periodicity_r16_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_rmtc_Periodicity_r16_2 = {
	"rmtc-Periodicity-r16",
	"rmtc-Periodicity-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_rmtc_Periodicity_r16_tags_2,
	sizeof(asn_DEF_NR_rmtc_Periodicity_r16_tags_2)
		/sizeof(asn_DEF_NR_rmtc_Periodicity_r16_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_rmtc_Periodicity_r16_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_rmtc_Periodicity_r16_tags_2)
		/sizeof(asn_DEF_NR_rmtc_Periodicity_r16_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_rmtc_Periodicity_r16_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_rmtc_Periodicity_r16_specs_2	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_measDurationSymbols_r16_value2enum_9[] = {
	{ 0,	4,	"sym1" },
	{ 1,	9,	"sym14or12" },
	{ 2,	9,	"sym28or24" },
	{ 3,	9,	"sym42or36" },
	{ 4,	9,	"sym70or60" }
};
static const unsigned int asn_MAP_NR_measDurationSymbols_r16_enum2value_9[] = {
	0,	/* sym1(0) */
	1,	/* sym14or12(1) */
	2,	/* sym28or24(2) */
	3,	/* sym42or36(3) */
	4	/* sym70or60(4) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_measDurationSymbols_r16_specs_9 = {
	asn_MAP_NR_measDurationSymbols_r16_value2enum_9,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_measDurationSymbols_r16_enum2value_9,	/* N => "tag"; sorted by N */
	5,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_measDurationSymbols_r16_tags_9[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_measDurationSymbols_r16_9 = {
	"measDurationSymbols-r16",
	"measDurationSymbols-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_measDurationSymbols_r16_tags_9,
	sizeof(asn_DEF_NR_measDurationSymbols_r16_tags_9)
		/sizeof(asn_DEF_NR_measDurationSymbols_r16_tags_9[0]) - 1, /* 1 */
	asn_DEF_NR_measDurationSymbols_r16_tags_9,	/* Same as above */
	sizeof(asn_DEF_NR_measDurationSymbols_r16_tags_9)
		/sizeof(asn_DEF_NR_measDurationSymbols_r16_tags_9[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_measDurationSymbols_r16_constr_9,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_measDurationSymbols_r16_specs_9	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_ref_SCS_CP_r16_value2enum_16[] = {
	{ 0,	5,	"kHz15" },
	{ 1,	5,	"kHz30" },
	{ 2,	9,	"kHz60-NCP" },
	{ 3,	9,	"kHz60-ECP" }
};
static const unsigned int asn_MAP_NR_ref_SCS_CP_r16_enum2value_16[] = {
	0,	/* kHz15(0) */
	1,	/* kHz30(1) */
	3,	/* kHz60-ECP(3) */
	2	/* kHz60-NCP(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ref_SCS_CP_r16_specs_16 = {
	asn_MAP_NR_ref_SCS_CP_r16_value2enum_16,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ref_SCS_CP_r16_enum2value_16,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ref_SCS_CP_r16_tags_16[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ref_SCS_CP_r16_16 = {
	"ref-SCS-CP-r16",
	"ref-SCS-CP-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ref_SCS_CP_r16_tags_16,
	sizeof(asn_DEF_NR_ref_SCS_CP_r16_tags_16)
		/sizeof(asn_DEF_NR_ref_SCS_CP_r16_tags_16[0]) - 1, /* 1 */
	asn_DEF_NR_ref_SCS_CP_r16_tags_16,	/* Same as above */
	sizeof(asn_DEF_NR_ref_SCS_CP_r16_tags_16)
		/sizeof(asn_DEF_NR_ref_SCS_CP_r16_tags_16[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ref_SCS_CP_r16_constr_16,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ref_SCS_CP_r16_specs_16	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_rmtc_Bandwidth_r17_value2enum_23[] = {
	{ 0,	6,	"mhz100" },
	{ 1,	6,	"mhz400" },
	{ 2,	6,	"mhz800" },
	{ 3,	7,	"mhz1600" },
	{ 4,	7,	"mhz2000" }
};
static const unsigned int asn_MAP_NR_rmtc_Bandwidth_r17_enum2value_23[] = {
	0,	/* mhz100(0) */
	3,	/* mhz1600(3) */
	4,	/* mhz2000(4) */
	1,	/* mhz400(1) */
	2	/* mhz800(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_rmtc_Bandwidth_r17_specs_23 = {
	asn_MAP_NR_rmtc_Bandwidth_r17_value2enum_23,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_rmtc_Bandwidth_r17_enum2value_23,	/* N => "tag"; sorted by N */
	5,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_rmtc_Bandwidth_r17_tags_23[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_rmtc_Bandwidth_r17_23 = {
	"rmtc-Bandwidth-r17",
	"rmtc-Bandwidth-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_rmtc_Bandwidth_r17_tags_23,
	sizeof(asn_DEF_NR_rmtc_Bandwidth_r17_tags_23)
		/sizeof(asn_DEF_NR_rmtc_Bandwidth_r17_tags_23[0]) - 1, /* 1 */
	asn_DEF_NR_rmtc_Bandwidth_r17_tags_23,	/* Same as above */
	sizeof(asn_DEF_NR_rmtc_Bandwidth_r17_tags_23)
		/sizeof(asn_DEF_NR_rmtc_Bandwidth_r17_tags_23[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_rmtc_Bandwidth_r17_constr_23,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_rmtc_Bandwidth_r17_specs_23	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_measDurationSymbols_v1700_value2enum_29[] = {
	{ 0,	6,	"sym140" },
	{ 1,	6,	"sym560" },
	{ 2,	7,	"sym1120" }
};
static const unsigned int asn_MAP_NR_measDurationSymbols_v1700_enum2value_29[] = {
	2,	/* sym1120(2) */
	0,	/* sym140(0) */
	1	/* sym560(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_measDurationSymbols_v1700_specs_29 = {
	asn_MAP_NR_measDurationSymbols_v1700_value2enum_29,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_measDurationSymbols_v1700_enum2value_29,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_measDurationSymbols_v1700_tags_29[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_measDurationSymbols_v1700_29 = {
	"measDurationSymbols-v1700",
	"measDurationSymbols-v1700",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_measDurationSymbols_v1700_tags_29,
	sizeof(asn_DEF_NR_measDurationSymbols_v1700_tags_29)
		/sizeof(asn_DEF_NR_measDurationSymbols_v1700_tags_29[0]) - 1, /* 1 */
	asn_DEF_NR_measDurationSymbols_v1700_tags_29,	/* Same as above */
	sizeof(asn_DEF_NR_measDurationSymbols_v1700_tags_29)
		/sizeof(asn_DEF_NR_measDurationSymbols_v1700_tags_29[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_measDurationSymbols_v1700_constr_29,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_measDurationSymbols_v1700_specs_29	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_ref_SCS_CP_v1700_value2enum_33[] = {
	{ 0,	6,	"kHz120" },
	{ 1,	6,	"kHz480" },
	{ 2,	6,	"kHz960" }
};
static const unsigned int asn_MAP_NR_ref_SCS_CP_v1700_enum2value_33[] = {
	0,	/* kHz120(0) */
	1,	/* kHz480(1) */
	2	/* kHz960(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ref_SCS_CP_v1700_specs_33 = {
	asn_MAP_NR_ref_SCS_CP_v1700_value2enum_33,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ref_SCS_CP_v1700_enum2value_33,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ref_SCS_CP_v1700_tags_33[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ref_SCS_CP_v1700_33 = {
	"ref-SCS-CP-v1700",
	"ref-SCS-CP-v1700",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ref_SCS_CP_v1700_tags_33,
	sizeof(asn_DEF_NR_ref_SCS_CP_v1700_tags_33)
		/sizeof(asn_DEF_NR_ref_SCS_CP_v1700_tags_33[0]) - 1, /* 1 */
	asn_DEF_NR_ref_SCS_CP_v1700_tags_33,	/* Same as above */
	sizeof(asn_DEF_NR_ref_SCS_CP_v1700_tags_33)
		/sizeof(asn_DEF_NR_ref_SCS_CP_v1700_tags_33[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ref_SCS_CP_v1700_constr_33,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ref_SCS_CP_v1700_specs_33	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_tci_StateInfo_r17_37[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RMTC_Config_r16__ext1__tci_StateInfo_r17, tci_StateId_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_TCI_StateId,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"tci-StateId-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RMTC_Config_r16__ext1__tci_StateInfo_r17, ref_ServCellId_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ServCellIndex,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ref-ServCellId-r17"
		},
};
static const int asn_MAP_NR_tci_StateInfo_r17_oms_37[] = { 1 };
static const ber_tlv_tag_t asn_DEF_NR_tci_StateInfo_r17_tags_37[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_tci_StateInfo_r17_tag2el_37[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* tci-StateId-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* ref-ServCellId-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_tci_StateInfo_r17_specs_37 = {
	sizeof(struct NR_RMTC_Config_r16__ext1__tci_StateInfo_r17),
	offsetof(struct NR_RMTC_Config_r16__ext1__tci_StateInfo_r17, _asn_ctx),
	asn_MAP_NR_tci_StateInfo_r17_tag2el_37,
	2,	/* Count of tags in the map */
	asn_MAP_NR_tci_StateInfo_r17_oms_37,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_tci_StateInfo_r17_37 = {
	"tci-StateInfo-r17",
	"tci-StateInfo-r17",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_tci_StateInfo_r17_tags_37,
	sizeof(asn_DEF_NR_tci_StateInfo_r17_tags_37)
		/sizeof(asn_DEF_NR_tci_StateInfo_r17_tags_37[0]) - 1, /* 1 */
	asn_DEF_NR_tci_StateInfo_r17_tags_37,	/* Same as above */
	sizeof(asn_DEF_NR_tci_StateInfo_r17_tags_37)
		/sizeof(asn_DEF_NR_tci_StateInfo_r17_tags_37[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_tci_StateInfo_r17_37,
	2,	/* Elements count */
	&asn_SPC_NR_tci_StateInfo_r17_specs_37	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext1_22[] = {
	{ ATF_POINTER, 4, offsetof(struct NR_RMTC_Config_r16__ext1, rmtc_Bandwidth_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_rmtc_Bandwidth_r17_23,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"rmtc-Bandwidth-r17"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_RMTC_Config_r16__ext1, measDurationSymbols_v1700),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_measDurationSymbols_v1700_29,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"measDurationSymbols-v1700"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_RMTC_Config_r16__ext1, ref_SCS_CP_v1700),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ref_SCS_CP_v1700_33,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ref-SCS-CP-v1700"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RMTC_Config_r16__ext1, tci_StateInfo_r17),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		0,
		&asn_DEF_NR_tci_StateInfo_r17_37,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"tci-StateInfo-r17"
		},
};
static const int asn_MAP_NR_ext1_oms_22[] = { 0, 1, 2, 3 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_22[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_22[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* rmtc-Bandwidth-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* measDurationSymbols-v1700 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* ref-SCS-CP-v1700 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 } /* tci-StateInfo-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_22 = {
	sizeof(struct NR_RMTC_Config_r16__ext1),
	offsetof(struct NR_RMTC_Config_r16__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_22,
	4,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_22,	/* Optional members */
	4, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_22 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_22,
	sizeof(asn_DEF_NR_ext1_tags_22)
		/sizeof(asn_DEF_NR_ext1_tags_22[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_22,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_22)
		/sizeof(asn_DEF_NR_ext1_tags_22[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_22,
	4,	/* Elements count */
	&asn_SPC_NR_ext1_specs_22	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext2_40[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_RMTC_Config_r16__ext2, ref_BWPId_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BWP_Id,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ref-BWPId-r17"
		},
};
static const int asn_MAP_NR_ext2_oms_40[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext2_tags_40[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext2_tag2el_40[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* ref-BWPId-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext2_specs_40 = {
	sizeof(struct NR_RMTC_Config_r16__ext2),
	offsetof(struct NR_RMTC_Config_r16__ext2, _asn_ctx),
	asn_MAP_NR_ext2_tag2el_40,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext2_oms_40,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext2_40 = {
	"ext2",
	"ext2",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext2_tags_40,
	sizeof(asn_DEF_NR_ext2_tags_40)
		/sizeof(asn_DEF_NR_ext2_tags_40[0]) - 1, /* 1 */
	asn_DEF_NR_ext2_tags_40,	/* Same as above */
	sizeof(asn_DEF_NR_ext2_tags_40)
		/sizeof(asn_DEF_NR_ext2_tags_40[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext2_40,
	1,	/* Elements count */
	&asn_SPC_NR_ext2_specs_40	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_RMTC_Config_r16_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RMTC_Config_r16, rmtc_Periodicity_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_rmtc_Periodicity_r16_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"rmtc-Periodicity-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RMTC_Config_r16, rmtc_SubframeOffset_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_rmtc_SubframeOffset_r16_constr_8,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_rmtc_SubframeOffset_r16_constraint_1
		},
		0, 0, /* No default value */
		"rmtc-SubframeOffset-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RMTC_Config_r16, measDurationSymbols_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_measDurationSymbols_r16_9,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"measDurationSymbols-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RMTC_Config_r16, rmtc_Frequency_r16),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ARFCN_ValueNR,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"rmtc-Frequency-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RMTC_Config_r16, ref_SCS_CP_r16),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ref_SCS_CP_r16_16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ref-SCS-CP-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_RMTC_Config_r16, ext1),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		0,
		&asn_DEF_NR_ext1_22,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RMTC_Config_r16, ext2),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		0,
		&asn_DEF_NR_ext2_40,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext2"
		},
};
static const int asn_MAP_NR_RMTC_Config_r16_oms_1[] = { 1, 5, 6 };
static const ber_tlv_tag_t asn_DEF_NR_RMTC_Config_r16_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_RMTC_Config_r16_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* rmtc-Periodicity-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* rmtc-SubframeOffset-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* measDurationSymbols-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* rmtc-Frequency-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* ref-SCS-CP-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* ext1 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 } /* ext2 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_RMTC_Config_r16_specs_1 = {
	sizeof(struct NR_RMTC_Config_r16),
	offsetof(struct NR_RMTC_Config_r16, _asn_ctx),
	asn_MAP_NR_RMTC_Config_r16_tag2el_1,
	7,	/* Count of tags in the map */
	asn_MAP_NR_RMTC_Config_r16_oms_1,	/* Optional members */
	1, 2,	/* Root/Additions */
	5,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_RMTC_Config_r16 = {
	"RMTC-Config-r16",
	"RMTC-Config-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_RMTC_Config_r16_tags_1,
	sizeof(asn_DEF_NR_RMTC_Config_r16_tags_1)
		/sizeof(asn_DEF_NR_RMTC_Config_r16_tags_1[0]), /* 1 */
	asn_DEF_NR_RMTC_Config_r16_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_RMTC_Config_r16_tags_1)
		/sizeof(asn_DEF_NR_RMTC_Config_r16_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_RMTC_Config_r16_1,
	7,	/* Elements count */
	&asn_SPC_NR_RMTC_Config_r16_specs_1	/* Additional specs */
};

