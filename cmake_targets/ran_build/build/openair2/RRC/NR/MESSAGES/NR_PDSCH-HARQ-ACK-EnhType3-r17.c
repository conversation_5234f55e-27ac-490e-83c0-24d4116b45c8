/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_PDSCH-HARQ-ACK-EnhType3-r17.h"

static int
memb_NativeInteger_constraint_4(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 1L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_BIT_STRING_constraint_6(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	const BIT_STRING_t *st = (const BIT_STRING_t *)sptr;
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	if(st->size > 0) {
		/* Size in bits */
		size = 8 * st->size - (st->bits_unused & 0x07);
	} else {
		size = 0;
	}
	
	if((size == 16UL)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_perCC_constraint_3(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 32UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_perHARQ_constraint_3(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 32UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_Member_constr_5 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_perCC_constr_4 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 5,  5,  1,  32 }	/* (SIZE(1..32)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_Member_constr_7 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 0,  0,  16,  16 }	/* (SIZE(16..16)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_perHARQ_constr_6 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 5,  5,  1,  32 }	/* (SIZE(1..32)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_perCC_constr_4 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 5,  5,  1,  32 }	/* (SIZE(1..32)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_perHARQ_constr_6 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 5,  5,  1,  32 }	/* (SIZE(1..32)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_applicable_r17_constr_3 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pdsch_HARQ_ACK_EnhType3NDI_r17_constr_8 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pdsch_HARQ_ACK_EnhType3CBG_r17_constr_10 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static asn_TYPE_member_t asn_MBR_NR_perCC_4[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (2 << 2)),
		0,
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_Member_constr_5,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NativeInteger_constraint_4
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_perCC_tags_4[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_perCC_specs_4 = {
	sizeof(struct NR_PDSCH_HARQ_ACK_EnhType3_r17__applicable_r17__perCC),
	offsetof(struct NR_PDSCH_HARQ_ACK_EnhType3_r17__applicable_r17__perCC, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_perCC_4 = {
	"perCC",
	"perCC",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_perCC_tags_4,
	sizeof(asn_DEF_NR_perCC_tags_4)
		/sizeof(asn_DEF_NR_perCC_tags_4[0]) - 1, /* 1 */
	asn_DEF_NR_perCC_tags_4,	/* Same as above */
	sizeof(asn_DEF_NR_perCC_tags_4)
		/sizeof(asn_DEF_NR_perCC_tags_4[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_perCC_constr_4,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_perCC_4,
	1,	/* Single element */
	&asn_SPC_NR_perCC_specs_4	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_perHARQ_6[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (3 << 2)),
		0,
		&asn_DEF_BIT_STRING,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_Member_constr_7,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_BIT_STRING_constraint_6
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_perHARQ_tags_6[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_perHARQ_specs_6 = {
	sizeof(struct NR_PDSCH_HARQ_ACK_EnhType3_r17__applicable_r17__perHARQ),
	offsetof(struct NR_PDSCH_HARQ_ACK_EnhType3_r17__applicable_r17__perHARQ, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_perHARQ_6 = {
	"perHARQ",
	"perHARQ",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_perHARQ_tags_6,
	sizeof(asn_DEF_NR_perHARQ_tags_6)
		/sizeof(asn_DEF_NR_perHARQ_tags_6[0]) - 1, /* 1 */
	asn_DEF_NR_perHARQ_tags_6,	/* Same as above */
	sizeof(asn_DEF_NR_perHARQ_tags_6)
		/sizeof(asn_DEF_NR_perHARQ_tags_6[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_perHARQ_constr_6,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_perHARQ_6,
	1,	/* Single element */
	&asn_SPC_NR_perHARQ_specs_6	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_applicable_r17_3[] = {
	{ ATF_POINTER, 0, offsetof(struct NR_PDSCH_HARQ_ACK_EnhType3_r17__applicable_r17, choice.perCC),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_perCC_4,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_perCC_constr_4,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_perCC_constraint_3
		},
		0, 0, /* No default value */
		"perCC"
		},
	{ ATF_POINTER, 0, offsetof(struct NR_PDSCH_HARQ_ACK_EnhType3_r17__applicable_r17, choice.perHARQ),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_perHARQ_6,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_perHARQ_constr_6,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_perHARQ_constraint_3
		},
		0, 0, /* No default value */
		"perHARQ"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_NR_applicable_r17_tag2el_3[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* perCC */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* perHARQ */
};
static asn_CHOICE_specifics_t asn_SPC_NR_applicable_r17_specs_3 = {
	sizeof(struct NR_PDSCH_HARQ_ACK_EnhType3_r17__applicable_r17),
	offsetof(struct NR_PDSCH_HARQ_ACK_EnhType3_r17__applicable_r17, _asn_ctx),
	offsetof(struct NR_PDSCH_HARQ_ACK_EnhType3_r17__applicable_r17, present),
	sizeof(((struct NR_PDSCH_HARQ_ACK_EnhType3_r17__applicable_r17 *)0)->present),
	asn_MAP_NR_applicable_r17_tag2el_3,
	2,	/* Count of tags in the map */
	0, 0,
	-1	/* Extensions start */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_applicable_r17_3 = {
	"applicable-r17",
	"applicable-r17",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_applicable_r17_constr_3,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		CHOICE_constraint
	},
	asn_MBR_NR_applicable_r17_3,
	2,	/* Elements count */
	&asn_SPC_NR_applicable_r17_specs_3	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pdsch_HARQ_ACK_EnhType3NDI_r17_value2enum_8[] = {
	{ 0,	4,	"true" }
};
static const unsigned int asn_MAP_NR_pdsch_HARQ_ACK_EnhType3NDI_r17_enum2value_8[] = {
	0	/* true(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pdsch_HARQ_ACK_EnhType3NDI_r17_specs_8 = {
	asn_MAP_NR_pdsch_HARQ_ACK_EnhType3NDI_r17_value2enum_8,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pdsch_HARQ_ACK_EnhType3NDI_r17_enum2value_8,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pdsch_HARQ_ACK_EnhType3NDI_r17_tags_8[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pdsch_HARQ_ACK_EnhType3NDI_r17_8 = {
	"pdsch-HARQ-ACK-EnhType3NDI-r17",
	"pdsch-HARQ-ACK-EnhType3NDI-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pdsch_HARQ_ACK_EnhType3NDI_r17_tags_8,
	sizeof(asn_DEF_NR_pdsch_HARQ_ACK_EnhType3NDI_r17_tags_8)
		/sizeof(asn_DEF_NR_pdsch_HARQ_ACK_EnhType3NDI_r17_tags_8[0]) - 1, /* 1 */
	asn_DEF_NR_pdsch_HARQ_ACK_EnhType3NDI_r17_tags_8,	/* Same as above */
	sizeof(asn_DEF_NR_pdsch_HARQ_ACK_EnhType3NDI_r17_tags_8)
		/sizeof(asn_DEF_NR_pdsch_HARQ_ACK_EnhType3NDI_r17_tags_8[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pdsch_HARQ_ACK_EnhType3NDI_r17_constr_8,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pdsch_HARQ_ACK_EnhType3NDI_r17_specs_8	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pdsch_HARQ_ACK_EnhType3CBG_r17_value2enum_10[] = {
	{ 0,	4,	"true" }
};
static const unsigned int asn_MAP_NR_pdsch_HARQ_ACK_EnhType3CBG_r17_enum2value_10[] = {
	0	/* true(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pdsch_HARQ_ACK_EnhType3CBG_r17_specs_10 = {
	asn_MAP_NR_pdsch_HARQ_ACK_EnhType3CBG_r17_value2enum_10,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pdsch_HARQ_ACK_EnhType3CBG_r17_enum2value_10,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pdsch_HARQ_ACK_EnhType3CBG_r17_tags_10[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pdsch_HARQ_ACK_EnhType3CBG_r17_10 = {
	"pdsch-HARQ-ACK-EnhType3CBG-r17",
	"pdsch-HARQ-ACK-EnhType3CBG-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pdsch_HARQ_ACK_EnhType3CBG_r17_tags_10,
	sizeof(asn_DEF_NR_pdsch_HARQ_ACK_EnhType3CBG_r17_tags_10)
		/sizeof(asn_DEF_NR_pdsch_HARQ_ACK_EnhType3CBG_r17_tags_10[0]) - 1, /* 1 */
	asn_DEF_NR_pdsch_HARQ_ACK_EnhType3CBG_r17_tags_10,	/* Same as above */
	sizeof(asn_DEF_NR_pdsch_HARQ_ACK_EnhType3CBG_r17_tags_10)
		/sizeof(asn_DEF_NR_pdsch_HARQ_ACK_EnhType3CBG_r17_tags_10[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pdsch_HARQ_ACK_EnhType3CBG_r17_constr_10,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pdsch_HARQ_ACK_EnhType3CBG_r17_specs_10	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_PDSCH_HARQ_ACK_EnhType3_r17_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PDSCH_HARQ_ACK_EnhType3_r17, pdsch_HARQ_ACK_EnhType3Index_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_PDSCH_HARQ_ACK_EnhType3Index_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pdsch-HARQ-ACK-EnhType3Index-r17"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PDSCH_HARQ_ACK_EnhType3_r17, applicable_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_applicable_r17_3,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"applicable-r17"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_PDSCH_HARQ_ACK_EnhType3_r17, pdsch_HARQ_ACK_EnhType3NDI_r17),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pdsch_HARQ_ACK_EnhType3NDI_r17_8,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pdsch-HARQ-ACK-EnhType3NDI-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PDSCH_HARQ_ACK_EnhType3_r17, pdsch_HARQ_ACK_EnhType3CBG_r17),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pdsch_HARQ_ACK_EnhType3CBG_r17_10,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pdsch-HARQ-ACK-EnhType3CBG-r17"
		},
};
static const int asn_MAP_NR_PDSCH_HARQ_ACK_EnhType3_r17_oms_1[] = { 2, 3 };
static const ber_tlv_tag_t asn_DEF_NR_PDSCH_HARQ_ACK_EnhType3_r17_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_PDSCH_HARQ_ACK_EnhType3_r17_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* pdsch-HARQ-ACK-EnhType3Index-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* applicable-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* pdsch-HARQ-ACK-EnhType3NDI-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 } /* pdsch-HARQ-ACK-EnhType3CBG-r17 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_PDSCH_HARQ_ACK_EnhType3_r17_specs_1 = {
	sizeof(struct NR_PDSCH_HARQ_ACK_EnhType3_r17),
	offsetof(struct NR_PDSCH_HARQ_ACK_EnhType3_r17, _asn_ctx),
	asn_MAP_NR_PDSCH_HARQ_ACK_EnhType3_r17_tag2el_1,
	4,	/* Count of tags in the map */
	asn_MAP_NR_PDSCH_HARQ_ACK_EnhType3_r17_oms_1,	/* Optional members */
	2, 0,	/* Root/Additions */
	4,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_PDSCH_HARQ_ACK_EnhType3_r17 = {
	"PDSCH-HARQ-ACK-EnhType3-r17",
	"PDSCH-HARQ-ACK-EnhType3-r17",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_PDSCH_HARQ_ACK_EnhType3_r17_tags_1,
	sizeof(asn_DEF_NR_PDSCH_HARQ_ACK_EnhType3_r17_tags_1)
		/sizeof(asn_DEF_NR_PDSCH_HARQ_ACK_EnhType3_r17_tags_1[0]), /* 1 */
	asn_DEF_NR_PDSCH_HARQ_ACK_EnhType3_r17_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_PDSCH_HARQ_ACK_EnhType3_r17_tags_1)
		/sizeof(asn_DEF_NR_PDSCH_HARQ_ACK_EnhType3_r17_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_PDSCH_HARQ_ACK_EnhType3_r17_1,
	4,	/* Elements count */
	&asn_SPC_NR_PDSCH_HARQ_ACK_EnhType3_r17_specs_1	/* Additional specs */
};

