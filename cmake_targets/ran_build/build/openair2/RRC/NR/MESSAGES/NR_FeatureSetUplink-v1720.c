/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_FeatureSetUplink-v1720.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_maxNumberCarriers_r17_constraint_11(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 16L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_phy_PrioritizationLowPriorityDG_HighPriorityCG_r17_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 16L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pucch_Repetition_F0_1_2_3_4_RRC_Config_r17_constr_2 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pucch_Repetition_F0_1_2_3_4_DynamicIndication_r17_constr_4 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_interSubslotFreqHopping_PUCCH_r17_constr_6 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_semiStaticHARQ_ACK_CodebookSub_SlotPUCCH_r17_constr_8 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pusch_PreparationLowPriority_r17_constr_12 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_scs_15kHz_r17_constr_17 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_scs_30kHz_r17_constr_21 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  4 }	/* (0..4) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_scs_60kHz_r17_constr_27 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  8 }	/* (0..8) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_scs_120kHz_r17_constr_37 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 5,  5,  0,  16 }	/* (0..16) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_maxNumberCarriers_r17_constr_55 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (1..16) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_extendedDC_LocationReport_r17_constr_56 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_phy_PrioritizationLowPriorityDG_HighPriorityCG_r17_constr_10 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (1..16) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_pucch_Repetition_F0_1_2_3_4_RRC_Config_r17_value2enum_2[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_pucch_Repetition_F0_1_2_3_4_RRC_Config_r17_enum2value_2[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pucch_Repetition_F0_1_2_3_4_RRC_Config_r17_specs_2 = {
	asn_MAP_NR_pucch_Repetition_F0_1_2_3_4_RRC_Config_r17_value2enum_2,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pucch_Repetition_F0_1_2_3_4_RRC_Config_r17_enum2value_2,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pucch_Repetition_F0_1_2_3_4_RRC_Config_r17_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pucch_Repetition_F0_1_2_3_4_RRC_Config_r17_2 = {
	"pucch-Repetition-F0-1-2-3-4-RRC-Config-r17",
	"pucch-Repetition-F0-1-2-3-4-RRC-Config-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pucch_Repetition_F0_1_2_3_4_RRC_Config_r17_tags_2,
	sizeof(asn_DEF_NR_pucch_Repetition_F0_1_2_3_4_RRC_Config_r17_tags_2)
		/sizeof(asn_DEF_NR_pucch_Repetition_F0_1_2_3_4_RRC_Config_r17_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_pucch_Repetition_F0_1_2_3_4_RRC_Config_r17_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_pucch_Repetition_F0_1_2_3_4_RRC_Config_r17_tags_2)
		/sizeof(asn_DEF_NR_pucch_Repetition_F0_1_2_3_4_RRC_Config_r17_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pucch_Repetition_F0_1_2_3_4_RRC_Config_r17_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pucch_Repetition_F0_1_2_3_4_RRC_Config_r17_specs_2	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pucch_Repetition_F0_1_2_3_4_DynamicIndication_r17_value2enum_4[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_pucch_Repetition_F0_1_2_3_4_DynamicIndication_r17_enum2value_4[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pucch_Repetition_F0_1_2_3_4_DynamicIndication_r17_specs_4 = {
	asn_MAP_NR_pucch_Repetition_F0_1_2_3_4_DynamicIndication_r17_value2enum_4,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pucch_Repetition_F0_1_2_3_4_DynamicIndication_r17_enum2value_4,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pucch_Repetition_F0_1_2_3_4_DynamicIndication_r17_tags_4[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pucch_Repetition_F0_1_2_3_4_DynamicIndication_r17_4 = {
	"pucch-Repetition-F0-1-2-3-4-DynamicIndication-r17",
	"pucch-Repetition-F0-1-2-3-4-DynamicIndication-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pucch_Repetition_F0_1_2_3_4_DynamicIndication_r17_tags_4,
	sizeof(asn_DEF_NR_pucch_Repetition_F0_1_2_3_4_DynamicIndication_r17_tags_4)
		/sizeof(asn_DEF_NR_pucch_Repetition_F0_1_2_3_4_DynamicIndication_r17_tags_4[0]) - 1, /* 1 */
	asn_DEF_NR_pucch_Repetition_F0_1_2_3_4_DynamicIndication_r17_tags_4,	/* Same as above */
	sizeof(asn_DEF_NR_pucch_Repetition_F0_1_2_3_4_DynamicIndication_r17_tags_4)
		/sizeof(asn_DEF_NR_pucch_Repetition_F0_1_2_3_4_DynamicIndication_r17_tags_4[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pucch_Repetition_F0_1_2_3_4_DynamicIndication_r17_constr_4,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pucch_Repetition_F0_1_2_3_4_DynamicIndication_r17_specs_4	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_interSubslotFreqHopping_PUCCH_r17_value2enum_6[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_interSubslotFreqHopping_PUCCH_r17_enum2value_6[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_interSubslotFreqHopping_PUCCH_r17_specs_6 = {
	asn_MAP_NR_interSubslotFreqHopping_PUCCH_r17_value2enum_6,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_interSubslotFreqHopping_PUCCH_r17_enum2value_6,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_interSubslotFreqHopping_PUCCH_r17_tags_6[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_interSubslotFreqHopping_PUCCH_r17_6 = {
	"interSubslotFreqHopping-PUCCH-r17",
	"interSubslotFreqHopping-PUCCH-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_interSubslotFreqHopping_PUCCH_r17_tags_6,
	sizeof(asn_DEF_NR_interSubslotFreqHopping_PUCCH_r17_tags_6)
		/sizeof(asn_DEF_NR_interSubslotFreqHopping_PUCCH_r17_tags_6[0]) - 1, /* 1 */
	asn_DEF_NR_interSubslotFreqHopping_PUCCH_r17_tags_6,	/* Same as above */
	sizeof(asn_DEF_NR_interSubslotFreqHopping_PUCCH_r17_tags_6)
		/sizeof(asn_DEF_NR_interSubslotFreqHopping_PUCCH_r17_tags_6[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_interSubslotFreqHopping_PUCCH_r17_constr_6,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_interSubslotFreqHopping_PUCCH_r17_specs_6	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_semiStaticHARQ_ACK_CodebookSub_SlotPUCCH_r17_value2enum_8[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_semiStaticHARQ_ACK_CodebookSub_SlotPUCCH_r17_enum2value_8[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_semiStaticHARQ_ACK_CodebookSub_SlotPUCCH_r17_specs_8 = {
	asn_MAP_NR_semiStaticHARQ_ACK_CodebookSub_SlotPUCCH_r17_value2enum_8,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_semiStaticHARQ_ACK_CodebookSub_SlotPUCCH_r17_enum2value_8,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_semiStaticHARQ_ACK_CodebookSub_SlotPUCCH_r17_tags_8[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_semiStaticHARQ_ACK_CodebookSub_SlotPUCCH_r17_8 = {
	"semiStaticHARQ-ACK-CodebookSub-SlotPUCCH-r17",
	"semiStaticHARQ-ACK-CodebookSub-SlotPUCCH-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_semiStaticHARQ_ACK_CodebookSub_SlotPUCCH_r17_tags_8,
	sizeof(asn_DEF_NR_semiStaticHARQ_ACK_CodebookSub_SlotPUCCH_r17_tags_8)
		/sizeof(asn_DEF_NR_semiStaticHARQ_ACK_CodebookSub_SlotPUCCH_r17_tags_8[0]) - 1, /* 1 */
	asn_DEF_NR_semiStaticHARQ_ACK_CodebookSub_SlotPUCCH_r17_tags_8,	/* Same as above */
	sizeof(asn_DEF_NR_semiStaticHARQ_ACK_CodebookSub_SlotPUCCH_r17_tags_8)
		/sizeof(asn_DEF_NR_semiStaticHARQ_ACK_CodebookSub_SlotPUCCH_r17_tags_8[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_semiStaticHARQ_ACK_CodebookSub_SlotPUCCH_r17_constr_8,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_semiStaticHARQ_ACK_CodebookSub_SlotPUCCH_r17_specs_8	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pusch_PreparationLowPriority_r17_value2enum_12[] = {
	{ 0,	4,	"sym0" },
	{ 1,	4,	"sym1" },
	{ 2,	4,	"sym2" }
};
static const unsigned int asn_MAP_NR_pusch_PreparationLowPriority_r17_enum2value_12[] = {
	0,	/* sym0(0) */
	1,	/* sym1(1) */
	2	/* sym2(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pusch_PreparationLowPriority_r17_specs_12 = {
	asn_MAP_NR_pusch_PreparationLowPriority_r17_value2enum_12,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pusch_PreparationLowPriority_r17_enum2value_12,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pusch_PreparationLowPriority_r17_tags_12[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pusch_PreparationLowPriority_r17_12 = {
	"pusch-PreparationLowPriority-r17",
	"pusch-PreparationLowPriority-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pusch_PreparationLowPriority_r17_tags_12,
	sizeof(asn_DEF_NR_pusch_PreparationLowPriority_r17_tags_12)
		/sizeof(asn_DEF_NR_pusch_PreparationLowPriority_r17_tags_12[0]) - 1, /* 1 */
	asn_DEF_NR_pusch_PreparationLowPriority_r17_tags_12,	/* Same as above */
	sizeof(asn_DEF_NR_pusch_PreparationLowPriority_r17_tags_12)
		/sizeof(asn_DEF_NR_pusch_PreparationLowPriority_r17_tags_12[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pusch_PreparationLowPriority_r17_constr_12,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pusch_PreparationLowPriority_r17_specs_12	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_scs_15kHz_r17_value2enum_17[] = {
	{ 0,	4,	"sym0" },
	{ 1,	4,	"sym1" },
	{ 2,	4,	"sym2" }
};
static const unsigned int asn_MAP_NR_scs_15kHz_r17_enum2value_17[] = {
	0,	/* sym0(0) */
	1,	/* sym1(1) */
	2	/* sym2(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_scs_15kHz_r17_specs_17 = {
	asn_MAP_NR_scs_15kHz_r17_value2enum_17,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_scs_15kHz_r17_enum2value_17,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_scs_15kHz_r17_tags_17[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_scs_15kHz_r17_17 = {
	"scs-15kHz-r17",
	"scs-15kHz-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_scs_15kHz_r17_tags_17,
	sizeof(asn_DEF_NR_scs_15kHz_r17_tags_17)
		/sizeof(asn_DEF_NR_scs_15kHz_r17_tags_17[0]) - 1, /* 1 */
	asn_DEF_NR_scs_15kHz_r17_tags_17,	/* Same as above */
	sizeof(asn_DEF_NR_scs_15kHz_r17_tags_17)
		/sizeof(asn_DEF_NR_scs_15kHz_r17_tags_17[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_scs_15kHz_r17_constr_17,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_scs_15kHz_r17_specs_17	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_scs_30kHz_r17_value2enum_21[] = {
	{ 0,	4,	"sym0" },
	{ 1,	4,	"sym1" },
	{ 2,	4,	"sym2" },
	{ 3,	4,	"sym3" },
	{ 4,	4,	"sym4" }
};
static const unsigned int asn_MAP_NR_scs_30kHz_r17_enum2value_21[] = {
	0,	/* sym0(0) */
	1,	/* sym1(1) */
	2,	/* sym2(2) */
	3,	/* sym3(3) */
	4	/* sym4(4) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_scs_30kHz_r17_specs_21 = {
	asn_MAP_NR_scs_30kHz_r17_value2enum_21,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_scs_30kHz_r17_enum2value_21,	/* N => "tag"; sorted by N */
	5,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_scs_30kHz_r17_tags_21[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_scs_30kHz_r17_21 = {
	"scs-30kHz-r17",
	"scs-30kHz-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_scs_30kHz_r17_tags_21,
	sizeof(asn_DEF_NR_scs_30kHz_r17_tags_21)
		/sizeof(asn_DEF_NR_scs_30kHz_r17_tags_21[0]) - 1, /* 1 */
	asn_DEF_NR_scs_30kHz_r17_tags_21,	/* Same as above */
	sizeof(asn_DEF_NR_scs_30kHz_r17_tags_21)
		/sizeof(asn_DEF_NR_scs_30kHz_r17_tags_21[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_scs_30kHz_r17_constr_21,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_scs_30kHz_r17_specs_21	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_scs_60kHz_r17_value2enum_27[] = {
	{ 0,	4,	"sym0" },
	{ 1,	4,	"sym1" },
	{ 2,	4,	"sym2" },
	{ 3,	4,	"sym3" },
	{ 4,	4,	"sym4" },
	{ 5,	4,	"sym5" },
	{ 6,	4,	"sym6" },
	{ 7,	4,	"sym7" },
	{ 8,	4,	"sym8" }
};
static const unsigned int asn_MAP_NR_scs_60kHz_r17_enum2value_27[] = {
	0,	/* sym0(0) */
	1,	/* sym1(1) */
	2,	/* sym2(2) */
	3,	/* sym3(3) */
	4,	/* sym4(4) */
	5,	/* sym5(5) */
	6,	/* sym6(6) */
	7,	/* sym7(7) */
	8	/* sym8(8) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_scs_60kHz_r17_specs_27 = {
	asn_MAP_NR_scs_60kHz_r17_value2enum_27,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_scs_60kHz_r17_enum2value_27,	/* N => "tag"; sorted by N */
	9,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_scs_60kHz_r17_tags_27[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_scs_60kHz_r17_27 = {
	"scs-60kHz-r17",
	"scs-60kHz-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_scs_60kHz_r17_tags_27,
	sizeof(asn_DEF_NR_scs_60kHz_r17_tags_27)
		/sizeof(asn_DEF_NR_scs_60kHz_r17_tags_27[0]) - 1, /* 1 */
	asn_DEF_NR_scs_60kHz_r17_tags_27,	/* Same as above */
	sizeof(asn_DEF_NR_scs_60kHz_r17_tags_27)
		/sizeof(asn_DEF_NR_scs_60kHz_r17_tags_27[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_scs_60kHz_r17_constr_27,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_scs_60kHz_r17_specs_27	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_scs_120kHz_r17_value2enum_37[] = {
	{ 0,	4,	"sym0" },
	{ 1,	4,	"sym1" },
	{ 2,	4,	"sym2" },
	{ 3,	4,	"sym3" },
	{ 4,	4,	"sym4" },
	{ 5,	4,	"sym5" },
	{ 6,	4,	"sym6" },
	{ 7,	4,	"sym7" },
	{ 8,	4,	"sym8" },
	{ 9,	4,	"sym9" },
	{ 10,	5,	"sym10" },
	{ 11,	5,	"sym11" },
	{ 12,	5,	"sym12" },
	{ 13,	5,	"sym13" },
	{ 14,	5,	"sym14" },
	{ 15,	5,	"sym15" },
	{ 16,	5,	"sym16" }
};
static const unsigned int asn_MAP_NR_scs_120kHz_r17_enum2value_37[] = {
	0,	/* sym0(0) */
	1,	/* sym1(1) */
	10,	/* sym10(10) */
	11,	/* sym11(11) */
	12,	/* sym12(12) */
	13,	/* sym13(13) */
	14,	/* sym14(14) */
	15,	/* sym15(15) */
	16,	/* sym16(16) */
	2,	/* sym2(2) */
	3,	/* sym3(3) */
	4,	/* sym4(4) */
	5,	/* sym5(5) */
	6,	/* sym6(6) */
	7,	/* sym7(7) */
	8,	/* sym8(8) */
	9	/* sym9(9) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_scs_120kHz_r17_specs_37 = {
	asn_MAP_NR_scs_120kHz_r17_value2enum_37,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_scs_120kHz_r17_enum2value_37,	/* N => "tag"; sorted by N */
	17,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_scs_120kHz_r17_tags_37[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_scs_120kHz_r17_37 = {
	"scs-120kHz-r17",
	"scs-120kHz-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_scs_120kHz_r17_tags_37,
	sizeof(asn_DEF_NR_scs_120kHz_r17_tags_37)
		/sizeof(asn_DEF_NR_scs_120kHz_r17_tags_37[0]) - 1, /* 1 */
	asn_DEF_NR_scs_120kHz_r17_tags_37,	/* Same as above */
	sizeof(asn_DEF_NR_scs_120kHz_r17_tags_37)
		/sizeof(asn_DEF_NR_scs_120kHz_r17_tags_37[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_scs_120kHz_r17_constr_37,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_scs_120kHz_r17_specs_37	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_additionalCancellationTime_r17_16[] = {
	{ ATF_POINTER, 4, offsetof(struct NR_FeatureSetUplink_v1720__phy_PrioritizationHighPriorityDG_LowPriorityCG_r17__additionalCancellationTime_r17, scs_15kHz_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_scs_15kHz_r17_17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs-15kHz-r17"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_FeatureSetUplink_v1720__phy_PrioritizationHighPriorityDG_LowPriorityCG_r17__additionalCancellationTime_r17, scs_30kHz_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_scs_30kHz_r17_21,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs-30kHz-r17"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_FeatureSetUplink_v1720__phy_PrioritizationHighPriorityDG_LowPriorityCG_r17__additionalCancellationTime_r17, scs_60kHz_r17),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_scs_60kHz_r17_27,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs-60kHz-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_FeatureSetUplink_v1720__phy_PrioritizationHighPriorityDG_LowPriorityCG_r17__additionalCancellationTime_r17, scs_120kHz_r17),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_scs_120kHz_r17_37,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs-120kHz-r17"
		},
};
static const int asn_MAP_NR_additionalCancellationTime_r17_oms_16[] = { 0, 1, 2, 3 };
static const ber_tlv_tag_t asn_DEF_NR_additionalCancellationTime_r17_tags_16[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_additionalCancellationTime_r17_tag2el_16[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* scs-15kHz-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* scs-30kHz-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* scs-60kHz-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 } /* scs-120kHz-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_additionalCancellationTime_r17_specs_16 = {
	sizeof(struct NR_FeatureSetUplink_v1720__phy_PrioritizationHighPriorityDG_LowPriorityCG_r17__additionalCancellationTime_r17),
	offsetof(struct NR_FeatureSetUplink_v1720__phy_PrioritizationHighPriorityDG_LowPriorityCG_r17__additionalCancellationTime_r17, _asn_ctx),
	asn_MAP_NR_additionalCancellationTime_r17_tag2el_16,
	4,	/* Count of tags in the map */
	asn_MAP_NR_additionalCancellationTime_r17_oms_16,	/* Optional members */
	4, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_additionalCancellationTime_r17_16 = {
	"additionalCancellationTime-r17",
	"additionalCancellationTime-r17",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_additionalCancellationTime_r17_tags_16,
	sizeof(asn_DEF_NR_additionalCancellationTime_r17_tags_16)
		/sizeof(asn_DEF_NR_additionalCancellationTime_r17_tags_16[0]) - 1, /* 1 */
	asn_DEF_NR_additionalCancellationTime_r17_tags_16,	/* Same as above */
	sizeof(asn_DEF_NR_additionalCancellationTime_r17_tags_16)
		/sizeof(asn_DEF_NR_additionalCancellationTime_r17_tags_16[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_additionalCancellationTime_r17_16,
	4,	/* Elements count */
	&asn_SPC_NR_additionalCancellationTime_r17_specs_16	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_phy_PrioritizationHighPriorityDG_LowPriorityCG_r17_11[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_FeatureSetUplink_v1720__phy_PrioritizationHighPriorityDG_LowPriorityCG_r17, pusch_PreparationLowPriority_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pusch_PreparationLowPriority_r17_12,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pusch-PreparationLowPriority-r17"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_FeatureSetUplink_v1720__phy_PrioritizationHighPriorityDG_LowPriorityCG_r17, additionalCancellationTime_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_additionalCancellationTime_r17_16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"additionalCancellationTime-r17"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_FeatureSetUplink_v1720__phy_PrioritizationHighPriorityDG_LowPriorityCG_r17, maxNumberCarriers_r17),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_maxNumberCarriers_r17_constr_55,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_maxNumberCarriers_r17_constraint_11
		},
		0, 0, /* No default value */
		"maxNumberCarriers-r17"
		},
};
static const ber_tlv_tag_t asn_DEF_NR_phy_PrioritizationHighPriorityDG_LowPriorityCG_r17_tags_11[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_phy_PrioritizationHighPriorityDG_LowPriorityCG_r17_tag2el_11[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* pusch-PreparationLowPriority-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* additionalCancellationTime-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* maxNumberCarriers-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_phy_PrioritizationHighPriorityDG_LowPriorityCG_r17_specs_11 = {
	sizeof(struct NR_FeatureSetUplink_v1720__phy_PrioritizationHighPriorityDG_LowPriorityCG_r17),
	offsetof(struct NR_FeatureSetUplink_v1720__phy_PrioritizationHighPriorityDG_LowPriorityCG_r17, _asn_ctx),
	asn_MAP_NR_phy_PrioritizationHighPriorityDG_LowPriorityCG_r17_tag2el_11,
	3,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_phy_PrioritizationHighPriorityDG_LowPriorityCG_r17_11 = {
	"phy-PrioritizationHighPriorityDG-LowPriorityCG-r17",
	"phy-PrioritizationHighPriorityDG-LowPriorityCG-r17",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_phy_PrioritizationHighPriorityDG_LowPriorityCG_r17_tags_11,
	sizeof(asn_DEF_NR_phy_PrioritizationHighPriorityDG_LowPriorityCG_r17_tags_11)
		/sizeof(asn_DEF_NR_phy_PrioritizationHighPriorityDG_LowPriorityCG_r17_tags_11[0]) - 1, /* 1 */
	asn_DEF_NR_phy_PrioritizationHighPriorityDG_LowPriorityCG_r17_tags_11,	/* Same as above */
	sizeof(asn_DEF_NR_phy_PrioritizationHighPriorityDG_LowPriorityCG_r17_tags_11)
		/sizeof(asn_DEF_NR_phy_PrioritizationHighPriorityDG_LowPriorityCG_r17_tags_11[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_phy_PrioritizationHighPriorityDG_LowPriorityCG_r17_11,
	3,	/* Elements count */
	&asn_SPC_NR_phy_PrioritizationHighPriorityDG_LowPriorityCG_r17_specs_11	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_extendedDC_LocationReport_r17_value2enum_56[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_extendedDC_LocationReport_r17_enum2value_56[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_extendedDC_LocationReport_r17_specs_56 = {
	asn_MAP_NR_extendedDC_LocationReport_r17_value2enum_56,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_extendedDC_LocationReport_r17_enum2value_56,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_extendedDC_LocationReport_r17_tags_56[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_extendedDC_LocationReport_r17_56 = {
	"extendedDC-LocationReport-r17",
	"extendedDC-LocationReport-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_extendedDC_LocationReport_r17_tags_56,
	sizeof(asn_DEF_NR_extendedDC_LocationReport_r17_tags_56)
		/sizeof(asn_DEF_NR_extendedDC_LocationReport_r17_tags_56[0]) - 1, /* 1 */
	asn_DEF_NR_extendedDC_LocationReport_r17_tags_56,	/* Same as above */
	sizeof(asn_DEF_NR_extendedDC_LocationReport_r17_tags_56)
		/sizeof(asn_DEF_NR_extendedDC_LocationReport_r17_tags_56[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_extendedDC_LocationReport_r17_constr_56,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_extendedDC_LocationReport_r17_specs_56	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_FeatureSetUplink_v1720_1[] = {
	{ ATF_POINTER, 7, offsetof(struct NR_FeatureSetUplink_v1720, pucch_Repetition_F0_1_2_3_4_RRC_Config_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pucch_Repetition_F0_1_2_3_4_RRC_Config_r17_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pucch-Repetition-F0-1-2-3-4-RRC-Config-r17"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_FeatureSetUplink_v1720, pucch_Repetition_F0_1_2_3_4_DynamicIndication_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pucch_Repetition_F0_1_2_3_4_DynamicIndication_r17_4,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pucch-Repetition-F0-1-2-3-4-DynamicIndication-r17"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_FeatureSetUplink_v1720, interSubslotFreqHopping_PUCCH_r17),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_interSubslotFreqHopping_PUCCH_r17_6,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"interSubslotFreqHopping-PUCCH-r17"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_FeatureSetUplink_v1720, semiStaticHARQ_ACK_CodebookSub_SlotPUCCH_r17),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_semiStaticHARQ_ACK_CodebookSub_SlotPUCCH_r17_8,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"semiStaticHARQ-ACK-CodebookSub-SlotPUCCH-r17"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_FeatureSetUplink_v1720, phy_PrioritizationLowPriorityDG_HighPriorityCG_r17),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_phy_PrioritizationLowPriorityDG_HighPriorityCG_r17_constr_10,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_phy_PrioritizationLowPriorityDG_HighPriorityCG_r17_constraint_1
		},
		0, 0, /* No default value */
		"phy-PrioritizationLowPriorityDG-HighPriorityCG-r17"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_FeatureSetUplink_v1720, phy_PrioritizationHighPriorityDG_LowPriorityCG_r17),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		0,
		&asn_DEF_NR_phy_PrioritizationHighPriorityDG_LowPriorityCG_r17_11,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"phy-PrioritizationHighPriorityDG-LowPriorityCG-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_FeatureSetUplink_v1720, extendedDC_LocationReport_r17),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_extendedDC_LocationReport_r17_56,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"extendedDC-LocationReport-r17"
		},
};
static const int asn_MAP_NR_FeatureSetUplink_v1720_oms_1[] = { 0, 1, 2, 3, 4, 5, 6 };
static const ber_tlv_tag_t asn_DEF_NR_FeatureSetUplink_v1720_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_FeatureSetUplink_v1720_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* pucch-Repetition-F0-1-2-3-4-RRC-Config-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* pucch-Repetition-F0-1-2-3-4-DynamicIndication-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* interSubslotFreqHopping-PUCCH-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* semiStaticHARQ-ACK-CodebookSub-SlotPUCCH-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* phy-PrioritizationLowPriorityDG-HighPriorityCG-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* phy-PrioritizationHighPriorityDG-LowPriorityCG-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 } /* extendedDC-LocationReport-r17 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_FeatureSetUplink_v1720_specs_1 = {
	sizeof(struct NR_FeatureSetUplink_v1720),
	offsetof(struct NR_FeatureSetUplink_v1720, _asn_ctx),
	asn_MAP_NR_FeatureSetUplink_v1720_tag2el_1,
	7,	/* Count of tags in the map */
	asn_MAP_NR_FeatureSetUplink_v1720_oms_1,	/* Optional members */
	7, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_FeatureSetUplink_v1720 = {
	"FeatureSetUplink-v1720",
	"FeatureSetUplink-v1720",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_FeatureSetUplink_v1720_tags_1,
	sizeof(asn_DEF_NR_FeatureSetUplink_v1720_tags_1)
		/sizeof(asn_DEF_NR_FeatureSetUplink_v1720_tags_1[0]), /* 1 */
	asn_DEF_NR_FeatureSetUplink_v1720_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_FeatureSetUplink_v1720_tags_1)
		/sizeof(asn_DEF_NR_FeatureSetUplink_v1720_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_FeatureSetUplink_v1720_1,
	7,	/* Elements count */
	&asn_SPC_NR_FeatureSetUplink_v1720_specs_1	/* Additional specs */
};

