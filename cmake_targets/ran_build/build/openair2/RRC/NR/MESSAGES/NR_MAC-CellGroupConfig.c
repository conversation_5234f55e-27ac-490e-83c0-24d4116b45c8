/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_MAC-CellGroupConfig.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_g_RNTI_ConfigToAddModList_r17_constraint_25(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 16UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_g_RNTI_ConfigToReleaseList_r17_constraint_25(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 16UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_g_CS_RNTI_ConfigToAddModList_r17_constraint_25(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 8UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_g_CS_RNTI_ConfigToReleaseList_r17_constraint_25(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 8UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_usePreBSR_r16_constr_13 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_lch_BasedPrioritization_r16_constr_16 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_enhancedSkipUplinkTxDynamic_r16_constr_21 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_enhancedSkipUplinkTxConfigured_r16_constr_23 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_intraCG_Prioritization_r17_constr_26 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_g_RNTI_ConfigToAddModList_r17_constr_34 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_g_RNTI_ConfigToReleaseList_r17_constr_36 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_g_CS_RNTI_ConfigToAddModList_r17_constr_38 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (SIZE(1..8)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_g_CS_RNTI_ConfigToReleaseList_r17_constr_40 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (SIZE(1..8)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_g_RNTI_ConfigToAddModList_r17_constr_34 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_g_RNTI_ConfigToReleaseList_r17_constr_36 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_g_CS_RNTI_ConfigToAddModList_r17_constr_38 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (SIZE(1..8)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_g_CS_RNTI_ConfigToReleaseList_r17_constr_40 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (SIZE(1..8)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_drx_LastTransmissionUL_r17_constr_45 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static asn_TYPE_member_t asn_MBR_NR_ext1_9[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_MAC_CellGroupConfig__ext1, csi_Mask),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_BOOLEAN,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"csi-Mask"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MAC_CellGroupConfig__ext1, dataInactivityTimer),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_DataInactivityTimer,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dataInactivityTimer"
		},
};
static const int asn_MAP_NR_ext1_oms_9[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_9[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_9[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* csi-Mask */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* dataInactivityTimer */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_9 = {
	sizeof(struct NR_MAC_CellGroupConfig__ext1),
	offsetof(struct NR_MAC_CellGroupConfig__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_9,
	2,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_9,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_9 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_9,
	sizeof(asn_DEF_NR_ext1_tags_9)
		/sizeof(asn_DEF_NR_ext1_tags_9[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_9,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_9)
		/sizeof(asn_DEF_NR_ext1_tags_9[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_9,
	2,	/* Elements count */
	&asn_SPC_NR_ext1_specs_9	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_usePreBSR_r16_value2enum_13[] = {
	{ 0,	4,	"true" }
};
static const unsigned int asn_MAP_NR_usePreBSR_r16_enum2value_13[] = {
	0	/* true(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_usePreBSR_r16_specs_13 = {
	asn_MAP_NR_usePreBSR_r16_value2enum_13,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_usePreBSR_r16_enum2value_13,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_usePreBSR_r16_tags_13[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_usePreBSR_r16_13 = {
	"usePreBSR-r16",
	"usePreBSR-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_usePreBSR_r16_tags_13,
	sizeof(asn_DEF_NR_usePreBSR_r16_tags_13)
		/sizeof(asn_DEF_NR_usePreBSR_r16_tags_13[0]) - 1, /* 1 */
	asn_DEF_NR_usePreBSR_r16_tags_13,	/* Same as above */
	sizeof(asn_DEF_NR_usePreBSR_r16_tags_13)
		/sizeof(asn_DEF_NR_usePreBSR_r16_tags_13[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_usePreBSR_r16_constr_13,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_usePreBSR_r16_specs_13	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_lch_BasedPrioritization_r16_value2enum_16[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_lch_BasedPrioritization_r16_enum2value_16[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_lch_BasedPrioritization_r16_specs_16 = {
	asn_MAP_NR_lch_BasedPrioritization_r16_value2enum_16,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_lch_BasedPrioritization_r16_enum2value_16,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_lch_BasedPrioritization_r16_tags_16[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_lch_BasedPrioritization_r16_16 = {
	"lch-BasedPrioritization-r16",
	"lch-BasedPrioritization-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_lch_BasedPrioritization_r16_tags_16,
	sizeof(asn_DEF_NR_lch_BasedPrioritization_r16_tags_16)
		/sizeof(asn_DEF_NR_lch_BasedPrioritization_r16_tags_16[0]) - 1, /* 1 */
	asn_DEF_NR_lch_BasedPrioritization_r16_tags_16,	/* Same as above */
	sizeof(asn_DEF_NR_lch_BasedPrioritization_r16_tags_16)
		/sizeof(asn_DEF_NR_lch_BasedPrioritization_r16_tags_16[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_lch_BasedPrioritization_r16_constr_16,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_lch_BasedPrioritization_r16_specs_16	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext2_12[] = {
	{ ATF_POINTER, 5, offsetof(struct NR_MAC_CellGroupConfig__ext2, usePreBSR_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_usePreBSR_r16_13,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"usePreBSR-r16"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_MAC_CellGroupConfig__ext2, schedulingRequestID_LBT_SCell_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_SchedulingRequestId,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"schedulingRequestID-LBT-SCell-r16"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_MAC_CellGroupConfig__ext2, lch_BasedPrioritization_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_lch_BasedPrioritization_r16_16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"lch-BasedPrioritization-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_MAC_CellGroupConfig__ext2, schedulingRequestID_BFR_SCell_r16),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_SchedulingRequestId,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"schedulingRequestID-BFR-SCell-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MAC_CellGroupConfig__ext2, drx_ConfigSecondaryGroup_r16),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_DRX_ConfigSecondaryGroup_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"drx-ConfigSecondaryGroup-r16"
		},
};
static const int asn_MAP_NR_ext2_oms_12[] = { 0, 1, 2, 3, 4 };
static const ber_tlv_tag_t asn_DEF_NR_ext2_tags_12[] = {
	(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext2_tag2el_12[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* usePreBSR-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* schedulingRequestID-LBT-SCell-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* lch-BasedPrioritization-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* schedulingRequestID-BFR-SCell-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 } /* drx-ConfigSecondaryGroup-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext2_specs_12 = {
	sizeof(struct NR_MAC_CellGroupConfig__ext2),
	offsetof(struct NR_MAC_CellGroupConfig__ext2, _asn_ctx),
	asn_MAP_NR_ext2_tag2el_12,
	5,	/* Count of tags in the map */
	asn_MAP_NR_ext2_oms_12,	/* Optional members */
	5, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext2_12 = {
	"ext2",
	"ext2",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext2_tags_12,
	sizeof(asn_DEF_NR_ext2_tags_12)
		/sizeof(asn_DEF_NR_ext2_tags_12[0]) - 1, /* 1 */
	asn_DEF_NR_ext2_tags_12,	/* Same as above */
	sizeof(asn_DEF_NR_ext2_tags_12)
		/sizeof(asn_DEF_NR_ext2_tags_12[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext2_12,
	5,	/* Elements count */
	&asn_SPC_NR_ext2_specs_12	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_enhancedSkipUplinkTxDynamic_r16_value2enum_21[] = {
	{ 0,	4,	"true" }
};
static const unsigned int asn_MAP_NR_enhancedSkipUplinkTxDynamic_r16_enum2value_21[] = {
	0	/* true(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_enhancedSkipUplinkTxDynamic_r16_specs_21 = {
	asn_MAP_NR_enhancedSkipUplinkTxDynamic_r16_value2enum_21,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_enhancedSkipUplinkTxDynamic_r16_enum2value_21,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_enhancedSkipUplinkTxDynamic_r16_tags_21[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_enhancedSkipUplinkTxDynamic_r16_21 = {
	"enhancedSkipUplinkTxDynamic-r16",
	"enhancedSkipUplinkTxDynamic-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_enhancedSkipUplinkTxDynamic_r16_tags_21,
	sizeof(asn_DEF_NR_enhancedSkipUplinkTxDynamic_r16_tags_21)
		/sizeof(asn_DEF_NR_enhancedSkipUplinkTxDynamic_r16_tags_21[0]) - 1, /* 1 */
	asn_DEF_NR_enhancedSkipUplinkTxDynamic_r16_tags_21,	/* Same as above */
	sizeof(asn_DEF_NR_enhancedSkipUplinkTxDynamic_r16_tags_21)
		/sizeof(asn_DEF_NR_enhancedSkipUplinkTxDynamic_r16_tags_21[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_enhancedSkipUplinkTxDynamic_r16_constr_21,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_enhancedSkipUplinkTxDynamic_r16_specs_21	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_enhancedSkipUplinkTxConfigured_r16_value2enum_23[] = {
	{ 0,	4,	"true" }
};
static const unsigned int asn_MAP_NR_enhancedSkipUplinkTxConfigured_r16_enum2value_23[] = {
	0	/* true(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_enhancedSkipUplinkTxConfigured_r16_specs_23 = {
	asn_MAP_NR_enhancedSkipUplinkTxConfigured_r16_value2enum_23,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_enhancedSkipUplinkTxConfigured_r16_enum2value_23,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_enhancedSkipUplinkTxConfigured_r16_tags_23[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_enhancedSkipUplinkTxConfigured_r16_23 = {
	"enhancedSkipUplinkTxConfigured-r16",
	"enhancedSkipUplinkTxConfigured-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_enhancedSkipUplinkTxConfigured_r16_tags_23,
	sizeof(asn_DEF_NR_enhancedSkipUplinkTxConfigured_r16_tags_23)
		/sizeof(asn_DEF_NR_enhancedSkipUplinkTxConfigured_r16_tags_23[0]) - 1, /* 1 */
	asn_DEF_NR_enhancedSkipUplinkTxConfigured_r16_tags_23,	/* Same as above */
	sizeof(asn_DEF_NR_enhancedSkipUplinkTxConfigured_r16_tags_23)
		/sizeof(asn_DEF_NR_enhancedSkipUplinkTxConfigured_r16_tags_23[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_enhancedSkipUplinkTxConfigured_r16_constr_23,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_enhancedSkipUplinkTxConfigured_r16_specs_23	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext3_20[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_MAC_CellGroupConfig__ext3, enhancedSkipUplinkTxDynamic_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_enhancedSkipUplinkTxDynamic_r16_21,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"enhancedSkipUplinkTxDynamic-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MAC_CellGroupConfig__ext3, enhancedSkipUplinkTxConfigured_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_enhancedSkipUplinkTxConfigured_r16_23,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"enhancedSkipUplinkTxConfigured-r16"
		},
};
static const int asn_MAP_NR_ext3_oms_20[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_ext3_tags_20[] = {
	(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext3_tag2el_20[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* enhancedSkipUplinkTxDynamic-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* enhancedSkipUplinkTxConfigured-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext3_specs_20 = {
	sizeof(struct NR_MAC_CellGroupConfig__ext3),
	offsetof(struct NR_MAC_CellGroupConfig__ext3, _asn_ctx),
	asn_MAP_NR_ext3_tag2el_20,
	2,	/* Count of tags in the map */
	asn_MAP_NR_ext3_oms_20,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext3_20 = {
	"ext3",
	"ext3",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext3_tags_20,
	sizeof(asn_DEF_NR_ext3_tags_20)
		/sizeof(asn_DEF_NR_ext3_tags_20[0]) - 1, /* 1 */
	asn_DEF_NR_ext3_tags_20,	/* Same as above */
	sizeof(asn_DEF_NR_ext3_tags_20)
		/sizeof(asn_DEF_NR_ext3_tags_20[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext3_20,
	2,	/* Elements count */
	&asn_SPC_NR_ext3_specs_20	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_intraCG_Prioritization_r17_value2enum_26[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_intraCG_Prioritization_r17_enum2value_26[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_intraCG_Prioritization_r17_specs_26 = {
	asn_MAP_NR_intraCG_Prioritization_r17_value2enum_26,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_intraCG_Prioritization_r17_enum2value_26,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_intraCG_Prioritization_r17_tags_26[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_intraCG_Prioritization_r17_26 = {
	"intraCG-Prioritization-r17",
	"intraCG-Prioritization-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_intraCG_Prioritization_r17_tags_26,
	sizeof(asn_DEF_NR_intraCG_Prioritization_r17_tags_26)
		/sizeof(asn_DEF_NR_intraCG_Prioritization_r17_tags_26[0]) - 1, /* 1 */
	asn_DEF_NR_intraCG_Prioritization_r17_tags_26,	/* Same as above */
	sizeof(asn_DEF_NR_intraCG_Prioritization_r17_tags_26)
		/sizeof(asn_DEF_NR_intraCG_Prioritization_r17_tags_26[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_intraCG_Prioritization_r17_constr_26,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_intraCG_Prioritization_r17_specs_26	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_g_RNTI_ConfigToAddModList_r17_34[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_MBS_RNTI_SpecificConfig_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_g_RNTI_ConfigToAddModList_r17_tags_34[] = {
	(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_g_RNTI_ConfigToAddModList_r17_specs_34 = {
	sizeof(struct NR_MAC_CellGroupConfig__ext4__g_RNTI_ConfigToAddModList_r17),
	offsetof(struct NR_MAC_CellGroupConfig__ext4__g_RNTI_ConfigToAddModList_r17, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_g_RNTI_ConfigToAddModList_r17_34 = {
	"g-RNTI-ConfigToAddModList-r17",
	"g-RNTI-ConfigToAddModList-r17",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_g_RNTI_ConfigToAddModList_r17_tags_34,
	sizeof(asn_DEF_NR_g_RNTI_ConfigToAddModList_r17_tags_34)
		/sizeof(asn_DEF_NR_g_RNTI_ConfigToAddModList_r17_tags_34[0]) - 1, /* 1 */
	asn_DEF_NR_g_RNTI_ConfigToAddModList_r17_tags_34,	/* Same as above */
	sizeof(asn_DEF_NR_g_RNTI_ConfigToAddModList_r17_tags_34)
		/sizeof(asn_DEF_NR_g_RNTI_ConfigToAddModList_r17_tags_34[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_g_RNTI_ConfigToAddModList_r17_constr_34,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_g_RNTI_ConfigToAddModList_r17_34,
	1,	/* Single element */
	&asn_SPC_NR_g_RNTI_ConfigToAddModList_r17_specs_34	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_g_RNTI_ConfigToReleaseList_r17_36[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (2 << 2)),
		0,
		&asn_DEF_NR_MBS_RNTI_SpecificConfigId_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_g_RNTI_ConfigToReleaseList_r17_tags_36[] = {
	(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_g_RNTI_ConfigToReleaseList_r17_specs_36 = {
	sizeof(struct NR_MAC_CellGroupConfig__ext4__g_RNTI_ConfigToReleaseList_r17),
	offsetof(struct NR_MAC_CellGroupConfig__ext4__g_RNTI_ConfigToReleaseList_r17, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_g_RNTI_ConfigToReleaseList_r17_36 = {
	"g-RNTI-ConfigToReleaseList-r17",
	"g-RNTI-ConfigToReleaseList-r17",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_g_RNTI_ConfigToReleaseList_r17_tags_36,
	sizeof(asn_DEF_NR_g_RNTI_ConfigToReleaseList_r17_tags_36)
		/sizeof(asn_DEF_NR_g_RNTI_ConfigToReleaseList_r17_tags_36[0]) - 1, /* 1 */
	asn_DEF_NR_g_RNTI_ConfigToReleaseList_r17_tags_36,	/* Same as above */
	sizeof(asn_DEF_NR_g_RNTI_ConfigToReleaseList_r17_tags_36)
		/sizeof(asn_DEF_NR_g_RNTI_ConfigToReleaseList_r17_tags_36[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_g_RNTI_ConfigToReleaseList_r17_constr_36,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_g_RNTI_ConfigToReleaseList_r17_36,
	1,	/* Single element */
	&asn_SPC_NR_g_RNTI_ConfigToReleaseList_r17_specs_36	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_g_CS_RNTI_ConfigToAddModList_r17_38[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_MBS_RNTI_SpecificConfig_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_g_CS_RNTI_ConfigToAddModList_r17_tags_38[] = {
	(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_g_CS_RNTI_ConfigToAddModList_r17_specs_38 = {
	sizeof(struct NR_MAC_CellGroupConfig__ext4__g_CS_RNTI_ConfigToAddModList_r17),
	offsetof(struct NR_MAC_CellGroupConfig__ext4__g_CS_RNTI_ConfigToAddModList_r17, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_g_CS_RNTI_ConfigToAddModList_r17_38 = {
	"g-CS-RNTI-ConfigToAddModList-r17",
	"g-CS-RNTI-ConfigToAddModList-r17",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_g_CS_RNTI_ConfigToAddModList_r17_tags_38,
	sizeof(asn_DEF_NR_g_CS_RNTI_ConfigToAddModList_r17_tags_38)
		/sizeof(asn_DEF_NR_g_CS_RNTI_ConfigToAddModList_r17_tags_38[0]) - 1, /* 1 */
	asn_DEF_NR_g_CS_RNTI_ConfigToAddModList_r17_tags_38,	/* Same as above */
	sizeof(asn_DEF_NR_g_CS_RNTI_ConfigToAddModList_r17_tags_38)
		/sizeof(asn_DEF_NR_g_CS_RNTI_ConfigToAddModList_r17_tags_38[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_g_CS_RNTI_ConfigToAddModList_r17_constr_38,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_g_CS_RNTI_ConfigToAddModList_r17_38,
	1,	/* Single element */
	&asn_SPC_NR_g_CS_RNTI_ConfigToAddModList_r17_specs_38	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_g_CS_RNTI_ConfigToReleaseList_r17_40[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (2 << 2)),
		0,
		&asn_DEF_NR_MBS_RNTI_SpecificConfigId_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_g_CS_RNTI_ConfigToReleaseList_r17_tags_40[] = {
	(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_g_CS_RNTI_ConfigToReleaseList_r17_specs_40 = {
	sizeof(struct NR_MAC_CellGroupConfig__ext4__g_CS_RNTI_ConfigToReleaseList_r17),
	offsetof(struct NR_MAC_CellGroupConfig__ext4__g_CS_RNTI_ConfigToReleaseList_r17, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_g_CS_RNTI_ConfigToReleaseList_r17_40 = {
	"g-CS-RNTI-ConfigToReleaseList-r17",
	"g-CS-RNTI-ConfigToReleaseList-r17",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_g_CS_RNTI_ConfigToReleaseList_r17_tags_40,
	sizeof(asn_DEF_NR_g_CS_RNTI_ConfigToReleaseList_r17_tags_40)
		/sizeof(asn_DEF_NR_g_CS_RNTI_ConfigToReleaseList_r17_tags_40[0]) - 1, /* 1 */
	asn_DEF_NR_g_CS_RNTI_ConfigToReleaseList_r17_tags_40,	/* Same as above */
	sizeof(asn_DEF_NR_g_CS_RNTI_ConfigToReleaseList_r17_tags_40)
		/sizeof(asn_DEF_NR_g_CS_RNTI_ConfigToReleaseList_r17_tags_40[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_g_CS_RNTI_ConfigToReleaseList_r17_constr_40,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_g_CS_RNTI_ConfigToReleaseList_r17_40,
	1,	/* Single element */
	&asn_SPC_NR_g_CS_RNTI_ConfigToReleaseList_r17_specs_40	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext4_25[] = {
	{ ATF_POINTER, 12, offsetof(struct NR_MAC_CellGroupConfig__ext4, intraCG_Prioritization_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_intraCG_Prioritization_r17_26,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"intraCG-Prioritization-r17"
		},
	{ ATF_POINTER, 11, offsetof(struct NR_MAC_CellGroupConfig__ext4, drx_ConfigSL_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_DRX_ConfigSL_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"drx-ConfigSL-r17"
		},
	{ ATF_POINTER, 10, offsetof(struct NR_MAC_CellGroupConfig__ext4, drx_ConfigExt_v1700),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_DRX_ConfigExt_v1700,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"drx-ConfigExt-v1700"
		},
	{ ATF_POINTER, 9, offsetof(struct NR_MAC_CellGroupConfig__ext4, schedulingRequestID_BFR_r17),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_SchedulingRequestId,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"schedulingRequestID-BFR-r17"
		},
	{ ATF_POINTER, 8, offsetof(struct NR_MAC_CellGroupConfig__ext4, schedulingRequestID_BFR2_r17),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_SchedulingRequestId,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"schedulingRequestID-BFR2-r17"
		},
	{ ATF_POINTER, 7, offsetof(struct NR_MAC_CellGroupConfig__ext4, schedulingRequestConfig_v1700),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_SchedulingRequestConfig_v1700,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"schedulingRequestConfig-v1700"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_MAC_CellGroupConfig__ext4, tar_Config_r17),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_TAR_Config_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"tar-Config-r17"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_MAC_CellGroupConfig__ext4, g_RNTI_ConfigToAddModList_r17),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		0,
		&asn_DEF_NR_g_RNTI_ConfigToAddModList_r17_34,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_g_RNTI_ConfigToAddModList_r17_constr_34,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_g_RNTI_ConfigToAddModList_r17_constraint_25
		},
		0, 0, /* No default value */
		"g-RNTI-ConfigToAddModList-r17"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_MAC_CellGroupConfig__ext4, g_RNTI_ConfigToReleaseList_r17),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		0,
		&asn_DEF_NR_g_RNTI_ConfigToReleaseList_r17_36,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_g_RNTI_ConfigToReleaseList_r17_constr_36,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_g_RNTI_ConfigToReleaseList_r17_constraint_25
		},
		0, 0, /* No default value */
		"g-RNTI-ConfigToReleaseList-r17"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_MAC_CellGroupConfig__ext4, g_CS_RNTI_ConfigToAddModList_r17),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		0,
		&asn_DEF_NR_g_CS_RNTI_ConfigToAddModList_r17_38,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_g_CS_RNTI_ConfigToAddModList_r17_constr_38,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_g_CS_RNTI_ConfigToAddModList_r17_constraint_25
		},
		0, 0, /* No default value */
		"g-CS-RNTI-ConfigToAddModList-r17"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_MAC_CellGroupConfig__ext4, g_CS_RNTI_ConfigToReleaseList_r17),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		0,
		&asn_DEF_NR_g_CS_RNTI_ConfigToReleaseList_r17_40,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_g_CS_RNTI_ConfigToReleaseList_r17_constr_40,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_g_CS_RNTI_ConfigToReleaseList_r17_constraint_25
		},
		0, 0, /* No default value */
		"g-CS-RNTI-ConfigToReleaseList-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MAC_CellGroupConfig__ext4, allowCSI_SRS_Tx_MulticastDRX_Active_r17),
		(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_BOOLEAN,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"allowCSI-SRS-Tx-MulticastDRX-Active-r17"
		},
};
static const int asn_MAP_NR_ext4_oms_25[] = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11 };
static const ber_tlv_tag_t asn_DEF_NR_ext4_tags_25[] = {
	(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext4_tag2el_25[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* intraCG-Prioritization-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* drx-ConfigSL-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* drx-ConfigExt-v1700 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* schedulingRequestID-BFR-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* schedulingRequestID-BFR2-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* schedulingRequestConfig-v1700 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* tar-Config-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* g-RNTI-ConfigToAddModList-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* g-RNTI-ConfigToReleaseList-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* g-CS-RNTI-ConfigToAddModList-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 }, /* g-CS-RNTI-ConfigToReleaseList-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (11 << 2)), 11, 0, 0 } /* allowCSI-SRS-Tx-MulticastDRX-Active-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext4_specs_25 = {
	sizeof(struct NR_MAC_CellGroupConfig__ext4),
	offsetof(struct NR_MAC_CellGroupConfig__ext4, _asn_ctx),
	asn_MAP_NR_ext4_tag2el_25,
	12,	/* Count of tags in the map */
	asn_MAP_NR_ext4_oms_25,	/* Optional members */
	12, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext4_25 = {
	"ext4",
	"ext4",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext4_tags_25,
	sizeof(asn_DEF_NR_ext4_tags_25)
		/sizeof(asn_DEF_NR_ext4_tags_25[0]) - 1, /* 1 */
	asn_DEF_NR_ext4_tags_25,	/* Same as above */
	sizeof(asn_DEF_NR_ext4_tags_25)
		/sizeof(asn_DEF_NR_ext4_tags_25[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext4_25,
	12,	/* Elements count */
	&asn_SPC_NR_ext4_specs_25	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_drx_LastTransmissionUL_r17_value2enum_45[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_drx_LastTransmissionUL_r17_enum2value_45[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_drx_LastTransmissionUL_r17_specs_45 = {
	asn_MAP_NR_drx_LastTransmissionUL_r17_value2enum_45,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_drx_LastTransmissionUL_r17_enum2value_45,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_drx_LastTransmissionUL_r17_tags_45[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_drx_LastTransmissionUL_r17_45 = {
	"drx-LastTransmissionUL-r17",
	"drx-LastTransmissionUL-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_drx_LastTransmissionUL_r17_tags_45,
	sizeof(asn_DEF_NR_drx_LastTransmissionUL_r17_tags_45)
		/sizeof(asn_DEF_NR_drx_LastTransmissionUL_r17_tags_45[0]) - 1, /* 1 */
	asn_DEF_NR_drx_LastTransmissionUL_r17_tags_45,	/* Same as above */
	sizeof(asn_DEF_NR_drx_LastTransmissionUL_r17_tags_45)
		/sizeof(asn_DEF_NR_drx_LastTransmissionUL_r17_tags_45[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_drx_LastTransmissionUL_r17_constr_45,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_drx_LastTransmissionUL_r17_specs_45	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext5_43[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_MAC_CellGroupConfig__ext5, schedulingRequestID_PosMG_Request_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_SchedulingRequestId,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"schedulingRequestID-PosMG-Request-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MAC_CellGroupConfig__ext5, drx_LastTransmissionUL_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_drx_LastTransmissionUL_r17_45,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"drx-LastTransmissionUL-r17"
		},
};
static const int asn_MAP_NR_ext5_oms_43[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_ext5_tags_43[] = {
	(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext5_tag2el_43[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* schedulingRequestID-PosMG-Request-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* drx-LastTransmissionUL-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext5_specs_43 = {
	sizeof(struct NR_MAC_CellGroupConfig__ext5),
	offsetof(struct NR_MAC_CellGroupConfig__ext5, _asn_ctx),
	asn_MAP_NR_ext5_tag2el_43,
	2,	/* Count of tags in the map */
	asn_MAP_NR_ext5_oms_43,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext5_43 = {
	"ext5",
	"ext5",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext5_tags_43,
	sizeof(asn_DEF_NR_ext5_tags_43)
		/sizeof(asn_DEF_NR_ext5_tags_43[0]) - 1, /* 1 */
	asn_DEF_NR_ext5_tags_43,	/* Same as above */
	sizeof(asn_DEF_NR_ext5_tags_43)
		/sizeof(asn_DEF_NR_ext5_tags_43[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext5_43,
	2,	/* Elements count */
	&asn_SPC_NR_ext5_specs_43	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_MAC_CellGroupConfig_1[] = {
	{ ATF_POINTER, 5, offsetof(struct NR_MAC_CellGroupConfig, drx_Config),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_DRX_Config,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"drx-Config"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_MAC_CellGroupConfig, schedulingRequestConfig),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_SchedulingRequestConfig,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"schedulingRequestConfig"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_MAC_CellGroupConfig, bsr_Config),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BSR_Config,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"bsr-Config"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_MAC_CellGroupConfig, tag_Config),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_TAG_Config,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"tag-Config"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MAC_CellGroupConfig, phr_Config),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_PHR_Config,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"phr-Config"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_MAC_CellGroupConfig, skipUplinkTxDynamic),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_BOOLEAN,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"skipUplinkTxDynamic"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_MAC_CellGroupConfig, ext1),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		0,
		&asn_DEF_NR_ext1_9,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_MAC_CellGroupConfig, ext2),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		0,
		&asn_DEF_NR_ext2_12,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext2"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_MAC_CellGroupConfig, ext3),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		0,
		&asn_DEF_NR_ext3_20,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext3"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_MAC_CellGroupConfig, ext4),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		0,
		&asn_DEF_NR_ext4_25,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext4"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MAC_CellGroupConfig, ext5),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		0,
		&asn_DEF_NR_ext5_43,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext5"
		},
};
static const int asn_MAP_NR_MAC_CellGroupConfig_oms_1[] = { 0, 1, 2, 3, 4, 6, 7, 8, 9, 10 };
static const ber_tlv_tag_t asn_DEF_NR_MAC_CellGroupConfig_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_MAC_CellGroupConfig_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* drx-Config */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* schedulingRequestConfig */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* bsr-Config */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* tag-Config */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* phr-Config */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* skipUplinkTxDynamic */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* ext1 */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* ext2 */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* ext3 */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* ext4 */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 } /* ext5 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_MAC_CellGroupConfig_specs_1 = {
	sizeof(struct NR_MAC_CellGroupConfig),
	offsetof(struct NR_MAC_CellGroupConfig, _asn_ctx),
	asn_MAP_NR_MAC_CellGroupConfig_tag2el_1,
	11,	/* Count of tags in the map */
	asn_MAP_NR_MAC_CellGroupConfig_oms_1,	/* Optional members */
	5, 5,	/* Root/Additions */
	6,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_MAC_CellGroupConfig = {
	"MAC-CellGroupConfig",
	"MAC-CellGroupConfig",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_MAC_CellGroupConfig_tags_1,
	sizeof(asn_DEF_NR_MAC_CellGroupConfig_tags_1)
		/sizeof(asn_DEF_NR_MAC_CellGroupConfig_tags_1[0]), /* 1 */
	asn_DEF_NR_MAC_CellGroupConfig_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_MAC_CellGroupConfig_tags_1)
		/sizeof(asn_DEF_NR_MAC_CellGroupConfig_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_MAC_CellGroupConfig_1,
	11,	/* Elements count */
	&asn_SPC_NR_MAC_CellGroupConfig_specs_1	/* Additional specs */
};

