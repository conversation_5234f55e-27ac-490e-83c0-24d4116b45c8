/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_Phy-ParametersFRX-Diff.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_pdcch_BlindDetectionMCG_UE_constraint_105(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 15L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_pdcch_BlindDetectionSCG_UE_constraint_105(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 15L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_dummy1_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	const BIT_STRING_t *st = (const BIT_STRING_t *)sptr;
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	if(st->size > 0) {
		/* Size in bits */
		size = 8 * st->size - (st->bits_unused & 0x07);
	} else {
		size = 0;
	}
	
	if((size == 2UL)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_twoFL_DMRS_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	const BIT_STRING_t *st = (const BIT_STRING_t *)sptr;
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	if(st->size > 0) {
		/* Size in bits */
		size = 8 * st->size - (st->bits_unused & 0x07);
	} else {
		size = 0;
	}
	
	if((size == 2UL)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_dummy2_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	const BIT_STRING_t *st = (const BIT_STRING_t *)sptr;
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	if(st->size > 0) {
		/* Size in bits */
		size = 8 * st->size - (st->bits_unused & 0x07);
	} else {
		size = 0;
	}
	
	if((size == 2UL)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_dummy3_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	const BIT_STRING_t *st = (const BIT_STRING_t *)sptr;
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	if(st->size > 0) {
		/* Size in bits */
		size = 8 * st->size - (st->bits_unused & 0x07);
	} else {
		size = 0;
	}
	
	if((size == 2UL)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_onePortsPTRS_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	const BIT_STRING_t *st = (const BIT_STRING_t *)sptr;
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	if(st->size > 0) {
		/* Size in bits */
		size = 8 * st->size - (st->bits_unused & 0x07);
	} else {
		size = 0;
	}
	
	if((size == 2UL)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_pdcch_BlindDetectionCA_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 4L && value <= 16L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dynamicSFI_constr_2 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_supportedDMRS_TypeDL_constr_8 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_supportedDMRS_TypeUL_constr_11 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_semiOpenLoopCSI_constr_14 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_csi_ReportWithoutPMI_constr_16 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_csi_ReportWithoutCQI_constr_18 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_twoPUCCH_F0_2_ConsecSymbols_constr_21 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pucch_F2_WithFH_constr_23 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pucch_F3_WithFH_constr_25 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pucch_F4_WithFH_constr_27 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pucch_F0_2WithoutFH_constr_29 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pucch_F1_3_4WithoutFH_constr_31 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_constr_33 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_uci_CodeBlockSegmentation_constr_35 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_onePUCCH_LongAndShortFormat_constr_37 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_twoPUCCH_AnyOthersInSlot_constr_39 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_intraSlotFreqHopping_PUSCH_constr_41 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pusch_LBRM_constr_43 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_tpc_PUSCH_RNTI_constr_46 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_tpc_PUCCH_RNTI_constr_48 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_tpc_SRS_RNTI_constr_50 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_absoluteTPC_Command_constr_52 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_twoDifferentTPC_Loop_PUSCH_constr_54 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_twoDifferentTPC_Loop_PUCCH_constr_56 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pusch_HalfPi_BPSK_constr_58 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pucch_F3_4_HalfPi_BPSK_constr_60 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_almostContiguousCP_OFDM_UL_constr_62 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_sp_CSI_RS_constr_64 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_sp_CSI_IM_constr_66 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_tdd_MultiDL_UL_SwitchPerSlot_constr_68 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_multipleCORESET_constr_70 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_sameSymbol_constr_78 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_diffSymbol_constr_80 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_mux_SR_HARQ_ACK_PUCCH_constr_82 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_mux_MultipleGroupCtrlCH_Overlap_constr_84 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dl_SchedulingOffset_PDSCH_TypeA_constr_86 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dl_SchedulingOffset_PDSCH_TypeB_constr_88 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ul_SchedulingOffset_constr_90 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dl_64QAM_MCS_TableAlt_constr_92 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ul_64QAM_MCS_TableAlt_constr_94 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_cqi_TableAlt_constr_96 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_oneFL_DMRS_TwoAdditionalDMRS_UL_constr_98 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_twoFL_DMRS_TwoAdditionalDMRS_UL_constr_100 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_oneFL_DMRS_ThreeAdditionalDMRS_UL_constr_102 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_pdcch_BlindDetectionMCG_UE_constr_106 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  1,  15 }	/* (1..15) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_pdcch_BlindDetectionSCG_UE_constr_107 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  1,  15 }	/* (1..15) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_constr_108 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_type1_HARQ_ACK_Codebook_r16_constr_111 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_enhancedPowerControl_r16_constr_113 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_simultaneousTCI_ActMultipleCC_r16_constr_115 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_simultaneousSpatialRelationMultipleCC_r16_constr_117 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_cli_RSSI_FDM_DL_r16_constr_119 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_cli_SRS_RSRP_FDM_DL_r16_constr_121 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_maxLayersMIMO_Adaptation_r16_constr_123 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_aggregationFactorSPS_DL_r16_constr_125 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_maxNumberResWithinSlotAcrossCC_OneFR_r16_constr_128 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  7 }	/* (0..7) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_maxNumberResAcrossCC_OneFR_r16_constr_137 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  13 }	/* (0..13) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_twoTCI_Act_servingCellInCC_List_r16_constr_154 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_cri_RI_CQI_WithoutNon_PMI_PortInd_r16_constr_157 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_cqi_4_BitsSubbandTN_NonSharedSpectrumChAccess_r17_constr_160 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_dummy1_constr_4 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 0,  0,  2,  2 }	/* (SIZE(2..2)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_twoFL_DMRS_constr_5 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 0,  0,  2,  2 }	/* (SIZE(2..2)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_dummy2_constr_6 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 0,  0,  2,  2 }	/* (SIZE(2..2)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_dummy3_constr_7 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 0,  0,  2,  2 }	/* (SIZE(2..2)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_onePortsPTRS_constr_20 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 0,  0,  2,  2 }	/* (SIZE(2..2)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_pdcch_BlindDetectionCA_constr_45 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  4,  16 }	/* (4..16) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_dynamicSFI_value2enum_2[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_dynamicSFI_enum2value_2[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dynamicSFI_specs_2 = {
	asn_MAP_NR_dynamicSFI_value2enum_2,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dynamicSFI_enum2value_2,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dynamicSFI_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dynamicSFI_2 = {
	"dynamicSFI",
	"dynamicSFI",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dynamicSFI_tags_2,
	sizeof(asn_DEF_NR_dynamicSFI_tags_2)
		/sizeof(asn_DEF_NR_dynamicSFI_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_dynamicSFI_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_dynamicSFI_tags_2)
		/sizeof(asn_DEF_NR_dynamicSFI_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dynamicSFI_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dynamicSFI_specs_2	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_supportedDMRS_TypeDL_value2enum_8[] = {
	{ 0,	5,	"type1" },
	{ 1,	9,	"type1And2" }
};
static const unsigned int asn_MAP_NR_supportedDMRS_TypeDL_enum2value_8[] = {
	0,	/* type1(0) */
	1	/* type1And2(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_supportedDMRS_TypeDL_specs_8 = {
	asn_MAP_NR_supportedDMRS_TypeDL_value2enum_8,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_supportedDMRS_TypeDL_enum2value_8,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_supportedDMRS_TypeDL_tags_8[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_supportedDMRS_TypeDL_8 = {
	"supportedDMRS-TypeDL",
	"supportedDMRS-TypeDL",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_supportedDMRS_TypeDL_tags_8,
	sizeof(asn_DEF_NR_supportedDMRS_TypeDL_tags_8)
		/sizeof(asn_DEF_NR_supportedDMRS_TypeDL_tags_8[0]) - 1, /* 1 */
	asn_DEF_NR_supportedDMRS_TypeDL_tags_8,	/* Same as above */
	sizeof(asn_DEF_NR_supportedDMRS_TypeDL_tags_8)
		/sizeof(asn_DEF_NR_supportedDMRS_TypeDL_tags_8[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_supportedDMRS_TypeDL_constr_8,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_supportedDMRS_TypeDL_specs_8	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_supportedDMRS_TypeUL_value2enum_11[] = {
	{ 0,	5,	"type1" },
	{ 1,	9,	"type1And2" }
};
static const unsigned int asn_MAP_NR_supportedDMRS_TypeUL_enum2value_11[] = {
	0,	/* type1(0) */
	1	/* type1And2(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_supportedDMRS_TypeUL_specs_11 = {
	asn_MAP_NR_supportedDMRS_TypeUL_value2enum_11,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_supportedDMRS_TypeUL_enum2value_11,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_supportedDMRS_TypeUL_tags_11[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_supportedDMRS_TypeUL_11 = {
	"supportedDMRS-TypeUL",
	"supportedDMRS-TypeUL",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_supportedDMRS_TypeUL_tags_11,
	sizeof(asn_DEF_NR_supportedDMRS_TypeUL_tags_11)
		/sizeof(asn_DEF_NR_supportedDMRS_TypeUL_tags_11[0]) - 1, /* 1 */
	asn_DEF_NR_supportedDMRS_TypeUL_tags_11,	/* Same as above */
	sizeof(asn_DEF_NR_supportedDMRS_TypeUL_tags_11)
		/sizeof(asn_DEF_NR_supportedDMRS_TypeUL_tags_11[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_supportedDMRS_TypeUL_constr_11,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_supportedDMRS_TypeUL_specs_11	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_semiOpenLoopCSI_value2enum_14[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_semiOpenLoopCSI_enum2value_14[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_semiOpenLoopCSI_specs_14 = {
	asn_MAP_NR_semiOpenLoopCSI_value2enum_14,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_semiOpenLoopCSI_enum2value_14,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_semiOpenLoopCSI_tags_14[] = {
	(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_semiOpenLoopCSI_14 = {
	"semiOpenLoopCSI",
	"semiOpenLoopCSI",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_semiOpenLoopCSI_tags_14,
	sizeof(asn_DEF_NR_semiOpenLoopCSI_tags_14)
		/sizeof(asn_DEF_NR_semiOpenLoopCSI_tags_14[0]) - 1, /* 1 */
	asn_DEF_NR_semiOpenLoopCSI_tags_14,	/* Same as above */
	sizeof(asn_DEF_NR_semiOpenLoopCSI_tags_14)
		/sizeof(asn_DEF_NR_semiOpenLoopCSI_tags_14[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_semiOpenLoopCSI_constr_14,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_semiOpenLoopCSI_specs_14	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_csi_ReportWithoutPMI_value2enum_16[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_csi_ReportWithoutPMI_enum2value_16[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_csi_ReportWithoutPMI_specs_16 = {
	asn_MAP_NR_csi_ReportWithoutPMI_value2enum_16,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_csi_ReportWithoutPMI_enum2value_16,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_csi_ReportWithoutPMI_tags_16[] = {
	(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_csi_ReportWithoutPMI_16 = {
	"csi-ReportWithoutPMI",
	"csi-ReportWithoutPMI",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_csi_ReportWithoutPMI_tags_16,
	sizeof(asn_DEF_NR_csi_ReportWithoutPMI_tags_16)
		/sizeof(asn_DEF_NR_csi_ReportWithoutPMI_tags_16[0]) - 1, /* 1 */
	asn_DEF_NR_csi_ReportWithoutPMI_tags_16,	/* Same as above */
	sizeof(asn_DEF_NR_csi_ReportWithoutPMI_tags_16)
		/sizeof(asn_DEF_NR_csi_ReportWithoutPMI_tags_16[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_csi_ReportWithoutPMI_constr_16,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_csi_ReportWithoutPMI_specs_16	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_csi_ReportWithoutCQI_value2enum_18[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_csi_ReportWithoutCQI_enum2value_18[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_csi_ReportWithoutCQI_specs_18 = {
	asn_MAP_NR_csi_ReportWithoutCQI_value2enum_18,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_csi_ReportWithoutCQI_enum2value_18,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_csi_ReportWithoutCQI_tags_18[] = {
	(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_csi_ReportWithoutCQI_18 = {
	"csi-ReportWithoutCQI",
	"csi-ReportWithoutCQI",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_csi_ReportWithoutCQI_tags_18,
	sizeof(asn_DEF_NR_csi_ReportWithoutCQI_tags_18)
		/sizeof(asn_DEF_NR_csi_ReportWithoutCQI_tags_18[0]) - 1, /* 1 */
	asn_DEF_NR_csi_ReportWithoutCQI_tags_18,	/* Same as above */
	sizeof(asn_DEF_NR_csi_ReportWithoutCQI_tags_18)
		/sizeof(asn_DEF_NR_csi_ReportWithoutCQI_tags_18[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_csi_ReportWithoutCQI_constr_18,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_csi_ReportWithoutCQI_specs_18	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_twoPUCCH_F0_2_ConsecSymbols_value2enum_21[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_twoPUCCH_F0_2_ConsecSymbols_enum2value_21[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_twoPUCCH_F0_2_ConsecSymbols_specs_21 = {
	asn_MAP_NR_twoPUCCH_F0_2_ConsecSymbols_value2enum_21,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_twoPUCCH_F0_2_ConsecSymbols_enum2value_21,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_twoPUCCH_F0_2_ConsecSymbols_tags_21[] = {
	(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_twoPUCCH_F0_2_ConsecSymbols_21 = {
	"twoPUCCH-F0-2-ConsecSymbols",
	"twoPUCCH-F0-2-ConsecSymbols",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_twoPUCCH_F0_2_ConsecSymbols_tags_21,
	sizeof(asn_DEF_NR_twoPUCCH_F0_2_ConsecSymbols_tags_21)
		/sizeof(asn_DEF_NR_twoPUCCH_F0_2_ConsecSymbols_tags_21[0]) - 1, /* 1 */
	asn_DEF_NR_twoPUCCH_F0_2_ConsecSymbols_tags_21,	/* Same as above */
	sizeof(asn_DEF_NR_twoPUCCH_F0_2_ConsecSymbols_tags_21)
		/sizeof(asn_DEF_NR_twoPUCCH_F0_2_ConsecSymbols_tags_21[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_twoPUCCH_F0_2_ConsecSymbols_constr_21,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_twoPUCCH_F0_2_ConsecSymbols_specs_21	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pucch_F2_WithFH_value2enum_23[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_pucch_F2_WithFH_enum2value_23[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pucch_F2_WithFH_specs_23 = {
	asn_MAP_NR_pucch_F2_WithFH_value2enum_23,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pucch_F2_WithFH_enum2value_23,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pucch_F2_WithFH_tags_23[] = {
	(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pucch_F2_WithFH_23 = {
	"pucch-F2-WithFH",
	"pucch-F2-WithFH",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pucch_F2_WithFH_tags_23,
	sizeof(asn_DEF_NR_pucch_F2_WithFH_tags_23)
		/sizeof(asn_DEF_NR_pucch_F2_WithFH_tags_23[0]) - 1, /* 1 */
	asn_DEF_NR_pucch_F2_WithFH_tags_23,	/* Same as above */
	sizeof(asn_DEF_NR_pucch_F2_WithFH_tags_23)
		/sizeof(asn_DEF_NR_pucch_F2_WithFH_tags_23[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pucch_F2_WithFH_constr_23,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pucch_F2_WithFH_specs_23	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pucch_F3_WithFH_value2enum_25[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_pucch_F3_WithFH_enum2value_25[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pucch_F3_WithFH_specs_25 = {
	asn_MAP_NR_pucch_F3_WithFH_value2enum_25,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pucch_F3_WithFH_enum2value_25,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pucch_F3_WithFH_tags_25[] = {
	(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pucch_F3_WithFH_25 = {
	"pucch-F3-WithFH",
	"pucch-F3-WithFH",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pucch_F3_WithFH_tags_25,
	sizeof(asn_DEF_NR_pucch_F3_WithFH_tags_25)
		/sizeof(asn_DEF_NR_pucch_F3_WithFH_tags_25[0]) - 1, /* 1 */
	asn_DEF_NR_pucch_F3_WithFH_tags_25,	/* Same as above */
	sizeof(asn_DEF_NR_pucch_F3_WithFH_tags_25)
		/sizeof(asn_DEF_NR_pucch_F3_WithFH_tags_25[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pucch_F3_WithFH_constr_25,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pucch_F3_WithFH_specs_25	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pucch_F4_WithFH_value2enum_27[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_pucch_F4_WithFH_enum2value_27[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pucch_F4_WithFH_specs_27 = {
	asn_MAP_NR_pucch_F4_WithFH_value2enum_27,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pucch_F4_WithFH_enum2value_27,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pucch_F4_WithFH_tags_27[] = {
	(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pucch_F4_WithFH_27 = {
	"pucch-F4-WithFH",
	"pucch-F4-WithFH",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pucch_F4_WithFH_tags_27,
	sizeof(asn_DEF_NR_pucch_F4_WithFH_tags_27)
		/sizeof(asn_DEF_NR_pucch_F4_WithFH_tags_27[0]) - 1, /* 1 */
	asn_DEF_NR_pucch_F4_WithFH_tags_27,	/* Same as above */
	sizeof(asn_DEF_NR_pucch_F4_WithFH_tags_27)
		/sizeof(asn_DEF_NR_pucch_F4_WithFH_tags_27[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pucch_F4_WithFH_constr_27,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pucch_F4_WithFH_specs_27	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pucch_F0_2WithoutFH_value2enum_29[] = {
	{ 0,	12,	"notSupported" }
};
static const unsigned int asn_MAP_NR_pucch_F0_2WithoutFH_enum2value_29[] = {
	0	/* notSupported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pucch_F0_2WithoutFH_specs_29 = {
	asn_MAP_NR_pucch_F0_2WithoutFH_value2enum_29,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pucch_F0_2WithoutFH_enum2value_29,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pucch_F0_2WithoutFH_tags_29[] = {
	(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pucch_F0_2WithoutFH_29 = {
	"pucch-F0-2WithoutFH",
	"pucch-F0-2WithoutFH",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pucch_F0_2WithoutFH_tags_29,
	sizeof(asn_DEF_NR_pucch_F0_2WithoutFH_tags_29)
		/sizeof(asn_DEF_NR_pucch_F0_2WithoutFH_tags_29[0]) - 1, /* 1 */
	asn_DEF_NR_pucch_F0_2WithoutFH_tags_29,	/* Same as above */
	sizeof(asn_DEF_NR_pucch_F0_2WithoutFH_tags_29)
		/sizeof(asn_DEF_NR_pucch_F0_2WithoutFH_tags_29[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pucch_F0_2WithoutFH_constr_29,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pucch_F0_2WithoutFH_specs_29	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pucch_F1_3_4WithoutFH_value2enum_31[] = {
	{ 0,	12,	"notSupported" }
};
static const unsigned int asn_MAP_NR_pucch_F1_3_4WithoutFH_enum2value_31[] = {
	0	/* notSupported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pucch_F1_3_4WithoutFH_specs_31 = {
	asn_MAP_NR_pucch_F1_3_4WithoutFH_value2enum_31,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pucch_F1_3_4WithoutFH_enum2value_31,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pucch_F1_3_4WithoutFH_tags_31[] = {
	(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pucch_F1_3_4WithoutFH_31 = {
	"pucch-F1-3-4WithoutFH",
	"pucch-F1-3-4WithoutFH",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pucch_F1_3_4WithoutFH_tags_31,
	sizeof(asn_DEF_NR_pucch_F1_3_4WithoutFH_tags_31)
		/sizeof(asn_DEF_NR_pucch_F1_3_4WithoutFH_tags_31[0]) - 1, /* 1 */
	asn_DEF_NR_pucch_F1_3_4WithoutFH_tags_31,	/* Same as above */
	sizeof(asn_DEF_NR_pucch_F1_3_4WithoutFH_tags_31)
		/sizeof(asn_DEF_NR_pucch_F1_3_4WithoutFH_tags_31[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pucch_F1_3_4WithoutFH_constr_31,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pucch_F1_3_4WithoutFH_specs_31	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_value2enum_33[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_enum2value_33[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_specs_33 = {
	asn_MAP_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_value2enum_33,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_enum2value_33,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_tags_33[] = {
	(ASN_TAG_CLASS_CONTEXT | (17 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_33 = {
	"mux-SR-HARQ-ACK-CSI-PUCCH-MultiPerSlot",
	"mux-SR-HARQ-ACK-CSI-PUCCH-MultiPerSlot",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_tags_33,
	sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_tags_33)
		/sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_tags_33[0]) - 1, /* 1 */
	asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_tags_33,	/* Same as above */
	sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_tags_33)
		/sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_tags_33[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_constr_33,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_specs_33	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_uci_CodeBlockSegmentation_value2enum_35[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_uci_CodeBlockSegmentation_enum2value_35[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_uci_CodeBlockSegmentation_specs_35 = {
	asn_MAP_NR_uci_CodeBlockSegmentation_value2enum_35,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_uci_CodeBlockSegmentation_enum2value_35,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_uci_CodeBlockSegmentation_tags_35[] = {
	(ASN_TAG_CLASS_CONTEXT | (18 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_uci_CodeBlockSegmentation_35 = {
	"uci-CodeBlockSegmentation",
	"uci-CodeBlockSegmentation",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_uci_CodeBlockSegmentation_tags_35,
	sizeof(asn_DEF_NR_uci_CodeBlockSegmentation_tags_35)
		/sizeof(asn_DEF_NR_uci_CodeBlockSegmentation_tags_35[0]) - 1, /* 1 */
	asn_DEF_NR_uci_CodeBlockSegmentation_tags_35,	/* Same as above */
	sizeof(asn_DEF_NR_uci_CodeBlockSegmentation_tags_35)
		/sizeof(asn_DEF_NR_uci_CodeBlockSegmentation_tags_35[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_uci_CodeBlockSegmentation_constr_35,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_uci_CodeBlockSegmentation_specs_35	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_onePUCCH_LongAndShortFormat_value2enum_37[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_onePUCCH_LongAndShortFormat_enum2value_37[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_onePUCCH_LongAndShortFormat_specs_37 = {
	asn_MAP_NR_onePUCCH_LongAndShortFormat_value2enum_37,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_onePUCCH_LongAndShortFormat_enum2value_37,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_onePUCCH_LongAndShortFormat_tags_37[] = {
	(ASN_TAG_CLASS_CONTEXT | (19 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_onePUCCH_LongAndShortFormat_37 = {
	"onePUCCH-LongAndShortFormat",
	"onePUCCH-LongAndShortFormat",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_onePUCCH_LongAndShortFormat_tags_37,
	sizeof(asn_DEF_NR_onePUCCH_LongAndShortFormat_tags_37)
		/sizeof(asn_DEF_NR_onePUCCH_LongAndShortFormat_tags_37[0]) - 1, /* 1 */
	asn_DEF_NR_onePUCCH_LongAndShortFormat_tags_37,	/* Same as above */
	sizeof(asn_DEF_NR_onePUCCH_LongAndShortFormat_tags_37)
		/sizeof(asn_DEF_NR_onePUCCH_LongAndShortFormat_tags_37[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_onePUCCH_LongAndShortFormat_constr_37,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_onePUCCH_LongAndShortFormat_specs_37	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_twoPUCCH_AnyOthersInSlot_value2enum_39[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_twoPUCCH_AnyOthersInSlot_enum2value_39[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_twoPUCCH_AnyOthersInSlot_specs_39 = {
	asn_MAP_NR_twoPUCCH_AnyOthersInSlot_value2enum_39,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_twoPUCCH_AnyOthersInSlot_enum2value_39,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_twoPUCCH_AnyOthersInSlot_tags_39[] = {
	(ASN_TAG_CLASS_CONTEXT | (20 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_twoPUCCH_AnyOthersInSlot_39 = {
	"twoPUCCH-AnyOthersInSlot",
	"twoPUCCH-AnyOthersInSlot",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_twoPUCCH_AnyOthersInSlot_tags_39,
	sizeof(asn_DEF_NR_twoPUCCH_AnyOthersInSlot_tags_39)
		/sizeof(asn_DEF_NR_twoPUCCH_AnyOthersInSlot_tags_39[0]) - 1, /* 1 */
	asn_DEF_NR_twoPUCCH_AnyOthersInSlot_tags_39,	/* Same as above */
	sizeof(asn_DEF_NR_twoPUCCH_AnyOthersInSlot_tags_39)
		/sizeof(asn_DEF_NR_twoPUCCH_AnyOthersInSlot_tags_39[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_twoPUCCH_AnyOthersInSlot_constr_39,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_twoPUCCH_AnyOthersInSlot_specs_39	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_intraSlotFreqHopping_PUSCH_value2enum_41[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_intraSlotFreqHopping_PUSCH_enum2value_41[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_intraSlotFreqHopping_PUSCH_specs_41 = {
	asn_MAP_NR_intraSlotFreqHopping_PUSCH_value2enum_41,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_intraSlotFreqHopping_PUSCH_enum2value_41,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_intraSlotFreqHopping_PUSCH_tags_41[] = {
	(ASN_TAG_CLASS_CONTEXT | (21 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_intraSlotFreqHopping_PUSCH_41 = {
	"intraSlotFreqHopping-PUSCH",
	"intraSlotFreqHopping-PUSCH",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_intraSlotFreqHopping_PUSCH_tags_41,
	sizeof(asn_DEF_NR_intraSlotFreqHopping_PUSCH_tags_41)
		/sizeof(asn_DEF_NR_intraSlotFreqHopping_PUSCH_tags_41[0]) - 1, /* 1 */
	asn_DEF_NR_intraSlotFreqHopping_PUSCH_tags_41,	/* Same as above */
	sizeof(asn_DEF_NR_intraSlotFreqHopping_PUSCH_tags_41)
		/sizeof(asn_DEF_NR_intraSlotFreqHopping_PUSCH_tags_41[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_intraSlotFreqHopping_PUSCH_constr_41,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_intraSlotFreqHopping_PUSCH_specs_41	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pusch_LBRM_value2enum_43[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_pusch_LBRM_enum2value_43[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pusch_LBRM_specs_43 = {
	asn_MAP_NR_pusch_LBRM_value2enum_43,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pusch_LBRM_enum2value_43,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pusch_LBRM_tags_43[] = {
	(ASN_TAG_CLASS_CONTEXT | (22 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pusch_LBRM_43 = {
	"pusch-LBRM",
	"pusch-LBRM",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pusch_LBRM_tags_43,
	sizeof(asn_DEF_NR_pusch_LBRM_tags_43)
		/sizeof(asn_DEF_NR_pusch_LBRM_tags_43[0]) - 1, /* 1 */
	asn_DEF_NR_pusch_LBRM_tags_43,	/* Same as above */
	sizeof(asn_DEF_NR_pusch_LBRM_tags_43)
		/sizeof(asn_DEF_NR_pusch_LBRM_tags_43[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pusch_LBRM_constr_43,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pusch_LBRM_specs_43	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_tpc_PUSCH_RNTI_value2enum_46[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_tpc_PUSCH_RNTI_enum2value_46[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_tpc_PUSCH_RNTI_specs_46 = {
	asn_MAP_NR_tpc_PUSCH_RNTI_value2enum_46,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_tpc_PUSCH_RNTI_enum2value_46,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_tpc_PUSCH_RNTI_tags_46[] = {
	(ASN_TAG_CLASS_CONTEXT | (24 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_tpc_PUSCH_RNTI_46 = {
	"tpc-PUSCH-RNTI",
	"tpc-PUSCH-RNTI",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_tpc_PUSCH_RNTI_tags_46,
	sizeof(asn_DEF_NR_tpc_PUSCH_RNTI_tags_46)
		/sizeof(asn_DEF_NR_tpc_PUSCH_RNTI_tags_46[0]) - 1, /* 1 */
	asn_DEF_NR_tpc_PUSCH_RNTI_tags_46,	/* Same as above */
	sizeof(asn_DEF_NR_tpc_PUSCH_RNTI_tags_46)
		/sizeof(asn_DEF_NR_tpc_PUSCH_RNTI_tags_46[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_tpc_PUSCH_RNTI_constr_46,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_tpc_PUSCH_RNTI_specs_46	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_tpc_PUCCH_RNTI_value2enum_48[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_tpc_PUCCH_RNTI_enum2value_48[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_tpc_PUCCH_RNTI_specs_48 = {
	asn_MAP_NR_tpc_PUCCH_RNTI_value2enum_48,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_tpc_PUCCH_RNTI_enum2value_48,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_tpc_PUCCH_RNTI_tags_48[] = {
	(ASN_TAG_CLASS_CONTEXT | (25 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_tpc_PUCCH_RNTI_48 = {
	"tpc-PUCCH-RNTI",
	"tpc-PUCCH-RNTI",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_tpc_PUCCH_RNTI_tags_48,
	sizeof(asn_DEF_NR_tpc_PUCCH_RNTI_tags_48)
		/sizeof(asn_DEF_NR_tpc_PUCCH_RNTI_tags_48[0]) - 1, /* 1 */
	asn_DEF_NR_tpc_PUCCH_RNTI_tags_48,	/* Same as above */
	sizeof(asn_DEF_NR_tpc_PUCCH_RNTI_tags_48)
		/sizeof(asn_DEF_NR_tpc_PUCCH_RNTI_tags_48[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_tpc_PUCCH_RNTI_constr_48,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_tpc_PUCCH_RNTI_specs_48	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_tpc_SRS_RNTI_value2enum_50[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_tpc_SRS_RNTI_enum2value_50[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_tpc_SRS_RNTI_specs_50 = {
	asn_MAP_NR_tpc_SRS_RNTI_value2enum_50,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_tpc_SRS_RNTI_enum2value_50,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_tpc_SRS_RNTI_tags_50[] = {
	(ASN_TAG_CLASS_CONTEXT | (26 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_tpc_SRS_RNTI_50 = {
	"tpc-SRS-RNTI",
	"tpc-SRS-RNTI",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_tpc_SRS_RNTI_tags_50,
	sizeof(asn_DEF_NR_tpc_SRS_RNTI_tags_50)
		/sizeof(asn_DEF_NR_tpc_SRS_RNTI_tags_50[0]) - 1, /* 1 */
	asn_DEF_NR_tpc_SRS_RNTI_tags_50,	/* Same as above */
	sizeof(asn_DEF_NR_tpc_SRS_RNTI_tags_50)
		/sizeof(asn_DEF_NR_tpc_SRS_RNTI_tags_50[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_tpc_SRS_RNTI_constr_50,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_tpc_SRS_RNTI_specs_50	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_absoluteTPC_Command_value2enum_52[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_absoluteTPC_Command_enum2value_52[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_absoluteTPC_Command_specs_52 = {
	asn_MAP_NR_absoluteTPC_Command_value2enum_52,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_absoluteTPC_Command_enum2value_52,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_absoluteTPC_Command_tags_52[] = {
	(ASN_TAG_CLASS_CONTEXT | (27 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_absoluteTPC_Command_52 = {
	"absoluteTPC-Command",
	"absoluteTPC-Command",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_absoluteTPC_Command_tags_52,
	sizeof(asn_DEF_NR_absoluteTPC_Command_tags_52)
		/sizeof(asn_DEF_NR_absoluteTPC_Command_tags_52[0]) - 1, /* 1 */
	asn_DEF_NR_absoluteTPC_Command_tags_52,	/* Same as above */
	sizeof(asn_DEF_NR_absoluteTPC_Command_tags_52)
		/sizeof(asn_DEF_NR_absoluteTPC_Command_tags_52[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_absoluteTPC_Command_constr_52,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_absoluteTPC_Command_specs_52	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_twoDifferentTPC_Loop_PUSCH_value2enum_54[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_twoDifferentTPC_Loop_PUSCH_enum2value_54[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_twoDifferentTPC_Loop_PUSCH_specs_54 = {
	asn_MAP_NR_twoDifferentTPC_Loop_PUSCH_value2enum_54,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_twoDifferentTPC_Loop_PUSCH_enum2value_54,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_twoDifferentTPC_Loop_PUSCH_tags_54[] = {
	(ASN_TAG_CLASS_CONTEXT | (28 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_twoDifferentTPC_Loop_PUSCH_54 = {
	"twoDifferentTPC-Loop-PUSCH",
	"twoDifferentTPC-Loop-PUSCH",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_twoDifferentTPC_Loop_PUSCH_tags_54,
	sizeof(asn_DEF_NR_twoDifferentTPC_Loop_PUSCH_tags_54)
		/sizeof(asn_DEF_NR_twoDifferentTPC_Loop_PUSCH_tags_54[0]) - 1, /* 1 */
	asn_DEF_NR_twoDifferentTPC_Loop_PUSCH_tags_54,	/* Same as above */
	sizeof(asn_DEF_NR_twoDifferentTPC_Loop_PUSCH_tags_54)
		/sizeof(asn_DEF_NR_twoDifferentTPC_Loop_PUSCH_tags_54[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_twoDifferentTPC_Loop_PUSCH_constr_54,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_twoDifferentTPC_Loop_PUSCH_specs_54	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_twoDifferentTPC_Loop_PUCCH_value2enum_56[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_twoDifferentTPC_Loop_PUCCH_enum2value_56[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_twoDifferentTPC_Loop_PUCCH_specs_56 = {
	asn_MAP_NR_twoDifferentTPC_Loop_PUCCH_value2enum_56,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_twoDifferentTPC_Loop_PUCCH_enum2value_56,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_twoDifferentTPC_Loop_PUCCH_tags_56[] = {
	(ASN_TAG_CLASS_CONTEXT | (29 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_twoDifferentTPC_Loop_PUCCH_56 = {
	"twoDifferentTPC-Loop-PUCCH",
	"twoDifferentTPC-Loop-PUCCH",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_twoDifferentTPC_Loop_PUCCH_tags_56,
	sizeof(asn_DEF_NR_twoDifferentTPC_Loop_PUCCH_tags_56)
		/sizeof(asn_DEF_NR_twoDifferentTPC_Loop_PUCCH_tags_56[0]) - 1, /* 1 */
	asn_DEF_NR_twoDifferentTPC_Loop_PUCCH_tags_56,	/* Same as above */
	sizeof(asn_DEF_NR_twoDifferentTPC_Loop_PUCCH_tags_56)
		/sizeof(asn_DEF_NR_twoDifferentTPC_Loop_PUCCH_tags_56[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_twoDifferentTPC_Loop_PUCCH_constr_56,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_twoDifferentTPC_Loop_PUCCH_specs_56	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pusch_HalfPi_BPSK_value2enum_58[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_pusch_HalfPi_BPSK_enum2value_58[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pusch_HalfPi_BPSK_specs_58 = {
	asn_MAP_NR_pusch_HalfPi_BPSK_value2enum_58,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pusch_HalfPi_BPSK_enum2value_58,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pusch_HalfPi_BPSK_tags_58[] = {
	(ASN_TAG_CLASS_CONTEXT | (30 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pusch_HalfPi_BPSK_58 = {
	"pusch-HalfPi-BPSK",
	"pusch-HalfPi-BPSK",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pusch_HalfPi_BPSK_tags_58,
	sizeof(asn_DEF_NR_pusch_HalfPi_BPSK_tags_58)
		/sizeof(asn_DEF_NR_pusch_HalfPi_BPSK_tags_58[0]) - 1, /* 1 */
	asn_DEF_NR_pusch_HalfPi_BPSK_tags_58,	/* Same as above */
	sizeof(asn_DEF_NR_pusch_HalfPi_BPSK_tags_58)
		/sizeof(asn_DEF_NR_pusch_HalfPi_BPSK_tags_58[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pusch_HalfPi_BPSK_constr_58,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pusch_HalfPi_BPSK_specs_58	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pucch_F3_4_HalfPi_BPSK_value2enum_60[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_pucch_F3_4_HalfPi_BPSK_enum2value_60[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pucch_F3_4_HalfPi_BPSK_specs_60 = {
	asn_MAP_NR_pucch_F3_4_HalfPi_BPSK_value2enum_60,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pucch_F3_4_HalfPi_BPSK_enum2value_60,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pucch_F3_4_HalfPi_BPSK_tags_60[] = {
	(ASN_TAG_CLASS_CONTEXT | (31 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pucch_F3_4_HalfPi_BPSK_60 = {
	"pucch-F3-4-HalfPi-BPSK",
	"pucch-F3-4-HalfPi-BPSK",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pucch_F3_4_HalfPi_BPSK_tags_60,
	sizeof(asn_DEF_NR_pucch_F3_4_HalfPi_BPSK_tags_60)
		/sizeof(asn_DEF_NR_pucch_F3_4_HalfPi_BPSK_tags_60[0]) - 1, /* 1 */
	asn_DEF_NR_pucch_F3_4_HalfPi_BPSK_tags_60,	/* Same as above */
	sizeof(asn_DEF_NR_pucch_F3_4_HalfPi_BPSK_tags_60)
		/sizeof(asn_DEF_NR_pucch_F3_4_HalfPi_BPSK_tags_60[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pucch_F3_4_HalfPi_BPSK_constr_60,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pucch_F3_4_HalfPi_BPSK_specs_60	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_almostContiguousCP_OFDM_UL_value2enum_62[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_almostContiguousCP_OFDM_UL_enum2value_62[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_almostContiguousCP_OFDM_UL_specs_62 = {
	asn_MAP_NR_almostContiguousCP_OFDM_UL_value2enum_62,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_almostContiguousCP_OFDM_UL_enum2value_62,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_almostContiguousCP_OFDM_UL_tags_62[] = {
	(ASN_TAG_CLASS_CONTEXT | (32 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_almostContiguousCP_OFDM_UL_62 = {
	"almostContiguousCP-OFDM-UL",
	"almostContiguousCP-OFDM-UL",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_almostContiguousCP_OFDM_UL_tags_62,
	sizeof(asn_DEF_NR_almostContiguousCP_OFDM_UL_tags_62)
		/sizeof(asn_DEF_NR_almostContiguousCP_OFDM_UL_tags_62[0]) - 1, /* 1 */
	asn_DEF_NR_almostContiguousCP_OFDM_UL_tags_62,	/* Same as above */
	sizeof(asn_DEF_NR_almostContiguousCP_OFDM_UL_tags_62)
		/sizeof(asn_DEF_NR_almostContiguousCP_OFDM_UL_tags_62[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_almostContiguousCP_OFDM_UL_constr_62,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_almostContiguousCP_OFDM_UL_specs_62	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_sp_CSI_RS_value2enum_64[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_sp_CSI_RS_enum2value_64[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_sp_CSI_RS_specs_64 = {
	asn_MAP_NR_sp_CSI_RS_value2enum_64,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_sp_CSI_RS_enum2value_64,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_sp_CSI_RS_tags_64[] = {
	(ASN_TAG_CLASS_CONTEXT | (33 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_sp_CSI_RS_64 = {
	"sp-CSI-RS",
	"sp-CSI-RS",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_sp_CSI_RS_tags_64,
	sizeof(asn_DEF_NR_sp_CSI_RS_tags_64)
		/sizeof(asn_DEF_NR_sp_CSI_RS_tags_64[0]) - 1, /* 1 */
	asn_DEF_NR_sp_CSI_RS_tags_64,	/* Same as above */
	sizeof(asn_DEF_NR_sp_CSI_RS_tags_64)
		/sizeof(asn_DEF_NR_sp_CSI_RS_tags_64[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_sp_CSI_RS_constr_64,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_sp_CSI_RS_specs_64	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_sp_CSI_IM_value2enum_66[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_sp_CSI_IM_enum2value_66[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_sp_CSI_IM_specs_66 = {
	asn_MAP_NR_sp_CSI_IM_value2enum_66,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_sp_CSI_IM_enum2value_66,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_sp_CSI_IM_tags_66[] = {
	(ASN_TAG_CLASS_CONTEXT | (34 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_sp_CSI_IM_66 = {
	"sp-CSI-IM",
	"sp-CSI-IM",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_sp_CSI_IM_tags_66,
	sizeof(asn_DEF_NR_sp_CSI_IM_tags_66)
		/sizeof(asn_DEF_NR_sp_CSI_IM_tags_66[0]) - 1, /* 1 */
	asn_DEF_NR_sp_CSI_IM_tags_66,	/* Same as above */
	sizeof(asn_DEF_NR_sp_CSI_IM_tags_66)
		/sizeof(asn_DEF_NR_sp_CSI_IM_tags_66[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_sp_CSI_IM_constr_66,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_sp_CSI_IM_specs_66	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_tdd_MultiDL_UL_SwitchPerSlot_value2enum_68[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_tdd_MultiDL_UL_SwitchPerSlot_enum2value_68[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_tdd_MultiDL_UL_SwitchPerSlot_specs_68 = {
	asn_MAP_NR_tdd_MultiDL_UL_SwitchPerSlot_value2enum_68,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_tdd_MultiDL_UL_SwitchPerSlot_enum2value_68,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_tdd_MultiDL_UL_SwitchPerSlot_tags_68[] = {
	(ASN_TAG_CLASS_CONTEXT | (35 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_tdd_MultiDL_UL_SwitchPerSlot_68 = {
	"tdd-MultiDL-UL-SwitchPerSlot",
	"tdd-MultiDL-UL-SwitchPerSlot",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_tdd_MultiDL_UL_SwitchPerSlot_tags_68,
	sizeof(asn_DEF_NR_tdd_MultiDL_UL_SwitchPerSlot_tags_68)
		/sizeof(asn_DEF_NR_tdd_MultiDL_UL_SwitchPerSlot_tags_68[0]) - 1, /* 1 */
	asn_DEF_NR_tdd_MultiDL_UL_SwitchPerSlot_tags_68,	/* Same as above */
	sizeof(asn_DEF_NR_tdd_MultiDL_UL_SwitchPerSlot_tags_68)
		/sizeof(asn_DEF_NR_tdd_MultiDL_UL_SwitchPerSlot_tags_68[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_tdd_MultiDL_UL_SwitchPerSlot_constr_68,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_tdd_MultiDL_UL_SwitchPerSlot_specs_68	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_multipleCORESET_value2enum_70[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_multipleCORESET_enum2value_70[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_multipleCORESET_specs_70 = {
	asn_MAP_NR_multipleCORESET_value2enum_70,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_multipleCORESET_enum2value_70,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_multipleCORESET_tags_70[] = {
	(ASN_TAG_CLASS_CONTEXT | (36 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_multipleCORESET_70 = {
	"multipleCORESET",
	"multipleCORESET",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_multipleCORESET_tags_70,
	sizeof(asn_DEF_NR_multipleCORESET_tags_70)
		/sizeof(asn_DEF_NR_multipleCORESET_tags_70[0]) - 1, /* 1 */
	asn_DEF_NR_multipleCORESET_tags_70,	/* Same as above */
	sizeof(asn_DEF_NR_multipleCORESET_tags_70)
		/sizeof(asn_DEF_NR_multipleCORESET_tags_70[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_multipleCORESET_constr_70,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_multipleCORESET_specs_70	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_sameSymbol_value2enum_78[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_sameSymbol_enum2value_78[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_sameSymbol_specs_78 = {
	asn_MAP_NR_sameSymbol_value2enum_78,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_sameSymbol_enum2value_78,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_sameSymbol_tags_78[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_sameSymbol_78 = {
	"sameSymbol",
	"sameSymbol",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_sameSymbol_tags_78,
	sizeof(asn_DEF_NR_sameSymbol_tags_78)
		/sizeof(asn_DEF_NR_sameSymbol_tags_78[0]) - 1, /* 1 */
	asn_DEF_NR_sameSymbol_tags_78,	/* Same as above */
	sizeof(asn_DEF_NR_sameSymbol_tags_78)
		/sizeof(asn_DEF_NR_sameSymbol_tags_78[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_sameSymbol_constr_78,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_sameSymbol_specs_78	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_diffSymbol_value2enum_80[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_diffSymbol_enum2value_80[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_diffSymbol_specs_80 = {
	asn_MAP_NR_diffSymbol_value2enum_80,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_diffSymbol_enum2value_80,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_diffSymbol_tags_80[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_diffSymbol_80 = {
	"diffSymbol",
	"diffSymbol",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_diffSymbol_tags_80,
	sizeof(asn_DEF_NR_diffSymbol_tags_80)
		/sizeof(asn_DEF_NR_diffSymbol_tags_80[0]) - 1, /* 1 */
	asn_DEF_NR_diffSymbol_tags_80,	/* Same as above */
	sizeof(asn_DEF_NR_diffSymbol_tags_80)
		/sizeof(asn_DEF_NR_diffSymbol_tags_80[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_diffSymbol_constr_80,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_diffSymbol_specs_80	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_77[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_Phy_ParametersFRX_Diff__ext1__mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot, sameSymbol),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_sameSymbol_78,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"sameSymbol"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersFRX_Diff__ext1__mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot, diffSymbol),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_diffSymbol_80,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"diffSymbol"
		},
};
static const int asn_MAP_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_oms_77[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_tags_77[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_tag2el_77[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* sameSymbol */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* diffSymbol */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_specs_77 = {
	sizeof(struct NR_Phy_ParametersFRX_Diff__ext1__mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot),
	offsetof(struct NR_Phy_ParametersFRX_Diff__ext1__mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot, _asn_ctx),
	asn_MAP_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_tag2el_77,
	2,	/* Count of tags in the map */
	asn_MAP_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_oms_77,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_77 = {
	"mux-SR-HARQ-ACK-CSI-PUCCH-OncePerSlot",
	"mux-SR-HARQ-ACK-CSI-PUCCH-OncePerSlot",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_tags_77,
	sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_tags_77)
		/sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_tags_77[0]) - 1, /* 1 */
	asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_tags_77,	/* Same as above */
	sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_tags_77)
		/sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_tags_77[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_77,
	2,	/* Elements count */
	&asn_SPC_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_specs_77	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_mux_SR_HARQ_ACK_PUCCH_value2enum_82[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_mux_SR_HARQ_ACK_PUCCH_enum2value_82[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_mux_SR_HARQ_ACK_PUCCH_specs_82 = {
	asn_MAP_NR_mux_SR_HARQ_ACK_PUCCH_value2enum_82,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_mux_SR_HARQ_ACK_PUCCH_enum2value_82,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_mux_SR_HARQ_ACK_PUCCH_tags_82[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mux_SR_HARQ_ACK_PUCCH_82 = {
	"mux-SR-HARQ-ACK-PUCCH",
	"mux-SR-HARQ-ACK-PUCCH",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_mux_SR_HARQ_ACK_PUCCH_tags_82,
	sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_PUCCH_tags_82)
		/sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_PUCCH_tags_82[0]) - 1, /* 1 */
	asn_DEF_NR_mux_SR_HARQ_ACK_PUCCH_tags_82,	/* Same as above */
	sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_PUCCH_tags_82)
		/sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_PUCCH_tags_82[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_mux_SR_HARQ_ACK_PUCCH_constr_82,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_mux_SR_HARQ_ACK_PUCCH_specs_82	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_mux_MultipleGroupCtrlCH_Overlap_value2enum_84[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_mux_MultipleGroupCtrlCH_Overlap_enum2value_84[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_mux_MultipleGroupCtrlCH_Overlap_specs_84 = {
	asn_MAP_NR_mux_MultipleGroupCtrlCH_Overlap_value2enum_84,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_mux_MultipleGroupCtrlCH_Overlap_enum2value_84,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_mux_MultipleGroupCtrlCH_Overlap_tags_84[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mux_MultipleGroupCtrlCH_Overlap_84 = {
	"mux-MultipleGroupCtrlCH-Overlap",
	"mux-MultipleGroupCtrlCH-Overlap",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_mux_MultipleGroupCtrlCH_Overlap_tags_84,
	sizeof(asn_DEF_NR_mux_MultipleGroupCtrlCH_Overlap_tags_84)
		/sizeof(asn_DEF_NR_mux_MultipleGroupCtrlCH_Overlap_tags_84[0]) - 1, /* 1 */
	asn_DEF_NR_mux_MultipleGroupCtrlCH_Overlap_tags_84,	/* Same as above */
	sizeof(asn_DEF_NR_mux_MultipleGroupCtrlCH_Overlap_tags_84)
		/sizeof(asn_DEF_NR_mux_MultipleGroupCtrlCH_Overlap_tags_84[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_mux_MultipleGroupCtrlCH_Overlap_constr_84,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_mux_MultipleGroupCtrlCH_Overlap_specs_84	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dl_SchedulingOffset_PDSCH_TypeA_value2enum_86[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_dl_SchedulingOffset_PDSCH_TypeA_enum2value_86[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dl_SchedulingOffset_PDSCH_TypeA_specs_86 = {
	asn_MAP_NR_dl_SchedulingOffset_PDSCH_TypeA_value2enum_86,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dl_SchedulingOffset_PDSCH_TypeA_enum2value_86,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeA_tags_86[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeA_86 = {
	"dl-SchedulingOffset-PDSCH-TypeA",
	"dl-SchedulingOffset-PDSCH-TypeA",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeA_tags_86,
	sizeof(asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeA_tags_86)
		/sizeof(asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeA_tags_86[0]) - 1, /* 1 */
	asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeA_tags_86,	/* Same as above */
	sizeof(asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeA_tags_86)
		/sizeof(asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeA_tags_86[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dl_SchedulingOffset_PDSCH_TypeA_constr_86,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dl_SchedulingOffset_PDSCH_TypeA_specs_86	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dl_SchedulingOffset_PDSCH_TypeB_value2enum_88[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_dl_SchedulingOffset_PDSCH_TypeB_enum2value_88[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dl_SchedulingOffset_PDSCH_TypeB_specs_88 = {
	asn_MAP_NR_dl_SchedulingOffset_PDSCH_TypeB_value2enum_88,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dl_SchedulingOffset_PDSCH_TypeB_enum2value_88,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeB_tags_88[] = {
	(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeB_88 = {
	"dl-SchedulingOffset-PDSCH-TypeB",
	"dl-SchedulingOffset-PDSCH-TypeB",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeB_tags_88,
	sizeof(asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeB_tags_88)
		/sizeof(asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeB_tags_88[0]) - 1, /* 1 */
	asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeB_tags_88,	/* Same as above */
	sizeof(asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeB_tags_88)
		/sizeof(asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeB_tags_88[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dl_SchedulingOffset_PDSCH_TypeB_constr_88,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dl_SchedulingOffset_PDSCH_TypeB_specs_88	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_ul_SchedulingOffset_value2enum_90[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_ul_SchedulingOffset_enum2value_90[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ul_SchedulingOffset_specs_90 = {
	asn_MAP_NR_ul_SchedulingOffset_value2enum_90,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ul_SchedulingOffset_enum2value_90,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ul_SchedulingOffset_tags_90[] = {
	(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ul_SchedulingOffset_90 = {
	"ul-SchedulingOffset",
	"ul-SchedulingOffset",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ul_SchedulingOffset_tags_90,
	sizeof(asn_DEF_NR_ul_SchedulingOffset_tags_90)
		/sizeof(asn_DEF_NR_ul_SchedulingOffset_tags_90[0]) - 1, /* 1 */
	asn_DEF_NR_ul_SchedulingOffset_tags_90,	/* Same as above */
	sizeof(asn_DEF_NR_ul_SchedulingOffset_tags_90)
		/sizeof(asn_DEF_NR_ul_SchedulingOffset_tags_90[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ul_SchedulingOffset_constr_90,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ul_SchedulingOffset_specs_90	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dl_64QAM_MCS_TableAlt_value2enum_92[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_dl_64QAM_MCS_TableAlt_enum2value_92[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dl_64QAM_MCS_TableAlt_specs_92 = {
	asn_MAP_NR_dl_64QAM_MCS_TableAlt_value2enum_92,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dl_64QAM_MCS_TableAlt_enum2value_92,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dl_64QAM_MCS_TableAlt_tags_92[] = {
	(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dl_64QAM_MCS_TableAlt_92 = {
	"dl-64QAM-MCS-TableAlt",
	"dl-64QAM-MCS-TableAlt",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dl_64QAM_MCS_TableAlt_tags_92,
	sizeof(asn_DEF_NR_dl_64QAM_MCS_TableAlt_tags_92)
		/sizeof(asn_DEF_NR_dl_64QAM_MCS_TableAlt_tags_92[0]) - 1, /* 1 */
	asn_DEF_NR_dl_64QAM_MCS_TableAlt_tags_92,	/* Same as above */
	sizeof(asn_DEF_NR_dl_64QAM_MCS_TableAlt_tags_92)
		/sizeof(asn_DEF_NR_dl_64QAM_MCS_TableAlt_tags_92[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dl_64QAM_MCS_TableAlt_constr_92,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dl_64QAM_MCS_TableAlt_specs_92	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_ul_64QAM_MCS_TableAlt_value2enum_94[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_ul_64QAM_MCS_TableAlt_enum2value_94[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ul_64QAM_MCS_TableAlt_specs_94 = {
	asn_MAP_NR_ul_64QAM_MCS_TableAlt_value2enum_94,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ul_64QAM_MCS_TableAlt_enum2value_94,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ul_64QAM_MCS_TableAlt_tags_94[] = {
	(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ul_64QAM_MCS_TableAlt_94 = {
	"ul-64QAM-MCS-TableAlt",
	"ul-64QAM-MCS-TableAlt",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ul_64QAM_MCS_TableAlt_tags_94,
	sizeof(asn_DEF_NR_ul_64QAM_MCS_TableAlt_tags_94)
		/sizeof(asn_DEF_NR_ul_64QAM_MCS_TableAlt_tags_94[0]) - 1, /* 1 */
	asn_DEF_NR_ul_64QAM_MCS_TableAlt_tags_94,	/* Same as above */
	sizeof(asn_DEF_NR_ul_64QAM_MCS_TableAlt_tags_94)
		/sizeof(asn_DEF_NR_ul_64QAM_MCS_TableAlt_tags_94[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ul_64QAM_MCS_TableAlt_constr_94,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ul_64QAM_MCS_TableAlt_specs_94	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_cqi_TableAlt_value2enum_96[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_cqi_TableAlt_enum2value_96[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_cqi_TableAlt_specs_96 = {
	asn_MAP_NR_cqi_TableAlt_value2enum_96,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_cqi_TableAlt_enum2value_96,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_cqi_TableAlt_tags_96[] = {
	(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_cqi_TableAlt_96 = {
	"cqi-TableAlt",
	"cqi-TableAlt",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_cqi_TableAlt_tags_96,
	sizeof(asn_DEF_NR_cqi_TableAlt_tags_96)
		/sizeof(asn_DEF_NR_cqi_TableAlt_tags_96[0]) - 1, /* 1 */
	asn_DEF_NR_cqi_TableAlt_tags_96,	/* Same as above */
	sizeof(asn_DEF_NR_cqi_TableAlt_tags_96)
		/sizeof(asn_DEF_NR_cqi_TableAlt_tags_96[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_cqi_TableAlt_constr_96,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_cqi_TableAlt_specs_96	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_oneFL_DMRS_TwoAdditionalDMRS_UL_value2enum_98[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_oneFL_DMRS_TwoAdditionalDMRS_UL_enum2value_98[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_oneFL_DMRS_TwoAdditionalDMRS_UL_specs_98 = {
	asn_MAP_NR_oneFL_DMRS_TwoAdditionalDMRS_UL_value2enum_98,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_oneFL_DMRS_TwoAdditionalDMRS_UL_enum2value_98,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_oneFL_DMRS_TwoAdditionalDMRS_UL_tags_98[] = {
	(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_oneFL_DMRS_TwoAdditionalDMRS_UL_98 = {
	"oneFL-DMRS-TwoAdditionalDMRS-UL",
	"oneFL-DMRS-TwoAdditionalDMRS-UL",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_oneFL_DMRS_TwoAdditionalDMRS_UL_tags_98,
	sizeof(asn_DEF_NR_oneFL_DMRS_TwoAdditionalDMRS_UL_tags_98)
		/sizeof(asn_DEF_NR_oneFL_DMRS_TwoAdditionalDMRS_UL_tags_98[0]) - 1, /* 1 */
	asn_DEF_NR_oneFL_DMRS_TwoAdditionalDMRS_UL_tags_98,	/* Same as above */
	sizeof(asn_DEF_NR_oneFL_DMRS_TwoAdditionalDMRS_UL_tags_98)
		/sizeof(asn_DEF_NR_oneFL_DMRS_TwoAdditionalDMRS_UL_tags_98[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_oneFL_DMRS_TwoAdditionalDMRS_UL_constr_98,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_oneFL_DMRS_TwoAdditionalDMRS_UL_specs_98	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_twoFL_DMRS_TwoAdditionalDMRS_UL_value2enum_100[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_twoFL_DMRS_TwoAdditionalDMRS_UL_enum2value_100[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_twoFL_DMRS_TwoAdditionalDMRS_UL_specs_100 = {
	asn_MAP_NR_twoFL_DMRS_TwoAdditionalDMRS_UL_value2enum_100,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_twoFL_DMRS_TwoAdditionalDMRS_UL_enum2value_100,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_twoFL_DMRS_TwoAdditionalDMRS_UL_tags_100[] = {
	(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_twoFL_DMRS_TwoAdditionalDMRS_UL_100 = {
	"twoFL-DMRS-TwoAdditionalDMRS-UL",
	"twoFL-DMRS-TwoAdditionalDMRS-UL",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_twoFL_DMRS_TwoAdditionalDMRS_UL_tags_100,
	sizeof(asn_DEF_NR_twoFL_DMRS_TwoAdditionalDMRS_UL_tags_100)
		/sizeof(asn_DEF_NR_twoFL_DMRS_TwoAdditionalDMRS_UL_tags_100[0]) - 1, /* 1 */
	asn_DEF_NR_twoFL_DMRS_TwoAdditionalDMRS_UL_tags_100,	/* Same as above */
	sizeof(asn_DEF_NR_twoFL_DMRS_TwoAdditionalDMRS_UL_tags_100)
		/sizeof(asn_DEF_NR_twoFL_DMRS_TwoAdditionalDMRS_UL_tags_100[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_twoFL_DMRS_TwoAdditionalDMRS_UL_constr_100,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_twoFL_DMRS_TwoAdditionalDMRS_UL_specs_100	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_oneFL_DMRS_ThreeAdditionalDMRS_UL_value2enum_102[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_oneFL_DMRS_ThreeAdditionalDMRS_UL_enum2value_102[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_oneFL_DMRS_ThreeAdditionalDMRS_UL_specs_102 = {
	asn_MAP_NR_oneFL_DMRS_ThreeAdditionalDMRS_UL_value2enum_102,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_oneFL_DMRS_ThreeAdditionalDMRS_UL_enum2value_102,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_oneFL_DMRS_ThreeAdditionalDMRS_UL_tags_102[] = {
	(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_oneFL_DMRS_ThreeAdditionalDMRS_UL_102 = {
	"oneFL-DMRS-ThreeAdditionalDMRS-UL",
	"oneFL-DMRS-ThreeAdditionalDMRS-UL",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_oneFL_DMRS_ThreeAdditionalDMRS_UL_tags_102,
	sizeof(asn_DEF_NR_oneFL_DMRS_ThreeAdditionalDMRS_UL_tags_102)
		/sizeof(asn_DEF_NR_oneFL_DMRS_ThreeAdditionalDMRS_UL_tags_102[0]) - 1, /* 1 */
	asn_DEF_NR_oneFL_DMRS_ThreeAdditionalDMRS_UL_tags_102,	/* Same as above */
	sizeof(asn_DEF_NR_oneFL_DMRS_ThreeAdditionalDMRS_UL_tags_102)
		/sizeof(asn_DEF_NR_oneFL_DMRS_ThreeAdditionalDMRS_UL_tags_102[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_oneFL_DMRS_ThreeAdditionalDMRS_UL_constr_102,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_oneFL_DMRS_ThreeAdditionalDMRS_UL_specs_102	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext1_73[] = {
	{ ATF_POINTER, 15, offsetof(struct NR_Phy_ParametersFRX_Diff__ext1, csi_RS_IM_ReceptionForFeedback),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_CSI_RS_IM_ReceptionForFeedback,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"csi-RS-IM-ReceptionForFeedback"
		},
	{ ATF_POINTER, 14, offsetof(struct NR_Phy_ParametersFRX_Diff__ext1, csi_RS_ProcFrameworkForSRS),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_CSI_RS_ProcFrameworkForSRS,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"csi-RS-ProcFrameworkForSRS"
		},
	{ ATF_POINTER, 13, offsetof(struct NR_Phy_ParametersFRX_Diff__ext1, csi_ReportFramework),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_CSI_ReportFramework,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"csi-ReportFramework"
		},
	{ ATF_POINTER, 12, offsetof(struct NR_Phy_ParametersFRX_Diff__ext1, mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		0,
		&asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_OncePerSlot_77,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mux-SR-HARQ-ACK-CSI-PUCCH-OncePerSlot"
		},
	{ ATF_POINTER, 11, offsetof(struct NR_Phy_ParametersFRX_Diff__ext1, mux_SR_HARQ_ACK_PUCCH),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_mux_SR_HARQ_ACK_PUCCH_82,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mux-SR-HARQ-ACK-PUCCH"
		},
	{ ATF_POINTER, 10, offsetof(struct NR_Phy_ParametersFRX_Diff__ext1, mux_MultipleGroupCtrlCH_Overlap),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_mux_MultipleGroupCtrlCH_Overlap_84,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mux-MultipleGroupCtrlCH-Overlap"
		},
	{ ATF_POINTER, 9, offsetof(struct NR_Phy_ParametersFRX_Diff__ext1, dl_SchedulingOffset_PDSCH_TypeA),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeA_86,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dl-SchedulingOffset-PDSCH-TypeA"
		},
	{ ATF_POINTER, 8, offsetof(struct NR_Phy_ParametersFRX_Diff__ext1, dl_SchedulingOffset_PDSCH_TypeB),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeB_88,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dl-SchedulingOffset-PDSCH-TypeB"
		},
	{ ATF_POINTER, 7, offsetof(struct NR_Phy_ParametersFRX_Diff__ext1, ul_SchedulingOffset),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ul_SchedulingOffset_90,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ul-SchedulingOffset"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_Phy_ParametersFRX_Diff__ext1, dl_64QAM_MCS_TableAlt),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dl_64QAM_MCS_TableAlt_92,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dl-64QAM-MCS-TableAlt"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_Phy_ParametersFRX_Diff__ext1, ul_64QAM_MCS_TableAlt),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ul_64QAM_MCS_TableAlt_94,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ul-64QAM-MCS-TableAlt"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_Phy_ParametersFRX_Diff__ext1, cqi_TableAlt),
		(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_cqi_TableAlt_96,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"cqi-TableAlt"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_Phy_ParametersFRX_Diff__ext1, oneFL_DMRS_TwoAdditionalDMRS_UL),
		(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_oneFL_DMRS_TwoAdditionalDMRS_UL_98,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"oneFL-DMRS-TwoAdditionalDMRS-UL"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_Phy_ParametersFRX_Diff__ext1, twoFL_DMRS_TwoAdditionalDMRS_UL),
		(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_twoFL_DMRS_TwoAdditionalDMRS_UL_100,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"twoFL-DMRS-TwoAdditionalDMRS-UL"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersFRX_Diff__ext1, oneFL_DMRS_ThreeAdditionalDMRS_UL),
		(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_oneFL_DMRS_ThreeAdditionalDMRS_UL_102,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"oneFL-DMRS-ThreeAdditionalDMRS-UL"
		},
};
static const int asn_MAP_NR_ext1_oms_73[] = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_73[] = {
	(ASN_TAG_CLASS_CONTEXT | (37 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_73[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* csi-RS-IM-ReceptionForFeedback */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* csi-RS-ProcFrameworkForSRS */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* csi-ReportFramework */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* mux-SR-HARQ-ACK-CSI-PUCCH-OncePerSlot */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* mux-SR-HARQ-ACK-PUCCH */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* mux-MultipleGroupCtrlCH-Overlap */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* dl-SchedulingOffset-PDSCH-TypeA */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* dl-SchedulingOffset-PDSCH-TypeB */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* ul-SchedulingOffset */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* dl-64QAM-MCS-TableAlt */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 }, /* ul-64QAM-MCS-TableAlt */
    { (ASN_TAG_CLASS_CONTEXT | (11 << 2)), 11, 0, 0 }, /* cqi-TableAlt */
    { (ASN_TAG_CLASS_CONTEXT | (12 << 2)), 12, 0, 0 }, /* oneFL-DMRS-TwoAdditionalDMRS-UL */
    { (ASN_TAG_CLASS_CONTEXT | (13 << 2)), 13, 0, 0 }, /* twoFL-DMRS-TwoAdditionalDMRS-UL */
    { (ASN_TAG_CLASS_CONTEXT | (14 << 2)), 14, 0, 0 } /* oneFL-DMRS-ThreeAdditionalDMRS-UL */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_73 = {
	sizeof(struct NR_Phy_ParametersFRX_Diff__ext1),
	offsetof(struct NR_Phy_ParametersFRX_Diff__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_73,
	15,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_73,	/* Optional members */
	15, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_73 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_73,
	sizeof(asn_DEF_NR_ext1_tags_73)
		/sizeof(asn_DEF_NR_ext1_tags_73[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_73,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_73)
		/sizeof(asn_DEF_NR_ext1_tags_73[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_73,
	15,	/* Elements count */
	&asn_SPC_NR_ext1_specs_73	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_pdcch_BlindDetectionNRDC_105[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_Phy_ParametersFRX_Diff__ext2__pdcch_BlindDetectionNRDC, pdcch_BlindDetectionMCG_UE),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_pdcch_BlindDetectionMCG_UE_constr_106,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_pdcch_BlindDetectionMCG_UE_constraint_105
		},
		0, 0, /* No default value */
		"pdcch-BlindDetectionMCG-UE"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_Phy_ParametersFRX_Diff__ext2__pdcch_BlindDetectionNRDC, pdcch_BlindDetectionSCG_UE),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_pdcch_BlindDetectionSCG_UE_constr_107,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_pdcch_BlindDetectionSCG_UE_constraint_105
		},
		0, 0, /* No default value */
		"pdcch-BlindDetectionSCG-UE"
		},
};
static const ber_tlv_tag_t asn_DEF_NR_pdcch_BlindDetectionNRDC_tags_105[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_pdcch_BlindDetectionNRDC_tag2el_105[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* pdcch-BlindDetectionMCG-UE */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* pdcch-BlindDetectionSCG-UE */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_pdcch_BlindDetectionNRDC_specs_105 = {
	sizeof(struct NR_Phy_ParametersFRX_Diff__ext2__pdcch_BlindDetectionNRDC),
	offsetof(struct NR_Phy_ParametersFRX_Diff__ext2__pdcch_BlindDetectionNRDC, _asn_ctx),
	asn_MAP_NR_pdcch_BlindDetectionNRDC_tag2el_105,
	2,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pdcch_BlindDetectionNRDC_105 = {
	"pdcch-BlindDetectionNRDC",
	"pdcch-BlindDetectionNRDC",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_pdcch_BlindDetectionNRDC_tags_105,
	sizeof(asn_DEF_NR_pdcch_BlindDetectionNRDC_tags_105)
		/sizeof(asn_DEF_NR_pdcch_BlindDetectionNRDC_tags_105[0]) - 1, /* 1 */
	asn_DEF_NR_pdcch_BlindDetectionNRDC_tags_105,	/* Same as above */
	sizeof(asn_DEF_NR_pdcch_BlindDetectionNRDC_tags_105)
		/sizeof(asn_DEF_NR_pdcch_BlindDetectionNRDC_tags_105[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_pdcch_BlindDetectionNRDC_105,
	2,	/* Elements count */
	&asn_SPC_NR_pdcch_BlindDetectionNRDC_specs_105	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_value2enum_108[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_enum2value_108[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_specs_108 = {
	asn_MAP_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_value2enum_108,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_enum2value_108,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_tags_108[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_108 = {
	"mux-HARQ-ACK-PUSCH-DiffSymbol",
	"mux-HARQ-ACK-PUSCH-DiffSymbol",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_tags_108,
	sizeof(asn_DEF_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_tags_108)
		/sizeof(asn_DEF_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_tags_108[0]) - 1, /* 1 */
	asn_DEF_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_tags_108,	/* Same as above */
	sizeof(asn_DEF_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_tags_108)
		/sizeof(asn_DEF_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_tags_108[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_constr_108,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_specs_108	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext2_104[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_Phy_ParametersFRX_Diff__ext2, pdcch_BlindDetectionNRDC),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_pdcch_BlindDetectionNRDC_105,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pdcch-BlindDetectionNRDC"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersFRX_Diff__ext2, mux_HARQ_ACK_PUSCH_DiffSymbol),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_mux_HARQ_ACK_PUSCH_DiffSymbol_108,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mux-HARQ-ACK-PUSCH-DiffSymbol"
		},
};
static const int asn_MAP_NR_ext2_oms_104[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_ext2_tags_104[] = {
	(ASN_TAG_CLASS_CONTEXT | (38 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext2_tag2el_104[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* pdcch-BlindDetectionNRDC */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* mux-HARQ-ACK-PUSCH-DiffSymbol */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext2_specs_104 = {
	sizeof(struct NR_Phy_ParametersFRX_Diff__ext2),
	offsetof(struct NR_Phy_ParametersFRX_Diff__ext2, _asn_ctx),
	asn_MAP_NR_ext2_tag2el_104,
	2,	/* Count of tags in the map */
	asn_MAP_NR_ext2_oms_104,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext2_104 = {
	"ext2",
	"ext2",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext2_tags_104,
	sizeof(asn_DEF_NR_ext2_tags_104)
		/sizeof(asn_DEF_NR_ext2_tags_104[0]) - 1, /* 1 */
	asn_DEF_NR_ext2_tags_104,	/* Same as above */
	sizeof(asn_DEF_NR_ext2_tags_104)
		/sizeof(asn_DEF_NR_ext2_tags_104[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext2_104,
	2,	/* Elements count */
	&asn_SPC_NR_ext2_specs_104	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_type1_HARQ_ACK_Codebook_r16_value2enum_111[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_type1_HARQ_ACK_Codebook_r16_enum2value_111[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_type1_HARQ_ACK_Codebook_r16_specs_111 = {
	asn_MAP_NR_type1_HARQ_ACK_Codebook_r16_value2enum_111,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_type1_HARQ_ACK_Codebook_r16_enum2value_111,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_type1_HARQ_ACK_Codebook_r16_tags_111[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_type1_HARQ_ACK_Codebook_r16_111 = {
	"type1-HARQ-ACK-Codebook-r16",
	"type1-HARQ-ACK-Codebook-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_type1_HARQ_ACK_Codebook_r16_tags_111,
	sizeof(asn_DEF_NR_type1_HARQ_ACK_Codebook_r16_tags_111)
		/sizeof(asn_DEF_NR_type1_HARQ_ACK_Codebook_r16_tags_111[0]) - 1, /* 1 */
	asn_DEF_NR_type1_HARQ_ACK_Codebook_r16_tags_111,	/* Same as above */
	sizeof(asn_DEF_NR_type1_HARQ_ACK_Codebook_r16_tags_111)
		/sizeof(asn_DEF_NR_type1_HARQ_ACK_Codebook_r16_tags_111[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_type1_HARQ_ACK_Codebook_r16_constr_111,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_type1_HARQ_ACK_Codebook_r16_specs_111	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_enhancedPowerControl_r16_value2enum_113[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_enhancedPowerControl_r16_enum2value_113[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_enhancedPowerControl_r16_specs_113 = {
	asn_MAP_NR_enhancedPowerControl_r16_value2enum_113,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_enhancedPowerControl_r16_enum2value_113,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_enhancedPowerControl_r16_tags_113[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_enhancedPowerControl_r16_113 = {
	"enhancedPowerControl-r16",
	"enhancedPowerControl-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_enhancedPowerControl_r16_tags_113,
	sizeof(asn_DEF_NR_enhancedPowerControl_r16_tags_113)
		/sizeof(asn_DEF_NR_enhancedPowerControl_r16_tags_113[0]) - 1, /* 1 */
	asn_DEF_NR_enhancedPowerControl_r16_tags_113,	/* Same as above */
	sizeof(asn_DEF_NR_enhancedPowerControl_r16_tags_113)
		/sizeof(asn_DEF_NR_enhancedPowerControl_r16_tags_113[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_enhancedPowerControl_r16_constr_113,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_enhancedPowerControl_r16_specs_113	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_simultaneousTCI_ActMultipleCC_r16_value2enum_115[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_simultaneousTCI_ActMultipleCC_r16_enum2value_115[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_simultaneousTCI_ActMultipleCC_r16_specs_115 = {
	asn_MAP_NR_simultaneousTCI_ActMultipleCC_r16_value2enum_115,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_simultaneousTCI_ActMultipleCC_r16_enum2value_115,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_simultaneousTCI_ActMultipleCC_r16_tags_115[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_simultaneousTCI_ActMultipleCC_r16_115 = {
	"simultaneousTCI-ActMultipleCC-r16",
	"simultaneousTCI-ActMultipleCC-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_simultaneousTCI_ActMultipleCC_r16_tags_115,
	sizeof(asn_DEF_NR_simultaneousTCI_ActMultipleCC_r16_tags_115)
		/sizeof(asn_DEF_NR_simultaneousTCI_ActMultipleCC_r16_tags_115[0]) - 1, /* 1 */
	asn_DEF_NR_simultaneousTCI_ActMultipleCC_r16_tags_115,	/* Same as above */
	sizeof(asn_DEF_NR_simultaneousTCI_ActMultipleCC_r16_tags_115)
		/sizeof(asn_DEF_NR_simultaneousTCI_ActMultipleCC_r16_tags_115[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_simultaneousTCI_ActMultipleCC_r16_constr_115,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_simultaneousTCI_ActMultipleCC_r16_specs_115	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_simultaneousSpatialRelationMultipleCC_r16_value2enum_117[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_simultaneousSpatialRelationMultipleCC_r16_enum2value_117[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_simultaneousSpatialRelationMultipleCC_r16_specs_117 = {
	asn_MAP_NR_simultaneousSpatialRelationMultipleCC_r16_value2enum_117,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_simultaneousSpatialRelationMultipleCC_r16_enum2value_117,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_simultaneousSpatialRelationMultipleCC_r16_tags_117[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_simultaneousSpatialRelationMultipleCC_r16_117 = {
	"simultaneousSpatialRelationMultipleCC-r16",
	"simultaneousSpatialRelationMultipleCC-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_simultaneousSpatialRelationMultipleCC_r16_tags_117,
	sizeof(asn_DEF_NR_simultaneousSpatialRelationMultipleCC_r16_tags_117)
		/sizeof(asn_DEF_NR_simultaneousSpatialRelationMultipleCC_r16_tags_117[0]) - 1, /* 1 */
	asn_DEF_NR_simultaneousSpatialRelationMultipleCC_r16_tags_117,	/* Same as above */
	sizeof(asn_DEF_NR_simultaneousSpatialRelationMultipleCC_r16_tags_117)
		/sizeof(asn_DEF_NR_simultaneousSpatialRelationMultipleCC_r16_tags_117[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_simultaneousSpatialRelationMultipleCC_r16_constr_117,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_simultaneousSpatialRelationMultipleCC_r16_specs_117	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_cli_RSSI_FDM_DL_r16_value2enum_119[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_cli_RSSI_FDM_DL_r16_enum2value_119[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_cli_RSSI_FDM_DL_r16_specs_119 = {
	asn_MAP_NR_cli_RSSI_FDM_DL_r16_value2enum_119,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_cli_RSSI_FDM_DL_r16_enum2value_119,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_cli_RSSI_FDM_DL_r16_tags_119[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_cli_RSSI_FDM_DL_r16_119 = {
	"cli-RSSI-FDM-DL-r16",
	"cli-RSSI-FDM-DL-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_cli_RSSI_FDM_DL_r16_tags_119,
	sizeof(asn_DEF_NR_cli_RSSI_FDM_DL_r16_tags_119)
		/sizeof(asn_DEF_NR_cli_RSSI_FDM_DL_r16_tags_119[0]) - 1, /* 1 */
	asn_DEF_NR_cli_RSSI_FDM_DL_r16_tags_119,	/* Same as above */
	sizeof(asn_DEF_NR_cli_RSSI_FDM_DL_r16_tags_119)
		/sizeof(asn_DEF_NR_cli_RSSI_FDM_DL_r16_tags_119[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_cli_RSSI_FDM_DL_r16_constr_119,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_cli_RSSI_FDM_DL_r16_specs_119	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_cli_SRS_RSRP_FDM_DL_r16_value2enum_121[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_cli_SRS_RSRP_FDM_DL_r16_enum2value_121[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_cli_SRS_RSRP_FDM_DL_r16_specs_121 = {
	asn_MAP_NR_cli_SRS_RSRP_FDM_DL_r16_value2enum_121,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_cli_SRS_RSRP_FDM_DL_r16_enum2value_121,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_cli_SRS_RSRP_FDM_DL_r16_tags_121[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_cli_SRS_RSRP_FDM_DL_r16_121 = {
	"cli-SRS-RSRP-FDM-DL-r16",
	"cli-SRS-RSRP-FDM-DL-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_cli_SRS_RSRP_FDM_DL_r16_tags_121,
	sizeof(asn_DEF_NR_cli_SRS_RSRP_FDM_DL_r16_tags_121)
		/sizeof(asn_DEF_NR_cli_SRS_RSRP_FDM_DL_r16_tags_121[0]) - 1, /* 1 */
	asn_DEF_NR_cli_SRS_RSRP_FDM_DL_r16_tags_121,	/* Same as above */
	sizeof(asn_DEF_NR_cli_SRS_RSRP_FDM_DL_r16_tags_121)
		/sizeof(asn_DEF_NR_cli_SRS_RSRP_FDM_DL_r16_tags_121[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_cli_SRS_RSRP_FDM_DL_r16_constr_121,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_cli_SRS_RSRP_FDM_DL_r16_specs_121	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_maxLayersMIMO_Adaptation_r16_value2enum_123[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_maxLayersMIMO_Adaptation_r16_enum2value_123[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_maxLayersMIMO_Adaptation_r16_specs_123 = {
	asn_MAP_NR_maxLayersMIMO_Adaptation_r16_value2enum_123,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_maxLayersMIMO_Adaptation_r16_enum2value_123,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_maxLayersMIMO_Adaptation_r16_tags_123[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_maxLayersMIMO_Adaptation_r16_123 = {
	"maxLayersMIMO-Adaptation-r16",
	"maxLayersMIMO-Adaptation-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_maxLayersMIMO_Adaptation_r16_tags_123,
	sizeof(asn_DEF_NR_maxLayersMIMO_Adaptation_r16_tags_123)
		/sizeof(asn_DEF_NR_maxLayersMIMO_Adaptation_r16_tags_123[0]) - 1, /* 1 */
	asn_DEF_NR_maxLayersMIMO_Adaptation_r16_tags_123,	/* Same as above */
	sizeof(asn_DEF_NR_maxLayersMIMO_Adaptation_r16_tags_123)
		/sizeof(asn_DEF_NR_maxLayersMIMO_Adaptation_r16_tags_123[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_maxLayersMIMO_Adaptation_r16_constr_123,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_maxLayersMIMO_Adaptation_r16_specs_123	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_aggregationFactorSPS_DL_r16_value2enum_125[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_aggregationFactorSPS_DL_r16_enum2value_125[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_aggregationFactorSPS_DL_r16_specs_125 = {
	asn_MAP_NR_aggregationFactorSPS_DL_r16_value2enum_125,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_aggregationFactorSPS_DL_r16_enum2value_125,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_aggregationFactorSPS_DL_r16_tags_125[] = {
	(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_aggregationFactorSPS_DL_r16_125 = {
	"aggregationFactorSPS-DL-r16",
	"aggregationFactorSPS-DL-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_aggregationFactorSPS_DL_r16_tags_125,
	sizeof(asn_DEF_NR_aggregationFactorSPS_DL_r16_tags_125)
		/sizeof(asn_DEF_NR_aggregationFactorSPS_DL_r16_tags_125[0]) - 1, /* 1 */
	asn_DEF_NR_aggregationFactorSPS_DL_r16_tags_125,	/* Same as above */
	sizeof(asn_DEF_NR_aggregationFactorSPS_DL_r16_tags_125)
		/sizeof(asn_DEF_NR_aggregationFactorSPS_DL_r16_tags_125[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_aggregationFactorSPS_DL_r16_constr_125,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_aggregationFactorSPS_DL_r16_specs_125	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_maxNumberResWithinSlotAcrossCC_OneFR_r16_value2enum_128[] = {
	{ 0,	2,	"n2" },
	{ 1,	2,	"n4" },
	{ 2,	2,	"n8" },
	{ 3,	3,	"n12" },
	{ 4,	3,	"n16" },
	{ 5,	3,	"n32" },
	{ 6,	3,	"n64" },
	{ 7,	4,	"n128" }
};
static const unsigned int asn_MAP_NR_maxNumberResWithinSlotAcrossCC_OneFR_r16_enum2value_128[] = {
	3,	/* n12(3) */
	7,	/* n128(7) */
	4,	/* n16(4) */
	0,	/* n2(0) */
	5,	/* n32(5) */
	1,	/* n4(1) */
	6,	/* n64(6) */
	2	/* n8(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_maxNumberResWithinSlotAcrossCC_OneFR_r16_specs_128 = {
	asn_MAP_NR_maxNumberResWithinSlotAcrossCC_OneFR_r16_value2enum_128,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_maxNumberResWithinSlotAcrossCC_OneFR_r16_enum2value_128,	/* N => "tag"; sorted by N */
	8,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_maxNumberResWithinSlotAcrossCC_OneFR_r16_tags_128[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_maxNumberResWithinSlotAcrossCC_OneFR_r16_128 = {
	"maxNumberResWithinSlotAcrossCC-OneFR-r16",
	"maxNumberResWithinSlotAcrossCC-OneFR-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_maxNumberResWithinSlotAcrossCC_OneFR_r16_tags_128,
	sizeof(asn_DEF_NR_maxNumberResWithinSlotAcrossCC_OneFR_r16_tags_128)
		/sizeof(asn_DEF_NR_maxNumberResWithinSlotAcrossCC_OneFR_r16_tags_128[0]) - 1, /* 1 */
	asn_DEF_NR_maxNumberResWithinSlotAcrossCC_OneFR_r16_tags_128,	/* Same as above */
	sizeof(asn_DEF_NR_maxNumberResWithinSlotAcrossCC_OneFR_r16_tags_128)
		/sizeof(asn_DEF_NR_maxNumberResWithinSlotAcrossCC_OneFR_r16_tags_128[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_maxNumberResWithinSlotAcrossCC_OneFR_r16_constr_128,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_maxNumberResWithinSlotAcrossCC_OneFR_r16_specs_128	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_maxNumberResAcrossCC_OneFR_r16_value2enum_137[] = {
	{ 0,	2,	"n2" },
	{ 1,	2,	"n4" },
	{ 2,	2,	"n8" },
	{ 3,	3,	"n12" },
	{ 4,	3,	"n16" },
	{ 5,	3,	"n32" },
	{ 6,	3,	"n40" },
	{ 7,	3,	"n48" },
	{ 8,	3,	"n64" },
	{ 9,	3,	"n72" },
	{ 10,	3,	"n80" },
	{ 11,	3,	"n96" },
	{ 12,	4,	"n128" },
	{ 13,	4,	"n256" }
};
static const unsigned int asn_MAP_NR_maxNumberResAcrossCC_OneFR_r16_enum2value_137[] = {
	3,	/* n12(3) */
	12,	/* n128(12) */
	4,	/* n16(4) */
	0,	/* n2(0) */
	13,	/* n256(13) */
	5,	/* n32(5) */
	1,	/* n4(1) */
	6,	/* n40(6) */
	7,	/* n48(7) */
	8,	/* n64(8) */
	9,	/* n72(9) */
	2,	/* n8(2) */
	10,	/* n80(10) */
	11	/* n96(11) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_maxNumberResAcrossCC_OneFR_r16_specs_137 = {
	asn_MAP_NR_maxNumberResAcrossCC_OneFR_r16_value2enum_137,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_maxNumberResAcrossCC_OneFR_r16_enum2value_137,	/* N => "tag"; sorted by N */
	14,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_maxNumberResAcrossCC_OneFR_r16_tags_137[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_maxNumberResAcrossCC_OneFR_r16_137 = {
	"maxNumberResAcrossCC-OneFR-r16",
	"maxNumberResAcrossCC-OneFR-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_maxNumberResAcrossCC_OneFR_r16_tags_137,
	sizeof(asn_DEF_NR_maxNumberResAcrossCC_OneFR_r16_tags_137)
		/sizeof(asn_DEF_NR_maxNumberResAcrossCC_OneFR_r16_tags_137[0]) - 1, /* 1 */
	asn_DEF_NR_maxNumberResAcrossCC_OneFR_r16_tags_137,	/* Same as above */
	sizeof(asn_DEF_NR_maxNumberResAcrossCC_OneFR_r16_tags_137)
		/sizeof(asn_DEF_NR_maxNumberResAcrossCC_OneFR_r16_tags_137[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_maxNumberResAcrossCC_OneFR_r16_constr_137,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_maxNumberResAcrossCC_OneFR_r16_specs_137	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_maxTotalResourcesForOneFreqRange_r16_127[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_Phy_ParametersFRX_Diff__ext3__maxTotalResourcesForOneFreqRange_r16, maxNumberResWithinSlotAcrossCC_OneFR_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_maxNumberResWithinSlotAcrossCC_OneFR_r16_128,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"maxNumberResWithinSlotAcrossCC-OneFR-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersFRX_Diff__ext3__maxTotalResourcesForOneFreqRange_r16, maxNumberResAcrossCC_OneFR_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_maxNumberResAcrossCC_OneFR_r16_137,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"maxNumberResAcrossCC-OneFR-r16"
		},
};
static const int asn_MAP_NR_maxTotalResourcesForOneFreqRange_r16_oms_127[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_maxTotalResourcesForOneFreqRange_r16_tags_127[] = {
	(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_maxTotalResourcesForOneFreqRange_r16_tag2el_127[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* maxNumberResWithinSlotAcrossCC-OneFR-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* maxNumberResAcrossCC-OneFR-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_maxTotalResourcesForOneFreqRange_r16_specs_127 = {
	sizeof(struct NR_Phy_ParametersFRX_Diff__ext3__maxTotalResourcesForOneFreqRange_r16),
	offsetof(struct NR_Phy_ParametersFRX_Diff__ext3__maxTotalResourcesForOneFreqRange_r16, _asn_ctx),
	asn_MAP_NR_maxTotalResourcesForOneFreqRange_r16_tag2el_127,
	2,	/* Count of tags in the map */
	asn_MAP_NR_maxTotalResourcesForOneFreqRange_r16_oms_127,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_maxTotalResourcesForOneFreqRange_r16_127 = {
	"maxTotalResourcesForOneFreqRange-r16",
	"maxTotalResourcesForOneFreqRange-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_maxTotalResourcesForOneFreqRange_r16_tags_127,
	sizeof(asn_DEF_NR_maxTotalResourcesForOneFreqRange_r16_tags_127)
		/sizeof(asn_DEF_NR_maxTotalResourcesForOneFreqRange_r16_tags_127[0]) - 1, /* 1 */
	asn_DEF_NR_maxTotalResourcesForOneFreqRange_r16_tags_127,	/* Same as above */
	sizeof(asn_DEF_NR_maxTotalResourcesForOneFreqRange_r16_tags_127)
		/sizeof(asn_DEF_NR_maxTotalResourcesForOneFreqRange_r16_tags_127[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_maxTotalResourcesForOneFreqRange_r16_127,
	2,	/* Elements count */
	&asn_SPC_NR_maxTotalResourcesForOneFreqRange_r16_specs_127	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext3_110[] = {
	{ ATF_POINTER, 10, offsetof(struct NR_Phy_ParametersFRX_Diff__ext3, type1_HARQ_ACK_Codebook_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_type1_HARQ_ACK_Codebook_r16_111,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"type1-HARQ-ACK-Codebook-r16"
		},
	{ ATF_POINTER, 9, offsetof(struct NR_Phy_ParametersFRX_Diff__ext3, enhancedPowerControl_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_enhancedPowerControl_r16_113,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"enhancedPowerControl-r16"
		},
	{ ATF_POINTER, 8, offsetof(struct NR_Phy_ParametersFRX_Diff__ext3, simultaneousTCI_ActMultipleCC_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_simultaneousTCI_ActMultipleCC_r16_115,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"simultaneousTCI-ActMultipleCC-r16"
		},
	{ ATF_POINTER, 7, offsetof(struct NR_Phy_ParametersFRX_Diff__ext3, simultaneousSpatialRelationMultipleCC_r16),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_simultaneousSpatialRelationMultipleCC_r16_117,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"simultaneousSpatialRelationMultipleCC-r16"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_Phy_ParametersFRX_Diff__ext3, cli_RSSI_FDM_DL_r16),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_cli_RSSI_FDM_DL_r16_119,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"cli-RSSI-FDM-DL-r16"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_Phy_ParametersFRX_Diff__ext3, cli_SRS_RSRP_FDM_DL_r16),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_cli_SRS_RSRP_FDM_DL_r16_121,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"cli-SRS-RSRP-FDM-DL-r16"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_Phy_ParametersFRX_Diff__ext3, maxLayersMIMO_Adaptation_r16),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_maxLayersMIMO_Adaptation_r16_123,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"maxLayersMIMO-Adaptation-r16"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_Phy_ParametersFRX_Diff__ext3, aggregationFactorSPS_DL_r16),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_aggregationFactorSPS_DL_r16_125,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"aggregationFactorSPS-DL-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_Phy_ParametersFRX_Diff__ext3, maxTotalResourcesForOneFreqRange_r16),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		0,
		&asn_DEF_NR_maxTotalResourcesForOneFreqRange_r16_127,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"maxTotalResourcesForOneFreqRange-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersFRX_Diff__ext3, csi_ReportFrameworkExt_r16),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_CSI_ReportFrameworkExt_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"csi-ReportFrameworkExt-r16"
		},
};
static const int asn_MAP_NR_ext3_oms_110[] = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9 };
static const ber_tlv_tag_t asn_DEF_NR_ext3_tags_110[] = {
	(ASN_TAG_CLASS_CONTEXT | (39 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext3_tag2el_110[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* type1-HARQ-ACK-Codebook-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* enhancedPowerControl-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* simultaneousTCI-ActMultipleCC-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* simultaneousSpatialRelationMultipleCC-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* cli-RSSI-FDM-DL-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* cli-SRS-RSRP-FDM-DL-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* maxLayersMIMO-Adaptation-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* aggregationFactorSPS-DL-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* maxTotalResourcesForOneFreqRange-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 } /* csi-ReportFrameworkExt-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext3_specs_110 = {
	sizeof(struct NR_Phy_ParametersFRX_Diff__ext3),
	offsetof(struct NR_Phy_ParametersFRX_Diff__ext3, _asn_ctx),
	asn_MAP_NR_ext3_tag2el_110,
	10,	/* Count of tags in the map */
	asn_MAP_NR_ext3_oms_110,	/* Optional members */
	10, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext3_110 = {
	"ext3",
	"ext3",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext3_tags_110,
	sizeof(asn_DEF_NR_ext3_tags_110)
		/sizeof(asn_DEF_NR_ext3_tags_110[0]) - 1, /* 1 */
	asn_DEF_NR_ext3_tags_110,	/* Same as above */
	sizeof(asn_DEF_NR_ext3_tags_110)
		/sizeof(asn_DEF_NR_ext3_tags_110[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext3_110,
	10,	/* Elements count */
	&asn_SPC_NR_ext3_specs_110	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_twoTCI_Act_servingCellInCC_List_r16_value2enum_154[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_twoTCI_Act_servingCellInCC_List_r16_enum2value_154[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_twoTCI_Act_servingCellInCC_List_r16_specs_154 = {
	asn_MAP_NR_twoTCI_Act_servingCellInCC_List_r16_value2enum_154,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_twoTCI_Act_servingCellInCC_List_r16_enum2value_154,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_twoTCI_Act_servingCellInCC_List_r16_tags_154[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_twoTCI_Act_servingCellInCC_List_r16_154 = {
	"twoTCI-Act-servingCellInCC-List-r16",
	"twoTCI-Act-servingCellInCC-List-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_twoTCI_Act_servingCellInCC_List_r16_tags_154,
	sizeof(asn_DEF_NR_twoTCI_Act_servingCellInCC_List_r16_tags_154)
		/sizeof(asn_DEF_NR_twoTCI_Act_servingCellInCC_List_r16_tags_154[0]) - 1, /* 1 */
	asn_DEF_NR_twoTCI_Act_servingCellInCC_List_r16_tags_154,	/* Same as above */
	sizeof(asn_DEF_NR_twoTCI_Act_servingCellInCC_List_r16_tags_154)
		/sizeof(asn_DEF_NR_twoTCI_Act_servingCellInCC_List_r16_tags_154[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_twoTCI_Act_servingCellInCC_List_r16_constr_154,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_twoTCI_Act_servingCellInCC_List_r16_specs_154	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext4_153[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersFRX_Diff__ext4, twoTCI_Act_servingCellInCC_List_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_twoTCI_Act_servingCellInCC_List_r16_154,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"twoTCI-Act-servingCellInCC-List-r16"
		},
};
static const int asn_MAP_NR_ext4_oms_153[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext4_tags_153[] = {
	(ASN_TAG_CLASS_CONTEXT | (40 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext4_tag2el_153[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* twoTCI-Act-servingCellInCC-List-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext4_specs_153 = {
	sizeof(struct NR_Phy_ParametersFRX_Diff__ext4),
	offsetof(struct NR_Phy_ParametersFRX_Diff__ext4, _asn_ctx),
	asn_MAP_NR_ext4_tag2el_153,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext4_oms_153,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext4_153 = {
	"ext4",
	"ext4",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext4_tags_153,
	sizeof(asn_DEF_NR_ext4_tags_153)
		/sizeof(asn_DEF_NR_ext4_tags_153[0]) - 1, /* 1 */
	asn_DEF_NR_ext4_tags_153,	/* Same as above */
	sizeof(asn_DEF_NR_ext4_tags_153)
		/sizeof(asn_DEF_NR_ext4_tags_153[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext4_153,
	1,	/* Elements count */
	&asn_SPC_NR_ext4_specs_153	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_cri_RI_CQI_WithoutNon_PMI_PortInd_r16_value2enum_157[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_cri_RI_CQI_WithoutNon_PMI_PortInd_r16_enum2value_157[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_cri_RI_CQI_WithoutNon_PMI_PortInd_r16_specs_157 = {
	asn_MAP_NR_cri_RI_CQI_WithoutNon_PMI_PortInd_r16_value2enum_157,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_cri_RI_CQI_WithoutNon_PMI_PortInd_r16_enum2value_157,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_cri_RI_CQI_WithoutNon_PMI_PortInd_r16_tags_157[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_cri_RI_CQI_WithoutNon_PMI_PortInd_r16_157 = {
	"cri-RI-CQI-WithoutNon-PMI-PortInd-r16",
	"cri-RI-CQI-WithoutNon-PMI-PortInd-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_cri_RI_CQI_WithoutNon_PMI_PortInd_r16_tags_157,
	sizeof(asn_DEF_NR_cri_RI_CQI_WithoutNon_PMI_PortInd_r16_tags_157)
		/sizeof(asn_DEF_NR_cri_RI_CQI_WithoutNon_PMI_PortInd_r16_tags_157[0]) - 1, /* 1 */
	asn_DEF_NR_cri_RI_CQI_WithoutNon_PMI_PortInd_r16_tags_157,	/* Same as above */
	sizeof(asn_DEF_NR_cri_RI_CQI_WithoutNon_PMI_PortInd_r16_tags_157)
		/sizeof(asn_DEF_NR_cri_RI_CQI_WithoutNon_PMI_PortInd_r16_tags_157[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_cri_RI_CQI_WithoutNon_PMI_PortInd_r16_constr_157,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_cri_RI_CQI_WithoutNon_PMI_PortInd_r16_specs_157	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext5_156[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersFRX_Diff__ext5, cri_RI_CQI_WithoutNon_PMI_PortInd_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_cri_RI_CQI_WithoutNon_PMI_PortInd_r16_157,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"cri-RI-CQI-WithoutNon-PMI-PortInd-r16"
		},
};
static const int asn_MAP_NR_ext5_oms_156[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext5_tags_156[] = {
	(ASN_TAG_CLASS_CONTEXT | (41 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext5_tag2el_156[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* cri-RI-CQI-WithoutNon-PMI-PortInd-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext5_specs_156 = {
	sizeof(struct NR_Phy_ParametersFRX_Diff__ext5),
	offsetof(struct NR_Phy_ParametersFRX_Diff__ext5, _asn_ctx),
	asn_MAP_NR_ext5_tag2el_156,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext5_oms_156,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext5_156 = {
	"ext5",
	"ext5",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext5_tags_156,
	sizeof(asn_DEF_NR_ext5_tags_156)
		/sizeof(asn_DEF_NR_ext5_tags_156[0]) - 1, /* 1 */
	asn_DEF_NR_ext5_tags_156,	/* Same as above */
	sizeof(asn_DEF_NR_ext5_tags_156)
		/sizeof(asn_DEF_NR_ext5_tags_156[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext5_156,
	1,	/* Elements count */
	&asn_SPC_NR_ext5_specs_156	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_cqi_4_BitsSubbandTN_NonSharedSpectrumChAccess_r17_value2enum_160[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_cqi_4_BitsSubbandTN_NonSharedSpectrumChAccess_r17_enum2value_160[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_cqi_4_BitsSubbandTN_NonSharedSpectrumChAccess_r17_specs_160 = {
	asn_MAP_NR_cqi_4_BitsSubbandTN_NonSharedSpectrumChAccess_r17_value2enum_160,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_cqi_4_BitsSubbandTN_NonSharedSpectrumChAccess_r17_enum2value_160,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_cqi_4_BitsSubbandTN_NonSharedSpectrumChAccess_r17_tags_160[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_cqi_4_BitsSubbandTN_NonSharedSpectrumChAccess_r17_160 = {
	"cqi-4-BitsSubbandTN-NonSharedSpectrumChAccess-r17",
	"cqi-4-BitsSubbandTN-NonSharedSpectrumChAccess-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_cqi_4_BitsSubbandTN_NonSharedSpectrumChAccess_r17_tags_160,
	sizeof(asn_DEF_NR_cqi_4_BitsSubbandTN_NonSharedSpectrumChAccess_r17_tags_160)
		/sizeof(asn_DEF_NR_cqi_4_BitsSubbandTN_NonSharedSpectrumChAccess_r17_tags_160[0]) - 1, /* 1 */
	asn_DEF_NR_cqi_4_BitsSubbandTN_NonSharedSpectrumChAccess_r17_tags_160,	/* Same as above */
	sizeof(asn_DEF_NR_cqi_4_BitsSubbandTN_NonSharedSpectrumChAccess_r17_tags_160)
		/sizeof(asn_DEF_NR_cqi_4_BitsSubbandTN_NonSharedSpectrumChAccess_r17_tags_160[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_cqi_4_BitsSubbandTN_NonSharedSpectrumChAccess_r17_constr_160,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_cqi_4_BitsSubbandTN_NonSharedSpectrumChAccess_r17_specs_160	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext6_159[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersFRX_Diff__ext6, cqi_4_BitsSubbandTN_NonSharedSpectrumChAccess_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_cqi_4_BitsSubbandTN_NonSharedSpectrumChAccess_r17_160,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"cqi-4-BitsSubbandTN-NonSharedSpectrumChAccess-r17"
		},
};
static const int asn_MAP_NR_ext6_oms_159[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext6_tags_159[] = {
	(ASN_TAG_CLASS_CONTEXT | (42 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext6_tag2el_159[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* cqi-4-BitsSubbandTN-NonSharedSpectrumChAccess-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext6_specs_159 = {
	sizeof(struct NR_Phy_ParametersFRX_Diff__ext6),
	offsetof(struct NR_Phy_ParametersFRX_Diff__ext6, _asn_ctx),
	asn_MAP_NR_ext6_tag2el_159,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext6_oms_159,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext6_159 = {
	"ext6",
	"ext6",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext6_tags_159,
	sizeof(asn_DEF_NR_ext6_tags_159)
		/sizeof(asn_DEF_NR_ext6_tags_159[0]) - 1, /* 1 */
	asn_DEF_NR_ext6_tags_159,	/* Same as above */
	sizeof(asn_DEF_NR_ext6_tags_159)
		/sizeof(asn_DEF_NR_ext6_tags_159[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext6_159,
	1,	/* Elements count */
	&asn_SPC_NR_ext6_specs_159	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_Phy_ParametersFRX_Diff_1[] = {
	{ ATF_POINTER, 43, offsetof(struct NR_Phy_ParametersFRX_Diff, dynamicSFI),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dynamicSFI_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dynamicSFI"
		},
	{ ATF_POINTER, 42, offsetof(struct NR_Phy_ParametersFRX_Diff, dummy1),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_BIT_STRING,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_dummy1_constr_4,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_dummy1_constraint_1
		},
		0, 0, /* No default value */
		"dummy1"
		},
	{ ATF_POINTER, 41, offsetof(struct NR_Phy_ParametersFRX_Diff, twoFL_DMRS),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_BIT_STRING,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_twoFL_DMRS_constr_5,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_twoFL_DMRS_constraint_1
		},
		0, 0, /* No default value */
		"twoFL-DMRS"
		},
	{ ATF_POINTER, 40, offsetof(struct NR_Phy_ParametersFRX_Diff, dummy2),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_BIT_STRING,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_dummy2_constr_6,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_dummy2_constraint_1
		},
		0, 0, /* No default value */
		"dummy2"
		},
	{ ATF_POINTER, 39, offsetof(struct NR_Phy_ParametersFRX_Diff, dummy3),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_BIT_STRING,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_dummy3_constr_7,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_dummy3_constraint_1
		},
		0, 0, /* No default value */
		"dummy3"
		},
	{ ATF_POINTER, 38, offsetof(struct NR_Phy_ParametersFRX_Diff, supportedDMRS_TypeDL),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_supportedDMRS_TypeDL_8,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedDMRS-TypeDL"
		},
	{ ATF_POINTER, 37, offsetof(struct NR_Phy_ParametersFRX_Diff, supportedDMRS_TypeUL),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_supportedDMRS_TypeUL_11,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedDMRS-TypeUL"
		},
	{ ATF_POINTER, 36, offsetof(struct NR_Phy_ParametersFRX_Diff, semiOpenLoopCSI),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_semiOpenLoopCSI_14,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"semiOpenLoopCSI"
		},
	{ ATF_POINTER, 35, offsetof(struct NR_Phy_ParametersFRX_Diff, csi_ReportWithoutPMI),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_csi_ReportWithoutPMI_16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"csi-ReportWithoutPMI"
		},
	{ ATF_POINTER, 34, offsetof(struct NR_Phy_ParametersFRX_Diff, csi_ReportWithoutCQI),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_csi_ReportWithoutCQI_18,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"csi-ReportWithoutCQI"
		},
	{ ATF_POINTER, 33, offsetof(struct NR_Phy_ParametersFRX_Diff, onePortsPTRS),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_BIT_STRING,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_onePortsPTRS_constr_20,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_onePortsPTRS_constraint_1
		},
		0, 0, /* No default value */
		"onePortsPTRS"
		},
	{ ATF_POINTER, 32, offsetof(struct NR_Phy_ParametersFRX_Diff, twoPUCCH_F0_2_ConsecSymbols),
		(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_twoPUCCH_F0_2_ConsecSymbols_21,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"twoPUCCH-F0-2-ConsecSymbols"
		},
	{ ATF_POINTER, 31, offsetof(struct NR_Phy_ParametersFRX_Diff, pucch_F2_WithFH),
		(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pucch_F2_WithFH_23,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pucch-F2-WithFH"
		},
	{ ATF_POINTER, 30, offsetof(struct NR_Phy_ParametersFRX_Diff, pucch_F3_WithFH),
		(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pucch_F3_WithFH_25,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pucch-F3-WithFH"
		},
	{ ATF_POINTER, 29, offsetof(struct NR_Phy_ParametersFRX_Diff, pucch_F4_WithFH),
		(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pucch_F4_WithFH_27,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pucch-F4-WithFH"
		},
	{ ATF_POINTER, 28, offsetof(struct NR_Phy_ParametersFRX_Diff, pucch_F0_2WithoutFH),
		(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pucch_F0_2WithoutFH_29,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pucch-F0-2WithoutFH"
		},
	{ ATF_POINTER, 27, offsetof(struct NR_Phy_ParametersFRX_Diff, pucch_F1_3_4WithoutFH),
		(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pucch_F1_3_4WithoutFH_31,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pucch-F1-3-4WithoutFH"
		},
	{ ATF_POINTER, 26, offsetof(struct NR_Phy_ParametersFRX_Diff, mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot),
		(ASN_TAG_CLASS_CONTEXT | (17 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_mux_SR_HARQ_ACK_CSI_PUCCH_MultiPerSlot_33,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mux-SR-HARQ-ACK-CSI-PUCCH-MultiPerSlot"
		},
	{ ATF_POINTER, 25, offsetof(struct NR_Phy_ParametersFRX_Diff, uci_CodeBlockSegmentation),
		(ASN_TAG_CLASS_CONTEXT | (18 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_uci_CodeBlockSegmentation_35,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"uci-CodeBlockSegmentation"
		},
	{ ATF_POINTER, 24, offsetof(struct NR_Phy_ParametersFRX_Diff, onePUCCH_LongAndShortFormat),
		(ASN_TAG_CLASS_CONTEXT | (19 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_onePUCCH_LongAndShortFormat_37,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"onePUCCH-LongAndShortFormat"
		},
	{ ATF_POINTER, 23, offsetof(struct NR_Phy_ParametersFRX_Diff, twoPUCCH_AnyOthersInSlot),
		(ASN_TAG_CLASS_CONTEXT | (20 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_twoPUCCH_AnyOthersInSlot_39,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"twoPUCCH-AnyOthersInSlot"
		},
	{ ATF_POINTER, 22, offsetof(struct NR_Phy_ParametersFRX_Diff, intraSlotFreqHopping_PUSCH),
		(ASN_TAG_CLASS_CONTEXT | (21 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_intraSlotFreqHopping_PUSCH_41,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"intraSlotFreqHopping-PUSCH"
		},
	{ ATF_POINTER, 21, offsetof(struct NR_Phy_ParametersFRX_Diff, pusch_LBRM),
		(ASN_TAG_CLASS_CONTEXT | (22 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pusch_LBRM_43,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pusch-LBRM"
		},
	{ ATF_POINTER, 20, offsetof(struct NR_Phy_ParametersFRX_Diff, pdcch_BlindDetectionCA),
		(ASN_TAG_CLASS_CONTEXT | (23 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_pdcch_BlindDetectionCA_constr_45,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_pdcch_BlindDetectionCA_constraint_1
		},
		0, 0, /* No default value */
		"pdcch-BlindDetectionCA"
		},
	{ ATF_POINTER, 19, offsetof(struct NR_Phy_ParametersFRX_Diff, tpc_PUSCH_RNTI),
		(ASN_TAG_CLASS_CONTEXT | (24 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_tpc_PUSCH_RNTI_46,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"tpc-PUSCH-RNTI"
		},
	{ ATF_POINTER, 18, offsetof(struct NR_Phy_ParametersFRX_Diff, tpc_PUCCH_RNTI),
		(ASN_TAG_CLASS_CONTEXT | (25 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_tpc_PUCCH_RNTI_48,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"tpc-PUCCH-RNTI"
		},
	{ ATF_POINTER, 17, offsetof(struct NR_Phy_ParametersFRX_Diff, tpc_SRS_RNTI),
		(ASN_TAG_CLASS_CONTEXT | (26 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_tpc_SRS_RNTI_50,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"tpc-SRS-RNTI"
		},
	{ ATF_POINTER, 16, offsetof(struct NR_Phy_ParametersFRX_Diff, absoluteTPC_Command),
		(ASN_TAG_CLASS_CONTEXT | (27 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_absoluteTPC_Command_52,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"absoluteTPC-Command"
		},
	{ ATF_POINTER, 15, offsetof(struct NR_Phy_ParametersFRX_Diff, twoDifferentTPC_Loop_PUSCH),
		(ASN_TAG_CLASS_CONTEXT | (28 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_twoDifferentTPC_Loop_PUSCH_54,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"twoDifferentTPC-Loop-PUSCH"
		},
	{ ATF_POINTER, 14, offsetof(struct NR_Phy_ParametersFRX_Diff, twoDifferentTPC_Loop_PUCCH),
		(ASN_TAG_CLASS_CONTEXT | (29 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_twoDifferentTPC_Loop_PUCCH_56,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"twoDifferentTPC-Loop-PUCCH"
		},
	{ ATF_POINTER, 13, offsetof(struct NR_Phy_ParametersFRX_Diff, pusch_HalfPi_BPSK),
		(ASN_TAG_CLASS_CONTEXT | (30 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pusch_HalfPi_BPSK_58,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pusch-HalfPi-BPSK"
		},
	{ ATF_POINTER, 12, offsetof(struct NR_Phy_ParametersFRX_Diff, pucch_F3_4_HalfPi_BPSK),
		(ASN_TAG_CLASS_CONTEXT | (31 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pucch_F3_4_HalfPi_BPSK_60,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pucch-F3-4-HalfPi-BPSK"
		},
	{ ATF_POINTER, 11, offsetof(struct NR_Phy_ParametersFRX_Diff, almostContiguousCP_OFDM_UL),
		(ASN_TAG_CLASS_CONTEXT | (32 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_almostContiguousCP_OFDM_UL_62,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"almostContiguousCP-OFDM-UL"
		},
	{ ATF_POINTER, 10, offsetof(struct NR_Phy_ParametersFRX_Diff, sp_CSI_RS),
		(ASN_TAG_CLASS_CONTEXT | (33 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_sp_CSI_RS_64,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"sp-CSI-RS"
		},
	{ ATF_POINTER, 9, offsetof(struct NR_Phy_ParametersFRX_Diff, sp_CSI_IM),
		(ASN_TAG_CLASS_CONTEXT | (34 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_sp_CSI_IM_66,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"sp-CSI-IM"
		},
	{ ATF_POINTER, 8, offsetof(struct NR_Phy_ParametersFRX_Diff, tdd_MultiDL_UL_SwitchPerSlot),
		(ASN_TAG_CLASS_CONTEXT | (35 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_tdd_MultiDL_UL_SwitchPerSlot_68,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"tdd-MultiDL-UL-SwitchPerSlot"
		},
	{ ATF_POINTER, 7, offsetof(struct NR_Phy_ParametersFRX_Diff, multipleCORESET),
		(ASN_TAG_CLASS_CONTEXT | (36 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_multipleCORESET_70,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"multipleCORESET"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_Phy_ParametersFRX_Diff, ext1),
		(ASN_TAG_CLASS_CONTEXT | (37 << 2)),
		0,
		&asn_DEF_NR_ext1_73,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_Phy_ParametersFRX_Diff, ext2),
		(ASN_TAG_CLASS_CONTEXT | (38 << 2)),
		0,
		&asn_DEF_NR_ext2_104,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext2"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_Phy_ParametersFRX_Diff, ext3),
		(ASN_TAG_CLASS_CONTEXT | (39 << 2)),
		0,
		&asn_DEF_NR_ext3_110,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext3"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_Phy_ParametersFRX_Diff, ext4),
		(ASN_TAG_CLASS_CONTEXT | (40 << 2)),
		0,
		&asn_DEF_NR_ext4_153,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext4"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_Phy_ParametersFRX_Diff, ext5),
		(ASN_TAG_CLASS_CONTEXT | (41 << 2)),
		0,
		&asn_DEF_NR_ext5_156,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext5"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersFRX_Diff, ext6),
		(ASN_TAG_CLASS_CONTEXT | (42 << 2)),
		0,
		&asn_DEF_NR_ext6_159,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext6"
		},
};
static const int asn_MAP_NR_Phy_ParametersFRX_Diff_oms_1[] = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42 };
static const ber_tlv_tag_t asn_DEF_NR_Phy_ParametersFRX_Diff_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_Phy_ParametersFRX_Diff_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* dynamicSFI */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* dummy1 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* twoFL-DMRS */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* dummy2 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* dummy3 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* supportedDMRS-TypeDL */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* supportedDMRS-TypeUL */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* semiOpenLoopCSI */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* csi-ReportWithoutPMI */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* csi-ReportWithoutCQI */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 }, /* onePortsPTRS */
    { (ASN_TAG_CLASS_CONTEXT | (11 << 2)), 11, 0, 0 }, /* twoPUCCH-F0-2-ConsecSymbols */
    { (ASN_TAG_CLASS_CONTEXT | (12 << 2)), 12, 0, 0 }, /* pucch-F2-WithFH */
    { (ASN_TAG_CLASS_CONTEXT | (13 << 2)), 13, 0, 0 }, /* pucch-F3-WithFH */
    { (ASN_TAG_CLASS_CONTEXT | (14 << 2)), 14, 0, 0 }, /* pucch-F4-WithFH */
    { (ASN_TAG_CLASS_CONTEXT | (15 << 2)), 15, 0, 0 }, /* pucch-F0-2WithoutFH */
    { (ASN_TAG_CLASS_CONTEXT | (16 << 2)), 16, 0, 0 }, /* pucch-F1-3-4WithoutFH */
    { (ASN_TAG_CLASS_CONTEXT | (17 << 2)), 17, 0, 0 }, /* mux-SR-HARQ-ACK-CSI-PUCCH-MultiPerSlot */
    { (ASN_TAG_CLASS_CONTEXT | (18 << 2)), 18, 0, 0 }, /* uci-CodeBlockSegmentation */
    { (ASN_TAG_CLASS_CONTEXT | (19 << 2)), 19, 0, 0 }, /* onePUCCH-LongAndShortFormat */
    { (ASN_TAG_CLASS_CONTEXT | (20 << 2)), 20, 0, 0 }, /* twoPUCCH-AnyOthersInSlot */
    { (ASN_TAG_CLASS_CONTEXT | (21 << 2)), 21, 0, 0 }, /* intraSlotFreqHopping-PUSCH */
    { (ASN_TAG_CLASS_CONTEXT | (22 << 2)), 22, 0, 0 }, /* pusch-LBRM */
    { (ASN_TAG_CLASS_CONTEXT | (23 << 2)), 23, 0, 0 }, /* pdcch-BlindDetectionCA */
    { (ASN_TAG_CLASS_CONTEXT | (24 << 2)), 24, 0, 0 }, /* tpc-PUSCH-RNTI */
    { (ASN_TAG_CLASS_CONTEXT | (25 << 2)), 25, 0, 0 }, /* tpc-PUCCH-RNTI */
    { (ASN_TAG_CLASS_CONTEXT | (26 << 2)), 26, 0, 0 }, /* tpc-SRS-RNTI */
    { (ASN_TAG_CLASS_CONTEXT | (27 << 2)), 27, 0, 0 }, /* absoluteTPC-Command */
    { (ASN_TAG_CLASS_CONTEXT | (28 << 2)), 28, 0, 0 }, /* twoDifferentTPC-Loop-PUSCH */
    { (ASN_TAG_CLASS_CONTEXT | (29 << 2)), 29, 0, 0 }, /* twoDifferentTPC-Loop-PUCCH */
    { (ASN_TAG_CLASS_CONTEXT | (30 << 2)), 30, 0, 0 }, /* pusch-HalfPi-BPSK */
    { (ASN_TAG_CLASS_CONTEXT | (31 << 2)), 31, 0, 0 }, /* pucch-F3-4-HalfPi-BPSK */
    { (ASN_TAG_CLASS_CONTEXT | (32 << 2)), 32, 0, 0 }, /* almostContiguousCP-OFDM-UL */
    { (ASN_TAG_CLASS_CONTEXT | (33 << 2)), 33, 0, 0 }, /* sp-CSI-RS */
    { (ASN_TAG_CLASS_CONTEXT | (34 << 2)), 34, 0, 0 }, /* sp-CSI-IM */
    { (ASN_TAG_CLASS_CONTEXT | (35 << 2)), 35, 0, 0 }, /* tdd-MultiDL-UL-SwitchPerSlot */
    { (ASN_TAG_CLASS_CONTEXT | (36 << 2)), 36, 0, 0 }, /* multipleCORESET */
    { (ASN_TAG_CLASS_CONTEXT | (37 << 2)), 37, 0, 0 }, /* ext1 */
    { (ASN_TAG_CLASS_CONTEXT | (38 << 2)), 38, 0, 0 }, /* ext2 */
    { (ASN_TAG_CLASS_CONTEXT | (39 << 2)), 39, 0, 0 }, /* ext3 */
    { (ASN_TAG_CLASS_CONTEXT | (40 << 2)), 40, 0, 0 }, /* ext4 */
    { (ASN_TAG_CLASS_CONTEXT | (41 << 2)), 41, 0, 0 }, /* ext5 */
    { (ASN_TAG_CLASS_CONTEXT | (42 << 2)), 42, 0, 0 } /* ext6 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_Phy_ParametersFRX_Diff_specs_1 = {
	sizeof(struct NR_Phy_ParametersFRX_Diff),
	offsetof(struct NR_Phy_ParametersFRX_Diff, _asn_ctx),
	asn_MAP_NR_Phy_ParametersFRX_Diff_tag2el_1,
	43,	/* Count of tags in the map */
	asn_MAP_NR_Phy_ParametersFRX_Diff_oms_1,	/* Optional members */
	37, 6,	/* Root/Additions */
	37,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_Phy_ParametersFRX_Diff = {
	"Phy-ParametersFRX-Diff",
	"Phy-ParametersFRX-Diff",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_Phy_ParametersFRX_Diff_tags_1,
	sizeof(asn_DEF_NR_Phy_ParametersFRX_Diff_tags_1)
		/sizeof(asn_DEF_NR_Phy_ParametersFRX_Diff_tags_1[0]), /* 1 */
	asn_DEF_NR_Phy_ParametersFRX_Diff_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_Phy_ParametersFRX_Diff_tags_1)
		/sizeof(asn_DEF_NR_Phy_ParametersFRX_Diff_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_Phy_ParametersFRX_Diff_1,
	43,	/* Elements count */
	&asn_SPC_NR_Phy_ParametersFRX_Diff_specs_1	/* Additional specs */
};

