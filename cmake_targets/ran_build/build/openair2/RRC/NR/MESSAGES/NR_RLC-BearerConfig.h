/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_RLC_BearerConfig_H_
#define	_NR_RLC_BearerConfig_H_


#include <asn_application.h>

/* Including external dependencies */
#include "NR_LogicalChannelIdentity.h"
#include <NativeEnumerated.h>
#include "NR_SRB-Identity.h"
#include "NR_DRB-Identity.h"
#include <constr_CHOICE.h>
#include <constr_SEQUENCE.h>
#include "NR_LogicalChannelIdentityExt-r17.h"
#include "NR_SRB-Identity-v1700.h"

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_RLC_BearerConfig__servedRadioBearer_PR {
	NR_RLC_BearerConfig__servedRadioBearer_PR_NOTHING,	/* No components present */
	NR_RLC_BearerConfig__servedRadioBearer_PR_srb_Identity,
	NR_RLC_BearerConfig__servedRadioBearer_PR_drb_Identity
} NR_RLC_BearerConfig__servedRadioBearer_PR;
typedef enum NR_RLC_BearerConfig__reestablishRLC {
	NR_RLC_BearerConfig__reestablishRLC_true	= 0
} e_NR_RLC_BearerConfig__reestablishRLC;

/* Forward declarations */
struct NR_RLC_Config;
struct NR_LogicalChannelConfig;
struct NR_RLC_Config_v1610;
struct NR_RLC_Config_v1700;
struct NR_MulticastRLC_BearerConfig_r17;

/* NR_RLC-BearerConfig */
typedef struct NR_RLC_BearerConfig {
	NR_LogicalChannelIdentity_t	 logicalChannelIdentity;
	struct NR_RLC_BearerConfig__servedRadioBearer {
		NR_RLC_BearerConfig__servedRadioBearer_PR present;
		union NR_RLC_BearerConfig__NR_servedRadioBearer_u {
			NR_SRB_Identity_t	 srb_Identity;
			NR_DRB_Identity_t	 drb_Identity;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *servedRadioBearer;
	long	*reestablishRLC;	/* OPTIONAL */
	struct NR_RLC_Config	*rlc_Config;	/* OPTIONAL */
	struct NR_LogicalChannelConfig	*mac_LogicalChannelConfig;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	struct NR_RLC_BearerConfig__ext1 {
		struct NR_RLC_Config_v1610	*rlc_Config_v1610;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext1;
	struct NR_RLC_BearerConfig__ext2 {
		struct NR_RLC_Config_v1700	*rlc_Config_v1700;	/* OPTIONAL */
		NR_LogicalChannelIdentityExt_r17_t	*logicalChannelIdentityExt_r17;	/* OPTIONAL */
		struct NR_MulticastRLC_BearerConfig_r17	*multicastRLC_BearerConfig_r17;	/* OPTIONAL */
		NR_SRB_Identity_v1700_t	*servedRadioBearerSRB4_r17;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext2;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_RLC_BearerConfig_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_reestablishRLC_6;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_RLC_BearerConfig;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_RLC_BearerConfig_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_RLC_BearerConfig_1[7];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_RLC-Config.h"
#include "NR_LogicalChannelConfig.h"
#include "NR_RLC-Config-v1610.h"
#include "NR_RLC-Config-v1700.h"
#include "NR_MulticastRLC-BearerConfig-r17.h"

#endif	/* _NR_RLC_BearerConfig_H_ */
#include <asn_internal.h>
