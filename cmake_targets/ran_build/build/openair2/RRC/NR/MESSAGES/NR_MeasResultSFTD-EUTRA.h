/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_MeasResultSFTD_EUTRA_H_
#define	_NR_MeasResultSFTD_EUTRA_H_


#include <asn_application.h>

/* Including external dependencies */
#include "NR_EUTRA-PhysCellId.h"
#include <NativeInteger.h>
#include "NR_RSRP-Range.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* NR_MeasResultSFTD-EUTRA */
typedef struct NR_MeasResultSFTD_EUTRA {
	NR_EUTRA_PhysCellId_t	 eutra_PhysCellId;
	long	 sfn_OffsetResult;
	long	 frameBoundaryOffsetResult;
	NR_RSRP_Range_t	*rsrp_Result;	/* OPTIONAL */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_MeasResultSFTD_EUTRA_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_NR_MeasResultSFTD_EUTRA;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_MeasResultSFTD_EUTRA_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_MeasResultSFTD_EUTRA_1[4];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_MeasResultSFTD_EUTRA_H_ */
#include <asn_internal.h>
