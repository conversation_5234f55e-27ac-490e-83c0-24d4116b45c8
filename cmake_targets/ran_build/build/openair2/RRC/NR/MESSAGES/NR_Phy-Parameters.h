/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_Phy_Parameters_H_
#define	_NR_Phy_Parameters_H_


#include <asn_application.h>

/* Including external dependencies */
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct NR_Phy_ParametersCommon;
struct NR_Phy_ParametersXDD_Diff;
struct NR_Phy_ParametersFRX_Diff;
struct NR_Phy_ParametersFR1;
struct NR_Phy_ParametersFR2;

/* NR_Phy-Parameters */
typedef struct NR_Phy_Parameters {
	struct NR_Phy_ParametersCommon	*phy_ParametersCommon;	/* OPTIONAL */
	struct NR_Phy_ParametersXDD_Diff	*phy_ParametersXDD_Diff;	/* OPTIONAL */
	struct NR_Phy_ParametersFRX_Diff	*phy_ParametersFRX_Diff;	/* OPTIONAL */
	struct NR_Phy_ParametersFR1	*phy_ParametersFR1;	/* OPTIONAL */
	struct NR_Phy_ParametersFR2	*phy_ParametersFR2;	/* OPTIONAL */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_Phy_Parameters_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_NR_Phy_Parameters;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_Phy_Parameters_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_Phy_Parameters_1[5];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_Phy-ParametersCommon.h"
#include "NR_Phy-ParametersXDD-Diff.h"
#include "NR_Phy-ParametersFRX-Diff.h"
#include "NR_Phy-ParametersFR1.h"
#include "NR_Phy-ParametersFR2.h"

#endif	/* _NR_Phy_Parameters_H_ */
#include <asn_internal.h>
