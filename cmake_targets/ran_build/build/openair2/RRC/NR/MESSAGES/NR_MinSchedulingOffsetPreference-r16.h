/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_MinSchedulingOffsetPreference_r16_H_
#define	_NR_MinSchedulingOffsetPreference_r16_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_MinSchedulingOffsetPreference_r16__preferredK0_r16__preferredK0_SCS_15kHz_r16 {
	NR_MinSchedulingOffsetPreference_r16__preferredK0_r16__preferredK0_SCS_15kHz_r16_sl1	= 0,
	NR_MinSchedulingOffsetPreference_r16__preferredK0_r16__preferredK0_SCS_15kHz_r16_sl2	= 1,
	NR_MinSchedulingOffsetPreference_r16__preferredK0_r16__preferredK0_SCS_15kHz_r16_sl4	= 2,
	NR_MinSchedulingOffsetPreference_r16__preferredK0_r16__preferredK0_SCS_15kHz_r16_sl6	= 3
} e_NR_MinSchedulingOffsetPreference_r16__preferredK0_r16__preferredK0_SCS_15kHz_r16;
typedef enum NR_MinSchedulingOffsetPreference_r16__preferredK0_r16__preferredK0_SCS_30kHz_r16 {
	NR_MinSchedulingOffsetPreference_r16__preferredK0_r16__preferredK0_SCS_30kHz_r16_sl1	= 0,
	NR_MinSchedulingOffsetPreference_r16__preferredK0_r16__preferredK0_SCS_30kHz_r16_sl2	= 1,
	NR_MinSchedulingOffsetPreference_r16__preferredK0_r16__preferredK0_SCS_30kHz_r16_sl4	= 2,
	NR_MinSchedulingOffsetPreference_r16__preferredK0_r16__preferredK0_SCS_30kHz_r16_sl6	= 3
} e_NR_MinSchedulingOffsetPreference_r16__preferredK0_r16__preferredK0_SCS_30kHz_r16;
typedef enum NR_MinSchedulingOffsetPreference_r16__preferredK0_r16__preferredK0_SCS_60kHz_r16 {
	NR_MinSchedulingOffsetPreference_r16__preferredK0_r16__preferredK0_SCS_60kHz_r16_sl2	= 0,
	NR_MinSchedulingOffsetPreference_r16__preferredK0_r16__preferredK0_SCS_60kHz_r16_sl4	= 1,
	NR_MinSchedulingOffsetPreference_r16__preferredK0_r16__preferredK0_SCS_60kHz_r16_sl8	= 2,
	NR_MinSchedulingOffsetPreference_r16__preferredK0_r16__preferredK0_SCS_60kHz_r16_sl12	= 3
} e_NR_MinSchedulingOffsetPreference_r16__preferredK0_r16__preferredK0_SCS_60kHz_r16;
typedef enum NR_MinSchedulingOffsetPreference_r16__preferredK0_r16__preferredK0_SCS_120kHz_r16 {
	NR_MinSchedulingOffsetPreference_r16__preferredK0_r16__preferredK0_SCS_120kHz_r16_sl2	= 0,
	NR_MinSchedulingOffsetPreference_r16__preferredK0_r16__preferredK0_SCS_120kHz_r16_sl4	= 1,
	NR_MinSchedulingOffsetPreference_r16__preferredK0_r16__preferredK0_SCS_120kHz_r16_sl8	= 2,
	NR_MinSchedulingOffsetPreference_r16__preferredK0_r16__preferredK0_SCS_120kHz_r16_sl12	= 3
} e_NR_MinSchedulingOffsetPreference_r16__preferredK0_r16__preferredK0_SCS_120kHz_r16;
typedef enum NR_MinSchedulingOffsetPreference_r16__preferredK2_r16__preferredK2_SCS_15kHz_r16 {
	NR_MinSchedulingOffsetPreference_r16__preferredK2_r16__preferredK2_SCS_15kHz_r16_sl1	= 0,
	NR_MinSchedulingOffsetPreference_r16__preferredK2_r16__preferredK2_SCS_15kHz_r16_sl2	= 1,
	NR_MinSchedulingOffsetPreference_r16__preferredK2_r16__preferredK2_SCS_15kHz_r16_sl4	= 2,
	NR_MinSchedulingOffsetPreference_r16__preferredK2_r16__preferredK2_SCS_15kHz_r16_sl6	= 3
} e_NR_MinSchedulingOffsetPreference_r16__preferredK2_r16__preferredK2_SCS_15kHz_r16;
typedef enum NR_MinSchedulingOffsetPreference_r16__preferredK2_r16__preferredK2_SCS_30kHz_r16 {
	NR_MinSchedulingOffsetPreference_r16__preferredK2_r16__preferredK2_SCS_30kHz_r16_sl1	= 0,
	NR_MinSchedulingOffsetPreference_r16__preferredK2_r16__preferredK2_SCS_30kHz_r16_sl2	= 1,
	NR_MinSchedulingOffsetPreference_r16__preferredK2_r16__preferredK2_SCS_30kHz_r16_sl4	= 2,
	NR_MinSchedulingOffsetPreference_r16__preferredK2_r16__preferredK2_SCS_30kHz_r16_sl6	= 3
} e_NR_MinSchedulingOffsetPreference_r16__preferredK2_r16__preferredK2_SCS_30kHz_r16;
typedef enum NR_MinSchedulingOffsetPreference_r16__preferredK2_r16__preferredK2_SCS_60kHz_r16 {
	NR_MinSchedulingOffsetPreference_r16__preferredK2_r16__preferredK2_SCS_60kHz_r16_sl2	= 0,
	NR_MinSchedulingOffsetPreference_r16__preferredK2_r16__preferredK2_SCS_60kHz_r16_sl4	= 1,
	NR_MinSchedulingOffsetPreference_r16__preferredK2_r16__preferredK2_SCS_60kHz_r16_sl8	= 2,
	NR_MinSchedulingOffsetPreference_r16__preferredK2_r16__preferredK2_SCS_60kHz_r16_sl12	= 3
} e_NR_MinSchedulingOffsetPreference_r16__preferredK2_r16__preferredK2_SCS_60kHz_r16;
typedef enum NR_MinSchedulingOffsetPreference_r16__preferredK2_r16__preferredK2_SCS_120kHz_r16 {
	NR_MinSchedulingOffsetPreference_r16__preferredK2_r16__preferredK2_SCS_120kHz_r16_sl2	= 0,
	NR_MinSchedulingOffsetPreference_r16__preferredK2_r16__preferredK2_SCS_120kHz_r16_sl4	= 1,
	NR_MinSchedulingOffsetPreference_r16__preferredK2_r16__preferredK2_SCS_120kHz_r16_sl8	= 2,
	NR_MinSchedulingOffsetPreference_r16__preferredK2_r16__preferredK2_SCS_120kHz_r16_sl12	= 3
} e_NR_MinSchedulingOffsetPreference_r16__preferredK2_r16__preferredK2_SCS_120kHz_r16;

/* NR_MinSchedulingOffsetPreference-r16 */
typedef struct NR_MinSchedulingOffsetPreference_r16 {
	struct NR_MinSchedulingOffsetPreference_r16__preferredK0_r16 {
		long	*preferredK0_SCS_15kHz_r16;	/* OPTIONAL */
		long	*preferredK0_SCS_30kHz_r16;	/* OPTIONAL */
		long	*preferredK0_SCS_60kHz_r16;	/* OPTIONAL */
		long	*preferredK0_SCS_120kHz_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *preferredK0_r16;
	struct NR_MinSchedulingOffsetPreference_r16__preferredK2_r16 {
		long	*preferredK2_SCS_15kHz_r16;	/* OPTIONAL */
		long	*preferredK2_SCS_30kHz_r16;	/* OPTIONAL */
		long	*preferredK2_SCS_60kHz_r16;	/* OPTIONAL */
		long	*preferredK2_SCS_120kHz_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *preferredK2_r16;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_MinSchedulingOffsetPreference_r16_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_preferredK0_SCS_15kHz_r16_3;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_preferredK0_SCS_30kHz_r16_8;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_preferredK0_SCS_60kHz_r16_13;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_preferredK0_SCS_120kHz_r16_18;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_preferredK2_SCS_15kHz_r16_24;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_preferredK2_SCS_30kHz_r16_29;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_preferredK2_SCS_60kHz_r16_34;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_preferredK2_SCS_120kHz_r16_39;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_MinSchedulingOffsetPreference_r16;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_MinSchedulingOffsetPreference_r16_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_MinSchedulingOffsetPreference_r16_1[2];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_MinSchedulingOffsetPreference_r16_H_ */
#include <asn_internal.h>
