/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_MCCH-Config-r17.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_mcch_WindowStartSlot_r17_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 79L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_mcch_WindowDuration_r17_constr_4 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  7 }	/* (0..7) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_mcch_ModificationPeriod_r17_constr_13 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  15 }	/* (0..15) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_mcch_WindowStartSlot_r17_constr_3 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 7,  7,  0,  79 }	/* (0..79) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_mcch_WindowDuration_r17_value2enum_4[] = {
	{ 0,	3,	"sl2" },
	{ 1,	3,	"sl4" },
	{ 2,	3,	"sl8" },
	{ 3,	4,	"sl10" },
	{ 4,	4,	"sl20" },
	{ 5,	4,	"sl40" },
	{ 6,	4,	"sl80" },
	{ 7,	5,	"sl160" }
};
static const unsigned int asn_MAP_NR_mcch_WindowDuration_r17_enum2value_4[] = {
	3,	/* sl10(3) */
	7,	/* sl160(7) */
	0,	/* sl2(0) */
	4,	/* sl20(4) */
	1,	/* sl4(1) */
	5,	/* sl40(5) */
	2,	/* sl8(2) */
	6	/* sl80(6) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_mcch_WindowDuration_r17_specs_4 = {
	asn_MAP_NR_mcch_WindowDuration_r17_value2enum_4,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_mcch_WindowDuration_r17_enum2value_4,	/* N => "tag"; sorted by N */
	8,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_mcch_WindowDuration_r17_tags_4[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mcch_WindowDuration_r17_4 = {
	"mcch-WindowDuration-r17",
	"mcch-WindowDuration-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_mcch_WindowDuration_r17_tags_4,
	sizeof(asn_DEF_NR_mcch_WindowDuration_r17_tags_4)
		/sizeof(asn_DEF_NR_mcch_WindowDuration_r17_tags_4[0]) - 1, /* 1 */
	asn_DEF_NR_mcch_WindowDuration_r17_tags_4,	/* Same as above */
	sizeof(asn_DEF_NR_mcch_WindowDuration_r17_tags_4)
		/sizeof(asn_DEF_NR_mcch_WindowDuration_r17_tags_4[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_mcch_WindowDuration_r17_constr_4,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_mcch_WindowDuration_r17_specs_4	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_mcch_ModificationPeriod_r17_value2enum_13[] = {
	{ 0,	3,	"rf2" },
	{ 1,	3,	"rf4" },
	{ 2,	3,	"rf8" },
	{ 3,	4,	"rf16" },
	{ 4,	4,	"rf32" },
	{ 5,	4,	"rf64" },
	{ 6,	5,	"rf128" },
	{ 7,	5,	"rf256" },
	{ 8,	5,	"rf512" },
	{ 9,	6,	"rf1024" },
	{ 10,	5,	"r2048" },
	{ 11,	6,	"rf4096" },
	{ 12,	6,	"rf8192" },
	{ 13,	7,	"rf16384" },
	{ 14,	7,	"rf32768" },
	{ 15,	7,	"rf65536" }
};
static const unsigned int asn_MAP_NR_mcch_ModificationPeriod_r17_enum2value_13[] = {
	10,	/* r2048(10) */
	9,	/* rf1024(9) */
	6,	/* rf128(6) */
	3,	/* rf16(3) */
	13,	/* rf16384(13) */
	0,	/* rf2(0) */
	7,	/* rf256(7) */
	4,	/* rf32(4) */
	14,	/* rf32768(14) */
	1,	/* rf4(1) */
	11,	/* rf4096(11) */
	8,	/* rf512(8) */
	5,	/* rf64(5) */
	15,	/* rf65536(15) */
	2,	/* rf8(2) */
	12	/* rf8192(12) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_mcch_ModificationPeriod_r17_specs_13 = {
	asn_MAP_NR_mcch_ModificationPeriod_r17_value2enum_13,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_mcch_ModificationPeriod_r17_enum2value_13,	/* N => "tag"; sorted by N */
	16,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_mcch_ModificationPeriod_r17_tags_13[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mcch_ModificationPeriod_r17_13 = {
	"mcch-ModificationPeriod-r17",
	"mcch-ModificationPeriod-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_mcch_ModificationPeriod_r17_tags_13,
	sizeof(asn_DEF_NR_mcch_ModificationPeriod_r17_tags_13)
		/sizeof(asn_DEF_NR_mcch_ModificationPeriod_r17_tags_13[0]) - 1, /* 1 */
	asn_DEF_NR_mcch_ModificationPeriod_r17_tags_13,	/* Same as above */
	sizeof(asn_DEF_NR_mcch_ModificationPeriod_r17_tags_13)
		/sizeof(asn_DEF_NR_mcch_ModificationPeriod_r17_tags_13[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_mcch_ModificationPeriod_r17_constr_13,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_mcch_ModificationPeriod_r17_specs_13	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_MCCH_Config_r17_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_MCCH_Config_r17, mcch_RepetitionPeriodAndOffset_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_MCCH_RepetitionPeriodAndOffset_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mcch-RepetitionPeriodAndOffset-r17"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_MCCH_Config_r17, mcch_WindowStartSlot_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_mcch_WindowStartSlot_r17_constr_3,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_mcch_WindowStartSlot_r17_constraint_1
		},
		0, 0, /* No default value */
		"mcch-WindowStartSlot-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MCCH_Config_r17, mcch_WindowDuration_r17),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_mcch_WindowDuration_r17_4,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mcch-WindowDuration-r17"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_MCCH_Config_r17, mcch_ModificationPeriod_r17),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_mcch_ModificationPeriod_r17_13,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mcch-ModificationPeriod-r17"
		},
};
static const int asn_MAP_NR_MCCH_Config_r17_oms_1[] = { 2 };
static const ber_tlv_tag_t asn_DEF_NR_MCCH_Config_r17_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_MCCH_Config_r17_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* mcch-RepetitionPeriodAndOffset-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* mcch-WindowStartSlot-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* mcch-WindowDuration-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 } /* mcch-ModificationPeriod-r17 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_MCCH_Config_r17_specs_1 = {
	sizeof(struct NR_MCCH_Config_r17),
	offsetof(struct NR_MCCH_Config_r17, _asn_ctx),
	asn_MAP_NR_MCCH_Config_r17_tag2el_1,
	4,	/* Count of tags in the map */
	asn_MAP_NR_MCCH_Config_r17_oms_1,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_MCCH_Config_r17 = {
	"MCCH-Config-r17",
	"MCCH-Config-r17",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_MCCH_Config_r17_tags_1,
	sizeof(asn_DEF_NR_MCCH_Config_r17_tags_1)
		/sizeof(asn_DEF_NR_MCCH_Config_r17_tags_1[0]), /* 1 */
	asn_DEF_NR_MCCH_Config_r17_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_MCCH_Config_r17_tags_1)
		/sizeof(asn_DEF_NR_MCCH_Config_r17_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_MCCH_Config_r17_1,
	4,	/* Elements count */
	&asn_SPC_NR_MCCH_Config_r17_specs_1	/* Additional specs */
};

