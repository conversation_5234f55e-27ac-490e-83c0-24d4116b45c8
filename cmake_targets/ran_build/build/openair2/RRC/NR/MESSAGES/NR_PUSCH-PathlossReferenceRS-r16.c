/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_PUSCH-PathlossReferenceRS-r16.h"

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_referenceSignal_r16_constr_3 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static asn_TYPE_member_t asn_MBR_NR_referenceSignal_r16_3[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PUSCH_PathlossReferenceRS_r16__referenceSignal_r16, choice.ssb_Index_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_SSB_Index,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ssb-Index-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PUSCH_PathlossReferenceRS_r16__referenceSignal_r16, choice.csi_RS_Index_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_NZP_CSI_RS_ResourceId,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"csi-RS-Index-r16"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_NR_referenceSignal_r16_tag2el_3[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* ssb-Index-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* csi-RS-Index-r16 */
};
static asn_CHOICE_specifics_t asn_SPC_NR_referenceSignal_r16_specs_3 = {
	sizeof(struct NR_PUSCH_PathlossReferenceRS_r16__referenceSignal_r16),
	offsetof(struct NR_PUSCH_PathlossReferenceRS_r16__referenceSignal_r16, _asn_ctx),
	offsetof(struct NR_PUSCH_PathlossReferenceRS_r16__referenceSignal_r16, present),
	sizeof(((struct NR_PUSCH_PathlossReferenceRS_r16__referenceSignal_r16 *)0)->present),
	asn_MAP_NR_referenceSignal_r16_tag2el_3,
	2,	/* Count of tags in the map */
	0, 0,
	-1	/* Extensions start */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_referenceSignal_r16_3 = {
	"referenceSignal-r16",
	"referenceSignal-r16",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_referenceSignal_r16_constr_3,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		CHOICE_constraint
	},
	asn_MBR_NR_referenceSignal_r16_3,
	2,	/* Elements count */
	&asn_SPC_NR_referenceSignal_r16_specs_3	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_PUSCH_PathlossReferenceRS_r16_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PUSCH_PathlossReferenceRS_r16, pusch_PathlossReferenceRS_Id_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_PUSCH_PathlossReferenceRS_Id_v1610,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pusch-PathlossReferenceRS-Id-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PUSCH_PathlossReferenceRS_r16, referenceSignal_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_referenceSignal_r16_3,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"referenceSignal-r16"
		},
};
static const ber_tlv_tag_t asn_DEF_NR_PUSCH_PathlossReferenceRS_r16_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_PUSCH_PathlossReferenceRS_r16_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* pusch-PathlossReferenceRS-Id-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* referenceSignal-r16 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_PUSCH_PathlossReferenceRS_r16_specs_1 = {
	sizeof(struct NR_PUSCH_PathlossReferenceRS_r16),
	offsetof(struct NR_PUSCH_PathlossReferenceRS_r16, _asn_ctx),
	asn_MAP_NR_PUSCH_PathlossReferenceRS_r16_tag2el_1,
	2,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_PUSCH_PathlossReferenceRS_r16 = {
	"PUSCH-PathlossReferenceRS-r16",
	"PUSCH-PathlossReferenceRS-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_PUSCH_PathlossReferenceRS_r16_tags_1,
	sizeof(asn_DEF_NR_PUSCH_PathlossReferenceRS_r16_tags_1)
		/sizeof(asn_DEF_NR_PUSCH_PathlossReferenceRS_r16_tags_1[0]), /* 1 */
	asn_DEF_NR_PUSCH_PathlossReferenceRS_r16_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_PUSCH_PathlossReferenceRS_r16_tags_1)
		/sizeof(asn_DEF_NR_PUSCH_PathlossReferenceRS_r16_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_PUSCH_PathlossReferenceRS_r16_1,
	2,	/* Elements count */
	&asn_SPC_NR_PUSCH_PathlossReferenceRS_r16_specs_1	/* Additional specs */
};

