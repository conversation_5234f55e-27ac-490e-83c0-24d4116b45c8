/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_DummyD_H_
#define	_NR_DummyD_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <NativeInteger.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_DummyD__maxNumberTxPortsPerResource {
	NR_DummyD__maxNumberTxPortsPerResource_p4	= 0,
	NR_DummyD__maxNumberTxPortsPerResource_p8	= 1,
	NR_DummyD__maxNumberTxPortsPerResource_p12	= 2,
	NR_DummyD__maxNumberTxPortsPerResource_p16	= 3,
	NR_DummyD__maxNumberTxPortsPerResource_p24	= 4,
	NR_DummyD__maxNumberTxPortsPerResource_p32	= 5
} e_NR_DummyD__maxNumberTxPortsPerResource;
typedef enum NR_DummyD__amplitudeScalingType {
	NR_DummyD__amplitudeScalingType_wideband	= 0,
	NR_DummyD__amplitudeScalingType_widebandAndSubband	= 1
} e_NR_DummyD__amplitudeScalingType;
typedef enum NR_DummyD__amplitudeSubsetRestriction {
	NR_DummyD__amplitudeSubsetRestriction_supported	= 0
} e_NR_DummyD__amplitudeSubsetRestriction;

/* NR_DummyD */
typedef struct NR_DummyD {
	long	 maxNumberTxPortsPerResource;
	long	 maxNumberResources;
	long	 totalNumberTxPorts;
	long	 parameterLx;
	long	 amplitudeScalingType;
	long	*amplitudeSubsetRestriction;	/* OPTIONAL */
	long	 maxNumberCSI_RS_PerResourceSet;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_DummyD_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumberTxPortsPerResource_2;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_amplitudeScalingType_12;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_amplitudeSubsetRestriction_15;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_DummyD;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_DummyD_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_DummyD_1[7];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_DummyD_H_ */
#include <asn_internal.h>
