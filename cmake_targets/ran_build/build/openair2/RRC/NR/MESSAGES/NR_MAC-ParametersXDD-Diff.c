/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_MAC-ParametersXDD-Diff.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_skipUplinkTxDynamic_constr_2 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_logicalChannelSR_DelayTimer_constr_4 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_longDRX_Cycle_constr_6 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_shortDRX_Cycle_constr_8 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_multipleSR_Configurations_constr_10 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_multipleConfiguredGrants_constr_12 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_secondaryDRX_Group_r16_constr_16 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_enhancedSkipUplinkTxDynamic_r16_constr_19 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_enhancedSkipUplinkTxConfigured_r16_constr_21 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_skipUplinkTxDynamic_value2enum_2[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_skipUplinkTxDynamic_enum2value_2[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_skipUplinkTxDynamic_specs_2 = {
	asn_MAP_NR_skipUplinkTxDynamic_value2enum_2,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_skipUplinkTxDynamic_enum2value_2,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_skipUplinkTxDynamic_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_skipUplinkTxDynamic_2 = {
	"skipUplinkTxDynamic",
	"skipUplinkTxDynamic",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_skipUplinkTxDynamic_tags_2,
	sizeof(asn_DEF_NR_skipUplinkTxDynamic_tags_2)
		/sizeof(asn_DEF_NR_skipUplinkTxDynamic_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_skipUplinkTxDynamic_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_skipUplinkTxDynamic_tags_2)
		/sizeof(asn_DEF_NR_skipUplinkTxDynamic_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_skipUplinkTxDynamic_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_skipUplinkTxDynamic_specs_2	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_logicalChannelSR_DelayTimer_value2enum_4[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_logicalChannelSR_DelayTimer_enum2value_4[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_logicalChannelSR_DelayTimer_specs_4 = {
	asn_MAP_NR_logicalChannelSR_DelayTimer_value2enum_4,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_logicalChannelSR_DelayTimer_enum2value_4,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_logicalChannelSR_DelayTimer_tags_4[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_logicalChannelSR_DelayTimer_4 = {
	"logicalChannelSR-DelayTimer",
	"logicalChannelSR-DelayTimer",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_logicalChannelSR_DelayTimer_tags_4,
	sizeof(asn_DEF_NR_logicalChannelSR_DelayTimer_tags_4)
		/sizeof(asn_DEF_NR_logicalChannelSR_DelayTimer_tags_4[0]) - 1, /* 1 */
	asn_DEF_NR_logicalChannelSR_DelayTimer_tags_4,	/* Same as above */
	sizeof(asn_DEF_NR_logicalChannelSR_DelayTimer_tags_4)
		/sizeof(asn_DEF_NR_logicalChannelSR_DelayTimer_tags_4[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_logicalChannelSR_DelayTimer_constr_4,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_logicalChannelSR_DelayTimer_specs_4	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_longDRX_Cycle_value2enum_6[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_longDRX_Cycle_enum2value_6[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_longDRX_Cycle_specs_6 = {
	asn_MAP_NR_longDRX_Cycle_value2enum_6,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_longDRX_Cycle_enum2value_6,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_longDRX_Cycle_tags_6[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_longDRX_Cycle_6 = {
	"longDRX-Cycle",
	"longDRX-Cycle",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_longDRX_Cycle_tags_6,
	sizeof(asn_DEF_NR_longDRX_Cycle_tags_6)
		/sizeof(asn_DEF_NR_longDRX_Cycle_tags_6[0]) - 1, /* 1 */
	asn_DEF_NR_longDRX_Cycle_tags_6,	/* Same as above */
	sizeof(asn_DEF_NR_longDRX_Cycle_tags_6)
		/sizeof(asn_DEF_NR_longDRX_Cycle_tags_6[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_longDRX_Cycle_constr_6,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_longDRX_Cycle_specs_6	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_shortDRX_Cycle_value2enum_8[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_shortDRX_Cycle_enum2value_8[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_shortDRX_Cycle_specs_8 = {
	asn_MAP_NR_shortDRX_Cycle_value2enum_8,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_shortDRX_Cycle_enum2value_8,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_shortDRX_Cycle_tags_8[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_shortDRX_Cycle_8 = {
	"shortDRX-Cycle",
	"shortDRX-Cycle",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_shortDRX_Cycle_tags_8,
	sizeof(asn_DEF_NR_shortDRX_Cycle_tags_8)
		/sizeof(asn_DEF_NR_shortDRX_Cycle_tags_8[0]) - 1, /* 1 */
	asn_DEF_NR_shortDRX_Cycle_tags_8,	/* Same as above */
	sizeof(asn_DEF_NR_shortDRX_Cycle_tags_8)
		/sizeof(asn_DEF_NR_shortDRX_Cycle_tags_8[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_shortDRX_Cycle_constr_8,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_shortDRX_Cycle_specs_8	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_multipleSR_Configurations_value2enum_10[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_multipleSR_Configurations_enum2value_10[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_multipleSR_Configurations_specs_10 = {
	asn_MAP_NR_multipleSR_Configurations_value2enum_10,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_multipleSR_Configurations_enum2value_10,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_multipleSR_Configurations_tags_10[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_multipleSR_Configurations_10 = {
	"multipleSR-Configurations",
	"multipleSR-Configurations",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_multipleSR_Configurations_tags_10,
	sizeof(asn_DEF_NR_multipleSR_Configurations_tags_10)
		/sizeof(asn_DEF_NR_multipleSR_Configurations_tags_10[0]) - 1, /* 1 */
	asn_DEF_NR_multipleSR_Configurations_tags_10,	/* Same as above */
	sizeof(asn_DEF_NR_multipleSR_Configurations_tags_10)
		/sizeof(asn_DEF_NR_multipleSR_Configurations_tags_10[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_multipleSR_Configurations_constr_10,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_multipleSR_Configurations_specs_10	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_multipleConfiguredGrants_value2enum_12[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_multipleConfiguredGrants_enum2value_12[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_multipleConfiguredGrants_specs_12 = {
	asn_MAP_NR_multipleConfiguredGrants_value2enum_12,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_multipleConfiguredGrants_enum2value_12,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_multipleConfiguredGrants_tags_12[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_multipleConfiguredGrants_12 = {
	"multipleConfiguredGrants",
	"multipleConfiguredGrants",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_multipleConfiguredGrants_tags_12,
	sizeof(asn_DEF_NR_multipleConfiguredGrants_tags_12)
		/sizeof(asn_DEF_NR_multipleConfiguredGrants_tags_12[0]) - 1, /* 1 */
	asn_DEF_NR_multipleConfiguredGrants_tags_12,	/* Same as above */
	sizeof(asn_DEF_NR_multipleConfiguredGrants_tags_12)
		/sizeof(asn_DEF_NR_multipleConfiguredGrants_tags_12[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_multipleConfiguredGrants_constr_12,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_multipleConfiguredGrants_specs_12	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_secondaryDRX_Group_r16_value2enum_16[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_secondaryDRX_Group_r16_enum2value_16[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_secondaryDRX_Group_r16_specs_16 = {
	asn_MAP_NR_secondaryDRX_Group_r16_value2enum_16,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_secondaryDRX_Group_r16_enum2value_16,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_secondaryDRX_Group_r16_tags_16[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_secondaryDRX_Group_r16_16 = {
	"secondaryDRX-Group-r16",
	"secondaryDRX-Group-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_secondaryDRX_Group_r16_tags_16,
	sizeof(asn_DEF_NR_secondaryDRX_Group_r16_tags_16)
		/sizeof(asn_DEF_NR_secondaryDRX_Group_r16_tags_16[0]) - 1, /* 1 */
	asn_DEF_NR_secondaryDRX_Group_r16_tags_16,	/* Same as above */
	sizeof(asn_DEF_NR_secondaryDRX_Group_r16_tags_16)
		/sizeof(asn_DEF_NR_secondaryDRX_Group_r16_tags_16[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_secondaryDRX_Group_r16_constr_16,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_secondaryDRX_Group_r16_specs_16	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext1_15[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_MAC_ParametersXDD_Diff__ext1, secondaryDRX_Group_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_secondaryDRX_Group_r16_16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"secondaryDRX-Group-r16"
		},
};
static const int asn_MAP_NR_ext1_oms_15[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_15[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_15[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* secondaryDRX-Group-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_15 = {
	sizeof(struct NR_MAC_ParametersXDD_Diff__ext1),
	offsetof(struct NR_MAC_ParametersXDD_Diff__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_15,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_15,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_15 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_15,
	sizeof(asn_DEF_NR_ext1_tags_15)
		/sizeof(asn_DEF_NR_ext1_tags_15[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_15,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_15)
		/sizeof(asn_DEF_NR_ext1_tags_15[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_15,
	1,	/* Elements count */
	&asn_SPC_NR_ext1_specs_15	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_enhancedSkipUplinkTxDynamic_r16_value2enum_19[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_enhancedSkipUplinkTxDynamic_r16_enum2value_19[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_enhancedSkipUplinkTxDynamic_r16_specs_19 = {
	asn_MAP_NR_enhancedSkipUplinkTxDynamic_r16_value2enum_19,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_enhancedSkipUplinkTxDynamic_r16_enum2value_19,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_enhancedSkipUplinkTxDynamic_r16_tags_19[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_enhancedSkipUplinkTxDynamic_r16_19 = {
	"enhancedSkipUplinkTxDynamic-r16",
	"enhancedSkipUplinkTxDynamic-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_enhancedSkipUplinkTxDynamic_r16_tags_19,
	sizeof(asn_DEF_NR_enhancedSkipUplinkTxDynamic_r16_tags_19)
		/sizeof(asn_DEF_NR_enhancedSkipUplinkTxDynamic_r16_tags_19[0]) - 1, /* 1 */
	asn_DEF_NR_enhancedSkipUplinkTxDynamic_r16_tags_19,	/* Same as above */
	sizeof(asn_DEF_NR_enhancedSkipUplinkTxDynamic_r16_tags_19)
		/sizeof(asn_DEF_NR_enhancedSkipUplinkTxDynamic_r16_tags_19[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_enhancedSkipUplinkTxDynamic_r16_constr_19,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_enhancedSkipUplinkTxDynamic_r16_specs_19	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_enhancedSkipUplinkTxConfigured_r16_value2enum_21[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_enhancedSkipUplinkTxConfigured_r16_enum2value_21[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_enhancedSkipUplinkTxConfigured_r16_specs_21 = {
	asn_MAP_NR_enhancedSkipUplinkTxConfigured_r16_value2enum_21,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_enhancedSkipUplinkTxConfigured_r16_enum2value_21,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_enhancedSkipUplinkTxConfigured_r16_tags_21[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_enhancedSkipUplinkTxConfigured_r16_21 = {
	"enhancedSkipUplinkTxConfigured-r16",
	"enhancedSkipUplinkTxConfigured-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_enhancedSkipUplinkTxConfigured_r16_tags_21,
	sizeof(asn_DEF_NR_enhancedSkipUplinkTxConfigured_r16_tags_21)
		/sizeof(asn_DEF_NR_enhancedSkipUplinkTxConfigured_r16_tags_21[0]) - 1, /* 1 */
	asn_DEF_NR_enhancedSkipUplinkTxConfigured_r16_tags_21,	/* Same as above */
	sizeof(asn_DEF_NR_enhancedSkipUplinkTxConfigured_r16_tags_21)
		/sizeof(asn_DEF_NR_enhancedSkipUplinkTxConfigured_r16_tags_21[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_enhancedSkipUplinkTxConfigured_r16_constr_21,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_enhancedSkipUplinkTxConfigured_r16_specs_21	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext2_18[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_MAC_ParametersXDD_Diff__ext2, enhancedSkipUplinkTxDynamic_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_enhancedSkipUplinkTxDynamic_r16_19,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"enhancedSkipUplinkTxDynamic-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MAC_ParametersXDD_Diff__ext2, enhancedSkipUplinkTxConfigured_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_enhancedSkipUplinkTxConfigured_r16_21,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"enhancedSkipUplinkTxConfigured-r16"
		},
};
static const int asn_MAP_NR_ext2_oms_18[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_ext2_tags_18[] = {
	(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext2_tag2el_18[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* enhancedSkipUplinkTxDynamic-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* enhancedSkipUplinkTxConfigured-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext2_specs_18 = {
	sizeof(struct NR_MAC_ParametersXDD_Diff__ext2),
	offsetof(struct NR_MAC_ParametersXDD_Diff__ext2, _asn_ctx),
	asn_MAP_NR_ext2_tag2el_18,
	2,	/* Count of tags in the map */
	asn_MAP_NR_ext2_oms_18,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext2_18 = {
	"ext2",
	"ext2",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext2_tags_18,
	sizeof(asn_DEF_NR_ext2_tags_18)
		/sizeof(asn_DEF_NR_ext2_tags_18[0]) - 1, /* 1 */
	asn_DEF_NR_ext2_tags_18,	/* Same as above */
	sizeof(asn_DEF_NR_ext2_tags_18)
		/sizeof(asn_DEF_NR_ext2_tags_18[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext2_18,
	2,	/* Elements count */
	&asn_SPC_NR_ext2_specs_18	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_MAC_ParametersXDD_Diff_1[] = {
	{ ATF_POINTER, 8, offsetof(struct NR_MAC_ParametersXDD_Diff, skipUplinkTxDynamic),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_skipUplinkTxDynamic_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"skipUplinkTxDynamic"
		},
	{ ATF_POINTER, 7, offsetof(struct NR_MAC_ParametersXDD_Diff, logicalChannelSR_DelayTimer),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_logicalChannelSR_DelayTimer_4,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"logicalChannelSR-DelayTimer"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_MAC_ParametersXDD_Diff, longDRX_Cycle),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_longDRX_Cycle_6,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"longDRX-Cycle"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_MAC_ParametersXDD_Diff, shortDRX_Cycle),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_shortDRX_Cycle_8,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"shortDRX-Cycle"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_MAC_ParametersXDD_Diff, multipleSR_Configurations),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_multipleSR_Configurations_10,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"multipleSR-Configurations"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_MAC_ParametersXDD_Diff, multipleConfiguredGrants),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_multipleConfiguredGrants_12,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"multipleConfiguredGrants"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_MAC_ParametersXDD_Diff, ext1),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		0,
		&asn_DEF_NR_ext1_15,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MAC_ParametersXDD_Diff, ext2),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		0,
		&asn_DEF_NR_ext2_18,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext2"
		},
};
static const int asn_MAP_NR_MAC_ParametersXDD_Diff_oms_1[] = { 0, 1, 2, 3, 4, 5, 6, 7 };
static const ber_tlv_tag_t asn_DEF_NR_MAC_ParametersXDD_Diff_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_MAC_ParametersXDD_Diff_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* skipUplinkTxDynamic */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* logicalChannelSR-DelayTimer */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* longDRX-Cycle */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* shortDRX-Cycle */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* multipleSR-Configurations */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* multipleConfiguredGrants */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* ext1 */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 } /* ext2 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_MAC_ParametersXDD_Diff_specs_1 = {
	sizeof(struct NR_MAC_ParametersXDD_Diff),
	offsetof(struct NR_MAC_ParametersXDD_Diff, _asn_ctx),
	asn_MAP_NR_MAC_ParametersXDD_Diff_tag2el_1,
	8,	/* Count of tags in the map */
	asn_MAP_NR_MAC_ParametersXDD_Diff_oms_1,	/* Optional members */
	6, 2,	/* Root/Additions */
	6,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_MAC_ParametersXDD_Diff = {
	"MAC-ParametersXDD-Diff",
	"MAC-ParametersXDD-Diff",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_MAC_ParametersXDD_Diff_tags_1,
	sizeof(asn_DEF_NR_MAC_ParametersXDD_Diff_tags_1)
		/sizeof(asn_DEF_NR_MAC_ParametersXDD_Diff_tags_1[0]), /* 1 */
	asn_DEF_NR_MAC_ParametersXDD_Diff_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_MAC_ParametersXDD_Diff_tags_1)
		/sizeof(asn_DEF_NR_MAC_ParametersXDD_Diff_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_MAC_ParametersXDD_Diff_1,
	8,	/* Elements count */
	&asn_SPC_NR_MAC_ParametersXDD_Diff_specs_1	/* Additional specs */
};

