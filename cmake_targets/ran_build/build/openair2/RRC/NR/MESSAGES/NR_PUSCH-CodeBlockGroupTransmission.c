/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_PUSCH-CodeBlockGroupTransmission.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_maxCodeBlockGroupsPerTransportBlock_constr_2 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_maxCodeBlockGroupsPerTransportBlock_value2enum_2[] = {
	{ 0,	2,	"n2" },
	{ 1,	2,	"n4" },
	{ 2,	2,	"n6" },
	{ 3,	2,	"n8" }
};
static const unsigned int asn_MAP_NR_maxCodeBlockGroupsPerTransportBlock_enum2value_2[] = {
	0,	/* n2(0) */
	1,	/* n4(1) */
	2,	/* n6(2) */
	3	/* n8(3) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_maxCodeBlockGroupsPerTransportBlock_specs_2 = {
	asn_MAP_NR_maxCodeBlockGroupsPerTransportBlock_value2enum_2,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_maxCodeBlockGroupsPerTransportBlock_enum2value_2,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_maxCodeBlockGroupsPerTransportBlock_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_maxCodeBlockGroupsPerTransportBlock_2 = {
	"maxCodeBlockGroupsPerTransportBlock",
	"maxCodeBlockGroupsPerTransportBlock",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_maxCodeBlockGroupsPerTransportBlock_tags_2,
	sizeof(asn_DEF_NR_maxCodeBlockGroupsPerTransportBlock_tags_2)
		/sizeof(asn_DEF_NR_maxCodeBlockGroupsPerTransportBlock_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_maxCodeBlockGroupsPerTransportBlock_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_maxCodeBlockGroupsPerTransportBlock_tags_2)
		/sizeof(asn_DEF_NR_maxCodeBlockGroupsPerTransportBlock_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_maxCodeBlockGroupsPerTransportBlock_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_maxCodeBlockGroupsPerTransportBlock_specs_2	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_PUSCH_CodeBlockGroupTransmission_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PUSCH_CodeBlockGroupTransmission, maxCodeBlockGroupsPerTransportBlock),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_maxCodeBlockGroupsPerTransportBlock_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"maxCodeBlockGroupsPerTransportBlock"
		},
};
static const ber_tlv_tag_t asn_DEF_NR_PUSCH_CodeBlockGroupTransmission_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_PUSCH_CodeBlockGroupTransmission_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* maxCodeBlockGroupsPerTransportBlock */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_PUSCH_CodeBlockGroupTransmission_specs_1 = {
	sizeof(struct NR_PUSCH_CodeBlockGroupTransmission),
	offsetof(struct NR_PUSCH_CodeBlockGroupTransmission, _asn_ctx),
	asn_MAP_NR_PUSCH_CodeBlockGroupTransmission_tag2el_1,
	1,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_PUSCH_CodeBlockGroupTransmission = {
	"PUSCH-CodeBlockGroupTransmission",
	"PUSCH-CodeBlockGroupTransmission",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_PUSCH_CodeBlockGroupTransmission_tags_1,
	sizeof(asn_DEF_NR_PUSCH_CodeBlockGroupTransmission_tags_1)
		/sizeof(asn_DEF_NR_PUSCH_CodeBlockGroupTransmission_tags_1[0]), /* 1 */
	asn_DEF_NR_PUSCH_CodeBlockGroupTransmission_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_PUSCH_CodeBlockGroupTransmission_tags_1)
		/sizeof(asn_DEF_NR_PUSCH_CodeBlockGroupTransmission_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_PUSCH_CodeBlockGroupTransmission_1,
	1,	/* Elements count */
	&asn_SPC_NR_PUSCH_CodeBlockGroupTransmission_specs_1	/* Additional specs */
};

