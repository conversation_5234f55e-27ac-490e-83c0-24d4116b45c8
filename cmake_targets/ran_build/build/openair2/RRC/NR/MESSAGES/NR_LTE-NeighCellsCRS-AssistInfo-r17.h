/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_LTE_NeighCellsCRS_AssistInfo_r17_H_
#define	_NR_LTE_NeighCellsCRS_AssistInfo_r17_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <NativeInteger.h>
#include "NR_EUTRA-PhysCellId.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_LTE_NeighCellsCRS_AssistInfo_r17__neighCarrierBandwidthDL_r17 {
	NR_LTE_NeighCellsCRS_AssistInfo_r17__neighCarrierBandwidthDL_r17_n6	= 0,
	NR_LTE_NeighCellsCRS_AssistInfo_r17__neighCarrierBandwidthDL_r17_n15	= 1,
	NR_LTE_NeighCellsCRS_AssistInfo_r17__neighCarrierBandwidthDL_r17_n25	= 2,
	NR_LTE_NeighCellsCRS_AssistInfo_r17__neighCarrierBandwidthDL_r17_n50	= 3,
	NR_LTE_NeighCellsCRS_AssistInfo_r17__neighCarrierBandwidthDL_r17_n75	= 4,
	NR_LTE_NeighCellsCRS_AssistInfo_r17__neighCarrierBandwidthDL_r17_n100	= 5,
	NR_LTE_NeighCellsCRS_AssistInfo_r17__neighCarrierBandwidthDL_r17_spare2	= 6,
	NR_LTE_NeighCellsCRS_AssistInfo_r17__neighCarrierBandwidthDL_r17_spare1	= 7
} e_NR_LTE_NeighCellsCRS_AssistInfo_r17__neighCarrierBandwidthDL_r17;
typedef enum NR_LTE_NeighCellsCRS_AssistInfo_r17__neighCRS_muting_r17 {
	NR_LTE_NeighCellsCRS_AssistInfo_r17__neighCRS_muting_r17_enabled	= 0
} e_NR_LTE_NeighCellsCRS_AssistInfo_r17__neighCRS_muting_r17;
typedef enum NR_LTE_NeighCellsCRS_AssistInfo_r17__neighNrofCRS_Ports_r17 {
	NR_LTE_NeighCellsCRS_AssistInfo_r17__neighNrofCRS_Ports_r17_n1	= 0,
	NR_LTE_NeighCellsCRS_AssistInfo_r17__neighNrofCRS_Ports_r17_n2	= 1,
	NR_LTE_NeighCellsCRS_AssistInfo_r17__neighNrofCRS_Ports_r17_n4	= 2
} e_NR_LTE_NeighCellsCRS_AssistInfo_r17__neighNrofCRS_Ports_r17;
typedef enum NR_LTE_NeighCellsCRS_AssistInfo_r17__neighV_Shift_r17 {
	NR_LTE_NeighCellsCRS_AssistInfo_r17__neighV_Shift_r17_n0	= 0,
	NR_LTE_NeighCellsCRS_AssistInfo_r17__neighV_Shift_r17_n1	= 1,
	NR_LTE_NeighCellsCRS_AssistInfo_r17__neighV_Shift_r17_n2	= 2,
	NR_LTE_NeighCellsCRS_AssistInfo_r17__neighV_Shift_r17_n3	= 3,
	NR_LTE_NeighCellsCRS_AssistInfo_r17__neighV_Shift_r17_n4	= 4,
	NR_LTE_NeighCellsCRS_AssistInfo_r17__neighV_Shift_r17_n5	= 5
} e_NR_LTE_NeighCellsCRS_AssistInfo_r17__neighV_Shift_r17;

/* Forward declarations */
struct NR_EUTRA_MBSFN_SubframeConfigList;

/* NR_LTE-NeighCellsCRS-AssistInfo-r17 */
typedef struct NR_LTE_NeighCellsCRS_AssistInfo_r17 {
	long	*neighCarrierBandwidthDL_r17;	/* OPTIONAL */
	long	*neighCarrierFreqDL_r17;	/* OPTIONAL */
	NR_EUTRA_PhysCellId_t	*neighCellId_r17;	/* OPTIONAL */
	long	*neighCRS_muting_r17;	/* OPTIONAL */
	struct NR_EUTRA_MBSFN_SubframeConfigList	*neighMBSFN_SubframeConfigList_r17;	/* OPTIONAL */
	long	*neighNrofCRS_Ports_r17;	/* OPTIONAL */
	long	*neighV_Shift_r17;	/* OPTIONAL */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_LTE_NeighCellsCRS_AssistInfo_r17_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_neighCarrierBandwidthDL_r17_2;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_neighCRS_muting_r17_13;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_neighNrofCRS_Ports_r17_16;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_neighV_Shift_r17_20;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_LTE_NeighCellsCRS_AssistInfo_r17;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_LTE_NeighCellsCRS_AssistInfo_r17_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_LTE_NeighCellsCRS_AssistInfo_r17_1[7];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_EUTRA-MBSFN-SubframeConfigList.h"

#endif	/* _NR_LTE_NeighCellsCRS_AssistInfo_r17_H_ */
#include <asn_internal.h>
