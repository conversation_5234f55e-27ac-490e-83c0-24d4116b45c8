/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_MIMOParam-r17.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_additionalPCI_ToAddModList_r17_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 7UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_additionalPCI_ToReleaseList_r17_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 7UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_uplink_PowerControlToAddModList_r17_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 64UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_uplink_PowerControlToReleaseList_r17_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 64UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_additionalPCI_ToAddModList_r17_constr_2 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  7 }	/* (SIZE(1..7)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_additionalPCI_ToReleaseList_r17_constr_4 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  7 }	/* (SIZE(1..7)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_unifiedTCI_StateType_r17_constr_6 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_uplink_PowerControlToAddModList_r17_constr_9 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 6,  6,  1,  64 }	/* (SIZE(1..64)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_uplink_PowerControlToReleaseList_r17_constr_11 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 6,  6,  1,  64 }	/* (SIZE(1..64)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_sfnSchemePDCCH_r17_constr_13 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_sfnSchemePDSCH_r17_constr_16 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_additionalPCI_ToAddModList_r17_constr_2 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  7 }	/* (SIZE(1..7)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_additionalPCI_ToReleaseList_r17_constr_4 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  7 }	/* (SIZE(1..7)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_uplink_PowerControlToAddModList_r17_constr_9 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 6,  6,  1,  64 }	/* (SIZE(1..64)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_uplink_PowerControlToReleaseList_r17_constr_11 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 6,  6,  1,  64 }	/* (SIZE(1..64)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static asn_TYPE_member_t asn_MBR_NR_additionalPCI_ToAddModList_r17_2[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_SSB_MTC_AdditionalPCI_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_additionalPCI_ToAddModList_r17_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_additionalPCI_ToAddModList_r17_specs_2 = {
	sizeof(struct NR_MIMOParam_r17__additionalPCI_ToAddModList_r17),
	offsetof(struct NR_MIMOParam_r17__additionalPCI_ToAddModList_r17, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_additionalPCI_ToAddModList_r17_2 = {
	"additionalPCI-ToAddModList-r17",
	"additionalPCI-ToAddModList-r17",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_additionalPCI_ToAddModList_r17_tags_2,
	sizeof(asn_DEF_NR_additionalPCI_ToAddModList_r17_tags_2)
		/sizeof(asn_DEF_NR_additionalPCI_ToAddModList_r17_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_additionalPCI_ToAddModList_r17_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_additionalPCI_ToAddModList_r17_tags_2)
		/sizeof(asn_DEF_NR_additionalPCI_ToAddModList_r17_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_additionalPCI_ToAddModList_r17_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_additionalPCI_ToAddModList_r17_2,
	1,	/* Single element */
	&asn_SPC_NR_additionalPCI_ToAddModList_r17_specs_2	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_additionalPCI_ToReleaseList_r17_4[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (2 << 2)),
		0,
		&asn_DEF_NR_AdditionalPCIIndex_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_additionalPCI_ToReleaseList_r17_tags_4[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_additionalPCI_ToReleaseList_r17_specs_4 = {
	sizeof(struct NR_MIMOParam_r17__additionalPCI_ToReleaseList_r17),
	offsetof(struct NR_MIMOParam_r17__additionalPCI_ToReleaseList_r17, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_additionalPCI_ToReleaseList_r17_4 = {
	"additionalPCI-ToReleaseList-r17",
	"additionalPCI-ToReleaseList-r17",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_additionalPCI_ToReleaseList_r17_tags_4,
	sizeof(asn_DEF_NR_additionalPCI_ToReleaseList_r17_tags_4)
		/sizeof(asn_DEF_NR_additionalPCI_ToReleaseList_r17_tags_4[0]) - 1, /* 1 */
	asn_DEF_NR_additionalPCI_ToReleaseList_r17_tags_4,	/* Same as above */
	sizeof(asn_DEF_NR_additionalPCI_ToReleaseList_r17_tags_4)
		/sizeof(asn_DEF_NR_additionalPCI_ToReleaseList_r17_tags_4[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_additionalPCI_ToReleaseList_r17_constr_4,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_additionalPCI_ToReleaseList_r17_4,
	1,	/* Single element */
	&asn_SPC_NR_additionalPCI_ToReleaseList_r17_specs_4	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_unifiedTCI_StateType_r17_value2enum_6[] = {
	{ 0,	8,	"separate" },
	{ 1,	5,	"joint" }
};
static const unsigned int asn_MAP_NR_unifiedTCI_StateType_r17_enum2value_6[] = {
	1,	/* joint(1) */
	0	/* separate(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_unifiedTCI_StateType_r17_specs_6 = {
	asn_MAP_NR_unifiedTCI_StateType_r17_value2enum_6,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_unifiedTCI_StateType_r17_enum2value_6,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_unifiedTCI_StateType_r17_tags_6[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_unifiedTCI_StateType_r17_6 = {
	"unifiedTCI-StateType-r17",
	"unifiedTCI-StateType-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_unifiedTCI_StateType_r17_tags_6,
	sizeof(asn_DEF_NR_unifiedTCI_StateType_r17_tags_6)
		/sizeof(asn_DEF_NR_unifiedTCI_StateType_r17_tags_6[0]) - 1, /* 1 */
	asn_DEF_NR_unifiedTCI_StateType_r17_tags_6,	/* Same as above */
	sizeof(asn_DEF_NR_unifiedTCI_StateType_r17_tags_6)
		/sizeof(asn_DEF_NR_unifiedTCI_StateType_r17_tags_6[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_unifiedTCI_StateType_r17_constr_6,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_unifiedTCI_StateType_r17_specs_6	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_uplink_PowerControlToAddModList_r17_9[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_Uplink_powerControl_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_uplink_PowerControlToAddModList_r17_tags_9[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_uplink_PowerControlToAddModList_r17_specs_9 = {
	sizeof(struct NR_MIMOParam_r17__uplink_PowerControlToAddModList_r17),
	offsetof(struct NR_MIMOParam_r17__uplink_PowerControlToAddModList_r17, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_uplink_PowerControlToAddModList_r17_9 = {
	"uplink-PowerControlToAddModList-r17",
	"uplink-PowerControlToAddModList-r17",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_uplink_PowerControlToAddModList_r17_tags_9,
	sizeof(asn_DEF_NR_uplink_PowerControlToAddModList_r17_tags_9)
		/sizeof(asn_DEF_NR_uplink_PowerControlToAddModList_r17_tags_9[0]) - 1, /* 1 */
	asn_DEF_NR_uplink_PowerControlToAddModList_r17_tags_9,	/* Same as above */
	sizeof(asn_DEF_NR_uplink_PowerControlToAddModList_r17_tags_9)
		/sizeof(asn_DEF_NR_uplink_PowerControlToAddModList_r17_tags_9[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_uplink_PowerControlToAddModList_r17_constr_9,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_uplink_PowerControlToAddModList_r17_9,
	1,	/* Single element */
	&asn_SPC_NR_uplink_PowerControlToAddModList_r17_specs_9	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_uplink_PowerControlToReleaseList_r17_11[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (2 << 2)),
		0,
		&asn_DEF_NR_Uplink_powerControlId_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_uplink_PowerControlToReleaseList_r17_tags_11[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_uplink_PowerControlToReleaseList_r17_specs_11 = {
	sizeof(struct NR_MIMOParam_r17__uplink_PowerControlToReleaseList_r17),
	offsetof(struct NR_MIMOParam_r17__uplink_PowerControlToReleaseList_r17, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_uplink_PowerControlToReleaseList_r17_11 = {
	"uplink-PowerControlToReleaseList-r17",
	"uplink-PowerControlToReleaseList-r17",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_uplink_PowerControlToReleaseList_r17_tags_11,
	sizeof(asn_DEF_NR_uplink_PowerControlToReleaseList_r17_tags_11)
		/sizeof(asn_DEF_NR_uplink_PowerControlToReleaseList_r17_tags_11[0]) - 1, /* 1 */
	asn_DEF_NR_uplink_PowerControlToReleaseList_r17_tags_11,	/* Same as above */
	sizeof(asn_DEF_NR_uplink_PowerControlToReleaseList_r17_tags_11)
		/sizeof(asn_DEF_NR_uplink_PowerControlToReleaseList_r17_tags_11[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_uplink_PowerControlToReleaseList_r17_constr_11,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_uplink_PowerControlToReleaseList_r17_11,
	1,	/* Single element */
	&asn_SPC_NR_uplink_PowerControlToReleaseList_r17_specs_11	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_sfnSchemePDCCH_r17_value2enum_13[] = {
	{ 0,	10,	"sfnSchemeA" },
	{ 1,	10,	"sfnSchemeB" }
};
static const unsigned int asn_MAP_NR_sfnSchemePDCCH_r17_enum2value_13[] = {
	0,	/* sfnSchemeA(0) */
	1	/* sfnSchemeB(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_sfnSchemePDCCH_r17_specs_13 = {
	asn_MAP_NR_sfnSchemePDCCH_r17_value2enum_13,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_sfnSchemePDCCH_r17_enum2value_13,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_sfnSchemePDCCH_r17_tags_13[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_sfnSchemePDCCH_r17_13 = {
	"sfnSchemePDCCH-r17",
	"sfnSchemePDCCH-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_sfnSchemePDCCH_r17_tags_13,
	sizeof(asn_DEF_NR_sfnSchemePDCCH_r17_tags_13)
		/sizeof(asn_DEF_NR_sfnSchemePDCCH_r17_tags_13[0]) - 1, /* 1 */
	asn_DEF_NR_sfnSchemePDCCH_r17_tags_13,	/* Same as above */
	sizeof(asn_DEF_NR_sfnSchemePDCCH_r17_tags_13)
		/sizeof(asn_DEF_NR_sfnSchemePDCCH_r17_tags_13[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_sfnSchemePDCCH_r17_constr_13,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_sfnSchemePDCCH_r17_specs_13	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_sfnSchemePDSCH_r17_value2enum_16[] = {
	{ 0,	10,	"sfnSchemeA" },
	{ 1,	10,	"sfnSchemeB" }
};
static const unsigned int asn_MAP_NR_sfnSchemePDSCH_r17_enum2value_16[] = {
	0,	/* sfnSchemeA(0) */
	1	/* sfnSchemeB(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_sfnSchemePDSCH_r17_specs_16 = {
	asn_MAP_NR_sfnSchemePDSCH_r17_value2enum_16,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_sfnSchemePDSCH_r17_enum2value_16,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_sfnSchemePDSCH_r17_tags_16[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_sfnSchemePDSCH_r17_16 = {
	"sfnSchemePDSCH-r17",
	"sfnSchemePDSCH-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_sfnSchemePDSCH_r17_tags_16,
	sizeof(asn_DEF_NR_sfnSchemePDSCH_r17_tags_16)
		/sizeof(asn_DEF_NR_sfnSchemePDSCH_r17_tags_16[0]) - 1, /* 1 */
	asn_DEF_NR_sfnSchemePDSCH_r17_tags_16,	/* Same as above */
	sizeof(asn_DEF_NR_sfnSchemePDSCH_r17_tags_16)
		/sizeof(asn_DEF_NR_sfnSchemePDSCH_r17_tags_16[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_sfnSchemePDSCH_r17_constr_16,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_sfnSchemePDSCH_r17_specs_16	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_MIMOParam_r17_1[] = {
	{ ATF_POINTER, 7, offsetof(struct NR_MIMOParam_r17, additionalPCI_ToAddModList_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_additionalPCI_ToAddModList_r17_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_additionalPCI_ToAddModList_r17_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_additionalPCI_ToAddModList_r17_constraint_1
		},
		0, 0, /* No default value */
		"additionalPCI-ToAddModList-r17"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_MIMOParam_r17, additionalPCI_ToReleaseList_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_additionalPCI_ToReleaseList_r17_4,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_additionalPCI_ToReleaseList_r17_constr_4,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_additionalPCI_ToReleaseList_r17_constraint_1
		},
		0, 0, /* No default value */
		"additionalPCI-ToReleaseList-r17"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_MIMOParam_r17, unifiedTCI_StateType_r17),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_unifiedTCI_StateType_r17_6,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"unifiedTCI-StateType-r17"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_MIMOParam_r17, uplink_PowerControlToAddModList_r17),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		0,
		&asn_DEF_NR_uplink_PowerControlToAddModList_r17_9,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_uplink_PowerControlToAddModList_r17_constr_9,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_uplink_PowerControlToAddModList_r17_constraint_1
		},
		0, 0, /* No default value */
		"uplink-PowerControlToAddModList-r17"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_MIMOParam_r17, uplink_PowerControlToReleaseList_r17),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		0,
		&asn_DEF_NR_uplink_PowerControlToReleaseList_r17_11,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_uplink_PowerControlToReleaseList_r17_constr_11,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_uplink_PowerControlToReleaseList_r17_constraint_1
		},
		0, 0, /* No default value */
		"uplink-PowerControlToReleaseList-r17"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_MIMOParam_r17, sfnSchemePDCCH_r17),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_sfnSchemePDCCH_r17_13,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"sfnSchemePDCCH-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MIMOParam_r17, sfnSchemePDSCH_r17),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_sfnSchemePDSCH_r17_16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"sfnSchemePDSCH-r17"
		},
};
static const int asn_MAP_NR_MIMOParam_r17_oms_1[] = { 0, 1, 2, 3, 4, 5, 6 };
static const ber_tlv_tag_t asn_DEF_NR_MIMOParam_r17_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_MIMOParam_r17_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* additionalPCI-ToAddModList-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* additionalPCI-ToReleaseList-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* unifiedTCI-StateType-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* uplink-PowerControlToAddModList-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* uplink-PowerControlToReleaseList-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* sfnSchemePDCCH-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 } /* sfnSchemePDSCH-r17 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_MIMOParam_r17_specs_1 = {
	sizeof(struct NR_MIMOParam_r17),
	offsetof(struct NR_MIMOParam_r17, _asn_ctx),
	asn_MAP_NR_MIMOParam_r17_tag2el_1,
	7,	/* Count of tags in the map */
	asn_MAP_NR_MIMOParam_r17_oms_1,	/* Optional members */
	7, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_MIMOParam_r17 = {
	"MIMOParam-r17",
	"MIMOParam-r17",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_MIMOParam_r17_tags_1,
	sizeof(asn_DEF_NR_MIMOParam_r17_tags_1)
		/sizeof(asn_DEF_NR_MIMOParam_r17_tags_1[0]), /* 1 */
	asn_DEF_NR_MIMOParam_r17_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_MIMOParam_r17_tags_1)
		/sizeof(asn_DEF_NR_MIMOParam_r17_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_MIMOParam_r17_1,
	7,	/* Elements count */
	&asn_SPC_NR_MIMOParam_r17_specs_1	/* Additional specs */
};

