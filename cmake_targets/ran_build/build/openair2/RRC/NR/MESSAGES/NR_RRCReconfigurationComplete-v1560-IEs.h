/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_RRCReconfigurationComplete_v1560_IEs_H_
#define	_NR_RRCReconfigurationComplete_v1560_IEs_H_


#include <asn_application.h>

/* Including external dependencies */
#include <OCTET_STRING.h>
#include <constr_CHOICE.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_RRCReconfigurationComplete_v1560_IEs__scg_Response_PR {
	NR_RRCReconfigurationComplete_v1560_IEs__scg_Response_PR_NOTHING,	/* No components present */
	NR_RRCReconfigurationComplete_v1560_IEs__scg_Response_PR_nr_SCG_Response,
	NR_RRCReconfigurationComplete_v1560_IEs__scg_Response_PR_eutra_SCG_Response
} NR_RRCReconfigurationComplete_v1560_IEs__scg_Response_PR;

/* Forward declarations */
struct NR_RRCReconfigurationComplete_v1610_IEs;

/* NR_RRCReconfigurationComplete-v1560-IEs */
typedef struct NR_RRCReconfigurationComplete_v1560_IEs {
	struct NR_RRCReconfigurationComplete_v1560_IEs__scg_Response {
		NR_RRCReconfigurationComplete_v1560_IEs__scg_Response_PR present;
		union NR_RRCReconfigurationComplete_v1560_IEs__NR_scg_Response_u {
			OCTET_STRING_t	 nr_SCG_Response;
			OCTET_STRING_t	 eutra_SCG_Response;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *scg_Response;
	struct NR_RRCReconfigurationComplete_v1610_IEs	*nonCriticalExtension;	/* OPTIONAL */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_RRCReconfigurationComplete_v1560_IEs_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_NR_RRCReconfigurationComplete_v1560_IEs;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_RRCReconfigurationComplete_v1560_IEs_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_RRCReconfigurationComplete_v1560_IEs_1[2];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_RRCReconfigurationComplete-v1610-IEs.h"

#endif	/* _NR_RRCReconfigurationComplete_v1560_IEs_H_ */
#include <asn_internal.h>
