/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_Phy-ParametersCommon.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_unifiedJointTCI_commonUpdate_r17_constraint_222(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 4L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_csi_RS_CFRA_ForHO_constr_2 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dynamicPRB_BundlingDL_constr_4 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_sp_CSI_ReportPUCCH_constr_6 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_sp_CSI_ReportPUSCH_constr_8 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_nzp_CSI_RS_IntefMgmt_constr_10 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_type2_SP_CSI_Feedback_LongPUCCH_constr_12 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_precoderGranularityCORESET_constr_14 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dynamicHARQ_ACK_Codebook_constr_16 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_semiStaticHARQ_ACK_Codebook_constr_18 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_spatialBundlingHARQ_ACK_constr_20 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dynamicBetaOffsetInd_HARQ_ACK_CSI_constr_22 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pucch_Repetition_F1_3_4_constr_24 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ra_Type0_PUSCH_constr_26 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dynamicSwitchRA_Type0_1_PDSCH_constr_28 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dynamicSwitchRA_Type0_1_PUSCH_constr_30 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pdsch_MappingTypeA_constr_32 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pdsch_MappingTypeB_constr_34 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_interleavingVRB_ToPRB_PDSCH_constr_36 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_interSlotFreqHopping_PUSCH_constr_38 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_type1_PUSCH_RepetitionMultiSlots_constr_40 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_type2_PUSCH_RepetitionMultiSlots_constr_42 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pusch_RepetitionMultiSlots_constr_44 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pdsch_RepetitionMultiSlots_constr_46 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_downlinkSPS_constr_48 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_configuredUL_GrantType1_constr_50 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_configuredUL_GrantType2_constr_52 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pre_EmptIndication_DL_constr_54 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_cbg_TransIndication_DL_constr_56 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_cbg_TransIndication_UL_constr_58 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_cbg_FlushIndication_DL_constr_60 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dynamicHARQ_ACK_CodeB_CBG_Retx_DL_constr_62 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_rateMatchingResrcSetSemi_Static_constr_64 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_rateMatchingResrcSetDynamic_constr_66 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_bwp_SwitchingDelay_constr_68 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dummy_constr_73 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_maxNumberSearchSpaces_constr_76 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_rateMatchingCtrlResrcSetDynamic_constr_78 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_maxLayersMIMO_Indication_constr_80 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_twoStepRACH_r16_constr_85 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dci_Format1_2And0_2_r16_constr_87 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_monitoringDCI_SameSearchSpace_r16_constr_89 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_type2_CG_ReleaseDCI_0_1_r16_constr_91 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_type2_CG_ReleaseDCI_0_2_r16_constr_93 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_sps_ReleaseDCI_1_1_r16_constr_95 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_sps_ReleaseDCI_1_2_r16_constr_97 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_csi_TriggerStateNon_ActiveBWP_r16_constr_99 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_separateSMTC_InterIAB_Support_r16_constr_101 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_separateRACH_IAB_Support_r16_constr_103 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ul_flexibleDL_SlotFormatSemiStatic_IAB_r16_constr_105 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ul_flexibleDL_SlotFormatDynamics_IAB_r16_constr_107 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dft_S_OFDM_WaveformUL_IAB_r16_constr_109 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dci_25_AI_RNTI_Support_IAB_r16_constr_111 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_t_DeltaReceptionSupport_IAB_r16_constr_113 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_guardSymbolReportReception_IAB_r16_constr_115 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_harqACK_CB_SpatialBundlingPUCCH_Group_r16_constr_117 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_non_SharedSpectrumChAccess_r16_constr_120 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_sharedSpectrumChAccess_r16_constr_122 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_maxNumberSRS_PosPathLossEstimateAllServingCells_r16_constr_124 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_extendedCG_Periodicities_r16_constr_129 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_extendedSPS_Periodicities_r16_constr_131 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_sharedSpectrumChAccess_r16_constr_135 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_non_SharedSpectrumChAccess_r16_constr_137 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dci_DL_PriorityIndicator_r16_constr_139 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dci_UL_PriorityIndicator_r16_constr_141 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_maxNumberPathlossRS_Update_r16_constr_143 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  4 }	/* (0..4) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_type2_HARQ_ACK_Codebook_r16_constr_149 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_maxNumberResWithinSlotAcrossCC_AcrossFR_r16_constr_152 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  7 }	/* (0..7) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_maxNumberResAcrossCC_AcrossFR_r16_constr_161 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  13 }	/* (0..13) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_maxNumberLongPUCCHs_r16_constr_177 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_harqACK_jointMultiDCI_MultiTRP_r16_constr_181 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_type1_r16_constr_184 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_type2_r16_constr_187 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_bwp_SwitchingMultiCCs_r16_constr_183 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_targetSMTC_SCG_r16_constr_193 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_supportRepetitionZeroOffsetRV_r16_constr_195 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_cbg_TransInOrderPUSCH_UL_r16_constr_197 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_type1_r16_constr_201 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_type2_r16_constr_204 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_bwp_SwitchingMultiDormancyCCs_r16_constr_200 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_supportRetx_Diff_CoresetPool_Multi_DCI_TRP_r16_constr_209 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pdcch_MonitoringAnyOccasionsWithSpanGapCrossCarrierSch_r16_constr_211 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_newBeamIdentifications2PortCSI_RS_r16_constr_215 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pathlossEstimation2PortCSI_RS_r16_constr_217 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_mux_HARQ_ACK_withoutPUCCH_onPUSCH_r16_constr_220 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_guardSymbolReportReception_IAB_r17_constr_223 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_restricted_IAB_DU_BeamReception_r17_constr_225 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_recommended_IAB_MT_BeamTransmission_r17_constr_227 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_case6_TimingAlignmentReception_IAB_r17_constr_229 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_case7_TimingAlignmentReception_IAB_r17_constr_231 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dl_tx_PowerAdjustment_IAB_r17_constr_233 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_desired_ul_tx_PowerAdjustment_r17_constr_235 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_fdm_SoftResourceAvailability_DynamicIndication_r17_constr_237 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_updated_T_DeltaRangeRecption_r17_constr_239 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_slotBasedDynamicPUCCH_Rep_r17_constr_241 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_non_SharedSpectrumChAccess_r17_constr_244 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_sharedSpectrumChAccess_r17_constr_246 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_mTRP_PDCCH_singleSpan_r17_constr_249 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_supportedActivatedPRS_ProcessingWindow_r17_constr_251 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_cg_TimeDomainAllocationExtension_r17_constr_255 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_unifiedJointTCI_commonUpdate_r17_constr_248 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  1,  4 }	/* (1..4) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ta_BasedPDC_TN_NonSharedSpectrumChAccess_r17_constr_258 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_directionalCollisionDC_IAB_r17_constr_260 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_priorityIndicatorInDCI_Multicast_r17_constr_263 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_priorityIndicatorInDCI_SPS_Multicast_r17_constr_265 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_twoHARQ_ACK_CodebookForUnicastAndMulticast_r17_constr_267 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_multiPUCCH_HARQ_ACK_ForMulticastUnicast_r17_constr_269 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_srs_AdditionalRepetition_r17_constr_271 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pusch_Repetition_CG_SDT_r17_constr_273 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_csi_RS_CFRA_ForHO_value2enum_2[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_csi_RS_CFRA_ForHO_enum2value_2[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_csi_RS_CFRA_ForHO_specs_2 = {
	asn_MAP_NR_csi_RS_CFRA_ForHO_value2enum_2,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_csi_RS_CFRA_ForHO_enum2value_2,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_csi_RS_CFRA_ForHO_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_csi_RS_CFRA_ForHO_2 = {
	"csi-RS-CFRA-ForHO",
	"csi-RS-CFRA-ForHO",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_csi_RS_CFRA_ForHO_tags_2,
	sizeof(asn_DEF_NR_csi_RS_CFRA_ForHO_tags_2)
		/sizeof(asn_DEF_NR_csi_RS_CFRA_ForHO_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_csi_RS_CFRA_ForHO_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_csi_RS_CFRA_ForHO_tags_2)
		/sizeof(asn_DEF_NR_csi_RS_CFRA_ForHO_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_csi_RS_CFRA_ForHO_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_csi_RS_CFRA_ForHO_specs_2	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dynamicPRB_BundlingDL_value2enum_4[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_dynamicPRB_BundlingDL_enum2value_4[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dynamicPRB_BundlingDL_specs_4 = {
	asn_MAP_NR_dynamicPRB_BundlingDL_value2enum_4,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dynamicPRB_BundlingDL_enum2value_4,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dynamicPRB_BundlingDL_tags_4[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dynamicPRB_BundlingDL_4 = {
	"dynamicPRB-BundlingDL",
	"dynamicPRB-BundlingDL",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dynamicPRB_BundlingDL_tags_4,
	sizeof(asn_DEF_NR_dynamicPRB_BundlingDL_tags_4)
		/sizeof(asn_DEF_NR_dynamicPRB_BundlingDL_tags_4[0]) - 1, /* 1 */
	asn_DEF_NR_dynamicPRB_BundlingDL_tags_4,	/* Same as above */
	sizeof(asn_DEF_NR_dynamicPRB_BundlingDL_tags_4)
		/sizeof(asn_DEF_NR_dynamicPRB_BundlingDL_tags_4[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dynamicPRB_BundlingDL_constr_4,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dynamicPRB_BundlingDL_specs_4	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_sp_CSI_ReportPUCCH_value2enum_6[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_sp_CSI_ReportPUCCH_enum2value_6[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_sp_CSI_ReportPUCCH_specs_6 = {
	asn_MAP_NR_sp_CSI_ReportPUCCH_value2enum_6,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_sp_CSI_ReportPUCCH_enum2value_6,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_sp_CSI_ReportPUCCH_tags_6[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_sp_CSI_ReportPUCCH_6 = {
	"sp-CSI-ReportPUCCH",
	"sp-CSI-ReportPUCCH",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_sp_CSI_ReportPUCCH_tags_6,
	sizeof(asn_DEF_NR_sp_CSI_ReportPUCCH_tags_6)
		/sizeof(asn_DEF_NR_sp_CSI_ReportPUCCH_tags_6[0]) - 1, /* 1 */
	asn_DEF_NR_sp_CSI_ReportPUCCH_tags_6,	/* Same as above */
	sizeof(asn_DEF_NR_sp_CSI_ReportPUCCH_tags_6)
		/sizeof(asn_DEF_NR_sp_CSI_ReportPUCCH_tags_6[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_sp_CSI_ReportPUCCH_constr_6,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_sp_CSI_ReportPUCCH_specs_6	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_sp_CSI_ReportPUSCH_value2enum_8[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_sp_CSI_ReportPUSCH_enum2value_8[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_sp_CSI_ReportPUSCH_specs_8 = {
	asn_MAP_NR_sp_CSI_ReportPUSCH_value2enum_8,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_sp_CSI_ReportPUSCH_enum2value_8,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_sp_CSI_ReportPUSCH_tags_8[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_sp_CSI_ReportPUSCH_8 = {
	"sp-CSI-ReportPUSCH",
	"sp-CSI-ReportPUSCH",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_sp_CSI_ReportPUSCH_tags_8,
	sizeof(asn_DEF_NR_sp_CSI_ReportPUSCH_tags_8)
		/sizeof(asn_DEF_NR_sp_CSI_ReportPUSCH_tags_8[0]) - 1, /* 1 */
	asn_DEF_NR_sp_CSI_ReportPUSCH_tags_8,	/* Same as above */
	sizeof(asn_DEF_NR_sp_CSI_ReportPUSCH_tags_8)
		/sizeof(asn_DEF_NR_sp_CSI_ReportPUSCH_tags_8[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_sp_CSI_ReportPUSCH_constr_8,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_sp_CSI_ReportPUSCH_specs_8	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_nzp_CSI_RS_IntefMgmt_value2enum_10[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_nzp_CSI_RS_IntefMgmt_enum2value_10[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_nzp_CSI_RS_IntefMgmt_specs_10 = {
	asn_MAP_NR_nzp_CSI_RS_IntefMgmt_value2enum_10,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_nzp_CSI_RS_IntefMgmt_enum2value_10,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_nzp_CSI_RS_IntefMgmt_tags_10[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_nzp_CSI_RS_IntefMgmt_10 = {
	"nzp-CSI-RS-IntefMgmt",
	"nzp-CSI-RS-IntefMgmt",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_nzp_CSI_RS_IntefMgmt_tags_10,
	sizeof(asn_DEF_NR_nzp_CSI_RS_IntefMgmt_tags_10)
		/sizeof(asn_DEF_NR_nzp_CSI_RS_IntefMgmt_tags_10[0]) - 1, /* 1 */
	asn_DEF_NR_nzp_CSI_RS_IntefMgmt_tags_10,	/* Same as above */
	sizeof(asn_DEF_NR_nzp_CSI_RS_IntefMgmt_tags_10)
		/sizeof(asn_DEF_NR_nzp_CSI_RS_IntefMgmt_tags_10[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_nzp_CSI_RS_IntefMgmt_constr_10,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_nzp_CSI_RS_IntefMgmt_specs_10	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_type2_SP_CSI_Feedback_LongPUCCH_value2enum_12[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_type2_SP_CSI_Feedback_LongPUCCH_enum2value_12[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_type2_SP_CSI_Feedback_LongPUCCH_specs_12 = {
	asn_MAP_NR_type2_SP_CSI_Feedback_LongPUCCH_value2enum_12,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_type2_SP_CSI_Feedback_LongPUCCH_enum2value_12,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_type2_SP_CSI_Feedback_LongPUCCH_tags_12[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_type2_SP_CSI_Feedback_LongPUCCH_12 = {
	"type2-SP-CSI-Feedback-LongPUCCH",
	"type2-SP-CSI-Feedback-LongPUCCH",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_type2_SP_CSI_Feedback_LongPUCCH_tags_12,
	sizeof(asn_DEF_NR_type2_SP_CSI_Feedback_LongPUCCH_tags_12)
		/sizeof(asn_DEF_NR_type2_SP_CSI_Feedback_LongPUCCH_tags_12[0]) - 1, /* 1 */
	asn_DEF_NR_type2_SP_CSI_Feedback_LongPUCCH_tags_12,	/* Same as above */
	sizeof(asn_DEF_NR_type2_SP_CSI_Feedback_LongPUCCH_tags_12)
		/sizeof(asn_DEF_NR_type2_SP_CSI_Feedback_LongPUCCH_tags_12[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_type2_SP_CSI_Feedback_LongPUCCH_constr_12,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_type2_SP_CSI_Feedback_LongPUCCH_specs_12	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_precoderGranularityCORESET_value2enum_14[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_precoderGranularityCORESET_enum2value_14[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_precoderGranularityCORESET_specs_14 = {
	asn_MAP_NR_precoderGranularityCORESET_value2enum_14,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_precoderGranularityCORESET_enum2value_14,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_precoderGranularityCORESET_tags_14[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_precoderGranularityCORESET_14 = {
	"precoderGranularityCORESET",
	"precoderGranularityCORESET",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_precoderGranularityCORESET_tags_14,
	sizeof(asn_DEF_NR_precoderGranularityCORESET_tags_14)
		/sizeof(asn_DEF_NR_precoderGranularityCORESET_tags_14[0]) - 1, /* 1 */
	asn_DEF_NR_precoderGranularityCORESET_tags_14,	/* Same as above */
	sizeof(asn_DEF_NR_precoderGranularityCORESET_tags_14)
		/sizeof(asn_DEF_NR_precoderGranularityCORESET_tags_14[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_precoderGranularityCORESET_constr_14,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_precoderGranularityCORESET_specs_14	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dynamicHARQ_ACK_Codebook_value2enum_16[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_dynamicHARQ_ACK_Codebook_enum2value_16[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dynamicHARQ_ACK_Codebook_specs_16 = {
	asn_MAP_NR_dynamicHARQ_ACK_Codebook_value2enum_16,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dynamicHARQ_ACK_Codebook_enum2value_16,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dynamicHARQ_ACK_Codebook_tags_16[] = {
	(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dynamicHARQ_ACK_Codebook_16 = {
	"dynamicHARQ-ACK-Codebook",
	"dynamicHARQ-ACK-Codebook",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dynamicHARQ_ACK_Codebook_tags_16,
	sizeof(asn_DEF_NR_dynamicHARQ_ACK_Codebook_tags_16)
		/sizeof(asn_DEF_NR_dynamicHARQ_ACK_Codebook_tags_16[0]) - 1, /* 1 */
	asn_DEF_NR_dynamicHARQ_ACK_Codebook_tags_16,	/* Same as above */
	sizeof(asn_DEF_NR_dynamicHARQ_ACK_Codebook_tags_16)
		/sizeof(asn_DEF_NR_dynamicHARQ_ACK_Codebook_tags_16[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dynamicHARQ_ACK_Codebook_constr_16,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dynamicHARQ_ACK_Codebook_specs_16	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_semiStaticHARQ_ACK_Codebook_value2enum_18[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_semiStaticHARQ_ACK_Codebook_enum2value_18[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_semiStaticHARQ_ACK_Codebook_specs_18 = {
	asn_MAP_NR_semiStaticHARQ_ACK_Codebook_value2enum_18,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_semiStaticHARQ_ACK_Codebook_enum2value_18,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_semiStaticHARQ_ACK_Codebook_tags_18[] = {
	(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_semiStaticHARQ_ACK_Codebook_18 = {
	"semiStaticHARQ-ACK-Codebook",
	"semiStaticHARQ-ACK-Codebook",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_semiStaticHARQ_ACK_Codebook_tags_18,
	sizeof(asn_DEF_NR_semiStaticHARQ_ACK_Codebook_tags_18)
		/sizeof(asn_DEF_NR_semiStaticHARQ_ACK_Codebook_tags_18[0]) - 1, /* 1 */
	asn_DEF_NR_semiStaticHARQ_ACK_Codebook_tags_18,	/* Same as above */
	sizeof(asn_DEF_NR_semiStaticHARQ_ACK_Codebook_tags_18)
		/sizeof(asn_DEF_NR_semiStaticHARQ_ACK_Codebook_tags_18[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_semiStaticHARQ_ACK_Codebook_constr_18,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_semiStaticHARQ_ACK_Codebook_specs_18	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_spatialBundlingHARQ_ACK_value2enum_20[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_spatialBundlingHARQ_ACK_enum2value_20[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_spatialBundlingHARQ_ACK_specs_20 = {
	asn_MAP_NR_spatialBundlingHARQ_ACK_value2enum_20,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_spatialBundlingHARQ_ACK_enum2value_20,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_spatialBundlingHARQ_ACK_tags_20[] = {
	(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_spatialBundlingHARQ_ACK_20 = {
	"spatialBundlingHARQ-ACK",
	"spatialBundlingHARQ-ACK",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_spatialBundlingHARQ_ACK_tags_20,
	sizeof(asn_DEF_NR_spatialBundlingHARQ_ACK_tags_20)
		/sizeof(asn_DEF_NR_spatialBundlingHARQ_ACK_tags_20[0]) - 1, /* 1 */
	asn_DEF_NR_spatialBundlingHARQ_ACK_tags_20,	/* Same as above */
	sizeof(asn_DEF_NR_spatialBundlingHARQ_ACK_tags_20)
		/sizeof(asn_DEF_NR_spatialBundlingHARQ_ACK_tags_20[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_spatialBundlingHARQ_ACK_constr_20,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_spatialBundlingHARQ_ACK_specs_20	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dynamicBetaOffsetInd_HARQ_ACK_CSI_value2enum_22[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_dynamicBetaOffsetInd_HARQ_ACK_CSI_enum2value_22[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dynamicBetaOffsetInd_HARQ_ACK_CSI_specs_22 = {
	asn_MAP_NR_dynamicBetaOffsetInd_HARQ_ACK_CSI_value2enum_22,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dynamicBetaOffsetInd_HARQ_ACK_CSI_enum2value_22,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dynamicBetaOffsetInd_HARQ_ACK_CSI_tags_22[] = {
	(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dynamicBetaOffsetInd_HARQ_ACK_CSI_22 = {
	"dynamicBetaOffsetInd-HARQ-ACK-CSI",
	"dynamicBetaOffsetInd-HARQ-ACK-CSI",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dynamicBetaOffsetInd_HARQ_ACK_CSI_tags_22,
	sizeof(asn_DEF_NR_dynamicBetaOffsetInd_HARQ_ACK_CSI_tags_22)
		/sizeof(asn_DEF_NR_dynamicBetaOffsetInd_HARQ_ACK_CSI_tags_22[0]) - 1, /* 1 */
	asn_DEF_NR_dynamicBetaOffsetInd_HARQ_ACK_CSI_tags_22,	/* Same as above */
	sizeof(asn_DEF_NR_dynamicBetaOffsetInd_HARQ_ACK_CSI_tags_22)
		/sizeof(asn_DEF_NR_dynamicBetaOffsetInd_HARQ_ACK_CSI_tags_22[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dynamicBetaOffsetInd_HARQ_ACK_CSI_constr_22,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dynamicBetaOffsetInd_HARQ_ACK_CSI_specs_22	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pucch_Repetition_F1_3_4_value2enum_24[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_pucch_Repetition_F1_3_4_enum2value_24[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pucch_Repetition_F1_3_4_specs_24 = {
	asn_MAP_NR_pucch_Repetition_F1_3_4_value2enum_24,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pucch_Repetition_F1_3_4_enum2value_24,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pucch_Repetition_F1_3_4_tags_24[] = {
	(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pucch_Repetition_F1_3_4_24 = {
	"pucch-Repetition-F1-3-4",
	"pucch-Repetition-F1-3-4",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pucch_Repetition_F1_3_4_tags_24,
	sizeof(asn_DEF_NR_pucch_Repetition_F1_3_4_tags_24)
		/sizeof(asn_DEF_NR_pucch_Repetition_F1_3_4_tags_24[0]) - 1, /* 1 */
	asn_DEF_NR_pucch_Repetition_F1_3_4_tags_24,	/* Same as above */
	sizeof(asn_DEF_NR_pucch_Repetition_F1_3_4_tags_24)
		/sizeof(asn_DEF_NR_pucch_Repetition_F1_3_4_tags_24[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pucch_Repetition_F1_3_4_constr_24,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pucch_Repetition_F1_3_4_specs_24	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_ra_Type0_PUSCH_value2enum_26[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_ra_Type0_PUSCH_enum2value_26[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ra_Type0_PUSCH_specs_26 = {
	asn_MAP_NR_ra_Type0_PUSCH_value2enum_26,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ra_Type0_PUSCH_enum2value_26,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ra_Type0_PUSCH_tags_26[] = {
	(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ra_Type0_PUSCH_26 = {
	"ra-Type0-PUSCH",
	"ra-Type0-PUSCH",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ra_Type0_PUSCH_tags_26,
	sizeof(asn_DEF_NR_ra_Type0_PUSCH_tags_26)
		/sizeof(asn_DEF_NR_ra_Type0_PUSCH_tags_26[0]) - 1, /* 1 */
	asn_DEF_NR_ra_Type0_PUSCH_tags_26,	/* Same as above */
	sizeof(asn_DEF_NR_ra_Type0_PUSCH_tags_26)
		/sizeof(asn_DEF_NR_ra_Type0_PUSCH_tags_26[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ra_Type0_PUSCH_constr_26,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ra_Type0_PUSCH_specs_26	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dynamicSwitchRA_Type0_1_PDSCH_value2enum_28[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_dynamicSwitchRA_Type0_1_PDSCH_enum2value_28[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dynamicSwitchRA_Type0_1_PDSCH_specs_28 = {
	asn_MAP_NR_dynamicSwitchRA_Type0_1_PDSCH_value2enum_28,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dynamicSwitchRA_Type0_1_PDSCH_enum2value_28,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dynamicSwitchRA_Type0_1_PDSCH_tags_28[] = {
	(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dynamicSwitchRA_Type0_1_PDSCH_28 = {
	"dynamicSwitchRA-Type0-1-PDSCH",
	"dynamicSwitchRA-Type0-1-PDSCH",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dynamicSwitchRA_Type0_1_PDSCH_tags_28,
	sizeof(asn_DEF_NR_dynamicSwitchRA_Type0_1_PDSCH_tags_28)
		/sizeof(asn_DEF_NR_dynamicSwitchRA_Type0_1_PDSCH_tags_28[0]) - 1, /* 1 */
	asn_DEF_NR_dynamicSwitchRA_Type0_1_PDSCH_tags_28,	/* Same as above */
	sizeof(asn_DEF_NR_dynamicSwitchRA_Type0_1_PDSCH_tags_28)
		/sizeof(asn_DEF_NR_dynamicSwitchRA_Type0_1_PDSCH_tags_28[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dynamicSwitchRA_Type0_1_PDSCH_constr_28,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dynamicSwitchRA_Type0_1_PDSCH_specs_28	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dynamicSwitchRA_Type0_1_PUSCH_value2enum_30[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_dynamicSwitchRA_Type0_1_PUSCH_enum2value_30[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dynamicSwitchRA_Type0_1_PUSCH_specs_30 = {
	asn_MAP_NR_dynamicSwitchRA_Type0_1_PUSCH_value2enum_30,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dynamicSwitchRA_Type0_1_PUSCH_enum2value_30,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dynamicSwitchRA_Type0_1_PUSCH_tags_30[] = {
	(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dynamicSwitchRA_Type0_1_PUSCH_30 = {
	"dynamicSwitchRA-Type0-1-PUSCH",
	"dynamicSwitchRA-Type0-1-PUSCH",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dynamicSwitchRA_Type0_1_PUSCH_tags_30,
	sizeof(asn_DEF_NR_dynamicSwitchRA_Type0_1_PUSCH_tags_30)
		/sizeof(asn_DEF_NR_dynamicSwitchRA_Type0_1_PUSCH_tags_30[0]) - 1, /* 1 */
	asn_DEF_NR_dynamicSwitchRA_Type0_1_PUSCH_tags_30,	/* Same as above */
	sizeof(asn_DEF_NR_dynamicSwitchRA_Type0_1_PUSCH_tags_30)
		/sizeof(asn_DEF_NR_dynamicSwitchRA_Type0_1_PUSCH_tags_30[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dynamicSwitchRA_Type0_1_PUSCH_constr_30,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dynamicSwitchRA_Type0_1_PUSCH_specs_30	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pdsch_MappingTypeA_value2enum_32[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_pdsch_MappingTypeA_enum2value_32[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pdsch_MappingTypeA_specs_32 = {
	asn_MAP_NR_pdsch_MappingTypeA_value2enum_32,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pdsch_MappingTypeA_enum2value_32,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pdsch_MappingTypeA_tags_32[] = {
	(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pdsch_MappingTypeA_32 = {
	"pdsch-MappingTypeA",
	"pdsch-MappingTypeA",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pdsch_MappingTypeA_tags_32,
	sizeof(asn_DEF_NR_pdsch_MappingTypeA_tags_32)
		/sizeof(asn_DEF_NR_pdsch_MappingTypeA_tags_32[0]) - 1, /* 1 */
	asn_DEF_NR_pdsch_MappingTypeA_tags_32,	/* Same as above */
	sizeof(asn_DEF_NR_pdsch_MappingTypeA_tags_32)
		/sizeof(asn_DEF_NR_pdsch_MappingTypeA_tags_32[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pdsch_MappingTypeA_constr_32,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pdsch_MappingTypeA_specs_32	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pdsch_MappingTypeB_value2enum_34[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_pdsch_MappingTypeB_enum2value_34[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pdsch_MappingTypeB_specs_34 = {
	asn_MAP_NR_pdsch_MappingTypeB_value2enum_34,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pdsch_MappingTypeB_enum2value_34,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pdsch_MappingTypeB_tags_34[] = {
	(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pdsch_MappingTypeB_34 = {
	"pdsch-MappingTypeB",
	"pdsch-MappingTypeB",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pdsch_MappingTypeB_tags_34,
	sizeof(asn_DEF_NR_pdsch_MappingTypeB_tags_34)
		/sizeof(asn_DEF_NR_pdsch_MappingTypeB_tags_34[0]) - 1, /* 1 */
	asn_DEF_NR_pdsch_MappingTypeB_tags_34,	/* Same as above */
	sizeof(asn_DEF_NR_pdsch_MappingTypeB_tags_34)
		/sizeof(asn_DEF_NR_pdsch_MappingTypeB_tags_34[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pdsch_MappingTypeB_constr_34,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pdsch_MappingTypeB_specs_34	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_interleavingVRB_ToPRB_PDSCH_value2enum_36[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_interleavingVRB_ToPRB_PDSCH_enum2value_36[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_interleavingVRB_ToPRB_PDSCH_specs_36 = {
	asn_MAP_NR_interleavingVRB_ToPRB_PDSCH_value2enum_36,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_interleavingVRB_ToPRB_PDSCH_enum2value_36,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_interleavingVRB_ToPRB_PDSCH_tags_36[] = {
	(ASN_TAG_CLASS_CONTEXT | (17 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_interleavingVRB_ToPRB_PDSCH_36 = {
	"interleavingVRB-ToPRB-PDSCH",
	"interleavingVRB-ToPRB-PDSCH",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_interleavingVRB_ToPRB_PDSCH_tags_36,
	sizeof(asn_DEF_NR_interleavingVRB_ToPRB_PDSCH_tags_36)
		/sizeof(asn_DEF_NR_interleavingVRB_ToPRB_PDSCH_tags_36[0]) - 1, /* 1 */
	asn_DEF_NR_interleavingVRB_ToPRB_PDSCH_tags_36,	/* Same as above */
	sizeof(asn_DEF_NR_interleavingVRB_ToPRB_PDSCH_tags_36)
		/sizeof(asn_DEF_NR_interleavingVRB_ToPRB_PDSCH_tags_36[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_interleavingVRB_ToPRB_PDSCH_constr_36,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_interleavingVRB_ToPRB_PDSCH_specs_36	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_interSlotFreqHopping_PUSCH_value2enum_38[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_interSlotFreqHopping_PUSCH_enum2value_38[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_interSlotFreqHopping_PUSCH_specs_38 = {
	asn_MAP_NR_interSlotFreqHopping_PUSCH_value2enum_38,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_interSlotFreqHopping_PUSCH_enum2value_38,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_interSlotFreqHopping_PUSCH_tags_38[] = {
	(ASN_TAG_CLASS_CONTEXT | (18 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_interSlotFreqHopping_PUSCH_38 = {
	"interSlotFreqHopping-PUSCH",
	"interSlotFreqHopping-PUSCH",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_interSlotFreqHopping_PUSCH_tags_38,
	sizeof(asn_DEF_NR_interSlotFreqHopping_PUSCH_tags_38)
		/sizeof(asn_DEF_NR_interSlotFreqHopping_PUSCH_tags_38[0]) - 1, /* 1 */
	asn_DEF_NR_interSlotFreqHopping_PUSCH_tags_38,	/* Same as above */
	sizeof(asn_DEF_NR_interSlotFreqHopping_PUSCH_tags_38)
		/sizeof(asn_DEF_NR_interSlotFreqHopping_PUSCH_tags_38[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_interSlotFreqHopping_PUSCH_constr_38,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_interSlotFreqHopping_PUSCH_specs_38	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_type1_PUSCH_RepetitionMultiSlots_value2enum_40[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_type1_PUSCH_RepetitionMultiSlots_enum2value_40[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_type1_PUSCH_RepetitionMultiSlots_specs_40 = {
	asn_MAP_NR_type1_PUSCH_RepetitionMultiSlots_value2enum_40,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_type1_PUSCH_RepetitionMultiSlots_enum2value_40,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_type1_PUSCH_RepetitionMultiSlots_tags_40[] = {
	(ASN_TAG_CLASS_CONTEXT | (19 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_type1_PUSCH_RepetitionMultiSlots_40 = {
	"type1-PUSCH-RepetitionMultiSlots",
	"type1-PUSCH-RepetitionMultiSlots",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_type1_PUSCH_RepetitionMultiSlots_tags_40,
	sizeof(asn_DEF_NR_type1_PUSCH_RepetitionMultiSlots_tags_40)
		/sizeof(asn_DEF_NR_type1_PUSCH_RepetitionMultiSlots_tags_40[0]) - 1, /* 1 */
	asn_DEF_NR_type1_PUSCH_RepetitionMultiSlots_tags_40,	/* Same as above */
	sizeof(asn_DEF_NR_type1_PUSCH_RepetitionMultiSlots_tags_40)
		/sizeof(asn_DEF_NR_type1_PUSCH_RepetitionMultiSlots_tags_40[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_type1_PUSCH_RepetitionMultiSlots_constr_40,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_type1_PUSCH_RepetitionMultiSlots_specs_40	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_type2_PUSCH_RepetitionMultiSlots_value2enum_42[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_type2_PUSCH_RepetitionMultiSlots_enum2value_42[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_type2_PUSCH_RepetitionMultiSlots_specs_42 = {
	asn_MAP_NR_type2_PUSCH_RepetitionMultiSlots_value2enum_42,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_type2_PUSCH_RepetitionMultiSlots_enum2value_42,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_type2_PUSCH_RepetitionMultiSlots_tags_42[] = {
	(ASN_TAG_CLASS_CONTEXT | (20 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_type2_PUSCH_RepetitionMultiSlots_42 = {
	"type2-PUSCH-RepetitionMultiSlots",
	"type2-PUSCH-RepetitionMultiSlots",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_type2_PUSCH_RepetitionMultiSlots_tags_42,
	sizeof(asn_DEF_NR_type2_PUSCH_RepetitionMultiSlots_tags_42)
		/sizeof(asn_DEF_NR_type2_PUSCH_RepetitionMultiSlots_tags_42[0]) - 1, /* 1 */
	asn_DEF_NR_type2_PUSCH_RepetitionMultiSlots_tags_42,	/* Same as above */
	sizeof(asn_DEF_NR_type2_PUSCH_RepetitionMultiSlots_tags_42)
		/sizeof(asn_DEF_NR_type2_PUSCH_RepetitionMultiSlots_tags_42[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_type2_PUSCH_RepetitionMultiSlots_constr_42,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_type2_PUSCH_RepetitionMultiSlots_specs_42	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pusch_RepetitionMultiSlots_value2enum_44[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_pusch_RepetitionMultiSlots_enum2value_44[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pusch_RepetitionMultiSlots_specs_44 = {
	asn_MAP_NR_pusch_RepetitionMultiSlots_value2enum_44,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pusch_RepetitionMultiSlots_enum2value_44,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pusch_RepetitionMultiSlots_tags_44[] = {
	(ASN_TAG_CLASS_CONTEXT | (21 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pusch_RepetitionMultiSlots_44 = {
	"pusch-RepetitionMultiSlots",
	"pusch-RepetitionMultiSlots",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pusch_RepetitionMultiSlots_tags_44,
	sizeof(asn_DEF_NR_pusch_RepetitionMultiSlots_tags_44)
		/sizeof(asn_DEF_NR_pusch_RepetitionMultiSlots_tags_44[0]) - 1, /* 1 */
	asn_DEF_NR_pusch_RepetitionMultiSlots_tags_44,	/* Same as above */
	sizeof(asn_DEF_NR_pusch_RepetitionMultiSlots_tags_44)
		/sizeof(asn_DEF_NR_pusch_RepetitionMultiSlots_tags_44[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pusch_RepetitionMultiSlots_constr_44,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pusch_RepetitionMultiSlots_specs_44	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pdsch_RepetitionMultiSlots_value2enum_46[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_pdsch_RepetitionMultiSlots_enum2value_46[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pdsch_RepetitionMultiSlots_specs_46 = {
	asn_MAP_NR_pdsch_RepetitionMultiSlots_value2enum_46,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pdsch_RepetitionMultiSlots_enum2value_46,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pdsch_RepetitionMultiSlots_tags_46[] = {
	(ASN_TAG_CLASS_CONTEXT | (22 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pdsch_RepetitionMultiSlots_46 = {
	"pdsch-RepetitionMultiSlots",
	"pdsch-RepetitionMultiSlots",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pdsch_RepetitionMultiSlots_tags_46,
	sizeof(asn_DEF_NR_pdsch_RepetitionMultiSlots_tags_46)
		/sizeof(asn_DEF_NR_pdsch_RepetitionMultiSlots_tags_46[0]) - 1, /* 1 */
	asn_DEF_NR_pdsch_RepetitionMultiSlots_tags_46,	/* Same as above */
	sizeof(asn_DEF_NR_pdsch_RepetitionMultiSlots_tags_46)
		/sizeof(asn_DEF_NR_pdsch_RepetitionMultiSlots_tags_46[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pdsch_RepetitionMultiSlots_constr_46,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pdsch_RepetitionMultiSlots_specs_46	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_downlinkSPS_value2enum_48[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_downlinkSPS_enum2value_48[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_downlinkSPS_specs_48 = {
	asn_MAP_NR_downlinkSPS_value2enum_48,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_downlinkSPS_enum2value_48,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_downlinkSPS_tags_48[] = {
	(ASN_TAG_CLASS_CONTEXT | (23 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_downlinkSPS_48 = {
	"downlinkSPS",
	"downlinkSPS",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_downlinkSPS_tags_48,
	sizeof(asn_DEF_NR_downlinkSPS_tags_48)
		/sizeof(asn_DEF_NR_downlinkSPS_tags_48[0]) - 1, /* 1 */
	asn_DEF_NR_downlinkSPS_tags_48,	/* Same as above */
	sizeof(asn_DEF_NR_downlinkSPS_tags_48)
		/sizeof(asn_DEF_NR_downlinkSPS_tags_48[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_downlinkSPS_constr_48,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_downlinkSPS_specs_48	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_configuredUL_GrantType1_value2enum_50[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_configuredUL_GrantType1_enum2value_50[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_configuredUL_GrantType1_specs_50 = {
	asn_MAP_NR_configuredUL_GrantType1_value2enum_50,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_configuredUL_GrantType1_enum2value_50,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_configuredUL_GrantType1_tags_50[] = {
	(ASN_TAG_CLASS_CONTEXT | (24 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_configuredUL_GrantType1_50 = {
	"configuredUL-GrantType1",
	"configuredUL-GrantType1",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_configuredUL_GrantType1_tags_50,
	sizeof(asn_DEF_NR_configuredUL_GrantType1_tags_50)
		/sizeof(asn_DEF_NR_configuredUL_GrantType1_tags_50[0]) - 1, /* 1 */
	asn_DEF_NR_configuredUL_GrantType1_tags_50,	/* Same as above */
	sizeof(asn_DEF_NR_configuredUL_GrantType1_tags_50)
		/sizeof(asn_DEF_NR_configuredUL_GrantType1_tags_50[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_configuredUL_GrantType1_constr_50,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_configuredUL_GrantType1_specs_50	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_configuredUL_GrantType2_value2enum_52[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_configuredUL_GrantType2_enum2value_52[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_configuredUL_GrantType2_specs_52 = {
	asn_MAP_NR_configuredUL_GrantType2_value2enum_52,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_configuredUL_GrantType2_enum2value_52,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_configuredUL_GrantType2_tags_52[] = {
	(ASN_TAG_CLASS_CONTEXT | (25 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_configuredUL_GrantType2_52 = {
	"configuredUL-GrantType2",
	"configuredUL-GrantType2",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_configuredUL_GrantType2_tags_52,
	sizeof(asn_DEF_NR_configuredUL_GrantType2_tags_52)
		/sizeof(asn_DEF_NR_configuredUL_GrantType2_tags_52[0]) - 1, /* 1 */
	asn_DEF_NR_configuredUL_GrantType2_tags_52,	/* Same as above */
	sizeof(asn_DEF_NR_configuredUL_GrantType2_tags_52)
		/sizeof(asn_DEF_NR_configuredUL_GrantType2_tags_52[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_configuredUL_GrantType2_constr_52,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_configuredUL_GrantType2_specs_52	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pre_EmptIndication_DL_value2enum_54[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_pre_EmptIndication_DL_enum2value_54[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pre_EmptIndication_DL_specs_54 = {
	asn_MAP_NR_pre_EmptIndication_DL_value2enum_54,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pre_EmptIndication_DL_enum2value_54,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pre_EmptIndication_DL_tags_54[] = {
	(ASN_TAG_CLASS_CONTEXT | (26 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pre_EmptIndication_DL_54 = {
	"pre-EmptIndication-DL",
	"pre-EmptIndication-DL",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pre_EmptIndication_DL_tags_54,
	sizeof(asn_DEF_NR_pre_EmptIndication_DL_tags_54)
		/sizeof(asn_DEF_NR_pre_EmptIndication_DL_tags_54[0]) - 1, /* 1 */
	asn_DEF_NR_pre_EmptIndication_DL_tags_54,	/* Same as above */
	sizeof(asn_DEF_NR_pre_EmptIndication_DL_tags_54)
		/sizeof(asn_DEF_NR_pre_EmptIndication_DL_tags_54[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pre_EmptIndication_DL_constr_54,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pre_EmptIndication_DL_specs_54	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_cbg_TransIndication_DL_value2enum_56[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_cbg_TransIndication_DL_enum2value_56[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_cbg_TransIndication_DL_specs_56 = {
	asn_MAP_NR_cbg_TransIndication_DL_value2enum_56,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_cbg_TransIndication_DL_enum2value_56,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_cbg_TransIndication_DL_tags_56[] = {
	(ASN_TAG_CLASS_CONTEXT | (27 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_cbg_TransIndication_DL_56 = {
	"cbg-TransIndication-DL",
	"cbg-TransIndication-DL",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_cbg_TransIndication_DL_tags_56,
	sizeof(asn_DEF_NR_cbg_TransIndication_DL_tags_56)
		/sizeof(asn_DEF_NR_cbg_TransIndication_DL_tags_56[0]) - 1, /* 1 */
	asn_DEF_NR_cbg_TransIndication_DL_tags_56,	/* Same as above */
	sizeof(asn_DEF_NR_cbg_TransIndication_DL_tags_56)
		/sizeof(asn_DEF_NR_cbg_TransIndication_DL_tags_56[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_cbg_TransIndication_DL_constr_56,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_cbg_TransIndication_DL_specs_56	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_cbg_TransIndication_UL_value2enum_58[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_cbg_TransIndication_UL_enum2value_58[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_cbg_TransIndication_UL_specs_58 = {
	asn_MAP_NR_cbg_TransIndication_UL_value2enum_58,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_cbg_TransIndication_UL_enum2value_58,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_cbg_TransIndication_UL_tags_58[] = {
	(ASN_TAG_CLASS_CONTEXT | (28 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_cbg_TransIndication_UL_58 = {
	"cbg-TransIndication-UL",
	"cbg-TransIndication-UL",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_cbg_TransIndication_UL_tags_58,
	sizeof(asn_DEF_NR_cbg_TransIndication_UL_tags_58)
		/sizeof(asn_DEF_NR_cbg_TransIndication_UL_tags_58[0]) - 1, /* 1 */
	asn_DEF_NR_cbg_TransIndication_UL_tags_58,	/* Same as above */
	sizeof(asn_DEF_NR_cbg_TransIndication_UL_tags_58)
		/sizeof(asn_DEF_NR_cbg_TransIndication_UL_tags_58[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_cbg_TransIndication_UL_constr_58,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_cbg_TransIndication_UL_specs_58	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_cbg_FlushIndication_DL_value2enum_60[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_cbg_FlushIndication_DL_enum2value_60[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_cbg_FlushIndication_DL_specs_60 = {
	asn_MAP_NR_cbg_FlushIndication_DL_value2enum_60,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_cbg_FlushIndication_DL_enum2value_60,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_cbg_FlushIndication_DL_tags_60[] = {
	(ASN_TAG_CLASS_CONTEXT | (29 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_cbg_FlushIndication_DL_60 = {
	"cbg-FlushIndication-DL",
	"cbg-FlushIndication-DL",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_cbg_FlushIndication_DL_tags_60,
	sizeof(asn_DEF_NR_cbg_FlushIndication_DL_tags_60)
		/sizeof(asn_DEF_NR_cbg_FlushIndication_DL_tags_60[0]) - 1, /* 1 */
	asn_DEF_NR_cbg_FlushIndication_DL_tags_60,	/* Same as above */
	sizeof(asn_DEF_NR_cbg_FlushIndication_DL_tags_60)
		/sizeof(asn_DEF_NR_cbg_FlushIndication_DL_tags_60[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_cbg_FlushIndication_DL_constr_60,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_cbg_FlushIndication_DL_specs_60	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dynamicHARQ_ACK_CodeB_CBG_Retx_DL_value2enum_62[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_dynamicHARQ_ACK_CodeB_CBG_Retx_DL_enum2value_62[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dynamicHARQ_ACK_CodeB_CBG_Retx_DL_specs_62 = {
	asn_MAP_NR_dynamicHARQ_ACK_CodeB_CBG_Retx_DL_value2enum_62,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dynamicHARQ_ACK_CodeB_CBG_Retx_DL_enum2value_62,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dynamicHARQ_ACK_CodeB_CBG_Retx_DL_tags_62[] = {
	(ASN_TAG_CLASS_CONTEXT | (30 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dynamicHARQ_ACK_CodeB_CBG_Retx_DL_62 = {
	"dynamicHARQ-ACK-CodeB-CBG-Retx-DL",
	"dynamicHARQ-ACK-CodeB-CBG-Retx-DL",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dynamicHARQ_ACK_CodeB_CBG_Retx_DL_tags_62,
	sizeof(asn_DEF_NR_dynamicHARQ_ACK_CodeB_CBG_Retx_DL_tags_62)
		/sizeof(asn_DEF_NR_dynamicHARQ_ACK_CodeB_CBG_Retx_DL_tags_62[0]) - 1, /* 1 */
	asn_DEF_NR_dynamicHARQ_ACK_CodeB_CBG_Retx_DL_tags_62,	/* Same as above */
	sizeof(asn_DEF_NR_dynamicHARQ_ACK_CodeB_CBG_Retx_DL_tags_62)
		/sizeof(asn_DEF_NR_dynamicHARQ_ACK_CodeB_CBG_Retx_DL_tags_62[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dynamicHARQ_ACK_CodeB_CBG_Retx_DL_constr_62,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dynamicHARQ_ACK_CodeB_CBG_Retx_DL_specs_62	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_rateMatchingResrcSetSemi_Static_value2enum_64[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_rateMatchingResrcSetSemi_Static_enum2value_64[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_rateMatchingResrcSetSemi_Static_specs_64 = {
	asn_MAP_NR_rateMatchingResrcSetSemi_Static_value2enum_64,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_rateMatchingResrcSetSemi_Static_enum2value_64,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_rateMatchingResrcSetSemi_Static_tags_64[] = {
	(ASN_TAG_CLASS_CONTEXT | (31 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_rateMatchingResrcSetSemi_Static_64 = {
	"rateMatchingResrcSetSemi-Static",
	"rateMatchingResrcSetSemi-Static",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_rateMatchingResrcSetSemi_Static_tags_64,
	sizeof(asn_DEF_NR_rateMatchingResrcSetSemi_Static_tags_64)
		/sizeof(asn_DEF_NR_rateMatchingResrcSetSemi_Static_tags_64[0]) - 1, /* 1 */
	asn_DEF_NR_rateMatchingResrcSetSemi_Static_tags_64,	/* Same as above */
	sizeof(asn_DEF_NR_rateMatchingResrcSetSemi_Static_tags_64)
		/sizeof(asn_DEF_NR_rateMatchingResrcSetSemi_Static_tags_64[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_rateMatchingResrcSetSemi_Static_constr_64,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_rateMatchingResrcSetSemi_Static_specs_64	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_rateMatchingResrcSetDynamic_value2enum_66[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_rateMatchingResrcSetDynamic_enum2value_66[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_rateMatchingResrcSetDynamic_specs_66 = {
	asn_MAP_NR_rateMatchingResrcSetDynamic_value2enum_66,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_rateMatchingResrcSetDynamic_enum2value_66,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_rateMatchingResrcSetDynamic_tags_66[] = {
	(ASN_TAG_CLASS_CONTEXT | (32 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_rateMatchingResrcSetDynamic_66 = {
	"rateMatchingResrcSetDynamic",
	"rateMatchingResrcSetDynamic",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_rateMatchingResrcSetDynamic_tags_66,
	sizeof(asn_DEF_NR_rateMatchingResrcSetDynamic_tags_66)
		/sizeof(asn_DEF_NR_rateMatchingResrcSetDynamic_tags_66[0]) - 1, /* 1 */
	asn_DEF_NR_rateMatchingResrcSetDynamic_tags_66,	/* Same as above */
	sizeof(asn_DEF_NR_rateMatchingResrcSetDynamic_tags_66)
		/sizeof(asn_DEF_NR_rateMatchingResrcSetDynamic_tags_66[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_rateMatchingResrcSetDynamic_constr_66,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_rateMatchingResrcSetDynamic_specs_66	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_bwp_SwitchingDelay_value2enum_68[] = {
	{ 0,	5,	"type1" },
	{ 1,	5,	"type2" }
};
static const unsigned int asn_MAP_NR_bwp_SwitchingDelay_enum2value_68[] = {
	0,	/* type1(0) */
	1	/* type2(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_bwp_SwitchingDelay_specs_68 = {
	asn_MAP_NR_bwp_SwitchingDelay_value2enum_68,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_bwp_SwitchingDelay_enum2value_68,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_bwp_SwitchingDelay_tags_68[] = {
	(ASN_TAG_CLASS_CONTEXT | (33 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_bwp_SwitchingDelay_68 = {
	"bwp-SwitchingDelay",
	"bwp-SwitchingDelay",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_bwp_SwitchingDelay_tags_68,
	sizeof(asn_DEF_NR_bwp_SwitchingDelay_tags_68)
		/sizeof(asn_DEF_NR_bwp_SwitchingDelay_tags_68[0]) - 1, /* 1 */
	asn_DEF_NR_bwp_SwitchingDelay_tags_68,	/* Same as above */
	sizeof(asn_DEF_NR_bwp_SwitchingDelay_tags_68)
		/sizeof(asn_DEF_NR_bwp_SwitchingDelay_tags_68[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_bwp_SwitchingDelay_constr_68,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_bwp_SwitchingDelay_specs_68	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dummy_value2enum_73[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_dummy_enum2value_73[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dummy_specs_73 = {
	asn_MAP_NR_dummy_value2enum_73,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dummy_enum2value_73,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dummy_tags_73[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dummy_73 = {
	"dummy",
	"dummy",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dummy_tags_73,
	sizeof(asn_DEF_NR_dummy_tags_73)
		/sizeof(asn_DEF_NR_dummy_tags_73[0]) - 1, /* 1 */
	asn_DEF_NR_dummy_tags_73,	/* Same as above */
	sizeof(asn_DEF_NR_dummy_tags_73)
		/sizeof(asn_DEF_NR_dummy_tags_73[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dummy_constr_73,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dummy_specs_73	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext1_72[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersCommon__ext1, dummy),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dummy_73,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dummy"
		},
};
static const int asn_MAP_NR_ext1_oms_72[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_72[] = {
	(ASN_TAG_CLASS_CONTEXT | (34 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_72[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* dummy */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_72 = {
	sizeof(struct NR_Phy_ParametersCommon__ext1),
	offsetof(struct NR_Phy_ParametersCommon__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_72,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_72,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_72 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_72,
	sizeof(asn_DEF_NR_ext1_tags_72)
		/sizeof(asn_DEF_NR_ext1_tags_72[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_72,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_72)
		/sizeof(asn_DEF_NR_ext1_tags_72[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_72,
	1,	/* Elements count */
	&asn_SPC_NR_ext1_specs_72	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_maxNumberSearchSpaces_value2enum_76[] = {
	{ 0,	3,	"n10" }
};
static const unsigned int asn_MAP_NR_maxNumberSearchSpaces_enum2value_76[] = {
	0	/* n10(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_maxNumberSearchSpaces_specs_76 = {
	asn_MAP_NR_maxNumberSearchSpaces_value2enum_76,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_maxNumberSearchSpaces_enum2value_76,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_maxNumberSearchSpaces_tags_76[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_maxNumberSearchSpaces_76 = {
	"maxNumberSearchSpaces",
	"maxNumberSearchSpaces",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_maxNumberSearchSpaces_tags_76,
	sizeof(asn_DEF_NR_maxNumberSearchSpaces_tags_76)
		/sizeof(asn_DEF_NR_maxNumberSearchSpaces_tags_76[0]) - 1, /* 1 */
	asn_DEF_NR_maxNumberSearchSpaces_tags_76,	/* Same as above */
	sizeof(asn_DEF_NR_maxNumberSearchSpaces_tags_76)
		/sizeof(asn_DEF_NR_maxNumberSearchSpaces_tags_76[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_maxNumberSearchSpaces_constr_76,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_maxNumberSearchSpaces_specs_76	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_rateMatchingCtrlResrcSetDynamic_value2enum_78[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_rateMatchingCtrlResrcSetDynamic_enum2value_78[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_rateMatchingCtrlResrcSetDynamic_specs_78 = {
	asn_MAP_NR_rateMatchingCtrlResrcSetDynamic_value2enum_78,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_rateMatchingCtrlResrcSetDynamic_enum2value_78,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_rateMatchingCtrlResrcSetDynamic_tags_78[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_rateMatchingCtrlResrcSetDynamic_78 = {
	"rateMatchingCtrlResrcSetDynamic",
	"rateMatchingCtrlResrcSetDynamic",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_rateMatchingCtrlResrcSetDynamic_tags_78,
	sizeof(asn_DEF_NR_rateMatchingCtrlResrcSetDynamic_tags_78)
		/sizeof(asn_DEF_NR_rateMatchingCtrlResrcSetDynamic_tags_78[0]) - 1, /* 1 */
	asn_DEF_NR_rateMatchingCtrlResrcSetDynamic_tags_78,	/* Same as above */
	sizeof(asn_DEF_NR_rateMatchingCtrlResrcSetDynamic_tags_78)
		/sizeof(asn_DEF_NR_rateMatchingCtrlResrcSetDynamic_tags_78[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_rateMatchingCtrlResrcSetDynamic_constr_78,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_rateMatchingCtrlResrcSetDynamic_specs_78	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_maxLayersMIMO_Indication_value2enum_80[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_maxLayersMIMO_Indication_enum2value_80[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_maxLayersMIMO_Indication_specs_80 = {
	asn_MAP_NR_maxLayersMIMO_Indication_value2enum_80,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_maxLayersMIMO_Indication_enum2value_80,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_maxLayersMIMO_Indication_tags_80[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_maxLayersMIMO_Indication_80 = {
	"maxLayersMIMO-Indication",
	"maxLayersMIMO-Indication",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_maxLayersMIMO_Indication_tags_80,
	sizeof(asn_DEF_NR_maxLayersMIMO_Indication_tags_80)
		/sizeof(asn_DEF_NR_maxLayersMIMO_Indication_tags_80[0]) - 1, /* 1 */
	asn_DEF_NR_maxLayersMIMO_Indication_tags_80,	/* Same as above */
	sizeof(asn_DEF_NR_maxLayersMIMO_Indication_tags_80)
		/sizeof(asn_DEF_NR_maxLayersMIMO_Indication_tags_80[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_maxLayersMIMO_Indication_constr_80,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_maxLayersMIMO_Indication_specs_80	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext2_75[] = {
	{ ATF_POINTER, 3, offsetof(struct NR_Phy_ParametersCommon__ext2, maxNumberSearchSpaces),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_maxNumberSearchSpaces_76,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"maxNumberSearchSpaces"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_Phy_ParametersCommon__ext2, rateMatchingCtrlResrcSetDynamic),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_rateMatchingCtrlResrcSetDynamic_78,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"rateMatchingCtrlResrcSetDynamic"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersCommon__ext2, maxLayersMIMO_Indication),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_maxLayersMIMO_Indication_80,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"maxLayersMIMO-Indication"
		},
};
static const int asn_MAP_NR_ext2_oms_75[] = { 0, 1, 2 };
static const ber_tlv_tag_t asn_DEF_NR_ext2_tags_75[] = {
	(ASN_TAG_CLASS_CONTEXT | (35 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext2_tag2el_75[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* maxNumberSearchSpaces */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* rateMatchingCtrlResrcSetDynamic */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* maxLayersMIMO-Indication */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext2_specs_75 = {
	sizeof(struct NR_Phy_ParametersCommon__ext2),
	offsetof(struct NR_Phy_ParametersCommon__ext2, _asn_ctx),
	asn_MAP_NR_ext2_tag2el_75,
	3,	/* Count of tags in the map */
	asn_MAP_NR_ext2_oms_75,	/* Optional members */
	3, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext2_75 = {
	"ext2",
	"ext2",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext2_tags_75,
	sizeof(asn_DEF_NR_ext2_tags_75)
		/sizeof(asn_DEF_NR_ext2_tags_75[0]) - 1, /* 1 */
	asn_DEF_NR_ext2_tags_75,	/* Same as above */
	sizeof(asn_DEF_NR_ext2_tags_75)
		/sizeof(asn_DEF_NR_ext2_tags_75[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext2_75,
	3,	/* Elements count */
	&asn_SPC_NR_ext2_specs_75	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext3_82[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersCommon__ext3, spCellPlacement),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_CarrierAggregationVariant,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"spCellPlacement"
		},
};
static const int asn_MAP_NR_ext3_oms_82[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext3_tags_82[] = {
	(ASN_TAG_CLASS_CONTEXT | (36 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext3_tag2el_82[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* spCellPlacement */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext3_specs_82 = {
	sizeof(struct NR_Phy_ParametersCommon__ext3),
	offsetof(struct NR_Phy_ParametersCommon__ext3, _asn_ctx),
	asn_MAP_NR_ext3_tag2el_82,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext3_oms_82,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext3_82 = {
	"ext3",
	"ext3",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext3_tags_82,
	sizeof(asn_DEF_NR_ext3_tags_82)
		/sizeof(asn_DEF_NR_ext3_tags_82[0]) - 1, /* 1 */
	asn_DEF_NR_ext3_tags_82,	/* Same as above */
	sizeof(asn_DEF_NR_ext3_tags_82)
		/sizeof(asn_DEF_NR_ext3_tags_82[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext3_82,
	1,	/* Elements count */
	&asn_SPC_NR_ext3_specs_82	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_twoStepRACH_r16_value2enum_85[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_twoStepRACH_r16_enum2value_85[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_twoStepRACH_r16_specs_85 = {
	asn_MAP_NR_twoStepRACH_r16_value2enum_85,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_twoStepRACH_r16_enum2value_85,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_twoStepRACH_r16_tags_85[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_twoStepRACH_r16_85 = {
	"twoStepRACH-r16",
	"twoStepRACH-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_twoStepRACH_r16_tags_85,
	sizeof(asn_DEF_NR_twoStepRACH_r16_tags_85)
		/sizeof(asn_DEF_NR_twoStepRACH_r16_tags_85[0]) - 1, /* 1 */
	asn_DEF_NR_twoStepRACH_r16_tags_85,	/* Same as above */
	sizeof(asn_DEF_NR_twoStepRACH_r16_tags_85)
		/sizeof(asn_DEF_NR_twoStepRACH_r16_tags_85[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_twoStepRACH_r16_constr_85,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_twoStepRACH_r16_specs_85	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dci_Format1_2And0_2_r16_value2enum_87[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_dci_Format1_2And0_2_r16_enum2value_87[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dci_Format1_2And0_2_r16_specs_87 = {
	asn_MAP_NR_dci_Format1_2And0_2_r16_value2enum_87,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dci_Format1_2And0_2_r16_enum2value_87,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dci_Format1_2And0_2_r16_tags_87[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dci_Format1_2And0_2_r16_87 = {
	"dci-Format1-2And0-2-r16",
	"dci-Format1-2And0-2-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dci_Format1_2And0_2_r16_tags_87,
	sizeof(asn_DEF_NR_dci_Format1_2And0_2_r16_tags_87)
		/sizeof(asn_DEF_NR_dci_Format1_2And0_2_r16_tags_87[0]) - 1, /* 1 */
	asn_DEF_NR_dci_Format1_2And0_2_r16_tags_87,	/* Same as above */
	sizeof(asn_DEF_NR_dci_Format1_2And0_2_r16_tags_87)
		/sizeof(asn_DEF_NR_dci_Format1_2And0_2_r16_tags_87[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dci_Format1_2And0_2_r16_constr_87,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dci_Format1_2And0_2_r16_specs_87	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_monitoringDCI_SameSearchSpace_r16_value2enum_89[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_monitoringDCI_SameSearchSpace_r16_enum2value_89[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_monitoringDCI_SameSearchSpace_r16_specs_89 = {
	asn_MAP_NR_monitoringDCI_SameSearchSpace_r16_value2enum_89,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_monitoringDCI_SameSearchSpace_r16_enum2value_89,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_monitoringDCI_SameSearchSpace_r16_tags_89[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_monitoringDCI_SameSearchSpace_r16_89 = {
	"monitoringDCI-SameSearchSpace-r16",
	"monitoringDCI-SameSearchSpace-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_monitoringDCI_SameSearchSpace_r16_tags_89,
	sizeof(asn_DEF_NR_monitoringDCI_SameSearchSpace_r16_tags_89)
		/sizeof(asn_DEF_NR_monitoringDCI_SameSearchSpace_r16_tags_89[0]) - 1, /* 1 */
	asn_DEF_NR_monitoringDCI_SameSearchSpace_r16_tags_89,	/* Same as above */
	sizeof(asn_DEF_NR_monitoringDCI_SameSearchSpace_r16_tags_89)
		/sizeof(asn_DEF_NR_monitoringDCI_SameSearchSpace_r16_tags_89[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_monitoringDCI_SameSearchSpace_r16_constr_89,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_monitoringDCI_SameSearchSpace_r16_specs_89	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_type2_CG_ReleaseDCI_0_1_r16_value2enum_91[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_type2_CG_ReleaseDCI_0_1_r16_enum2value_91[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_type2_CG_ReleaseDCI_0_1_r16_specs_91 = {
	asn_MAP_NR_type2_CG_ReleaseDCI_0_1_r16_value2enum_91,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_type2_CG_ReleaseDCI_0_1_r16_enum2value_91,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_type2_CG_ReleaseDCI_0_1_r16_tags_91[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_type2_CG_ReleaseDCI_0_1_r16_91 = {
	"type2-CG-ReleaseDCI-0-1-r16",
	"type2-CG-ReleaseDCI-0-1-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_type2_CG_ReleaseDCI_0_1_r16_tags_91,
	sizeof(asn_DEF_NR_type2_CG_ReleaseDCI_0_1_r16_tags_91)
		/sizeof(asn_DEF_NR_type2_CG_ReleaseDCI_0_1_r16_tags_91[0]) - 1, /* 1 */
	asn_DEF_NR_type2_CG_ReleaseDCI_0_1_r16_tags_91,	/* Same as above */
	sizeof(asn_DEF_NR_type2_CG_ReleaseDCI_0_1_r16_tags_91)
		/sizeof(asn_DEF_NR_type2_CG_ReleaseDCI_0_1_r16_tags_91[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_type2_CG_ReleaseDCI_0_1_r16_constr_91,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_type2_CG_ReleaseDCI_0_1_r16_specs_91	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_type2_CG_ReleaseDCI_0_2_r16_value2enum_93[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_type2_CG_ReleaseDCI_0_2_r16_enum2value_93[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_type2_CG_ReleaseDCI_0_2_r16_specs_93 = {
	asn_MAP_NR_type2_CG_ReleaseDCI_0_2_r16_value2enum_93,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_type2_CG_ReleaseDCI_0_2_r16_enum2value_93,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_type2_CG_ReleaseDCI_0_2_r16_tags_93[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_type2_CG_ReleaseDCI_0_2_r16_93 = {
	"type2-CG-ReleaseDCI-0-2-r16",
	"type2-CG-ReleaseDCI-0-2-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_type2_CG_ReleaseDCI_0_2_r16_tags_93,
	sizeof(asn_DEF_NR_type2_CG_ReleaseDCI_0_2_r16_tags_93)
		/sizeof(asn_DEF_NR_type2_CG_ReleaseDCI_0_2_r16_tags_93[0]) - 1, /* 1 */
	asn_DEF_NR_type2_CG_ReleaseDCI_0_2_r16_tags_93,	/* Same as above */
	sizeof(asn_DEF_NR_type2_CG_ReleaseDCI_0_2_r16_tags_93)
		/sizeof(asn_DEF_NR_type2_CG_ReleaseDCI_0_2_r16_tags_93[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_type2_CG_ReleaseDCI_0_2_r16_constr_93,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_type2_CG_ReleaseDCI_0_2_r16_specs_93	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_sps_ReleaseDCI_1_1_r16_value2enum_95[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_sps_ReleaseDCI_1_1_r16_enum2value_95[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_sps_ReleaseDCI_1_1_r16_specs_95 = {
	asn_MAP_NR_sps_ReleaseDCI_1_1_r16_value2enum_95,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_sps_ReleaseDCI_1_1_r16_enum2value_95,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_sps_ReleaseDCI_1_1_r16_tags_95[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_sps_ReleaseDCI_1_1_r16_95 = {
	"sps-ReleaseDCI-1-1-r16",
	"sps-ReleaseDCI-1-1-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_sps_ReleaseDCI_1_1_r16_tags_95,
	sizeof(asn_DEF_NR_sps_ReleaseDCI_1_1_r16_tags_95)
		/sizeof(asn_DEF_NR_sps_ReleaseDCI_1_1_r16_tags_95[0]) - 1, /* 1 */
	asn_DEF_NR_sps_ReleaseDCI_1_1_r16_tags_95,	/* Same as above */
	sizeof(asn_DEF_NR_sps_ReleaseDCI_1_1_r16_tags_95)
		/sizeof(asn_DEF_NR_sps_ReleaseDCI_1_1_r16_tags_95[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_sps_ReleaseDCI_1_1_r16_constr_95,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_sps_ReleaseDCI_1_1_r16_specs_95	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_sps_ReleaseDCI_1_2_r16_value2enum_97[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_sps_ReleaseDCI_1_2_r16_enum2value_97[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_sps_ReleaseDCI_1_2_r16_specs_97 = {
	asn_MAP_NR_sps_ReleaseDCI_1_2_r16_value2enum_97,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_sps_ReleaseDCI_1_2_r16_enum2value_97,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_sps_ReleaseDCI_1_2_r16_tags_97[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_sps_ReleaseDCI_1_2_r16_97 = {
	"sps-ReleaseDCI-1-2-r16",
	"sps-ReleaseDCI-1-2-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_sps_ReleaseDCI_1_2_r16_tags_97,
	sizeof(asn_DEF_NR_sps_ReleaseDCI_1_2_r16_tags_97)
		/sizeof(asn_DEF_NR_sps_ReleaseDCI_1_2_r16_tags_97[0]) - 1, /* 1 */
	asn_DEF_NR_sps_ReleaseDCI_1_2_r16_tags_97,	/* Same as above */
	sizeof(asn_DEF_NR_sps_ReleaseDCI_1_2_r16_tags_97)
		/sizeof(asn_DEF_NR_sps_ReleaseDCI_1_2_r16_tags_97[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_sps_ReleaseDCI_1_2_r16_constr_97,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_sps_ReleaseDCI_1_2_r16_specs_97	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_csi_TriggerStateNon_ActiveBWP_r16_value2enum_99[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_csi_TriggerStateNon_ActiveBWP_r16_enum2value_99[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_csi_TriggerStateNon_ActiveBWP_r16_specs_99 = {
	asn_MAP_NR_csi_TriggerStateNon_ActiveBWP_r16_value2enum_99,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_csi_TriggerStateNon_ActiveBWP_r16_enum2value_99,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_csi_TriggerStateNon_ActiveBWP_r16_tags_99[] = {
	(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_csi_TriggerStateNon_ActiveBWP_r16_99 = {
	"csi-TriggerStateNon-ActiveBWP-r16",
	"csi-TriggerStateNon-ActiveBWP-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_csi_TriggerStateNon_ActiveBWP_r16_tags_99,
	sizeof(asn_DEF_NR_csi_TriggerStateNon_ActiveBWP_r16_tags_99)
		/sizeof(asn_DEF_NR_csi_TriggerStateNon_ActiveBWP_r16_tags_99[0]) - 1, /* 1 */
	asn_DEF_NR_csi_TriggerStateNon_ActiveBWP_r16_tags_99,	/* Same as above */
	sizeof(asn_DEF_NR_csi_TriggerStateNon_ActiveBWP_r16_tags_99)
		/sizeof(asn_DEF_NR_csi_TriggerStateNon_ActiveBWP_r16_tags_99[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_csi_TriggerStateNon_ActiveBWP_r16_constr_99,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_csi_TriggerStateNon_ActiveBWP_r16_specs_99	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_separateSMTC_InterIAB_Support_r16_value2enum_101[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_separateSMTC_InterIAB_Support_r16_enum2value_101[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_separateSMTC_InterIAB_Support_r16_specs_101 = {
	asn_MAP_NR_separateSMTC_InterIAB_Support_r16_value2enum_101,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_separateSMTC_InterIAB_Support_r16_enum2value_101,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_separateSMTC_InterIAB_Support_r16_tags_101[] = {
	(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_separateSMTC_InterIAB_Support_r16_101 = {
	"separateSMTC-InterIAB-Support-r16",
	"separateSMTC-InterIAB-Support-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_separateSMTC_InterIAB_Support_r16_tags_101,
	sizeof(asn_DEF_NR_separateSMTC_InterIAB_Support_r16_tags_101)
		/sizeof(asn_DEF_NR_separateSMTC_InterIAB_Support_r16_tags_101[0]) - 1, /* 1 */
	asn_DEF_NR_separateSMTC_InterIAB_Support_r16_tags_101,	/* Same as above */
	sizeof(asn_DEF_NR_separateSMTC_InterIAB_Support_r16_tags_101)
		/sizeof(asn_DEF_NR_separateSMTC_InterIAB_Support_r16_tags_101[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_separateSMTC_InterIAB_Support_r16_constr_101,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_separateSMTC_InterIAB_Support_r16_specs_101	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_separateRACH_IAB_Support_r16_value2enum_103[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_separateRACH_IAB_Support_r16_enum2value_103[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_separateRACH_IAB_Support_r16_specs_103 = {
	asn_MAP_NR_separateRACH_IAB_Support_r16_value2enum_103,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_separateRACH_IAB_Support_r16_enum2value_103,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_separateRACH_IAB_Support_r16_tags_103[] = {
	(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_separateRACH_IAB_Support_r16_103 = {
	"separateRACH-IAB-Support-r16",
	"separateRACH-IAB-Support-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_separateRACH_IAB_Support_r16_tags_103,
	sizeof(asn_DEF_NR_separateRACH_IAB_Support_r16_tags_103)
		/sizeof(asn_DEF_NR_separateRACH_IAB_Support_r16_tags_103[0]) - 1, /* 1 */
	asn_DEF_NR_separateRACH_IAB_Support_r16_tags_103,	/* Same as above */
	sizeof(asn_DEF_NR_separateRACH_IAB_Support_r16_tags_103)
		/sizeof(asn_DEF_NR_separateRACH_IAB_Support_r16_tags_103[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_separateRACH_IAB_Support_r16_constr_103,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_separateRACH_IAB_Support_r16_specs_103	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_ul_flexibleDL_SlotFormatSemiStatic_IAB_r16_value2enum_105[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_ul_flexibleDL_SlotFormatSemiStatic_IAB_r16_enum2value_105[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ul_flexibleDL_SlotFormatSemiStatic_IAB_r16_specs_105 = {
	asn_MAP_NR_ul_flexibleDL_SlotFormatSemiStatic_IAB_r16_value2enum_105,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ul_flexibleDL_SlotFormatSemiStatic_IAB_r16_enum2value_105,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ul_flexibleDL_SlotFormatSemiStatic_IAB_r16_tags_105[] = {
	(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ul_flexibleDL_SlotFormatSemiStatic_IAB_r16_105 = {
	"ul-flexibleDL-SlotFormatSemiStatic-IAB-r16",
	"ul-flexibleDL-SlotFormatSemiStatic-IAB-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ul_flexibleDL_SlotFormatSemiStatic_IAB_r16_tags_105,
	sizeof(asn_DEF_NR_ul_flexibleDL_SlotFormatSemiStatic_IAB_r16_tags_105)
		/sizeof(asn_DEF_NR_ul_flexibleDL_SlotFormatSemiStatic_IAB_r16_tags_105[0]) - 1, /* 1 */
	asn_DEF_NR_ul_flexibleDL_SlotFormatSemiStatic_IAB_r16_tags_105,	/* Same as above */
	sizeof(asn_DEF_NR_ul_flexibleDL_SlotFormatSemiStatic_IAB_r16_tags_105)
		/sizeof(asn_DEF_NR_ul_flexibleDL_SlotFormatSemiStatic_IAB_r16_tags_105[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ul_flexibleDL_SlotFormatSemiStatic_IAB_r16_constr_105,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ul_flexibleDL_SlotFormatSemiStatic_IAB_r16_specs_105	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_ul_flexibleDL_SlotFormatDynamics_IAB_r16_value2enum_107[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_ul_flexibleDL_SlotFormatDynamics_IAB_r16_enum2value_107[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ul_flexibleDL_SlotFormatDynamics_IAB_r16_specs_107 = {
	asn_MAP_NR_ul_flexibleDL_SlotFormatDynamics_IAB_r16_value2enum_107,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ul_flexibleDL_SlotFormatDynamics_IAB_r16_enum2value_107,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ul_flexibleDL_SlotFormatDynamics_IAB_r16_tags_107[] = {
	(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ul_flexibleDL_SlotFormatDynamics_IAB_r16_107 = {
	"ul-flexibleDL-SlotFormatDynamics-IAB-r16",
	"ul-flexibleDL-SlotFormatDynamics-IAB-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ul_flexibleDL_SlotFormatDynamics_IAB_r16_tags_107,
	sizeof(asn_DEF_NR_ul_flexibleDL_SlotFormatDynamics_IAB_r16_tags_107)
		/sizeof(asn_DEF_NR_ul_flexibleDL_SlotFormatDynamics_IAB_r16_tags_107[0]) - 1, /* 1 */
	asn_DEF_NR_ul_flexibleDL_SlotFormatDynamics_IAB_r16_tags_107,	/* Same as above */
	sizeof(asn_DEF_NR_ul_flexibleDL_SlotFormatDynamics_IAB_r16_tags_107)
		/sizeof(asn_DEF_NR_ul_flexibleDL_SlotFormatDynamics_IAB_r16_tags_107[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ul_flexibleDL_SlotFormatDynamics_IAB_r16_constr_107,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ul_flexibleDL_SlotFormatDynamics_IAB_r16_specs_107	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dft_S_OFDM_WaveformUL_IAB_r16_value2enum_109[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_dft_S_OFDM_WaveformUL_IAB_r16_enum2value_109[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dft_S_OFDM_WaveformUL_IAB_r16_specs_109 = {
	asn_MAP_NR_dft_S_OFDM_WaveformUL_IAB_r16_value2enum_109,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dft_S_OFDM_WaveformUL_IAB_r16_enum2value_109,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dft_S_OFDM_WaveformUL_IAB_r16_tags_109[] = {
	(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dft_S_OFDM_WaveformUL_IAB_r16_109 = {
	"dft-S-OFDM-WaveformUL-IAB-r16",
	"dft-S-OFDM-WaveformUL-IAB-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dft_S_OFDM_WaveformUL_IAB_r16_tags_109,
	sizeof(asn_DEF_NR_dft_S_OFDM_WaveformUL_IAB_r16_tags_109)
		/sizeof(asn_DEF_NR_dft_S_OFDM_WaveformUL_IAB_r16_tags_109[0]) - 1, /* 1 */
	asn_DEF_NR_dft_S_OFDM_WaveformUL_IAB_r16_tags_109,	/* Same as above */
	sizeof(asn_DEF_NR_dft_S_OFDM_WaveformUL_IAB_r16_tags_109)
		/sizeof(asn_DEF_NR_dft_S_OFDM_WaveformUL_IAB_r16_tags_109[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dft_S_OFDM_WaveformUL_IAB_r16_constr_109,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dft_S_OFDM_WaveformUL_IAB_r16_specs_109	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dci_25_AI_RNTI_Support_IAB_r16_value2enum_111[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_dci_25_AI_RNTI_Support_IAB_r16_enum2value_111[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dci_25_AI_RNTI_Support_IAB_r16_specs_111 = {
	asn_MAP_NR_dci_25_AI_RNTI_Support_IAB_r16_value2enum_111,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dci_25_AI_RNTI_Support_IAB_r16_enum2value_111,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dci_25_AI_RNTI_Support_IAB_r16_tags_111[] = {
	(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dci_25_AI_RNTI_Support_IAB_r16_111 = {
	"dci-25-AI-RNTI-Support-IAB-r16",
	"dci-25-AI-RNTI-Support-IAB-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dci_25_AI_RNTI_Support_IAB_r16_tags_111,
	sizeof(asn_DEF_NR_dci_25_AI_RNTI_Support_IAB_r16_tags_111)
		/sizeof(asn_DEF_NR_dci_25_AI_RNTI_Support_IAB_r16_tags_111[0]) - 1, /* 1 */
	asn_DEF_NR_dci_25_AI_RNTI_Support_IAB_r16_tags_111,	/* Same as above */
	sizeof(asn_DEF_NR_dci_25_AI_RNTI_Support_IAB_r16_tags_111)
		/sizeof(asn_DEF_NR_dci_25_AI_RNTI_Support_IAB_r16_tags_111[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dci_25_AI_RNTI_Support_IAB_r16_constr_111,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dci_25_AI_RNTI_Support_IAB_r16_specs_111	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_t_DeltaReceptionSupport_IAB_r16_value2enum_113[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_t_DeltaReceptionSupport_IAB_r16_enum2value_113[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_t_DeltaReceptionSupport_IAB_r16_specs_113 = {
	asn_MAP_NR_t_DeltaReceptionSupport_IAB_r16_value2enum_113,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_t_DeltaReceptionSupport_IAB_r16_enum2value_113,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_t_DeltaReceptionSupport_IAB_r16_tags_113[] = {
	(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_t_DeltaReceptionSupport_IAB_r16_113 = {
	"t-DeltaReceptionSupport-IAB-r16",
	"t-DeltaReceptionSupport-IAB-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_t_DeltaReceptionSupport_IAB_r16_tags_113,
	sizeof(asn_DEF_NR_t_DeltaReceptionSupport_IAB_r16_tags_113)
		/sizeof(asn_DEF_NR_t_DeltaReceptionSupport_IAB_r16_tags_113[0]) - 1, /* 1 */
	asn_DEF_NR_t_DeltaReceptionSupport_IAB_r16_tags_113,	/* Same as above */
	sizeof(asn_DEF_NR_t_DeltaReceptionSupport_IAB_r16_tags_113)
		/sizeof(asn_DEF_NR_t_DeltaReceptionSupport_IAB_r16_tags_113[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_t_DeltaReceptionSupport_IAB_r16_constr_113,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_t_DeltaReceptionSupport_IAB_r16_specs_113	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_guardSymbolReportReception_IAB_r16_value2enum_115[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_guardSymbolReportReception_IAB_r16_enum2value_115[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_guardSymbolReportReception_IAB_r16_specs_115 = {
	asn_MAP_NR_guardSymbolReportReception_IAB_r16_value2enum_115,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_guardSymbolReportReception_IAB_r16_enum2value_115,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_guardSymbolReportReception_IAB_r16_tags_115[] = {
	(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_guardSymbolReportReception_IAB_r16_115 = {
	"guardSymbolReportReception-IAB-r16",
	"guardSymbolReportReception-IAB-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_guardSymbolReportReception_IAB_r16_tags_115,
	sizeof(asn_DEF_NR_guardSymbolReportReception_IAB_r16_tags_115)
		/sizeof(asn_DEF_NR_guardSymbolReportReception_IAB_r16_tags_115[0]) - 1, /* 1 */
	asn_DEF_NR_guardSymbolReportReception_IAB_r16_tags_115,	/* Same as above */
	sizeof(asn_DEF_NR_guardSymbolReportReception_IAB_r16_tags_115)
		/sizeof(asn_DEF_NR_guardSymbolReportReception_IAB_r16_tags_115[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_guardSymbolReportReception_IAB_r16_constr_115,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_guardSymbolReportReception_IAB_r16_specs_115	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_harqACK_CB_SpatialBundlingPUCCH_Group_r16_value2enum_117[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_harqACK_CB_SpatialBundlingPUCCH_Group_r16_enum2value_117[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_harqACK_CB_SpatialBundlingPUCCH_Group_r16_specs_117 = {
	asn_MAP_NR_harqACK_CB_SpatialBundlingPUCCH_Group_r16_value2enum_117,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_harqACK_CB_SpatialBundlingPUCCH_Group_r16_enum2value_117,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_harqACK_CB_SpatialBundlingPUCCH_Group_r16_tags_117[] = {
	(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_harqACK_CB_SpatialBundlingPUCCH_Group_r16_117 = {
	"harqACK-CB-SpatialBundlingPUCCH-Group-r16",
	"harqACK-CB-SpatialBundlingPUCCH-Group-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_harqACK_CB_SpatialBundlingPUCCH_Group_r16_tags_117,
	sizeof(asn_DEF_NR_harqACK_CB_SpatialBundlingPUCCH_Group_r16_tags_117)
		/sizeof(asn_DEF_NR_harqACK_CB_SpatialBundlingPUCCH_Group_r16_tags_117[0]) - 1, /* 1 */
	asn_DEF_NR_harqACK_CB_SpatialBundlingPUCCH_Group_r16_tags_117,	/* Same as above */
	sizeof(asn_DEF_NR_harqACK_CB_SpatialBundlingPUCCH_Group_r16_tags_117)
		/sizeof(asn_DEF_NR_harqACK_CB_SpatialBundlingPUCCH_Group_r16_tags_117[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_harqACK_CB_SpatialBundlingPUCCH_Group_r16_constr_117,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_harqACK_CB_SpatialBundlingPUCCH_Group_r16_specs_117	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_non_SharedSpectrumChAccess_r16_value2enum_120[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_non_SharedSpectrumChAccess_r16_enum2value_120[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_non_SharedSpectrumChAccess_r16_specs_120 = {
	asn_MAP_NR_non_SharedSpectrumChAccess_r16_value2enum_120,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_non_SharedSpectrumChAccess_r16_enum2value_120,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_non_SharedSpectrumChAccess_r16_tags_120[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_non_SharedSpectrumChAccess_r16_120 = {
	"non-SharedSpectrumChAccess-r16",
	"non-SharedSpectrumChAccess-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_non_SharedSpectrumChAccess_r16_tags_120,
	sizeof(asn_DEF_NR_non_SharedSpectrumChAccess_r16_tags_120)
		/sizeof(asn_DEF_NR_non_SharedSpectrumChAccess_r16_tags_120[0]) - 1, /* 1 */
	asn_DEF_NR_non_SharedSpectrumChAccess_r16_tags_120,	/* Same as above */
	sizeof(asn_DEF_NR_non_SharedSpectrumChAccess_r16_tags_120)
		/sizeof(asn_DEF_NR_non_SharedSpectrumChAccess_r16_tags_120[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_non_SharedSpectrumChAccess_r16_constr_120,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_non_SharedSpectrumChAccess_r16_specs_120	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_sharedSpectrumChAccess_r16_value2enum_122[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_sharedSpectrumChAccess_r16_enum2value_122[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_sharedSpectrumChAccess_r16_specs_122 = {
	asn_MAP_NR_sharedSpectrumChAccess_r16_value2enum_122,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_sharedSpectrumChAccess_r16_enum2value_122,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_sharedSpectrumChAccess_r16_tags_122[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_sharedSpectrumChAccess_r16_122 = {
	"sharedSpectrumChAccess-r16",
	"sharedSpectrumChAccess-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_sharedSpectrumChAccess_r16_tags_122,
	sizeof(asn_DEF_NR_sharedSpectrumChAccess_r16_tags_122)
		/sizeof(asn_DEF_NR_sharedSpectrumChAccess_r16_tags_122[0]) - 1, /* 1 */
	asn_DEF_NR_sharedSpectrumChAccess_r16_tags_122,	/* Same as above */
	sizeof(asn_DEF_NR_sharedSpectrumChAccess_r16_tags_122)
		/sizeof(asn_DEF_NR_sharedSpectrumChAccess_r16_tags_122[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_sharedSpectrumChAccess_r16_constr_122,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_sharedSpectrumChAccess_r16_specs_122	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_crossSlotScheduling_r16_119[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_Phy_ParametersCommon__ext4__crossSlotScheduling_r16, non_SharedSpectrumChAccess_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_non_SharedSpectrumChAccess_r16_120,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"non-SharedSpectrumChAccess-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersCommon__ext4__crossSlotScheduling_r16, sharedSpectrumChAccess_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_sharedSpectrumChAccess_r16_122,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"sharedSpectrumChAccess-r16"
		},
};
static const int asn_MAP_NR_crossSlotScheduling_r16_oms_119[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_crossSlotScheduling_r16_tags_119[] = {
	(ASN_TAG_CLASS_CONTEXT | (17 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_crossSlotScheduling_r16_tag2el_119[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* non-SharedSpectrumChAccess-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* sharedSpectrumChAccess-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_crossSlotScheduling_r16_specs_119 = {
	sizeof(struct NR_Phy_ParametersCommon__ext4__crossSlotScheduling_r16),
	offsetof(struct NR_Phy_ParametersCommon__ext4__crossSlotScheduling_r16, _asn_ctx),
	asn_MAP_NR_crossSlotScheduling_r16_tag2el_119,
	2,	/* Count of tags in the map */
	asn_MAP_NR_crossSlotScheduling_r16_oms_119,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_crossSlotScheduling_r16_119 = {
	"crossSlotScheduling-r16",
	"crossSlotScheduling-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_crossSlotScheduling_r16_tags_119,
	sizeof(asn_DEF_NR_crossSlotScheduling_r16_tags_119)
		/sizeof(asn_DEF_NR_crossSlotScheduling_r16_tags_119[0]) - 1, /* 1 */
	asn_DEF_NR_crossSlotScheduling_r16_tags_119,	/* Same as above */
	sizeof(asn_DEF_NR_crossSlotScheduling_r16_tags_119)
		/sizeof(asn_DEF_NR_crossSlotScheduling_r16_tags_119[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_crossSlotScheduling_r16_119,
	2,	/* Elements count */
	&asn_SPC_NR_crossSlotScheduling_r16_specs_119	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_maxNumberSRS_PosPathLossEstimateAllServingCells_r16_value2enum_124[] = {
	{ 0,	2,	"n1" },
	{ 1,	2,	"n4" },
	{ 2,	2,	"n8" },
	{ 3,	3,	"n16" }
};
static const unsigned int asn_MAP_NR_maxNumberSRS_PosPathLossEstimateAllServingCells_r16_enum2value_124[] = {
	0,	/* n1(0) */
	3,	/* n16(3) */
	1,	/* n4(1) */
	2	/* n8(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_maxNumberSRS_PosPathLossEstimateAllServingCells_r16_specs_124 = {
	asn_MAP_NR_maxNumberSRS_PosPathLossEstimateAllServingCells_r16_value2enum_124,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_maxNumberSRS_PosPathLossEstimateAllServingCells_r16_enum2value_124,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_maxNumberSRS_PosPathLossEstimateAllServingCells_r16_tags_124[] = {
	(ASN_TAG_CLASS_CONTEXT | (18 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_maxNumberSRS_PosPathLossEstimateAllServingCells_r16_124 = {
	"maxNumberSRS-PosPathLossEstimateAllServingCells-r16",
	"maxNumberSRS-PosPathLossEstimateAllServingCells-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_maxNumberSRS_PosPathLossEstimateAllServingCells_r16_tags_124,
	sizeof(asn_DEF_NR_maxNumberSRS_PosPathLossEstimateAllServingCells_r16_tags_124)
		/sizeof(asn_DEF_NR_maxNumberSRS_PosPathLossEstimateAllServingCells_r16_tags_124[0]) - 1, /* 1 */
	asn_DEF_NR_maxNumberSRS_PosPathLossEstimateAllServingCells_r16_tags_124,	/* Same as above */
	sizeof(asn_DEF_NR_maxNumberSRS_PosPathLossEstimateAllServingCells_r16_tags_124)
		/sizeof(asn_DEF_NR_maxNumberSRS_PosPathLossEstimateAllServingCells_r16_tags_124[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_maxNumberSRS_PosPathLossEstimateAllServingCells_r16_constr_124,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_maxNumberSRS_PosPathLossEstimateAllServingCells_r16_specs_124	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_extendedCG_Periodicities_r16_value2enum_129[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_extendedCG_Periodicities_r16_enum2value_129[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_extendedCG_Periodicities_r16_specs_129 = {
	asn_MAP_NR_extendedCG_Periodicities_r16_value2enum_129,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_extendedCG_Periodicities_r16_enum2value_129,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_extendedCG_Periodicities_r16_tags_129[] = {
	(ASN_TAG_CLASS_CONTEXT | (19 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_extendedCG_Periodicities_r16_129 = {
	"extendedCG-Periodicities-r16",
	"extendedCG-Periodicities-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_extendedCG_Periodicities_r16_tags_129,
	sizeof(asn_DEF_NR_extendedCG_Periodicities_r16_tags_129)
		/sizeof(asn_DEF_NR_extendedCG_Periodicities_r16_tags_129[0]) - 1, /* 1 */
	asn_DEF_NR_extendedCG_Periodicities_r16_tags_129,	/* Same as above */
	sizeof(asn_DEF_NR_extendedCG_Periodicities_r16_tags_129)
		/sizeof(asn_DEF_NR_extendedCG_Periodicities_r16_tags_129[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_extendedCG_Periodicities_r16_constr_129,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_extendedCG_Periodicities_r16_specs_129	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_extendedSPS_Periodicities_r16_value2enum_131[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_extendedSPS_Periodicities_r16_enum2value_131[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_extendedSPS_Periodicities_r16_specs_131 = {
	asn_MAP_NR_extendedSPS_Periodicities_r16_value2enum_131,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_extendedSPS_Periodicities_r16_enum2value_131,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_extendedSPS_Periodicities_r16_tags_131[] = {
	(ASN_TAG_CLASS_CONTEXT | (20 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_extendedSPS_Periodicities_r16_131 = {
	"extendedSPS-Periodicities-r16",
	"extendedSPS-Periodicities-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_extendedSPS_Periodicities_r16_tags_131,
	sizeof(asn_DEF_NR_extendedSPS_Periodicities_r16_tags_131)
		/sizeof(asn_DEF_NR_extendedSPS_Periodicities_r16_tags_131[0]) - 1, /* 1 */
	asn_DEF_NR_extendedSPS_Periodicities_r16_tags_131,	/* Same as above */
	sizeof(asn_DEF_NR_extendedSPS_Periodicities_r16_tags_131)
		/sizeof(asn_DEF_NR_extendedSPS_Periodicities_r16_tags_131[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_extendedSPS_Periodicities_r16_constr_131,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_extendedSPS_Periodicities_r16_specs_131	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_sharedSpectrumChAccess_r16_value2enum_135[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_sharedSpectrumChAccess_r16_enum2value_135[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_sharedSpectrumChAccess_r16_specs_135 = {
	asn_MAP_NR_sharedSpectrumChAccess_r16_value2enum_135,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_sharedSpectrumChAccess_r16_enum2value_135,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_sharedSpectrumChAccess_r16_tags_135[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_sharedSpectrumChAccess_r16_135 = {
	"sharedSpectrumChAccess-r16",
	"sharedSpectrumChAccess-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_sharedSpectrumChAccess_r16_tags_135,
	sizeof(asn_DEF_NR_sharedSpectrumChAccess_r16_tags_135)
		/sizeof(asn_DEF_NR_sharedSpectrumChAccess_r16_tags_135[0]) - 1, /* 1 */
	asn_DEF_NR_sharedSpectrumChAccess_r16_tags_135,	/* Same as above */
	sizeof(asn_DEF_NR_sharedSpectrumChAccess_r16_tags_135)
		/sizeof(asn_DEF_NR_sharedSpectrumChAccess_r16_tags_135[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_sharedSpectrumChAccess_r16_constr_135,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_sharedSpectrumChAccess_r16_specs_135	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_non_SharedSpectrumChAccess_r16_value2enum_137[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_non_SharedSpectrumChAccess_r16_enum2value_137[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_non_SharedSpectrumChAccess_r16_specs_137 = {
	asn_MAP_NR_non_SharedSpectrumChAccess_r16_value2enum_137,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_non_SharedSpectrumChAccess_r16_enum2value_137,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_non_SharedSpectrumChAccess_r16_tags_137[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_non_SharedSpectrumChAccess_r16_137 = {
	"non-SharedSpectrumChAccess-r16",
	"non-SharedSpectrumChAccess-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_non_SharedSpectrumChAccess_r16_tags_137,
	sizeof(asn_DEF_NR_non_SharedSpectrumChAccess_r16_tags_137)
		/sizeof(asn_DEF_NR_non_SharedSpectrumChAccess_r16_tags_137[0]) - 1, /* 1 */
	asn_DEF_NR_non_SharedSpectrumChAccess_r16_tags_137,	/* Same as above */
	sizeof(asn_DEF_NR_non_SharedSpectrumChAccess_r16_tags_137)
		/sizeof(asn_DEF_NR_non_SharedSpectrumChAccess_r16_tags_137[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_non_SharedSpectrumChAccess_r16_constr_137,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_non_SharedSpectrumChAccess_r16_specs_137	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_pusch_RepetitionTypeA_r16_134[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_Phy_ParametersCommon__ext4__pusch_RepetitionTypeA_r16, sharedSpectrumChAccess_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_sharedSpectrumChAccess_r16_135,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"sharedSpectrumChAccess-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersCommon__ext4__pusch_RepetitionTypeA_r16, non_SharedSpectrumChAccess_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_non_SharedSpectrumChAccess_r16_137,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"non-SharedSpectrumChAccess-r16"
		},
};
static const int asn_MAP_NR_pusch_RepetitionTypeA_r16_oms_134[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_pusch_RepetitionTypeA_r16_tags_134[] = {
	(ASN_TAG_CLASS_CONTEXT | (22 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_pusch_RepetitionTypeA_r16_tag2el_134[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* sharedSpectrumChAccess-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* non-SharedSpectrumChAccess-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_pusch_RepetitionTypeA_r16_specs_134 = {
	sizeof(struct NR_Phy_ParametersCommon__ext4__pusch_RepetitionTypeA_r16),
	offsetof(struct NR_Phy_ParametersCommon__ext4__pusch_RepetitionTypeA_r16, _asn_ctx),
	asn_MAP_NR_pusch_RepetitionTypeA_r16_tag2el_134,
	2,	/* Count of tags in the map */
	asn_MAP_NR_pusch_RepetitionTypeA_r16_oms_134,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pusch_RepetitionTypeA_r16_134 = {
	"pusch-RepetitionTypeA-r16",
	"pusch-RepetitionTypeA-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_pusch_RepetitionTypeA_r16_tags_134,
	sizeof(asn_DEF_NR_pusch_RepetitionTypeA_r16_tags_134)
		/sizeof(asn_DEF_NR_pusch_RepetitionTypeA_r16_tags_134[0]) - 1, /* 1 */
	asn_DEF_NR_pusch_RepetitionTypeA_r16_tags_134,	/* Same as above */
	sizeof(asn_DEF_NR_pusch_RepetitionTypeA_r16_tags_134)
		/sizeof(asn_DEF_NR_pusch_RepetitionTypeA_r16_tags_134[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_pusch_RepetitionTypeA_r16_134,
	2,	/* Elements count */
	&asn_SPC_NR_pusch_RepetitionTypeA_r16_specs_134	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dci_DL_PriorityIndicator_r16_value2enum_139[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_dci_DL_PriorityIndicator_r16_enum2value_139[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dci_DL_PriorityIndicator_r16_specs_139 = {
	asn_MAP_NR_dci_DL_PriorityIndicator_r16_value2enum_139,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dci_DL_PriorityIndicator_r16_enum2value_139,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dci_DL_PriorityIndicator_r16_tags_139[] = {
	(ASN_TAG_CLASS_CONTEXT | (23 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dci_DL_PriorityIndicator_r16_139 = {
	"dci-DL-PriorityIndicator-r16",
	"dci-DL-PriorityIndicator-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dci_DL_PriorityIndicator_r16_tags_139,
	sizeof(asn_DEF_NR_dci_DL_PriorityIndicator_r16_tags_139)
		/sizeof(asn_DEF_NR_dci_DL_PriorityIndicator_r16_tags_139[0]) - 1, /* 1 */
	asn_DEF_NR_dci_DL_PriorityIndicator_r16_tags_139,	/* Same as above */
	sizeof(asn_DEF_NR_dci_DL_PriorityIndicator_r16_tags_139)
		/sizeof(asn_DEF_NR_dci_DL_PriorityIndicator_r16_tags_139[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dci_DL_PriorityIndicator_r16_constr_139,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dci_DL_PriorityIndicator_r16_specs_139	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dci_UL_PriorityIndicator_r16_value2enum_141[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_dci_UL_PriorityIndicator_r16_enum2value_141[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dci_UL_PriorityIndicator_r16_specs_141 = {
	asn_MAP_NR_dci_UL_PriorityIndicator_r16_value2enum_141,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dci_UL_PriorityIndicator_r16_enum2value_141,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dci_UL_PriorityIndicator_r16_tags_141[] = {
	(ASN_TAG_CLASS_CONTEXT | (24 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dci_UL_PriorityIndicator_r16_141 = {
	"dci-UL-PriorityIndicator-r16",
	"dci-UL-PriorityIndicator-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dci_UL_PriorityIndicator_r16_tags_141,
	sizeof(asn_DEF_NR_dci_UL_PriorityIndicator_r16_tags_141)
		/sizeof(asn_DEF_NR_dci_UL_PriorityIndicator_r16_tags_141[0]) - 1, /* 1 */
	asn_DEF_NR_dci_UL_PriorityIndicator_r16_tags_141,	/* Same as above */
	sizeof(asn_DEF_NR_dci_UL_PriorityIndicator_r16_tags_141)
		/sizeof(asn_DEF_NR_dci_UL_PriorityIndicator_r16_tags_141[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dci_UL_PriorityIndicator_r16_constr_141,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dci_UL_PriorityIndicator_r16_specs_141	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_maxNumberPathlossRS_Update_r16_value2enum_143[] = {
	{ 0,	2,	"n4" },
	{ 1,	2,	"n8" },
	{ 2,	3,	"n16" },
	{ 3,	3,	"n32" },
	{ 4,	3,	"n64" }
};
static const unsigned int asn_MAP_NR_maxNumberPathlossRS_Update_r16_enum2value_143[] = {
	2,	/* n16(2) */
	3,	/* n32(3) */
	0,	/* n4(0) */
	4,	/* n64(4) */
	1	/* n8(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_maxNumberPathlossRS_Update_r16_specs_143 = {
	asn_MAP_NR_maxNumberPathlossRS_Update_r16_value2enum_143,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_maxNumberPathlossRS_Update_r16_enum2value_143,	/* N => "tag"; sorted by N */
	5,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_maxNumberPathlossRS_Update_r16_tags_143[] = {
	(ASN_TAG_CLASS_CONTEXT | (25 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_maxNumberPathlossRS_Update_r16_143 = {
	"maxNumberPathlossRS-Update-r16",
	"maxNumberPathlossRS-Update-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_maxNumberPathlossRS_Update_r16_tags_143,
	sizeof(asn_DEF_NR_maxNumberPathlossRS_Update_r16_tags_143)
		/sizeof(asn_DEF_NR_maxNumberPathlossRS_Update_r16_tags_143[0]) - 1, /* 1 */
	asn_DEF_NR_maxNumberPathlossRS_Update_r16_tags_143,	/* Same as above */
	sizeof(asn_DEF_NR_maxNumberPathlossRS_Update_r16_tags_143)
		/sizeof(asn_DEF_NR_maxNumberPathlossRS_Update_r16_tags_143[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_maxNumberPathlossRS_Update_r16_constr_143,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_maxNumberPathlossRS_Update_r16_specs_143	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_type2_HARQ_ACK_Codebook_r16_value2enum_149[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_type2_HARQ_ACK_Codebook_r16_enum2value_149[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_type2_HARQ_ACK_Codebook_r16_specs_149 = {
	asn_MAP_NR_type2_HARQ_ACK_Codebook_r16_value2enum_149,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_type2_HARQ_ACK_Codebook_r16_enum2value_149,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_type2_HARQ_ACK_Codebook_r16_tags_149[] = {
	(ASN_TAG_CLASS_CONTEXT | (26 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_type2_HARQ_ACK_Codebook_r16_149 = {
	"type2-HARQ-ACK-Codebook-r16",
	"type2-HARQ-ACK-Codebook-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_type2_HARQ_ACK_Codebook_r16_tags_149,
	sizeof(asn_DEF_NR_type2_HARQ_ACK_Codebook_r16_tags_149)
		/sizeof(asn_DEF_NR_type2_HARQ_ACK_Codebook_r16_tags_149[0]) - 1, /* 1 */
	asn_DEF_NR_type2_HARQ_ACK_Codebook_r16_tags_149,	/* Same as above */
	sizeof(asn_DEF_NR_type2_HARQ_ACK_Codebook_r16_tags_149)
		/sizeof(asn_DEF_NR_type2_HARQ_ACK_Codebook_r16_tags_149[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_type2_HARQ_ACK_Codebook_r16_constr_149,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_type2_HARQ_ACK_Codebook_r16_specs_149	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_maxNumberResWithinSlotAcrossCC_AcrossFR_r16_value2enum_152[] = {
	{ 0,	2,	"n2" },
	{ 1,	2,	"n4" },
	{ 2,	2,	"n8" },
	{ 3,	3,	"n12" },
	{ 4,	3,	"n16" },
	{ 5,	3,	"n32" },
	{ 6,	3,	"n64" },
	{ 7,	4,	"n128" }
};
static const unsigned int asn_MAP_NR_maxNumberResWithinSlotAcrossCC_AcrossFR_r16_enum2value_152[] = {
	3,	/* n12(3) */
	7,	/* n128(7) */
	4,	/* n16(4) */
	0,	/* n2(0) */
	5,	/* n32(5) */
	1,	/* n4(1) */
	6,	/* n64(6) */
	2	/* n8(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_maxNumberResWithinSlotAcrossCC_AcrossFR_r16_specs_152 = {
	asn_MAP_NR_maxNumberResWithinSlotAcrossCC_AcrossFR_r16_value2enum_152,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_maxNumberResWithinSlotAcrossCC_AcrossFR_r16_enum2value_152,	/* N => "tag"; sorted by N */
	8,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_maxNumberResWithinSlotAcrossCC_AcrossFR_r16_tags_152[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_maxNumberResWithinSlotAcrossCC_AcrossFR_r16_152 = {
	"maxNumberResWithinSlotAcrossCC-AcrossFR-r16",
	"maxNumberResWithinSlotAcrossCC-AcrossFR-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_maxNumberResWithinSlotAcrossCC_AcrossFR_r16_tags_152,
	sizeof(asn_DEF_NR_maxNumberResWithinSlotAcrossCC_AcrossFR_r16_tags_152)
		/sizeof(asn_DEF_NR_maxNumberResWithinSlotAcrossCC_AcrossFR_r16_tags_152[0]) - 1, /* 1 */
	asn_DEF_NR_maxNumberResWithinSlotAcrossCC_AcrossFR_r16_tags_152,	/* Same as above */
	sizeof(asn_DEF_NR_maxNumberResWithinSlotAcrossCC_AcrossFR_r16_tags_152)
		/sizeof(asn_DEF_NR_maxNumberResWithinSlotAcrossCC_AcrossFR_r16_tags_152[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_maxNumberResWithinSlotAcrossCC_AcrossFR_r16_constr_152,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_maxNumberResWithinSlotAcrossCC_AcrossFR_r16_specs_152	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_maxNumberResAcrossCC_AcrossFR_r16_value2enum_161[] = {
	{ 0,	2,	"n2" },
	{ 1,	2,	"n4" },
	{ 2,	2,	"n8" },
	{ 3,	3,	"n12" },
	{ 4,	3,	"n16" },
	{ 5,	3,	"n32" },
	{ 6,	3,	"n40" },
	{ 7,	3,	"n48" },
	{ 8,	3,	"n64" },
	{ 9,	3,	"n72" },
	{ 10,	3,	"n80" },
	{ 11,	3,	"n96" },
	{ 12,	4,	"n128" },
	{ 13,	4,	"n256" }
};
static const unsigned int asn_MAP_NR_maxNumberResAcrossCC_AcrossFR_r16_enum2value_161[] = {
	3,	/* n12(3) */
	12,	/* n128(12) */
	4,	/* n16(4) */
	0,	/* n2(0) */
	13,	/* n256(13) */
	5,	/* n32(5) */
	1,	/* n4(1) */
	6,	/* n40(6) */
	7,	/* n48(7) */
	8,	/* n64(8) */
	9,	/* n72(9) */
	2,	/* n8(2) */
	10,	/* n80(10) */
	11	/* n96(11) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_maxNumberResAcrossCC_AcrossFR_r16_specs_161 = {
	asn_MAP_NR_maxNumberResAcrossCC_AcrossFR_r16_value2enum_161,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_maxNumberResAcrossCC_AcrossFR_r16_enum2value_161,	/* N => "tag"; sorted by N */
	14,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_maxNumberResAcrossCC_AcrossFR_r16_tags_161[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_maxNumberResAcrossCC_AcrossFR_r16_161 = {
	"maxNumberResAcrossCC-AcrossFR-r16",
	"maxNumberResAcrossCC-AcrossFR-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_maxNumberResAcrossCC_AcrossFR_r16_tags_161,
	sizeof(asn_DEF_NR_maxNumberResAcrossCC_AcrossFR_r16_tags_161)
		/sizeof(asn_DEF_NR_maxNumberResAcrossCC_AcrossFR_r16_tags_161[0]) - 1, /* 1 */
	asn_DEF_NR_maxNumberResAcrossCC_AcrossFR_r16_tags_161,	/* Same as above */
	sizeof(asn_DEF_NR_maxNumberResAcrossCC_AcrossFR_r16_tags_161)
		/sizeof(asn_DEF_NR_maxNumberResAcrossCC_AcrossFR_r16_tags_161[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_maxNumberResAcrossCC_AcrossFR_r16_constr_161,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_maxNumberResAcrossCC_AcrossFR_r16_specs_161	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_maxTotalResourcesForAcrossFreqRanges_r16_151[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16, maxNumberResWithinSlotAcrossCC_AcrossFR_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_maxNumberResWithinSlotAcrossCC_AcrossFR_r16_152,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"maxNumberResWithinSlotAcrossCC-AcrossFR-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16, maxNumberResAcrossCC_AcrossFR_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_maxNumberResAcrossCC_AcrossFR_r16_161,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"maxNumberResAcrossCC-AcrossFR-r16"
		},
};
static const int asn_MAP_NR_maxTotalResourcesForAcrossFreqRanges_r16_oms_151[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_maxTotalResourcesForAcrossFreqRanges_r16_tags_151[] = {
	(ASN_TAG_CLASS_CONTEXT | (27 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_maxTotalResourcesForAcrossFreqRanges_r16_tag2el_151[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* maxNumberResWithinSlotAcrossCC-AcrossFR-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* maxNumberResAcrossCC-AcrossFR-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_maxTotalResourcesForAcrossFreqRanges_r16_specs_151 = {
	sizeof(struct NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16),
	offsetof(struct NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16, _asn_ctx),
	asn_MAP_NR_maxTotalResourcesForAcrossFreqRanges_r16_tag2el_151,
	2,	/* Count of tags in the map */
	asn_MAP_NR_maxTotalResourcesForAcrossFreqRanges_r16_oms_151,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_maxTotalResourcesForAcrossFreqRanges_r16_151 = {
	"maxTotalResourcesForAcrossFreqRanges-r16",
	"maxTotalResourcesForAcrossFreqRanges-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_maxTotalResourcesForAcrossFreqRanges_r16_tags_151,
	sizeof(asn_DEF_NR_maxTotalResourcesForAcrossFreqRanges_r16_tags_151)
		/sizeof(asn_DEF_NR_maxTotalResourcesForAcrossFreqRanges_r16_tags_151[0]) - 1, /* 1 */
	asn_DEF_NR_maxTotalResourcesForAcrossFreqRanges_r16_tags_151,	/* Same as above */
	sizeof(asn_DEF_NR_maxTotalResourcesForAcrossFreqRanges_r16_tags_151)
		/sizeof(asn_DEF_NR_maxTotalResourcesForAcrossFreqRanges_r16_tags_151[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_maxTotalResourcesForAcrossFreqRanges_r16_151,
	2,	/* Elements count */
	&asn_SPC_NR_maxTotalResourcesForAcrossFreqRanges_r16_specs_151	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_maxNumberLongPUCCHs_r16_value2enum_177[] = {
	{ 0,	11,	"longAndLong" },
	{ 1,	12,	"longAndShort" },
	{ 2,	13,	"shortAndShort" }
};
static const unsigned int asn_MAP_NR_maxNumberLongPUCCHs_r16_enum2value_177[] = {
	0,	/* longAndLong(0) */
	1,	/* longAndShort(1) */
	2	/* shortAndShort(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_maxNumberLongPUCCHs_r16_specs_177 = {
	asn_MAP_NR_maxNumberLongPUCCHs_r16_value2enum_177,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_maxNumberLongPUCCHs_r16_enum2value_177,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_maxNumberLongPUCCHs_r16_tags_177[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_maxNumberLongPUCCHs_r16_177 = {
	"maxNumberLongPUCCHs-r16",
	"maxNumberLongPUCCHs-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_maxNumberLongPUCCHs_r16_tags_177,
	sizeof(asn_DEF_NR_maxNumberLongPUCCHs_r16_tags_177)
		/sizeof(asn_DEF_NR_maxNumberLongPUCCHs_r16_tags_177[0]) - 1, /* 1 */
	asn_DEF_NR_maxNumberLongPUCCHs_r16_tags_177,	/* Same as above */
	sizeof(asn_DEF_NR_maxNumberLongPUCCHs_r16_tags_177)
		/sizeof(asn_DEF_NR_maxNumberLongPUCCHs_r16_tags_177[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_maxNumberLongPUCCHs_r16_constr_177,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_maxNumberLongPUCCHs_r16_specs_177	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_harqACK_separateMultiDCI_MultiTRP_r16_176[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersCommon__ext4__harqACK_separateMultiDCI_MultiTRP_r16, maxNumberLongPUCCHs_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_maxNumberLongPUCCHs_r16_177,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"maxNumberLongPUCCHs-r16"
		},
};
static const int asn_MAP_NR_harqACK_separateMultiDCI_MultiTRP_r16_oms_176[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_harqACK_separateMultiDCI_MultiTRP_r16_tags_176[] = {
	(ASN_TAG_CLASS_CONTEXT | (28 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_harqACK_separateMultiDCI_MultiTRP_r16_tag2el_176[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* maxNumberLongPUCCHs-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_harqACK_separateMultiDCI_MultiTRP_r16_specs_176 = {
	sizeof(struct NR_Phy_ParametersCommon__ext4__harqACK_separateMultiDCI_MultiTRP_r16),
	offsetof(struct NR_Phy_ParametersCommon__ext4__harqACK_separateMultiDCI_MultiTRP_r16, _asn_ctx),
	asn_MAP_NR_harqACK_separateMultiDCI_MultiTRP_r16_tag2el_176,
	1,	/* Count of tags in the map */
	asn_MAP_NR_harqACK_separateMultiDCI_MultiTRP_r16_oms_176,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_harqACK_separateMultiDCI_MultiTRP_r16_176 = {
	"harqACK-separateMultiDCI-MultiTRP-r16",
	"harqACK-separateMultiDCI-MultiTRP-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_harqACK_separateMultiDCI_MultiTRP_r16_tags_176,
	sizeof(asn_DEF_NR_harqACK_separateMultiDCI_MultiTRP_r16_tags_176)
		/sizeof(asn_DEF_NR_harqACK_separateMultiDCI_MultiTRP_r16_tags_176[0]) - 1, /* 1 */
	asn_DEF_NR_harqACK_separateMultiDCI_MultiTRP_r16_tags_176,	/* Same as above */
	sizeof(asn_DEF_NR_harqACK_separateMultiDCI_MultiTRP_r16_tags_176)
		/sizeof(asn_DEF_NR_harqACK_separateMultiDCI_MultiTRP_r16_tags_176[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_harqACK_separateMultiDCI_MultiTRP_r16_176,
	1,	/* Elements count */
	&asn_SPC_NR_harqACK_separateMultiDCI_MultiTRP_r16_specs_176	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_harqACK_jointMultiDCI_MultiTRP_r16_value2enum_181[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_harqACK_jointMultiDCI_MultiTRP_r16_enum2value_181[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_harqACK_jointMultiDCI_MultiTRP_r16_specs_181 = {
	asn_MAP_NR_harqACK_jointMultiDCI_MultiTRP_r16_value2enum_181,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_harqACK_jointMultiDCI_MultiTRP_r16_enum2value_181,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_harqACK_jointMultiDCI_MultiTRP_r16_tags_181[] = {
	(ASN_TAG_CLASS_CONTEXT | (29 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_harqACK_jointMultiDCI_MultiTRP_r16_181 = {
	"harqACK-jointMultiDCI-MultiTRP-r16",
	"harqACK-jointMultiDCI-MultiTRP-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_harqACK_jointMultiDCI_MultiTRP_r16_tags_181,
	sizeof(asn_DEF_NR_harqACK_jointMultiDCI_MultiTRP_r16_tags_181)
		/sizeof(asn_DEF_NR_harqACK_jointMultiDCI_MultiTRP_r16_tags_181[0]) - 1, /* 1 */
	asn_DEF_NR_harqACK_jointMultiDCI_MultiTRP_r16_tags_181,	/* Same as above */
	sizeof(asn_DEF_NR_harqACK_jointMultiDCI_MultiTRP_r16_tags_181)
		/sizeof(asn_DEF_NR_harqACK_jointMultiDCI_MultiTRP_r16_tags_181[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_harqACK_jointMultiDCI_MultiTRP_r16_constr_181,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_harqACK_jointMultiDCI_MultiTRP_r16_specs_181	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_type1_r16_value2enum_184[] = {
	{ 0,	5,	"us100" },
	{ 1,	5,	"us200" }
};
static const unsigned int asn_MAP_NR_type1_r16_enum2value_184[] = {
	0,	/* us100(0) */
	1	/* us200(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_type1_r16_specs_184 = {
	asn_MAP_NR_type1_r16_value2enum_184,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_type1_r16_enum2value_184,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_type1_r16_tags_184[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_type1_r16_184 = {
	"type1-r16",
	"type1-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_type1_r16_tags_184,
	sizeof(asn_DEF_NR_type1_r16_tags_184)
		/sizeof(asn_DEF_NR_type1_r16_tags_184[0]) - 1, /* 1 */
	asn_DEF_NR_type1_r16_tags_184,	/* Same as above */
	sizeof(asn_DEF_NR_type1_r16_tags_184)
		/sizeof(asn_DEF_NR_type1_r16_tags_184[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_type1_r16_constr_184,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_type1_r16_specs_184	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_type2_r16_value2enum_187[] = {
	{ 0,	5,	"us200" },
	{ 1,	5,	"us400" },
	{ 2,	5,	"us800" },
	{ 3,	6,	"us1000" }
};
static const unsigned int asn_MAP_NR_type2_r16_enum2value_187[] = {
	3,	/* us1000(3) */
	0,	/* us200(0) */
	1,	/* us400(1) */
	2	/* us800(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_type2_r16_specs_187 = {
	asn_MAP_NR_type2_r16_value2enum_187,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_type2_r16_enum2value_187,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_type2_r16_tags_187[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_type2_r16_187 = {
	"type2-r16",
	"type2-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_type2_r16_tags_187,
	sizeof(asn_DEF_NR_type2_r16_tags_187)
		/sizeof(asn_DEF_NR_type2_r16_tags_187[0]) - 1, /* 1 */
	asn_DEF_NR_type2_r16_tags_187,	/* Same as above */
	sizeof(asn_DEF_NR_type2_r16_tags_187)
		/sizeof(asn_DEF_NR_type2_r16_tags_187[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_type2_r16_constr_187,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_type2_r16_specs_187	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_bwp_SwitchingMultiCCs_r16_183[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_Phy_ParametersCommon__ext4__bwp_SwitchingMultiCCs_r16, choice.type1_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_type1_r16_184,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"type1-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_Phy_ParametersCommon__ext4__bwp_SwitchingMultiCCs_r16, choice.type2_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_type2_r16_187,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"type2-r16"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_NR_bwp_SwitchingMultiCCs_r16_tag2el_183[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* type1-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* type2-r16 */
};
static asn_CHOICE_specifics_t asn_SPC_NR_bwp_SwitchingMultiCCs_r16_specs_183 = {
	sizeof(struct NR_Phy_ParametersCommon__ext4__bwp_SwitchingMultiCCs_r16),
	offsetof(struct NR_Phy_ParametersCommon__ext4__bwp_SwitchingMultiCCs_r16, _asn_ctx),
	offsetof(struct NR_Phy_ParametersCommon__ext4__bwp_SwitchingMultiCCs_r16, present),
	sizeof(((struct NR_Phy_ParametersCommon__ext4__bwp_SwitchingMultiCCs_r16 *)0)->present),
	asn_MAP_NR_bwp_SwitchingMultiCCs_r16_tag2el_183,
	2,	/* Count of tags in the map */
	0, 0,
	-1	/* Extensions start */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_bwp_SwitchingMultiCCs_r16_183 = {
	"bwp-SwitchingMultiCCs-r16",
	"bwp-SwitchingMultiCCs-r16",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_bwp_SwitchingMultiCCs_r16_constr_183,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		CHOICE_constraint
	},
	asn_MBR_NR_bwp_SwitchingMultiCCs_r16_183,
	2,	/* Elements count */
	&asn_SPC_NR_bwp_SwitchingMultiCCs_r16_specs_183	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext4_84[] = {
	{ ATF_POINTER, 31, offsetof(struct NR_Phy_ParametersCommon__ext4, twoStepRACH_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_twoStepRACH_r16_85,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"twoStepRACH-r16"
		},
	{ ATF_POINTER, 30, offsetof(struct NR_Phy_ParametersCommon__ext4, dci_Format1_2And0_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dci_Format1_2And0_2_r16_87,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dci-Format1-2And0-2-r16"
		},
	{ ATF_POINTER, 29, offsetof(struct NR_Phy_ParametersCommon__ext4, monitoringDCI_SameSearchSpace_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_monitoringDCI_SameSearchSpace_r16_89,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"monitoringDCI-SameSearchSpace-r16"
		},
	{ ATF_POINTER, 28, offsetof(struct NR_Phy_ParametersCommon__ext4, type2_CG_ReleaseDCI_0_1_r16),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_type2_CG_ReleaseDCI_0_1_r16_91,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"type2-CG-ReleaseDCI-0-1-r16"
		},
	{ ATF_POINTER, 27, offsetof(struct NR_Phy_ParametersCommon__ext4, type2_CG_ReleaseDCI_0_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_type2_CG_ReleaseDCI_0_2_r16_93,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"type2-CG-ReleaseDCI-0-2-r16"
		},
	{ ATF_POINTER, 26, offsetof(struct NR_Phy_ParametersCommon__ext4, sps_ReleaseDCI_1_1_r16),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_sps_ReleaseDCI_1_1_r16_95,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"sps-ReleaseDCI-1-1-r16"
		},
	{ ATF_POINTER, 25, offsetof(struct NR_Phy_ParametersCommon__ext4, sps_ReleaseDCI_1_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_sps_ReleaseDCI_1_2_r16_97,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"sps-ReleaseDCI-1-2-r16"
		},
	{ ATF_POINTER, 24, offsetof(struct NR_Phy_ParametersCommon__ext4, csi_TriggerStateNon_ActiveBWP_r16),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_csi_TriggerStateNon_ActiveBWP_r16_99,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"csi-TriggerStateNon-ActiveBWP-r16"
		},
	{ ATF_POINTER, 23, offsetof(struct NR_Phy_ParametersCommon__ext4, separateSMTC_InterIAB_Support_r16),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_separateSMTC_InterIAB_Support_r16_101,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"separateSMTC-InterIAB-Support-r16"
		},
	{ ATF_POINTER, 22, offsetof(struct NR_Phy_ParametersCommon__ext4, separateRACH_IAB_Support_r16),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_separateRACH_IAB_Support_r16_103,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"separateRACH-IAB-Support-r16"
		},
	{ ATF_POINTER, 21, offsetof(struct NR_Phy_ParametersCommon__ext4, ul_flexibleDL_SlotFormatSemiStatic_IAB_r16),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ul_flexibleDL_SlotFormatSemiStatic_IAB_r16_105,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ul-flexibleDL-SlotFormatSemiStatic-IAB-r16"
		},
	{ ATF_POINTER, 20, offsetof(struct NR_Phy_ParametersCommon__ext4, ul_flexibleDL_SlotFormatDynamics_IAB_r16),
		(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ul_flexibleDL_SlotFormatDynamics_IAB_r16_107,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ul-flexibleDL-SlotFormatDynamics-IAB-r16"
		},
	{ ATF_POINTER, 19, offsetof(struct NR_Phy_ParametersCommon__ext4, dft_S_OFDM_WaveformUL_IAB_r16),
		(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dft_S_OFDM_WaveformUL_IAB_r16_109,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dft-S-OFDM-WaveformUL-IAB-r16"
		},
	{ ATF_POINTER, 18, offsetof(struct NR_Phy_ParametersCommon__ext4, dci_25_AI_RNTI_Support_IAB_r16),
		(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dci_25_AI_RNTI_Support_IAB_r16_111,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dci-25-AI-RNTI-Support-IAB-r16"
		},
	{ ATF_POINTER, 17, offsetof(struct NR_Phy_ParametersCommon__ext4, t_DeltaReceptionSupport_IAB_r16),
		(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_t_DeltaReceptionSupport_IAB_r16_113,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"t-DeltaReceptionSupport-IAB-r16"
		},
	{ ATF_POINTER, 16, offsetof(struct NR_Phy_ParametersCommon__ext4, guardSymbolReportReception_IAB_r16),
		(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_guardSymbolReportReception_IAB_r16_115,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"guardSymbolReportReception-IAB-r16"
		},
	{ ATF_POINTER, 15, offsetof(struct NR_Phy_ParametersCommon__ext4, harqACK_CB_SpatialBundlingPUCCH_Group_r16),
		(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_harqACK_CB_SpatialBundlingPUCCH_Group_r16_117,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"harqACK-CB-SpatialBundlingPUCCH-Group-r16"
		},
	{ ATF_POINTER, 14, offsetof(struct NR_Phy_ParametersCommon__ext4, crossSlotScheduling_r16),
		(ASN_TAG_CLASS_CONTEXT | (17 << 2)),
		0,
		&asn_DEF_NR_crossSlotScheduling_r16_119,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"crossSlotScheduling-r16"
		},
	{ ATF_POINTER, 13, offsetof(struct NR_Phy_ParametersCommon__ext4, maxNumberSRS_PosPathLossEstimateAllServingCells_r16),
		(ASN_TAG_CLASS_CONTEXT | (18 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_maxNumberSRS_PosPathLossEstimateAllServingCells_r16_124,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"maxNumberSRS-PosPathLossEstimateAllServingCells-r16"
		},
	{ ATF_POINTER, 12, offsetof(struct NR_Phy_ParametersCommon__ext4, extendedCG_Periodicities_r16),
		(ASN_TAG_CLASS_CONTEXT | (19 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_extendedCG_Periodicities_r16_129,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"extendedCG-Periodicities-r16"
		},
	{ ATF_POINTER, 11, offsetof(struct NR_Phy_ParametersCommon__ext4, extendedSPS_Periodicities_r16),
		(ASN_TAG_CLASS_CONTEXT | (20 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_extendedSPS_Periodicities_r16_131,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"extendedSPS-Periodicities-r16"
		},
	{ ATF_POINTER, 10, offsetof(struct NR_Phy_ParametersCommon__ext4, codebookVariantsList_r16),
		(ASN_TAG_CLASS_CONTEXT | (21 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_CodebookVariantsList_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"codebookVariantsList-r16"
		},
	{ ATF_POINTER, 9, offsetof(struct NR_Phy_ParametersCommon__ext4, pusch_RepetitionTypeA_r16),
		(ASN_TAG_CLASS_CONTEXT | (22 << 2)),
		0,
		&asn_DEF_NR_pusch_RepetitionTypeA_r16_134,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pusch-RepetitionTypeA-r16"
		},
	{ ATF_POINTER, 8, offsetof(struct NR_Phy_ParametersCommon__ext4, dci_DL_PriorityIndicator_r16),
		(ASN_TAG_CLASS_CONTEXT | (23 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dci_DL_PriorityIndicator_r16_139,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dci-DL-PriorityIndicator-r16"
		},
	{ ATF_POINTER, 7, offsetof(struct NR_Phy_ParametersCommon__ext4, dci_UL_PriorityIndicator_r16),
		(ASN_TAG_CLASS_CONTEXT | (24 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dci_UL_PriorityIndicator_r16_141,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dci-UL-PriorityIndicator-r16"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_Phy_ParametersCommon__ext4, maxNumberPathlossRS_Update_r16),
		(ASN_TAG_CLASS_CONTEXT | (25 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_maxNumberPathlossRS_Update_r16_143,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"maxNumberPathlossRS-Update-r16"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_Phy_ParametersCommon__ext4, type2_HARQ_ACK_Codebook_r16),
		(ASN_TAG_CLASS_CONTEXT | (26 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_type2_HARQ_ACK_Codebook_r16_149,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"type2-HARQ-ACK-Codebook-r16"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_Phy_ParametersCommon__ext4, maxTotalResourcesForAcrossFreqRanges_r16),
		(ASN_TAG_CLASS_CONTEXT | (27 << 2)),
		0,
		&asn_DEF_NR_maxTotalResourcesForAcrossFreqRanges_r16_151,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"maxTotalResourcesForAcrossFreqRanges-r16"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_Phy_ParametersCommon__ext4, harqACK_separateMultiDCI_MultiTRP_r16),
		(ASN_TAG_CLASS_CONTEXT | (28 << 2)),
		0,
		&asn_DEF_NR_harqACK_separateMultiDCI_MultiTRP_r16_176,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"harqACK-separateMultiDCI-MultiTRP-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_Phy_ParametersCommon__ext4, harqACK_jointMultiDCI_MultiTRP_r16),
		(ASN_TAG_CLASS_CONTEXT | (29 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_harqACK_jointMultiDCI_MultiTRP_r16_181,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"harqACK-jointMultiDCI-MultiTRP-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersCommon__ext4, bwp_SwitchingMultiCCs_r16),
		(ASN_TAG_CLASS_CONTEXT | (30 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_bwp_SwitchingMultiCCs_r16_183,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"bwp-SwitchingMultiCCs-r16"
		},
};
static const int asn_MAP_NR_ext4_oms_84[] = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30 };
static const ber_tlv_tag_t asn_DEF_NR_ext4_tags_84[] = {
	(ASN_TAG_CLASS_CONTEXT | (37 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext4_tag2el_84[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* twoStepRACH-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* dci-Format1-2And0-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* monitoringDCI-SameSearchSpace-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* type2-CG-ReleaseDCI-0-1-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* type2-CG-ReleaseDCI-0-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* sps-ReleaseDCI-1-1-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* sps-ReleaseDCI-1-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* csi-TriggerStateNon-ActiveBWP-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* separateSMTC-InterIAB-Support-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* separateRACH-IAB-Support-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 }, /* ul-flexibleDL-SlotFormatSemiStatic-IAB-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (11 << 2)), 11, 0, 0 }, /* ul-flexibleDL-SlotFormatDynamics-IAB-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (12 << 2)), 12, 0, 0 }, /* dft-S-OFDM-WaveformUL-IAB-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (13 << 2)), 13, 0, 0 }, /* dci-25-AI-RNTI-Support-IAB-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (14 << 2)), 14, 0, 0 }, /* t-DeltaReceptionSupport-IAB-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (15 << 2)), 15, 0, 0 }, /* guardSymbolReportReception-IAB-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (16 << 2)), 16, 0, 0 }, /* harqACK-CB-SpatialBundlingPUCCH-Group-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (17 << 2)), 17, 0, 0 }, /* crossSlotScheduling-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (18 << 2)), 18, 0, 0 }, /* maxNumberSRS-PosPathLossEstimateAllServingCells-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (19 << 2)), 19, 0, 0 }, /* extendedCG-Periodicities-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (20 << 2)), 20, 0, 0 }, /* extendedSPS-Periodicities-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (21 << 2)), 21, 0, 0 }, /* codebookVariantsList-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (22 << 2)), 22, 0, 0 }, /* pusch-RepetitionTypeA-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (23 << 2)), 23, 0, 0 }, /* dci-DL-PriorityIndicator-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (24 << 2)), 24, 0, 0 }, /* dci-UL-PriorityIndicator-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (25 << 2)), 25, 0, 0 }, /* maxNumberPathlossRS-Update-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (26 << 2)), 26, 0, 0 }, /* type2-HARQ-ACK-Codebook-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (27 << 2)), 27, 0, 0 }, /* maxTotalResourcesForAcrossFreqRanges-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (28 << 2)), 28, 0, 0 }, /* harqACK-separateMultiDCI-MultiTRP-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (29 << 2)), 29, 0, 0 }, /* harqACK-jointMultiDCI-MultiTRP-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (30 << 2)), 30, 0, 0 } /* bwp-SwitchingMultiCCs-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext4_specs_84 = {
	sizeof(struct NR_Phy_ParametersCommon__ext4),
	offsetof(struct NR_Phy_ParametersCommon__ext4, _asn_ctx),
	asn_MAP_NR_ext4_tag2el_84,
	31,	/* Count of tags in the map */
	asn_MAP_NR_ext4_oms_84,	/* Optional members */
	31, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext4_84 = {
	"ext4",
	"ext4",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext4_tags_84,
	sizeof(asn_DEF_NR_ext4_tags_84)
		/sizeof(asn_DEF_NR_ext4_tags_84[0]) - 1, /* 1 */
	asn_DEF_NR_ext4_tags_84,	/* Same as above */
	sizeof(asn_DEF_NR_ext4_tags_84)
		/sizeof(asn_DEF_NR_ext4_tags_84[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext4_84,
	31,	/* Elements count */
	&asn_SPC_NR_ext4_specs_84	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_targetSMTC_SCG_r16_value2enum_193[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_targetSMTC_SCG_r16_enum2value_193[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_targetSMTC_SCG_r16_specs_193 = {
	asn_MAP_NR_targetSMTC_SCG_r16_value2enum_193,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_targetSMTC_SCG_r16_enum2value_193,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_targetSMTC_SCG_r16_tags_193[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_targetSMTC_SCG_r16_193 = {
	"targetSMTC-SCG-r16",
	"targetSMTC-SCG-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_targetSMTC_SCG_r16_tags_193,
	sizeof(asn_DEF_NR_targetSMTC_SCG_r16_tags_193)
		/sizeof(asn_DEF_NR_targetSMTC_SCG_r16_tags_193[0]) - 1, /* 1 */
	asn_DEF_NR_targetSMTC_SCG_r16_tags_193,	/* Same as above */
	sizeof(asn_DEF_NR_targetSMTC_SCG_r16_tags_193)
		/sizeof(asn_DEF_NR_targetSMTC_SCG_r16_tags_193[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_targetSMTC_SCG_r16_constr_193,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_targetSMTC_SCG_r16_specs_193	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_supportRepetitionZeroOffsetRV_r16_value2enum_195[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_supportRepetitionZeroOffsetRV_r16_enum2value_195[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_supportRepetitionZeroOffsetRV_r16_specs_195 = {
	asn_MAP_NR_supportRepetitionZeroOffsetRV_r16_value2enum_195,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_supportRepetitionZeroOffsetRV_r16_enum2value_195,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_supportRepetitionZeroOffsetRV_r16_tags_195[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_supportRepetitionZeroOffsetRV_r16_195 = {
	"supportRepetitionZeroOffsetRV-r16",
	"supportRepetitionZeroOffsetRV-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_supportRepetitionZeroOffsetRV_r16_tags_195,
	sizeof(asn_DEF_NR_supportRepetitionZeroOffsetRV_r16_tags_195)
		/sizeof(asn_DEF_NR_supportRepetitionZeroOffsetRV_r16_tags_195[0]) - 1, /* 1 */
	asn_DEF_NR_supportRepetitionZeroOffsetRV_r16_tags_195,	/* Same as above */
	sizeof(asn_DEF_NR_supportRepetitionZeroOffsetRV_r16_tags_195)
		/sizeof(asn_DEF_NR_supportRepetitionZeroOffsetRV_r16_tags_195[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_supportRepetitionZeroOffsetRV_r16_constr_195,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_supportRepetitionZeroOffsetRV_r16_specs_195	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_cbg_TransInOrderPUSCH_UL_r16_value2enum_197[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_cbg_TransInOrderPUSCH_UL_r16_enum2value_197[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_cbg_TransInOrderPUSCH_UL_r16_specs_197 = {
	asn_MAP_NR_cbg_TransInOrderPUSCH_UL_r16_value2enum_197,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_cbg_TransInOrderPUSCH_UL_r16_enum2value_197,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_cbg_TransInOrderPUSCH_UL_r16_tags_197[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_cbg_TransInOrderPUSCH_UL_r16_197 = {
	"cbg-TransInOrderPUSCH-UL-r16",
	"cbg-TransInOrderPUSCH-UL-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_cbg_TransInOrderPUSCH_UL_r16_tags_197,
	sizeof(asn_DEF_NR_cbg_TransInOrderPUSCH_UL_r16_tags_197)
		/sizeof(asn_DEF_NR_cbg_TransInOrderPUSCH_UL_r16_tags_197[0]) - 1, /* 1 */
	asn_DEF_NR_cbg_TransInOrderPUSCH_UL_r16_tags_197,	/* Same as above */
	sizeof(asn_DEF_NR_cbg_TransInOrderPUSCH_UL_r16_tags_197)
		/sizeof(asn_DEF_NR_cbg_TransInOrderPUSCH_UL_r16_tags_197[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_cbg_TransInOrderPUSCH_UL_r16_constr_197,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_cbg_TransInOrderPUSCH_UL_r16_specs_197	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext5_192[] = {
	{ ATF_POINTER, 3, offsetof(struct NR_Phy_ParametersCommon__ext5, targetSMTC_SCG_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_targetSMTC_SCG_r16_193,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"targetSMTC-SCG-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_Phy_ParametersCommon__ext5, supportRepetitionZeroOffsetRV_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_supportRepetitionZeroOffsetRV_r16_195,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportRepetitionZeroOffsetRV-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersCommon__ext5, cbg_TransInOrderPUSCH_UL_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_cbg_TransInOrderPUSCH_UL_r16_197,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"cbg-TransInOrderPUSCH-UL-r16"
		},
};
static const int asn_MAP_NR_ext5_oms_192[] = { 0, 1, 2 };
static const ber_tlv_tag_t asn_DEF_NR_ext5_tags_192[] = {
	(ASN_TAG_CLASS_CONTEXT | (38 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext5_tag2el_192[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* targetSMTC-SCG-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* supportRepetitionZeroOffsetRV-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* cbg-TransInOrderPUSCH-UL-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext5_specs_192 = {
	sizeof(struct NR_Phy_ParametersCommon__ext5),
	offsetof(struct NR_Phy_ParametersCommon__ext5, _asn_ctx),
	asn_MAP_NR_ext5_tag2el_192,
	3,	/* Count of tags in the map */
	asn_MAP_NR_ext5_oms_192,	/* Optional members */
	3, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext5_192 = {
	"ext5",
	"ext5",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext5_tags_192,
	sizeof(asn_DEF_NR_ext5_tags_192)
		/sizeof(asn_DEF_NR_ext5_tags_192[0]) - 1, /* 1 */
	asn_DEF_NR_ext5_tags_192,	/* Same as above */
	sizeof(asn_DEF_NR_ext5_tags_192)
		/sizeof(asn_DEF_NR_ext5_tags_192[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext5_192,
	3,	/* Elements count */
	&asn_SPC_NR_ext5_specs_192	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_type1_r16_value2enum_201[] = {
	{ 0,	5,	"us100" },
	{ 1,	5,	"us200" }
};
static const unsigned int asn_MAP_NR_type1_r16_enum2value_201[] = {
	0,	/* us100(0) */
	1	/* us200(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_type1_r16_specs_201 = {
	asn_MAP_NR_type1_r16_value2enum_201,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_type1_r16_enum2value_201,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_type1_r16_tags_201[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_type1_r16_201 = {
	"type1-r16",
	"type1-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_type1_r16_tags_201,
	sizeof(asn_DEF_NR_type1_r16_tags_201)
		/sizeof(asn_DEF_NR_type1_r16_tags_201[0]) - 1, /* 1 */
	asn_DEF_NR_type1_r16_tags_201,	/* Same as above */
	sizeof(asn_DEF_NR_type1_r16_tags_201)
		/sizeof(asn_DEF_NR_type1_r16_tags_201[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_type1_r16_constr_201,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_type1_r16_specs_201	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_type2_r16_value2enum_204[] = {
	{ 0,	5,	"us200" },
	{ 1,	5,	"us400" },
	{ 2,	5,	"us800" },
	{ 3,	6,	"us1000" }
};
static const unsigned int asn_MAP_NR_type2_r16_enum2value_204[] = {
	3,	/* us1000(3) */
	0,	/* us200(0) */
	1,	/* us400(1) */
	2	/* us800(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_type2_r16_specs_204 = {
	asn_MAP_NR_type2_r16_value2enum_204,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_type2_r16_enum2value_204,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_type2_r16_tags_204[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_type2_r16_204 = {
	"type2-r16",
	"type2-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_type2_r16_tags_204,
	sizeof(asn_DEF_NR_type2_r16_tags_204)
		/sizeof(asn_DEF_NR_type2_r16_tags_204[0]) - 1, /* 1 */
	asn_DEF_NR_type2_r16_tags_204,	/* Same as above */
	sizeof(asn_DEF_NR_type2_r16_tags_204)
		/sizeof(asn_DEF_NR_type2_r16_tags_204[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_type2_r16_constr_204,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_type2_r16_specs_204	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_bwp_SwitchingMultiDormancyCCs_r16_200[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_Phy_ParametersCommon__ext6__bwp_SwitchingMultiDormancyCCs_r16, choice.type1_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_type1_r16_201,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"type1-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_Phy_ParametersCommon__ext6__bwp_SwitchingMultiDormancyCCs_r16, choice.type2_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_type2_r16_204,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"type2-r16"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_NR_bwp_SwitchingMultiDormancyCCs_r16_tag2el_200[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* type1-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* type2-r16 */
};
static asn_CHOICE_specifics_t asn_SPC_NR_bwp_SwitchingMultiDormancyCCs_r16_specs_200 = {
	sizeof(struct NR_Phy_ParametersCommon__ext6__bwp_SwitchingMultiDormancyCCs_r16),
	offsetof(struct NR_Phy_ParametersCommon__ext6__bwp_SwitchingMultiDormancyCCs_r16, _asn_ctx),
	offsetof(struct NR_Phy_ParametersCommon__ext6__bwp_SwitchingMultiDormancyCCs_r16, present),
	sizeof(((struct NR_Phy_ParametersCommon__ext6__bwp_SwitchingMultiDormancyCCs_r16 *)0)->present),
	asn_MAP_NR_bwp_SwitchingMultiDormancyCCs_r16_tag2el_200,
	2,	/* Count of tags in the map */
	0, 0,
	-1	/* Extensions start */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_bwp_SwitchingMultiDormancyCCs_r16_200 = {
	"bwp-SwitchingMultiDormancyCCs-r16",
	"bwp-SwitchingMultiDormancyCCs-r16",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_bwp_SwitchingMultiDormancyCCs_r16_constr_200,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		CHOICE_constraint
	},
	asn_MBR_NR_bwp_SwitchingMultiDormancyCCs_r16_200,
	2,	/* Elements count */
	&asn_SPC_NR_bwp_SwitchingMultiDormancyCCs_r16_specs_200	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_supportRetx_Diff_CoresetPool_Multi_DCI_TRP_r16_value2enum_209[] = {
	{ 0,	12,	"notSupported" }
};
static const unsigned int asn_MAP_NR_supportRetx_Diff_CoresetPool_Multi_DCI_TRP_r16_enum2value_209[] = {
	0	/* notSupported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_supportRetx_Diff_CoresetPool_Multi_DCI_TRP_r16_specs_209 = {
	asn_MAP_NR_supportRetx_Diff_CoresetPool_Multi_DCI_TRP_r16_value2enum_209,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_supportRetx_Diff_CoresetPool_Multi_DCI_TRP_r16_enum2value_209,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_supportRetx_Diff_CoresetPool_Multi_DCI_TRP_r16_tags_209[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_supportRetx_Diff_CoresetPool_Multi_DCI_TRP_r16_209 = {
	"supportRetx-Diff-CoresetPool-Multi-DCI-TRP-r16",
	"supportRetx-Diff-CoresetPool-Multi-DCI-TRP-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_supportRetx_Diff_CoresetPool_Multi_DCI_TRP_r16_tags_209,
	sizeof(asn_DEF_NR_supportRetx_Diff_CoresetPool_Multi_DCI_TRP_r16_tags_209)
		/sizeof(asn_DEF_NR_supportRetx_Diff_CoresetPool_Multi_DCI_TRP_r16_tags_209[0]) - 1, /* 1 */
	asn_DEF_NR_supportRetx_Diff_CoresetPool_Multi_DCI_TRP_r16_tags_209,	/* Same as above */
	sizeof(asn_DEF_NR_supportRetx_Diff_CoresetPool_Multi_DCI_TRP_r16_tags_209)
		/sizeof(asn_DEF_NR_supportRetx_Diff_CoresetPool_Multi_DCI_TRP_r16_tags_209[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_supportRetx_Diff_CoresetPool_Multi_DCI_TRP_r16_constr_209,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_supportRetx_Diff_CoresetPool_Multi_DCI_TRP_r16_specs_209	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pdcch_MonitoringAnyOccasionsWithSpanGapCrossCarrierSch_r16_value2enum_211[] = {
	{ 0,	5,	"mode2" },
	{ 1,	5,	"mode3" }
};
static const unsigned int asn_MAP_NR_pdcch_MonitoringAnyOccasionsWithSpanGapCrossCarrierSch_r16_enum2value_211[] = {
	0,	/* mode2(0) */
	1	/* mode3(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pdcch_MonitoringAnyOccasionsWithSpanGapCrossCarrierSch_r16_specs_211 = {
	asn_MAP_NR_pdcch_MonitoringAnyOccasionsWithSpanGapCrossCarrierSch_r16_value2enum_211,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pdcch_MonitoringAnyOccasionsWithSpanGapCrossCarrierSch_r16_enum2value_211,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pdcch_MonitoringAnyOccasionsWithSpanGapCrossCarrierSch_r16_tags_211[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pdcch_MonitoringAnyOccasionsWithSpanGapCrossCarrierSch_r16_211 = {
	"pdcch-MonitoringAnyOccasionsWithSpanGapCrossCarrierSch-r16",
	"pdcch-MonitoringAnyOccasionsWithSpanGapCrossCarrierSch-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pdcch_MonitoringAnyOccasionsWithSpanGapCrossCarrierSch_r16_tags_211,
	sizeof(asn_DEF_NR_pdcch_MonitoringAnyOccasionsWithSpanGapCrossCarrierSch_r16_tags_211)
		/sizeof(asn_DEF_NR_pdcch_MonitoringAnyOccasionsWithSpanGapCrossCarrierSch_r16_tags_211[0]) - 1, /* 1 */
	asn_DEF_NR_pdcch_MonitoringAnyOccasionsWithSpanGapCrossCarrierSch_r16_tags_211,	/* Same as above */
	sizeof(asn_DEF_NR_pdcch_MonitoringAnyOccasionsWithSpanGapCrossCarrierSch_r16_tags_211)
		/sizeof(asn_DEF_NR_pdcch_MonitoringAnyOccasionsWithSpanGapCrossCarrierSch_r16_tags_211[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pdcch_MonitoringAnyOccasionsWithSpanGapCrossCarrierSch_r16_constr_211,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pdcch_MonitoringAnyOccasionsWithSpanGapCrossCarrierSch_r16_specs_211	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext6_199[] = {
	{ ATF_POINTER, 3, offsetof(struct NR_Phy_ParametersCommon__ext6, bwp_SwitchingMultiDormancyCCs_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_bwp_SwitchingMultiDormancyCCs_r16_200,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"bwp-SwitchingMultiDormancyCCs-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_Phy_ParametersCommon__ext6, supportRetx_Diff_CoresetPool_Multi_DCI_TRP_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_supportRetx_Diff_CoresetPool_Multi_DCI_TRP_r16_209,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportRetx-Diff-CoresetPool-Multi-DCI-TRP-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersCommon__ext6, pdcch_MonitoringAnyOccasionsWithSpanGapCrossCarrierSch_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pdcch_MonitoringAnyOccasionsWithSpanGapCrossCarrierSch_r16_211,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pdcch-MonitoringAnyOccasionsWithSpanGapCrossCarrierSch-r16"
		},
};
static const int asn_MAP_NR_ext6_oms_199[] = { 0, 1, 2 };
static const ber_tlv_tag_t asn_DEF_NR_ext6_tags_199[] = {
	(ASN_TAG_CLASS_CONTEXT | (39 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext6_tag2el_199[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* bwp-SwitchingMultiDormancyCCs-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* supportRetx-Diff-CoresetPool-Multi-DCI-TRP-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* pdcch-MonitoringAnyOccasionsWithSpanGapCrossCarrierSch-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext6_specs_199 = {
	sizeof(struct NR_Phy_ParametersCommon__ext6),
	offsetof(struct NR_Phy_ParametersCommon__ext6, _asn_ctx),
	asn_MAP_NR_ext6_tag2el_199,
	3,	/* Count of tags in the map */
	asn_MAP_NR_ext6_oms_199,	/* Optional members */
	3, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext6_199 = {
	"ext6",
	"ext6",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext6_tags_199,
	sizeof(asn_DEF_NR_ext6_tags_199)
		/sizeof(asn_DEF_NR_ext6_tags_199[0]) - 1, /* 1 */
	asn_DEF_NR_ext6_tags_199,	/* Same as above */
	sizeof(asn_DEF_NR_ext6_tags_199)
		/sizeof(asn_DEF_NR_ext6_tags_199[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext6_199,
	3,	/* Elements count */
	&asn_SPC_NR_ext6_specs_199	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_newBeamIdentifications2PortCSI_RS_r16_value2enum_215[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_newBeamIdentifications2PortCSI_RS_r16_enum2value_215[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_newBeamIdentifications2PortCSI_RS_r16_specs_215 = {
	asn_MAP_NR_newBeamIdentifications2PortCSI_RS_r16_value2enum_215,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_newBeamIdentifications2PortCSI_RS_r16_enum2value_215,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_newBeamIdentifications2PortCSI_RS_r16_tags_215[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_newBeamIdentifications2PortCSI_RS_r16_215 = {
	"newBeamIdentifications2PortCSI-RS-r16",
	"newBeamIdentifications2PortCSI-RS-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_newBeamIdentifications2PortCSI_RS_r16_tags_215,
	sizeof(asn_DEF_NR_newBeamIdentifications2PortCSI_RS_r16_tags_215)
		/sizeof(asn_DEF_NR_newBeamIdentifications2PortCSI_RS_r16_tags_215[0]) - 1, /* 1 */
	asn_DEF_NR_newBeamIdentifications2PortCSI_RS_r16_tags_215,	/* Same as above */
	sizeof(asn_DEF_NR_newBeamIdentifications2PortCSI_RS_r16_tags_215)
		/sizeof(asn_DEF_NR_newBeamIdentifications2PortCSI_RS_r16_tags_215[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_newBeamIdentifications2PortCSI_RS_r16_constr_215,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_newBeamIdentifications2PortCSI_RS_r16_specs_215	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pathlossEstimation2PortCSI_RS_r16_value2enum_217[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_pathlossEstimation2PortCSI_RS_r16_enum2value_217[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pathlossEstimation2PortCSI_RS_r16_specs_217 = {
	asn_MAP_NR_pathlossEstimation2PortCSI_RS_r16_value2enum_217,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pathlossEstimation2PortCSI_RS_r16_enum2value_217,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pathlossEstimation2PortCSI_RS_r16_tags_217[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pathlossEstimation2PortCSI_RS_r16_217 = {
	"pathlossEstimation2PortCSI-RS-r16",
	"pathlossEstimation2PortCSI-RS-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pathlossEstimation2PortCSI_RS_r16_tags_217,
	sizeof(asn_DEF_NR_pathlossEstimation2PortCSI_RS_r16_tags_217)
		/sizeof(asn_DEF_NR_pathlossEstimation2PortCSI_RS_r16_tags_217[0]) - 1, /* 1 */
	asn_DEF_NR_pathlossEstimation2PortCSI_RS_r16_tags_217,	/* Same as above */
	sizeof(asn_DEF_NR_pathlossEstimation2PortCSI_RS_r16_tags_217)
		/sizeof(asn_DEF_NR_pathlossEstimation2PortCSI_RS_r16_tags_217[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pathlossEstimation2PortCSI_RS_r16_constr_217,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pathlossEstimation2PortCSI_RS_r16_specs_217	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext7_214[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_Phy_ParametersCommon__ext7, newBeamIdentifications2PortCSI_RS_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_newBeamIdentifications2PortCSI_RS_r16_215,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"newBeamIdentifications2PortCSI-RS-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersCommon__ext7, pathlossEstimation2PortCSI_RS_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pathlossEstimation2PortCSI_RS_r16_217,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pathlossEstimation2PortCSI-RS-r16"
		},
};
static const int asn_MAP_NR_ext7_oms_214[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_ext7_tags_214[] = {
	(ASN_TAG_CLASS_CONTEXT | (40 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext7_tag2el_214[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* newBeamIdentifications2PortCSI-RS-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* pathlossEstimation2PortCSI-RS-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext7_specs_214 = {
	sizeof(struct NR_Phy_ParametersCommon__ext7),
	offsetof(struct NR_Phy_ParametersCommon__ext7, _asn_ctx),
	asn_MAP_NR_ext7_tag2el_214,
	2,	/* Count of tags in the map */
	asn_MAP_NR_ext7_oms_214,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext7_214 = {
	"ext7",
	"ext7",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext7_tags_214,
	sizeof(asn_DEF_NR_ext7_tags_214)
		/sizeof(asn_DEF_NR_ext7_tags_214[0]) - 1, /* 1 */
	asn_DEF_NR_ext7_tags_214,	/* Same as above */
	sizeof(asn_DEF_NR_ext7_tags_214)
		/sizeof(asn_DEF_NR_ext7_tags_214[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext7_214,
	2,	/* Elements count */
	&asn_SPC_NR_ext7_specs_214	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_mux_HARQ_ACK_withoutPUCCH_onPUSCH_r16_value2enum_220[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_mux_HARQ_ACK_withoutPUCCH_onPUSCH_r16_enum2value_220[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_mux_HARQ_ACK_withoutPUCCH_onPUSCH_r16_specs_220 = {
	asn_MAP_NR_mux_HARQ_ACK_withoutPUCCH_onPUSCH_r16_value2enum_220,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_mux_HARQ_ACK_withoutPUCCH_onPUSCH_r16_enum2value_220,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_mux_HARQ_ACK_withoutPUCCH_onPUSCH_r16_tags_220[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mux_HARQ_ACK_withoutPUCCH_onPUSCH_r16_220 = {
	"mux-HARQ-ACK-withoutPUCCH-onPUSCH-r16",
	"mux-HARQ-ACK-withoutPUCCH-onPUSCH-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_mux_HARQ_ACK_withoutPUCCH_onPUSCH_r16_tags_220,
	sizeof(asn_DEF_NR_mux_HARQ_ACK_withoutPUCCH_onPUSCH_r16_tags_220)
		/sizeof(asn_DEF_NR_mux_HARQ_ACK_withoutPUCCH_onPUSCH_r16_tags_220[0]) - 1, /* 1 */
	asn_DEF_NR_mux_HARQ_ACK_withoutPUCCH_onPUSCH_r16_tags_220,	/* Same as above */
	sizeof(asn_DEF_NR_mux_HARQ_ACK_withoutPUCCH_onPUSCH_r16_tags_220)
		/sizeof(asn_DEF_NR_mux_HARQ_ACK_withoutPUCCH_onPUSCH_r16_tags_220[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_mux_HARQ_ACK_withoutPUCCH_onPUSCH_r16_constr_220,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_mux_HARQ_ACK_withoutPUCCH_onPUSCH_r16_specs_220	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext8_219[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersCommon__ext8, mux_HARQ_ACK_withoutPUCCH_onPUSCH_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_mux_HARQ_ACK_withoutPUCCH_onPUSCH_r16_220,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mux-HARQ-ACK-withoutPUCCH-onPUSCH-r16"
		},
};
static const int asn_MAP_NR_ext8_oms_219[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext8_tags_219[] = {
	(ASN_TAG_CLASS_CONTEXT | (41 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext8_tag2el_219[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* mux-HARQ-ACK-withoutPUCCH-onPUSCH-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext8_specs_219 = {
	sizeof(struct NR_Phy_ParametersCommon__ext8),
	offsetof(struct NR_Phy_ParametersCommon__ext8, _asn_ctx),
	asn_MAP_NR_ext8_tag2el_219,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext8_oms_219,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext8_219 = {
	"ext8",
	"ext8",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext8_tags_219,
	sizeof(asn_DEF_NR_ext8_tags_219)
		/sizeof(asn_DEF_NR_ext8_tags_219[0]) - 1, /* 1 */
	asn_DEF_NR_ext8_tags_219,	/* Same as above */
	sizeof(asn_DEF_NR_ext8_tags_219)
		/sizeof(asn_DEF_NR_ext8_tags_219[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext8_219,
	1,	/* Elements count */
	&asn_SPC_NR_ext8_specs_219	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_guardSymbolReportReception_IAB_r17_value2enum_223[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_guardSymbolReportReception_IAB_r17_enum2value_223[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_guardSymbolReportReception_IAB_r17_specs_223 = {
	asn_MAP_NR_guardSymbolReportReception_IAB_r17_value2enum_223,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_guardSymbolReportReception_IAB_r17_enum2value_223,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_guardSymbolReportReception_IAB_r17_tags_223[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_guardSymbolReportReception_IAB_r17_223 = {
	"guardSymbolReportReception-IAB-r17",
	"guardSymbolReportReception-IAB-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_guardSymbolReportReception_IAB_r17_tags_223,
	sizeof(asn_DEF_NR_guardSymbolReportReception_IAB_r17_tags_223)
		/sizeof(asn_DEF_NR_guardSymbolReportReception_IAB_r17_tags_223[0]) - 1, /* 1 */
	asn_DEF_NR_guardSymbolReportReception_IAB_r17_tags_223,	/* Same as above */
	sizeof(asn_DEF_NR_guardSymbolReportReception_IAB_r17_tags_223)
		/sizeof(asn_DEF_NR_guardSymbolReportReception_IAB_r17_tags_223[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_guardSymbolReportReception_IAB_r17_constr_223,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_guardSymbolReportReception_IAB_r17_specs_223	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_restricted_IAB_DU_BeamReception_r17_value2enum_225[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_restricted_IAB_DU_BeamReception_r17_enum2value_225[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_restricted_IAB_DU_BeamReception_r17_specs_225 = {
	asn_MAP_NR_restricted_IAB_DU_BeamReception_r17_value2enum_225,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_restricted_IAB_DU_BeamReception_r17_enum2value_225,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_restricted_IAB_DU_BeamReception_r17_tags_225[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_restricted_IAB_DU_BeamReception_r17_225 = {
	"restricted-IAB-DU-BeamReception-r17",
	"restricted-IAB-DU-BeamReception-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_restricted_IAB_DU_BeamReception_r17_tags_225,
	sizeof(asn_DEF_NR_restricted_IAB_DU_BeamReception_r17_tags_225)
		/sizeof(asn_DEF_NR_restricted_IAB_DU_BeamReception_r17_tags_225[0]) - 1, /* 1 */
	asn_DEF_NR_restricted_IAB_DU_BeamReception_r17_tags_225,	/* Same as above */
	sizeof(asn_DEF_NR_restricted_IAB_DU_BeamReception_r17_tags_225)
		/sizeof(asn_DEF_NR_restricted_IAB_DU_BeamReception_r17_tags_225[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_restricted_IAB_DU_BeamReception_r17_constr_225,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_restricted_IAB_DU_BeamReception_r17_specs_225	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_recommended_IAB_MT_BeamTransmission_r17_value2enum_227[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_recommended_IAB_MT_BeamTransmission_r17_enum2value_227[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_recommended_IAB_MT_BeamTransmission_r17_specs_227 = {
	asn_MAP_NR_recommended_IAB_MT_BeamTransmission_r17_value2enum_227,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_recommended_IAB_MT_BeamTransmission_r17_enum2value_227,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_recommended_IAB_MT_BeamTransmission_r17_tags_227[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_recommended_IAB_MT_BeamTransmission_r17_227 = {
	"recommended-IAB-MT-BeamTransmission-r17",
	"recommended-IAB-MT-BeamTransmission-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_recommended_IAB_MT_BeamTransmission_r17_tags_227,
	sizeof(asn_DEF_NR_recommended_IAB_MT_BeamTransmission_r17_tags_227)
		/sizeof(asn_DEF_NR_recommended_IAB_MT_BeamTransmission_r17_tags_227[0]) - 1, /* 1 */
	asn_DEF_NR_recommended_IAB_MT_BeamTransmission_r17_tags_227,	/* Same as above */
	sizeof(asn_DEF_NR_recommended_IAB_MT_BeamTransmission_r17_tags_227)
		/sizeof(asn_DEF_NR_recommended_IAB_MT_BeamTransmission_r17_tags_227[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_recommended_IAB_MT_BeamTransmission_r17_constr_227,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_recommended_IAB_MT_BeamTransmission_r17_specs_227	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_case6_TimingAlignmentReception_IAB_r17_value2enum_229[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_case6_TimingAlignmentReception_IAB_r17_enum2value_229[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_case6_TimingAlignmentReception_IAB_r17_specs_229 = {
	asn_MAP_NR_case6_TimingAlignmentReception_IAB_r17_value2enum_229,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_case6_TimingAlignmentReception_IAB_r17_enum2value_229,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_case6_TimingAlignmentReception_IAB_r17_tags_229[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_case6_TimingAlignmentReception_IAB_r17_229 = {
	"case6-TimingAlignmentReception-IAB-r17",
	"case6-TimingAlignmentReception-IAB-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_case6_TimingAlignmentReception_IAB_r17_tags_229,
	sizeof(asn_DEF_NR_case6_TimingAlignmentReception_IAB_r17_tags_229)
		/sizeof(asn_DEF_NR_case6_TimingAlignmentReception_IAB_r17_tags_229[0]) - 1, /* 1 */
	asn_DEF_NR_case6_TimingAlignmentReception_IAB_r17_tags_229,	/* Same as above */
	sizeof(asn_DEF_NR_case6_TimingAlignmentReception_IAB_r17_tags_229)
		/sizeof(asn_DEF_NR_case6_TimingAlignmentReception_IAB_r17_tags_229[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_case6_TimingAlignmentReception_IAB_r17_constr_229,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_case6_TimingAlignmentReception_IAB_r17_specs_229	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_case7_TimingAlignmentReception_IAB_r17_value2enum_231[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_case7_TimingAlignmentReception_IAB_r17_enum2value_231[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_case7_TimingAlignmentReception_IAB_r17_specs_231 = {
	asn_MAP_NR_case7_TimingAlignmentReception_IAB_r17_value2enum_231,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_case7_TimingAlignmentReception_IAB_r17_enum2value_231,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_case7_TimingAlignmentReception_IAB_r17_tags_231[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_case7_TimingAlignmentReception_IAB_r17_231 = {
	"case7-TimingAlignmentReception-IAB-r17",
	"case7-TimingAlignmentReception-IAB-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_case7_TimingAlignmentReception_IAB_r17_tags_231,
	sizeof(asn_DEF_NR_case7_TimingAlignmentReception_IAB_r17_tags_231)
		/sizeof(asn_DEF_NR_case7_TimingAlignmentReception_IAB_r17_tags_231[0]) - 1, /* 1 */
	asn_DEF_NR_case7_TimingAlignmentReception_IAB_r17_tags_231,	/* Same as above */
	sizeof(asn_DEF_NR_case7_TimingAlignmentReception_IAB_r17_tags_231)
		/sizeof(asn_DEF_NR_case7_TimingAlignmentReception_IAB_r17_tags_231[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_case7_TimingAlignmentReception_IAB_r17_constr_231,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_case7_TimingAlignmentReception_IAB_r17_specs_231	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dl_tx_PowerAdjustment_IAB_r17_value2enum_233[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_dl_tx_PowerAdjustment_IAB_r17_enum2value_233[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dl_tx_PowerAdjustment_IAB_r17_specs_233 = {
	asn_MAP_NR_dl_tx_PowerAdjustment_IAB_r17_value2enum_233,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dl_tx_PowerAdjustment_IAB_r17_enum2value_233,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dl_tx_PowerAdjustment_IAB_r17_tags_233[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dl_tx_PowerAdjustment_IAB_r17_233 = {
	"dl-tx-PowerAdjustment-IAB-r17",
	"dl-tx-PowerAdjustment-IAB-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dl_tx_PowerAdjustment_IAB_r17_tags_233,
	sizeof(asn_DEF_NR_dl_tx_PowerAdjustment_IAB_r17_tags_233)
		/sizeof(asn_DEF_NR_dl_tx_PowerAdjustment_IAB_r17_tags_233[0]) - 1, /* 1 */
	asn_DEF_NR_dl_tx_PowerAdjustment_IAB_r17_tags_233,	/* Same as above */
	sizeof(asn_DEF_NR_dl_tx_PowerAdjustment_IAB_r17_tags_233)
		/sizeof(asn_DEF_NR_dl_tx_PowerAdjustment_IAB_r17_tags_233[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dl_tx_PowerAdjustment_IAB_r17_constr_233,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dl_tx_PowerAdjustment_IAB_r17_specs_233	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_desired_ul_tx_PowerAdjustment_r17_value2enum_235[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_desired_ul_tx_PowerAdjustment_r17_enum2value_235[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_desired_ul_tx_PowerAdjustment_r17_specs_235 = {
	asn_MAP_NR_desired_ul_tx_PowerAdjustment_r17_value2enum_235,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_desired_ul_tx_PowerAdjustment_r17_enum2value_235,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_desired_ul_tx_PowerAdjustment_r17_tags_235[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_desired_ul_tx_PowerAdjustment_r17_235 = {
	"desired-ul-tx-PowerAdjustment-r17",
	"desired-ul-tx-PowerAdjustment-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_desired_ul_tx_PowerAdjustment_r17_tags_235,
	sizeof(asn_DEF_NR_desired_ul_tx_PowerAdjustment_r17_tags_235)
		/sizeof(asn_DEF_NR_desired_ul_tx_PowerAdjustment_r17_tags_235[0]) - 1, /* 1 */
	asn_DEF_NR_desired_ul_tx_PowerAdjustment_r17_tags_235,	/* Same as above */
	sizeof(asn_DEF_NR_desired_ul_tx_PowerAdjustment_r17_tags_235)
		/sizeof(asn_DEF_NR_desired_ul_tx_PowerAdjustment_r17_tags_235[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_desired_ul_tx_PowerAdjustment_r17_constr_235,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_desired_ul_tx_PowerAdjustment_r17_specs_235	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_fdm_SoftResourceAvailability_DynamicIndication_r17_value2enum_237[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_fdm_SoftResourceAvailability_DynamicIndication_r17_enum2value_237[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_fdm_SoftResourceAvailability_DynamicIndication_r17_specs_237 = {
	asn_MAP_NR_fdm_SoftResourceAvailability_DynamicIndication_r17_value2enum_237,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_fdm_SoftResourceAvailability_DynamicIndication_r17_enum2value_237,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_fdm_SoftResourceAvailability_DynamicIndication_r17_tags_237[] = {
	(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_fdm_SoftResourceAvailability_DynamicIndication_r17_237 = {
	"fdm-SoftResourceAvailability-DynamicIndication-r17",
	"fdm-SoftResourceAvailability-DynamicIndication-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_fdm_SoftResourceAvailability_DynamicIndication_r17_tags_237,
	sizeof(asn_DEF_NR_fdm_SoftResourceAvailability_DynamicIndication_r17_tags_237)
		/sizeof(asn_DEF_NR_fdm_SoftResourceAvailability_DynamicIndication_r17_tags_237[0]) - 1, /* 1 */
	asn_DEF_NR_fdm_SoftResourceAvailability_DynamicIndication_r17_tags_237,	/* Same as above */
	sizeof(asn_DEF_NR_fdm_SoftResourceAvailability_DynamicIndication_r17_tags_237)
		/sizeof(asn_DEF_NR_fdm_SoftResourceAvailability_DynamicIndication_r17_tags_237[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_fdm_SoftResourceAvailability_DynamicIndication_r17_constr_237,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_fdm_SoftResourceAvailability_DynamicIndication_r17_specs_237	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_updated_T_DeltaRangeRecption_r17_value2enum_239[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_updated_T_DeltaRangeRecption_r17_enum2value_239[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_updated_T_DeltaRangeRecption_r17_specs_239 = {
	asn_MAP_NR_updated_T_DeltaRangeRecption_r17_value2enum_239,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_updated_T_DeltaRangeRecption_r17_enum2value_239,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_updated_T_DeltaRangeRecption_r17_tags_239[] = {
	(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_updated_T_DeltaRangeRecption_r17_239 = {
	"updated-T-DeltaRangeRecption-r17",
	"updated-T-DeltaRangeRecption-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_updated_T_DeltaRangeRecption_r17_tags_239,
	sizeof(asn_DEF_NR_updated_T_DeltaRangeRecption_r17_tags_239)
		/sizeof(asn_DEF_NR_updated_T_DeltaRangeRecption_r17_tags_239[0]) - 1, /* 1 */
	asn_DEF_NR_updated_T_DeltaRangeRecption_r17_tags_239,	/* Same as above */
	sizeof(asn_DEF_NR_updated_T_DeltaRangeRecption_r17_tags_239)
		/sizeof(asn_DEF_NR_updated_T_DeltaRangeRecption_r17_tags_239[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_updated_T_DeltaRangeRecption_r17_constr_239,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_updated_T_DeltaRangeRecption_r17_specs_239	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_slotBasedDynamicPUCCH_Rep_r17_value2enum_241[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_slotBasedDynamicPUCCH_Rep_r17_enum2value_241[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_slotBasedDynamicPUCCH_Rep_r17_specs_241 = {
	asn_MAP_NR_slotBasedDynamicPUCCH_Rep_r17_value2enum_241,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_slotBasedDynamicPUCCH_Rep_r17_enum2value_241,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_slotBasedDynamicPUCCH_Rep_r17_tags_241[] = {
	(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_slotBasedDynamicPUCCH_Rep_r17_241 = {
	"slotBasedDynamicPUCCH-Rep-r17",
	"slotBasedDynamicPUCCH-Rep-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_slotBasedDynamicPUCCH_Rep_r17_tags_241,
	sizeof(asn_DEF_NR_slotBasedDynamicPUCCH_Rep_r17_tags_241)
		/sizeof(asn_DEF_NR_slotBasedDynamicPUCCH_Rep_r17_tags_241[0]) - 1, /* 1 */
	asn_DEF_NR_slotBasedDynamicPUCCH_Rep_r17_tags_241,	/* Same as above */
	sizeof(asn_DEF_NR_slotBasedDynamicPUCCH_Rep_r17_tags_241)
		/sizeof(asn_DEF_NR_slotBasedDynamicPUCCH_Rep_r17_tags_241[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_slotBasedDynamicPUCCH_Rep_r17_constr_241,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_slotBasedDynamicPUCCH_Rep_r17_specs_241	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_non_SharedSpectrumChAccess_r17_value2enum_244[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_non_SharedSpectrumChAccess_r17_enum2value_244[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_non_SharedSpectrumChAccess_r17_specs_244 = {
	asn_MAP_NR_non_SharedSpectrumChAccess_r17_value2enum_244,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_non_SharedSpectrumChAccess_r17_enum2value_244,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_non_SharedSpectrumChAccess_r17_tags_244[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_non_SharedSpectrumChAccess_r17_244 = {
	"non-SharedSpectrumChAccess-r17",
	"non-SharedSpectrumChAccess-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_non_SharedSpectrumChAccess_r17_tags_244,
	sizeof(asn_DEF_NR_non_SharedSpectrumChAccess_r17_tags_244)
		/sizeof(asn_DEF_NR_non_SharedSpectrumChAccess_r17_tags_244[0]) - 1, /* 1 */
	asn_DEF_NR_non_SharedSpectrumChAccess_r17_tags_244,	/* Same as above */
	sizeof(asn_DEF_NR_non_SharedSpectrumChAccess_r17_tags_244)
		/sizeof(asn_DEF_NR_non_SharedSpectrumChAccess_r17_tags_244[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_non_SharedSpectrumChAccess_r17_constr_244,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_non_SharedSpectrumChAccess_r17_specs_244	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_sharedSpectrumChAccess_r17_value2enum_246[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_sharedSpectrumChAccess_r17_enum2value_246[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_sharedSpectrumChAccess_r17_specs_246 = {
	asn_MAP_NR_sharedSpectrumChAccess_r17_value2enum_246,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_sharedSpectrumChAccess_r17_enum2value_246,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_sharedSpectrumChAccess_r17_tags_246[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_sharedSpectrumChAccess_r17_246 = {
	"sharedSpectrumChAccess-r17",
	"sharedSpectrumChAccess-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_sharedSpectrumChAccess_r17_tags_246,
	sizeof(asn_DEF_NR_sharedSpectrumChAccess_r17_tags_246)
		/sizeof(asn_DEF_NR_sharedSpectrumChAccess_r17_tags_246[0]) - 1, /* 1 */
	asn_DEF_NR_sharedSpectrumChAccess_r17_tags_246,	/* Same as above */
	sizeof(asn_DEF_NR_sharedSpectrumChAccess_r17_tags_246)
		/sizeof(asn_DEF_NR_sharedSpectrumChAccess_r17_tags_246[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_sharedSpectrumChAccess_r17_constr_246,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_sharedSpectrumChAccess_r17_specs_246	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_sps_HARQ_ACK_Deferral_r17_243[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_Phy_ParametersCommon__ext9__sps_HARQ_ACK_Deferral_r17, non_SharedSpectrumChAccess_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_non_SharedSpectrumChAccess_r17_244,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"non-SharedSpectrumChAccess-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersCommon__ext9__sps_HARQ_ACK_Deferral_r17, sharedSpectrumChAccess_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_sharedSpectrumChAccess_r17_246,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"sharedSpectrumChAccess-r17"
		},
};
static const int asn_MAP_NR_sps_HARQ_ACK_Deferral_r17_oms_243[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_sps_HARQ_ACK_Deferral_r17_tags_243[] = {
	(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_sps_HARQ_ACK_Deferral_r17_tag2el_243[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* non-SharedSpectrumChAccess-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* sharedSpectrumChAccess-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_sps_HARQ_ACK_Deferral_r17_specs_243 = {
	sizeof(struct NR_Phy_ParametersCommon__ext9__sps_HARQ_ACK_Deferral_r17),
	offsetof(struct NR_Phy_ParametersCommon__ext9__sps_HARQ_ACK_Deferral_r17, _asn_ctx),
	asn_MAP_NR_sps_HARQ_ACK_Deferral_r17_tag2el_243,
	2,	/* Count of tags in the map */
	asn_MAP_NR_sps_HARQ_ACK_Deferral_r17_oms_243,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_sps_HARQ_ACK_Deferral_r17_243 = {
	"sps-HARQ-ACK-Deferral-r17",
	"sps-HARQ-ACK-Deferral-r17",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_sps_HARQ_ACK_Deferral_r17_tags_243,
	sizeof(asn_DEF_NR_sps_HARQ_ACK_Deferral_r17_tags_243)
		/sizeof(asn_DEF_NR_sps_HARQ_ACK_Deferral_r17_tags_243[0]) - 1, /* 1 */
	asn_DEF_NR_sps_HARQ_ACK_Deferral_r17_tags_243,	/* Same as above */
	sizeof(asn_DEF_NR_sps_HARQ_ACK_Deferral_r17_tags_243)
		/sizeof(asn_DEF_NR_sps_HARQ_ACK_Deferral_r17_tags_243[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_sps_HARQ_ACK_Deferral_r17_243,
	2,	/* Elements count */
	&asn_SPC_NR_sps_HARQ_ACK_Deferral_r17_specs_243	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_mTRP_PDCCH_singleSpan_r17_value2enum_249[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_mTRP_PDCCH_singleSpan_r17_enum2value_249[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_mTRP_PDCCH_singleSpan_r17_specs_249 = {
	asn_MAP_NR_mTRP_PDCCH_singleSpan_r17_value2enum_249,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_mTRP_PDCCH_singleSpan_r17_enum2value_249,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_mTRP_PDCCH_singleSpan_r17_tags_249[] = {
	(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mTRP_PDCCH_singleSpan_r17_249 = {
	"mTRP-PDCCH-singleSpan-r17",
	"mTRP-PDCCH-singleSpan-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_mTRP_PDCCH_singleSpan_r17_tags_249,
	sizeof(asn_DEF_NR_mTRP_PDCCH_singleSpan_r17_tags_249)
		/sizeof(asn_DEF_NR_mTRP_PDCCH_singleSpan_r17_tags_249[0]) - 1, /* 1 */
	asn_DEF_NR_mTRP_PDCCH_singleSpan_r17_tags_249,	/* Same as above */
	sizeof(asn_DEF_NR_mTRP_PDCCH_singleSpan_r17_tags_249)
		/sizeof(asn_DEF_NR_mTRP_PDCCH_singleSpan_r17_tags_249[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_mTRP_PDCCH_singleSpan_r17_constr_249,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_mTRP_PDCCH_singleSpan_r17_specs_249	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_supportedActivatedPRS_ProcessingWindow_r17_value2enum_251[] = {
	{ 0,	2,	"n2" },
	{ 1,	2,	"n3" },
	{ 2,	2,	"n4" }
};
static const unsigned int asn_MAP_NR_supportedActivatedPRS_ProcessingWindow_r17_enum2value_251[] = {
	0,	/* n2(0) */
	1,	/* n3(1) */
	2	/* n4(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_supportedActivatedPRS_ProcessingWindow_r17_specs_251 = {
	asn_MAP_NR_supportedActivatedPRS_ProcessingWindow_r17_value2enum_251,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_supportedActivatedPRS_ProcessingWindow_r17_enum2value_251,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_supportedActivatedPRS_ProcessingWindow_r17_tags_251[] = {
	(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_supportedActivatedPRS_ProcessingWindow_r17_251 = {
	"supportedActivatedPRS-ProcessingWindow-r17",
	"supportedActivatedPRS-ProcessingWindow-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_supportedActivatedPRS_ProcessingWindow_r17_tags_251,
	sizeof(asn_DEF_NR_supportedActivatedPRS_ProcessingWindow_r17_tags_251)
		/sizeof(asn_DEF_NR_supportedActivatedPRS_ProcessingWindow_r17_tags_251[0]) - 1, /* 1 */
	asn_DEF_NR_supportedActivatedPRS_ProcessingWindow_r17_tags_251,	/* Same as above */
	sizeof(asn_DEF_NR_supportedActivatedPRS_ProcessingWindow_r17_tags_251)
		/sizeof(asn_DEF_NR_supportedActivatedPRS_ProcessingWindow_r17_tags_251[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_supportedActivatedPRS_ProcessingWindow_r17_constr_251,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_supportedActivatedPRS_ProcessingWindow_r17_specs_251	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_cg_TimeDomainAllocationExtension_r17_value2enum_255[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_cg_TimeDomainAllocationExtension_r17_enum2value_255[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_cg_TimeDomainAllocationExtension_r17_specs_255 = {
	asn_MAP_NR_cg_TimeDomainAllocationExtension_r17_value2enum_255,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_cg_TimeDomainAllocationExtension_r17_enum2value_255,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_cg_TimeDomainAllocationExtension_r17_tags_255[] = {
	(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_cg_TimeDomainAllocationExtension_r17_255 = {
	"cg-TimeDomainAllocationExtension-r17",
	"cg-TimeDomainAllocationExtension-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_cg_TimeDomainAllocationExtension_r17_tags_255,
	sizeof(asn_DEF_NR_cg_TimeDomainAllocationExtension_r17_tags_255)
		/sizeof(asn_DEF_NR_cg_TimeDomainAllocationExtension_r17_tags_255[0]) - 1, /* 1 */
	asn_DEF_NR_cg_TimeDomainAllocationExtension_r17_tags_255,	/* Same as above */
	sizeof(asn_DEF_NR_cg_TimeDomainAllocationExtension_r17_tags_255)
		/sizeof(asn_DEF_NR_cg_TimeDomainAllocationExtension_r17_tags_255[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_cg_TimeDomainAllocationExtension_r17_constr_255,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_cg_TimeDomainAllocationExtension_r17_specs_255	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext9_222[] = {
	{ ATF_POINTER, 15, offsetof(struct NR_Phy_ParametersCommon__ext9, guardSymbolReportReception_IAB_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_guardSymbolReportReception_IAB_r17_223,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"guardSymbolReportReception-IAB-r17"
		},
	{ ATF_POINTER, 14, offsetof(struct NR_Phy_ParametersCommon__ext9, restricted_IAB_DU_BeamReception_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_restricted_IAB_DU_BeamReception_r17_225,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"restricted-IAB-DU-BeamReception-r17"
		},
	{ ATF_POINTER, 13, offsetof(struct NR_Phy_ParametersCommon__ext9, recommended_IAB_MT_BeamTransmission_r17),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_recommended_IAB_MT_BeamTransmission_r17_227,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"recommended-IAB-MT-BeamTransmission-r17"
		},
	{ ATF_POINTER, 12, offsetof(struct NR_Phy_ParametersCommon__ext9, case6_TimingAlignmentReception_IAB_r17),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_case6_TimingAlignmentReception_IAB_r17_229,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"case6-TimingAlignmentReception-IAB-r17"
		},
	{ ATF_POINTER, 11, offsetof(struct NR_Phy_ParametersCommon__ext9, case7_TimingAlignmentReception_IAB_r17),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_case7_TimingAlignmentReception_IAB_r17_231,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"case7-TimingAlignmentReception-IAB-r17"
		},
	{ ATF_POINTER, 10, offsetof(struct NR_Phy_ParametersCommon__ext9, dl_tx_PowerAdjustment_IAB_r17),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dl_tx_PowerAdjustment_IAB_r17_233,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dl-tx-PowerAdjustment-IAB-r17"
		},
	{ ATF_POINTER, 9, offsetof(struct NR_Phy_ParametersCommon__ext9, desired_ul_tx_PowerAdjustment_r17),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_desired_ul_tx_PowerAdjustment_r17_235,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"desired-ul-tx-PowerAdjustment-r17"
		},
	{ ATF_POINTER, 8, offsetof(struct NR_Phy_ParametersCommon__ext9, fdm_SoftResourceAvailability_DynamicIndication_r17),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_fdm_SoftResourceAvailability_DynamicIndication_r17_237,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"fdm-SoftResourceAvailability-DynamicIndication-r17"
		},
	{ ATF_POINTER, 7, offsetof(struct NR_Phy_ParametersCommon__ext9, updated_T_DeltaRangeRecption_r17),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_updated_T_DeltaRangeRecption_r17_239,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"updated-T-DeltaRangeRecption-r17"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_Phy_ParametersCommon__ext9, slotBasedDynamicPUCCH_Rep_r17),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_slotBasedDynamicPUCCH_Rep_r17_241,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"slotBasedDynamicPUCCH-Rep-r17"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_Phy_ParametersCommon__ext9, sps_HARQ_ACK_Deferral_r17),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		0,
		&asn_DEF_NR_sps_HARQ_ACK_Deferral_r17_243,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"sps-HARQ-ACK-Deferral-r17"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_Phy_ParametersCommon__ext9, unifiedJointTCI_commonUpdate_r17),
		(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_unifiedJointTCI_commonUpdate_r17_constr_248,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_unifiedJointTCI_commonUpdate_r17_constraint_222
		},
		0, 0, /* No default value */
		"unifiedJointTCI-commonUpdate-r17"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_Phy_ParametersCommon__ext9, mTRP_PDCCH_singleSpan_r17),
		(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_mTRP_PDCCH_singleSpan_r17_249,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mTRP-PDCCH-singleSpan-r17"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_Phy_ParametersCommon__ext9, supportedActivatedPRS_ProcessingWindow_r17),
		(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_supportedActivatedPRS_ProcessingWindow_r17_251,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedActivatedPRS-ProcessingWindow-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersCommon__ext9, cg_TimeDomainAllocationExtension_r17),
		(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_cg_TimeDomainAllocationExtension_r17_255,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"cg-TimeDomainAllocationExtension-r17"
		},
};
static const int asn_MAP_NR_ext9_oms_222[] = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14 };
static const ber_tlv_tag_t asn_DEF_NR_ext9_tags_222[] = {
	(ASN_TAG_CLASS_CONTEXT | (42 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext9_tag2el_222[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* guardSymbolReportReception-IAB-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* restricted-IAB-DU-BeamReception-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* recommended-IAB-MT-BeamTransmission-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* case6-TimingAlignmentReception-IAB-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* case7-TimingAlignmentReception-IAB-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* dl-tx-PowerAdjustment-IAB-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* desired-ul-tx-PowerAdjustment-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* fdm-SoftResourceAvailability-DynamicIndication-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* updated-T-DeltaRangeRecption-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* slotBasedDynamicPUCCH-Rep-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 }, /* sps-HARQ-ACK-Deferral-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (11 << 2)), 11, 0, 0 }, /* unifiedJointTCI-commonUpdate-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (12 << 2)), 12, 0, 0 }, /* mTRP-PDCCH-singleSpan-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (13 << 2)), 13, 0, 0 }, /* supportedActivatedPRS-ProcessingWindow-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (14 << 2)), 14, 0, 0 } /* cg-TimeDomainAllocationExtension-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext9_specs_222 = {
	sizeof(struct NR_Phy_ParametersCommon__ext9),
	offsetof(struct NR_Phy_ParametersCommon__ext9, _asn_ctx),
	asn_MAP_NR_ext9_tag2el_222,
	15,	/* Count of tags in the map */
	asn_MAP_NR_ext9_oms_222,	/* Optional members */
	15, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext9_222 = {
	"ext9",
	"ext9",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext9_tags_222,
	sizeof(asn_DEF_NR_ext9_tags_222)
		/sizeof(asn_DEF_NR_ext9_tags_222[0]) - 1, /* 1 */
	asn_DEF_NR_ext9_tags_222,	/* Same as above */
	sizeof(asn_DEF_NR_ext9_tags_222)
		/sizeof(asn_DEF_NR_ext9_tags_222[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext9_222,
	15,	/* Elements count */
	&asn_SPC_NR_ext9_specs_222	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_ta_BasedPDC_TN_NonSharedSpectrumChAccess_r17_value2enum_258[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_ta_BasedPDC_TN_NonSharedSpectrumChAccess_r17_enum2value_258[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ta_BasedPDC_TN_NonSharedSpectrumChAccess_r17_specs_258 = {
	asn_MAP_NR_ta_BasedPDC_TN_NonSharedSpectrumChAccess_r17_value2enum_258,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ta_BasedPDC_TN_NonSharedSpectrumChAccess_r17_enum2value_258,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ta_BasedPDC_TN_NonSharedSpectrumChAccess_r17_tags_258[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ta_BasedPDC_TN_NonSharedSpectrumChAccess_r17_258 = {
	"ta-BasedPDC-TN-NonSharedSpectrumChAccess-r17",
	"ta-BasedPDC-TN-NonSharedSpectrumChAccess-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ta_BasedPDC_TN_NonSharedSpectrumChAccess_r17_tags_258,
	sizeof(asn_DEF_NR_ta_BasedPDC_TN_NonSharedSpectrumChAccess_r17_tags_258)
		/sizeof(asn_DEF_NR_ta_BasedPDC_TN_NonSharedSpectrumChAccess_r17_tags_258[0]) - 1, /* 1 */
	asn_DEF_NR_ta_BasedPDC_TN_NonSharedSpectrumChAccess_r17_tags_258,	/* Same as above */
	sizeof(asn_DEF_NR_ta_BasedPDC_TN_NonSharedSpectrumChAccess_r17_tags_258)
		/sizeof(asn_DEF_NR_ta_BasedPDC_TN_NonSharedSpectrumChAccess_r17_tags_258[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ta_BasedPDC_TN_NonSharedSpectrumChAccess_r17_constr_258,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ta_BasedPDC_TN_NonSharedSpectrumChAccess_r17_specs_258	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_directionalCollisionDC_IAB_r17_value2enum_260[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_directionalCollisionDC_IAB_r17_enum2value_260[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_directionalCollisionDC_IAB_r17_specs_260 = {
	asn_MAP_NR_directionalCollisionDC_IAB_r17_value2enum_260,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_directionalCollisionDC_IAB_r17_enum2value_260,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_directionalCollisionDC_IAB_r17_tags_260[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_directionalCollisionDC_IAB_r17_260 = {
	"directionalCollisionDC-IAB-r17",
	"directionalCollisionDC-IAB-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_directionalCollisionDC_IAB_r17_tags_260,
	sizeof(asn_DEF_NR_directionalCollisionDC_IAB_r17_tags_260)
		/sizeof(asn_DEF_NR_directionalCollisionDC_IAB_r17_tags_260[0]) - 1, /* 1 */
	asn_DEF_NR_directionalCollisionDC_IAB_r17_tags_260,	/* Same as above */
	sizeof(asn_DEF_NR_directionalCollisionDC_IAB_r17_tags_260)
		/sizeof(asn_DEF_NR_directionalCollisionDC_IAB_r17_tags_260[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_directionalCollisionDC_IAB_r17_constr_260,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_directionalCollisionDC_IAB_r17_specs_260	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext10_257[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_Phy_ParametersCommon__ext10, ta_BasedPDC_TN_NonSharedSpectrumChAccess_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ta_BasedPDC_TN_NonSharedSpectrumChAccess_r17_258,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ta-BasedPDC-TN-NonSharedSpectrumChAccess-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersCommon__ext10, directionalCollisionDC_IAB_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_directionalCollisionDC_IAB_r17_260,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"directionalCollisionDC-IAB-r17"
		},
};
static const int asn_MAP_NR_ext10_oms_257[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_ext10_tags_257[] = {
	(ASN_TAG_CLASS_CONTEXT | (43 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext10_tag2el_257[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* ta-BasedPDC-TN-NonSharedSpectrumChAccess-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* directionalCollisionDC-IAB-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext10_specs_257 = {
	sizeof(struct NR_Phy_ParametersCommon__ext10),
	offsetof(struct NR_Phy_ParametersCommon__ext10, _asn_ctx),
	asn_MAP_NR_ext10_tag2el_257,
	2,	/* Count of tags in the map */
	asn_MAP_NR_ext10_oms_257,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext10_257 = {
	"ext10",
	"ext10",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext10_tags_257,
	sizeof(asn_DEF_NR_ext10_tags_257)
		/sizeof(asn_DEF_NR_ext10_tags_257[0]) - 1, /* 1 */
	asn_DEF_NR_ext10_tags_257,	/* Same as above */
	sizeof(asn_DEF_NR_ext10_tags_257)
		/sizeof(asn_DEF_NR_ext10_tags_257[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext10_257,
	2,	/* Elements count */
	&asn_SPC_NR_ext10_specs_257	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_priorityIndicatorInDCI_Multicast_r17_value2enum_263[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_priorityIndicatorInDCI_Multicast_r17_enum2value_263[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_priorityIndicatorInDCI_Multicast_r17_specs_263 = {
	asn_MAP_NR_priorityIndicatorInDCI_Multicast_r17_value2enum_263,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_priorityIndicatorInDCI_Multicast_r17_enum2value_263,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_priorityIndicatorInDCI_Multicast_r17_tags_263[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_priorityIndicatorInDCI_Multicast_r17_263 = {
	"priorityIndicatorInDCI-Multicast-r17",
	"priorityIndicatorInDCI-Multicast-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_priorityIndicatorInDCI_Multicast_r17_tags_263,
	sizeof(asn_DEF_NR_priorityIndicatorInDCI_Multicast_r17_tags_263)
		/sizeof(asn_DEF_NR_priorityIndicatorInDCI_Multicast_r17_tags_263[0]) - 1, /* 1 */
	asn_DEF_NR_priorityIndicatorInDCI_Multicast_r17_tags_263,	/* Same as above */
	sizeof(asn_DEF_NR_priorityIndicatorInDCI_Multicast_r17_tags_263)
		/sizeof(asn_DEF_NR_priorityIndicatorInDCI_Multicast_r17_tags_263[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_priorityIndicatorInDCI_Multicast_r17_constr_263,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_priorityIndicatorInDCI_Multicast_r17_specs_263	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_priorityIndicatorInDCI_SPS_Multicast_r17_value2enum_265[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_priorityIndicatorInDCI_SPS_Multicast_r17_enum2value_265[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_priorityIndicatorInDCI_SPS_Multicast_r17_specs_265 = {
	asn_MAP_NR_priorityIndicatorInDCI_SPS_Multicast_r17_value2enum_265,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_priorityIndicatorInDCI_SPS_Multicast_r17_enum2value_265,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_priorityIndicatorInDCI_SPS_Multicast_r17_tags_265[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_priorityIndicatorInDCI_SPS_Multicast_r17_265 = {
	"priorityIndicatorInDCI-SPS-Multicast-r17",
	"priorityIndicatorInDCI-SPS-Multicast-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_priorityIndicatorInDCI_SPS_Multicast_r17_tags_265,
	sizeof(asn_DEF_NR_priorityIndicatorInDCI_SPS_Multicast_r17_tags_265)
		/sizeof(asn_DEF_NR_priorityIndicatorInDCI_SPS_Multicast_r17_tags_265[0]) - 1, /* 1 */
	asn_DEF_NR_priorityIndicatorInDCI_SPS_Multicast_r17_tags_265,	/* Same as above */
	sizeof(asn_DEF_NR_priorityIndicatorInDCI_SPS_Multicast_r17_tags_265)
		/sizeof(asn_DEF_NR_priorityIndicatorInDCI_SPS_Multicast_r17_tags_265[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_priorityIndicatorInDCI_SPS_Multicast_r17_constr_265,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_priorityIndicatorInDCI_SPS_Multicast_r17_specs_265	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_twoHARQ_ACK_CodebookForUnicastAndMulticast_r17_value2enum_267[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_twoHARQ_ACK_CodebookForUnicastAndMulticast_r17_enum2value_267[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_twoHARQ_ACK_CodebookForUnicastAndMulticast_r17_specs_267 = {
	asn_MAP_NR_twoHARQ_ACK_CodebookForUnicastAndMulticast_r17_value2enum_267,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_twoHARQ_ACK_CodebookForUnicastAndMulticast_r17_enum2value_267,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_twoHARQ_ACK_CodebookForUnicastAndMulticast_r17_tags_267[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_twoHARQ_ACK_CodebookForUnicastAndMulticast_r17_267 = {
	"twoHARQ-ACK-CodebookForUnicastAndMulticast-r17",
	"twoHARQ-ACK-CodebookForUnicastAndMulticast-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_twoHARQ_ACK_CodebookForUnicastAndMulticast_r17_tags_267,
	sizeof(asn_DEF_NR_twoHARQ_ACK_CodebookForUnicastAndMulticast_r17_tags_267)
		/sizeof(asn_DEF_NR_twoHARQ_ACK_CodebookForUnicastAndMulticast_r17_tags_267[0]) - 1, /* 1 */
	asn_DEF_NR_twoHARQ_ACK_CodebookForUnicastAndMulticast_r17_tags_267,	/* Same as above */
	sizeof(asn_DEF_NR_twoHARQ_ACK_CodebookForUnicastAndMulticast_r17_tags_267)
		/sizeof(asn_DEF_NR_twoHARQ_ACK_CodebookForUnicastAndMulticast_r17_tags_267[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_twoHARQ_ACK_CodebookForUnicastAndMulticast_r17_constr_267,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_twoHARQ_ACK_CodebookForUnicastAndMulticast_r17_specs_267	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_multiPUCCH_HARQ_ACK_ForMulticastUnicast_r17_value2enum_269[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_multiPUCCH_HARQ_ACK_ForMulticastUnicast_r17_enum2value_269[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_multiPUCCH_HARQ_ACK_ForMulticastUnicast_r17_specs_269 = {
	asn_MAP_NR_multiPUCCH_HARQ_ACK_ForMulticastUnicast_r17_value2enum_269,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_multiPUCCH_HARQ_ACK_ForMulticastUnicast_r17_enum2value_269,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_multiPUCCH_HARQ_ACK_ForMulticastUnicast_r17_tags_269[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_multiPUCCH_HARQ_ACK_ForMulticastUnicast_r17_269 = {
	"multiPUCCH-HARQ-ACK-ForMulticastUnicast-r17",
	"multiPUCCH-HARQ-ACK-ForMulticastUnicast-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_multiPUCCH_HARQ_ACK_ForMulticastUnicast_r17_tags_269,
	sizeof(asn_DEF_NR_multiPUCCH_HARQ_ACK_ForMulticastUnicast_r17_tags_269)
		/sizeof(asn_DEF_NR_multiPUCCH_HARQ_ACK_ForMulticastUnicast_r17_tags_269[0]) - 1, /* 1 */
	asn_DEF_NR_multiPUCCH_HARQ_ACK_ForMulticastUnicast_r17_tags_269,	/* Same as above */
	sizeof(asn_DEF_NR_multiPUCCH_HARQ_ACK_ForMulticastUnicast_r17_tags_269)
		/sizeof(asn_DEF_NR_multiPUCCH_HARQ_ACK_ForMulticastUnicast_r17_tags_269[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_multiPUCCH_HARQ_ACK_ForMulticastUnicast_r17_constr_269,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_multiPUCCH_HARQ_ACK_ForMulticastUnicast_r17_specs_269	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_srs_AdditionalRepetition_r17_value2enum_271[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_srs_AdditionalRepetition_r17_enum2value_271[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_srs_AdditionalRepetition_r17_specs_271 = {
	asn_MAP_NR_srs_AdditionalRepetition_r17_value2enum_271,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_srs_AdditionalRepetition_r17_enum2value_271,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_srs_AdditionalRepetition_r17_tags_271[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_srs_AdditionalRepetition_r17_271 = {
	"srs-AdditionalRepetition-r17",
	"srs-AdditionalRepetition-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_srs_AdditionalRepetition_r17_tags_271,
	sizeof(asn_DEF_NR_srs_AdditionalRepetition_r17_tags_271)
		/sizeof(asn_DEF_NR_srs_AdditionalRepetition_r17_tags_271[0]) - 1, /* 1 */
	asn_DEF_NR_srs_AdditionalRepetition_r17_tags_271,	/* Same as above */
	sizeof(asn_DEF_NR_srs_AdditionalRepetition_r17_tags_271)
		/sizeof(asn_DEF_NR_srs_AdditionalRepetition_r17_tags_271[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_srs_AdditionalRepetition_r17_constr_271,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_srs_AdditionalRepetition_r17_specs_271	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pusch_Repetition_CG_SDT_r17_value2enum_273[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_pusch_Repetition_CG_SDT_r17_enum2value_273[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pusch_Repetition_CG_SDT_r17_specs_273 = {
	asn_MAP_NR_pusch_Repetition_CG_SDT_r17_value2enum_273,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pusch_Repetition_CG_SDT_r17_enum2value_273,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pusch_Repetition_CG_SDT_r17_tags_273[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pusch_Repetition_CG_SDT_r17_273 = {
	"pusch-Repetition-CG-SDT-r17",
	"pusch-Repetition-CG-SDT-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pusch_Repetition_CG_SDT_r17_tags_273,
	sizeof(asn_DEF_NR_pusch_Repetition_CG_SDT_r17_tags_273)
		/sizeof(asn_DEF_NR_pusch_Repetition_CG_SDT_r17_tags_273[0]) - 1, /* 1 */
	asn_DEF_NR_pusch_Repetition_CG_SDT_r17_tags_273,	/* Same as above */
	sizeof(asn_DEF_NR_pusch_Repetition_CG_SDT_r17_tags_273)
		/sizeof(asn_DEF_NR_pusch_Repetition_CG_SDT_r17_tags_273[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pusch_Repetition_CG_SDT_r17_constr_273,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pusch_Repetition_CG_SDT_r17_specs_273	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext11_262[] = {
	{ ATF_POINTER, 6, offsetof(struct NR_Phy_ParametersCommon__ext11, priorityIndicatorInDCI_Multicast_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_priorityIndicatorInDCI_Multicast_r17_263,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"priorityIndicatorInDCI-Multicast-r17"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_Phy_ParametersCommon__ext11, priorityIndicatorInDCI_SPS_Multicast_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_priorityIndicatorInDCI_SPS_Multicast_r17_265,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"priorityIndicatorInDCI-SPS-Multicast-r17"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_Phy_ParametersCommon__ext11, twoHARQ_ACK_CodebookForUnicastAndMulticast_r17),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_twoHARQ_ACK_CodebookForUnicastAndMulticast_r17_267,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"twoHARQ-ACK-CodebookForUnicastAndMulticast-r17"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_Phy_ParametersCommon__ext11, multiPUCCH_HARQ_ACK_ForMulticastUnicast_r17),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_multiPUCCH_HARQ_ACK_ForMulticastUnicast_r17_269,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"multiPUCCH-HARQ-ACK-ForMulticastUnicast-r17"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_Phy_ParametersCommon__ext11, srs_AdditionalRepetition_r17),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_srs_AdditionalRepetition_r17_271,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"srs-AdditionalRepetition-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersCommon__ext11, pusch_Repetition_CG_SDT_r17),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pusch_Repetition_CG_SDT_r17_273,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pusch-Repetition-CG-SDT-r17"
		},
};
static const int asn_MAP_NR_ext11_oms_262[] = { 0, 1, 2, 3, 4, 5 };
static const ber_tlv_tag_t asn_DEF_NR_ext11_tags_262[] = {
	(ASN_TAG_CLASS_CONTEXT | (44 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext11_tag2el_262[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* priorityIndicatorInDCI-Multicast-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* priorityIndicatorInDCI-SPS-Multicast-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* twoHARQ-ACK-CodebookForUnicastAndMulticast-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* multiPUCCH-HARQ-ACK-ForMulticastUnicast-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* srs-AdditionalRepetition-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 } /* pusch-Repetition-CG-SDT-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext11_specs_262 = {
	sizeof(struct NR_Phy_ParametersCommon__ext11),
	offsetof(struct NR_Phy_ParametersCommon__ext11, _asn_ctx),
	asn_MAP_NR_ext11_tag2el_262,
	6,	/* Count of tags in the map */
	asn_MAP_NR_ext11_oms_262,	/* Optional members */
	6, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext11_262 = {
	"ext11",
	"ext11",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext11_tags_262,
	sizeof(asn_DEF_NR_ext11_tags_262)
		/sizeof(asn_DEF_NR_ext11_tags_262[0]) - 1, /* 1 */
	asn_DEF_NR_ext11_tags_262,	/* Same as above */
	sizeof(asn_DEF_NR_ext11_tags_262)
		/sizeof(asn_DEF_NR_ext11_tags_262[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext11_262,
	6,	/* Elements count */
	&asn_SPC_NR_ext11_specs_262	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_Phy_ParametersCommon_1[] = {
	{ ATF_POINTER, 45, offsetof(struct NR_Phy_ParametersCommon, csi_RS_CFRA_ForHO),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_csi_RS_CFRA_ForHO_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"csi-RS-CFRA-ForHO"
		},
	{ ATF_POINTER, 44, offsetof(struct NR_Phy_ParametersCommon, dynamicPRB_BundlingDL),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dynamicPRB_BundlingDL_4,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dynamicPRB-BundlingDL"
		},
	{ ATF_POINTER, 43, offsetof(struct NR_Phy_ParametersCommon, sp_CSI_ReportPUCCH),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_sp_CSI_ReportPUCCH_6,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"sp-CSI-ReportPUCCH"
		},
	{ ATF_POINTER, 42, offsetof(struct NR_Phy_ParametersCommon, sp_CSI_ReportPUSCH),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_sp_CSI_ReportPUSCH_8,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"sp-CSI-ReportPUSCH"
		},
	{ ATF_POINTER, 41, offsetof(struct NR_Phy_ParametersCommon, nzp_CSI_RS_IntefMgmt),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_nzp_CSI_RS_IntefMgmt_10,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"nzp-CSI-RS-IntefMgmt"
		},
	{ ATF_POINTER, 40, offsetof(struct NR_Phy_ParametersCommon, type2_SP_CSI_Feedback_LongPUCCH),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_type2_SP_CSI_Feedback_LongPUCCH_12,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"type2-SP-CSI-Feedback-LongPUCCH"
		},
	{ ATF_POINTER, 39, offsetof(struct NR_Phy_ParametersCommon, precoderGranularityCORESET),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_precoderGranularityCORESET_14,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"precoderGranularityCORESET"
		},
	{ ATF_POINTER, 38, offsetof(struct NR_Phy_ParametersCommon, dynamicHARQ_ACK_Codebook),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dynamicHARQ_ACK_Codebook_16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dynamicHARQ-ACK-Codebook"
		},
	{ ATF_POINTER, 37, offsetof(struct NR_Phy_ParametersCommon, semiStaticHARQ_ACK_Codebook),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_semiStaticHARQ_ACK_Codebook_18,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"semiStaticHARQ-ACK-Codebook"
		},
	{ ATF_POINTER, 36, offsetof(struct NR_Phy_ParametersCommon, spatialBundlingHARQ_ACK),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_spatialBundlingHARQ_ACK_20,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"spatialBundlingHARQ-ACK"
		},
	{ ATF_POINTER, 35, offsetof(struct NR_Phy_ParametersCommon, dynamicBetaOffsetInd_HARQ_ACK_CSI),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dynamicBetaOffsetInd_HARQ_ACK_CSI_22,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dynamicBetaOffsetInd-HARQ-ACK-CSI"
		},
	{ ATF_POINTER, 34, offsetof(struct NR_Phy_ParametersCommon, pucch_Repetition_F1_3_4),
		(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pucch_Repetition_F1_3_4_24,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pucch-Repetition-F1-3-4"
		},
	{ ATF_POINTER, 33, offsetof(struct NR_Phy_ParametersCommon, ra_Type0_PUSCH),
		(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ra_Type0_PUSCH_26,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ra-Type0-PUSCH"
		},
	{ ATF_POINTER, 32, offsetof(struct NR_Phy_ParametersCommon, dynamicSwitchRA_Type0_1_PDSCH),
		(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dynamicSwitchRA_Type0_1_PDSCH_28,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dynamicSwitchRA-Type0-1-PDSCH"
		},
	{ ATF_POINTER, 31, offsetof(struct NR_Phy_ParametersCommon, dynamicSwitchRA_Type0_1_PUSCH),
		(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dynamicSwitchRA_Type0_1_PUSCH_30,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dynamicSwitchRA-Type0-1-PUSCH"
		},
	{ ATF_POINTER, 30, offsetof(struct NR_Phy_ParametersCommon, pdsch_MappingTypeA),
		(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pdsch_MappingTypeA_32,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pdsch-MappingTypeA"
		},
	{ ATF_POINTER, 29, offsetof(struct NR_Phy_ParametersCommon, pdsch_MappingTypeB),
		(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pdsch_MappingTypeB_34,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pdsch-MappingTypeB"
		},
	{ ATF_POINTER, 28, offsetof(struct NR_Phy_ParametersCommon, interleavingVRB_ToPRB_PDSCH),
		(ASN_TAG_CLASS_CONTEXT | (17 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_interleavingVRB_ToPRB_PDSCH_36,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"interleavingVRB-ToPRB-PDSCH"
		},
	{ ATF_POINTER, 27, offsetof(struct NR_Phy_ParametersCommon, interSlotFreqHopping_PUSCH),
		(ASN_TAG_CLASS_CONTEXT | (18 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_interSlotFreqHopping_PUSCH_38,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"interSlotFreqHopping-PUSCH"
		},
	{ ATF_POINTER, 26, offsetof(struct NR_Phy_ParametersCommon, type1_PUSCH_RepetitionMultiSlots),
		(ASN_TAG_CLASS_CONTEXT | (19 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_type1_PUSCH_RepetitionMultiSlots_40,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"type1-PUSCH-RepetitionMultiSlots"
		},
	{ ATF_POINTER, 25, offsetof(struct NR_Phy_ParametersCommon, type2_PUSCH_RepetitionMultiSlots),
		(ASN_TAG_CLASS_CONTEXT | (20 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_type2_PUSCH_RepetitionMultiSlots_42,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"type2-PUSCH-RepetitionMultiSlots"
		},
	{ ATF_POINTER, 24, offsetof(struct NR_Phy_ParametersCommon, pusch_RepetitionMultiSlots),
		(ASN_TAG_CLASS_CONTEXT | (21 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pusch_RepetitionMultiSlots_44,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pusch-RepetitionMultiSlots"
		},
	{ ATF_POINTER, 23, offsetof(struct NR_Phy_ParametersCommon, pdsch_RepetitionMultiSlots),
		(ASN_TAG_CLASS_CONTEXT | (22 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pdsch_RepetitionMultiSlots_46,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pdsch-RepetitionMultiSlots"
		},
	{ ATF_POINTER, 22, offsetof(struct NR_Phy_ParametersCommon, downlinkSPS),
		(ASN_TAG_CLASS_CONTEXT | (23 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_downlinkSPS_48,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"downlinkSPS"
		},
	{ ATF_POINTER, 21, offsetof(struct NR_Phy_ParametersCommon, configuredUL_GrantType1),
		(ASN_TAG_CLASS_CONTEXT | (24 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_configuredUL_GrantType1_50,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"configuredUL-GrantType1"
		},
	{ ATF_POINTER, 20, offsetof(struct NR_Phy_ParametersCommon, configuredUL_GrantType2),
		(ASN_TAG_CLASS_CONTEXT | (25 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_configuredUL_GrantType2_52,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"configuredUL-GrantType2"
		},
	{ ATF_POINTER, 19, offsetof(struct NR_Phy_ParametersCommon, pre_EmptIndication_DL),
		(ASN_TAG_CLASS_CONTEXT | (26 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pre_EmptIndication_DL_54,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pre-EmptIndication-DL"
		},
	{ ATF_POINTER, 18, offsetof(struct NR_Phy_ParametersCommon, cbg_TransIndication_DL),
		(ASN_TAG_CLASS_CONTEXT | (27 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_cbg_TransIndication_DL_56,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"cbg-TransIndication-DL"
		},
	{ ATF_POINTER, 17, offsetof(struct NR_Phy_ParametersCommon, cbg_TransIndication_UL),
		(ASN_TAG_CLASS_CONTEXT | (28 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_cbg_TransIndication_UL_58,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"cbg-TransIndication-UL"
		},
	{ ATF_POINTER, 16, offsetof(struct NR_Phy_ParametersCommon, cbg_FlushIndication_DL),
		(ASN_TAG_CLASS_CONTEXT | (29 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_cbg_FlushIndication_DL_60,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"cbg-FlushIndication-DL"
		},
	{ ATF_POINTER, 15, offsetof(struct NR_Phy_ParametersCommon, dynamicHARQ_ACK_CodeB_CBG_Retx_DL),
		(ASN_TAG_CLASS_CONTEXT | (30 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dynamicHARQ_ACK_CodeB_CBG_Retx_DL_62,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dynamicHARQ-ACK-CodeB-CBG-Retx-DL"
		},
	{ ATF_POINTER, 14, offsetof(struct NR_Phy_ParametersCommon, rateMatchingResrcSetSemi_Static),
		(ASN_TAG_CLASS_CONTEXT | (31 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_rateMatchingResrcSetSemi_Static_64,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"rateMatchingResrcSetSemi-Static"
		},
	{ ATF_POINTER, 13, offsetof(struct NR_Phy_ParametersCommon, rateMatchingResrcSetDynamic),
		(ASN_TAG_CLASS_CONTEXT | (32 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_rateMatchingResrcSetDynamic_66,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"rateMatchingResrcSetDynamic"
		},
	{ ATF_POINTER, 12, offsetof(struct NR_Phy_ParametersCommon, bwp_SwitchingDelay),
		(ASN_TAG_CLASS_CONTEXT | (33 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_bwp_SwitchingDelay_68,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"bwp-SwitchingDelay"
		},
	{ ATF_POINTER, 11, offsetof(struct NR_Phy_ParametersCommon, ext1),
		(ASN_TAG_CLASS_CONTEXT | (34 << 2)),
		0,
		&asn_DEF_NR_ext1_72,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
	{ ATF_POINTER, 10, offsetof(struct NR_Phy_ParametersCommon, ext2),
		(ASN_TAG_CLASS_CONTEXT | (35 << 2)),
		0,
		&asn_DEF_NR_ext2_75,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext2"
		},
	{ ATF_POINTER, 9, offsetof(struct NR_Phy_ParametersCommon, ext3),
		(ASN_TAG_CLASS_CONTEXT | (36 << 2)),
		0,
		&asn_DEF_NR_ext3_82,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext3"
		},
	{ ATF_POINTER, 8, offsetof(struct NR_Phy_ParametersCommon, ext4),
		(ASN_TAG_CLASS_CONTEXT | (37 << 2)),
		0,
		&asn_DEF_NR_ext4_84,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext4"
		},
	{ ATF_POINTER, 7, offsetof(struct NR_Phy_ParametersCommon, ext5),
		(ASN_TAG_CLASS_CONTEXT | (38 << 2)),
		0,
		&asn_DEF_NR_ext5_192,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext5"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_Phy_ParametersCommon, ext6),
		(ASN_TAG_CLASS_CONTEXT | (39 << 2)),
		0,
		&asn_DEF_NR_ext6_199,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext6"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_Phy_ParametersCommon, ext7),
		(ASN_TAG_CLASS_CONTEXT | (40 << 2)),
		0,
		&asn_DEF_NR_ext7_214,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext7"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_Phy_ParametersCommon, ext8),
		(ASN_TAG_CLASS_CONTEXT | (41 << 2)),
		0,
		&asn_DEF_NR_ext8_219,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext8"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_Phy_ParametersCommon, ext9),
		(ASN_TAG_CLASS_CONTEXT | (42 << 2)),
		0,
		&asn_DEF_NR_ext9_222,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext9"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_Phy_ParametersCommon, ext10),
		(ASN_TAG_CLASS_CONTEXT | (43 << 2)),
		0,
		&asn_DEF_NR_ext10_257,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext10"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersCommon, ext11),
		(ASN_TAG_CLASS_CONTEXT | (44 << 2)),
		0,
		&asn_DEF_NR_ext11_262,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext11"
		},
};
static const int asn_MAP_NR_Phy_ParametersCommon_oms_1[] = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44 };
static const ber_tlv_tag_t asn_DEF_NR_Phy_ParametersCommon_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_Phy_ParametersCommon_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* csi-RS-CFRA-ForHO */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* dynamicPRB-BundlingDL */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* sp-CSI-ReportPUCCH */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* sp-CSI-ReportPUSCH */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* nzp-CSI-RS-IntefMgmt */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* type2-SP-CSI-Feedback-LongPUCCH */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* precoderGranularityCORESET */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* dynamicHARQ-ACK-Codebook */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* semiStaticHARQ-ACK-Codebook */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* spatialBundlingHARQ-ACK */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 }, /* dynamicBetaOffsetInd-HARQ-ACK-CSI */
    { (ASN_TAG_CLASS_CONTEXT | (11 << 2)), 11, 0, 0 }, /* pucch-Repetition-F1-3-4 */
    { (ASN_TAG_CLASS_CONTEXT | (12 << 2)), 12, 0, 0 }, /* ra-Type0-PUSCH */
    { (ASN_TAG_CLASS_CONTEXT | (13 << 2)), 13, 0, 0 }, /* dynamicSwitchRA-Type0-1-PDSCH */
    { (ASN_TAG_CLASS_CONTEXT | (14 << 2)), 14, 0, 0 }, /* dynamicSwitchRA-Type0-1-PUSCH */
    { (ASN_TAG_CLASS_CONTEXT | (15 << 2)), 15, 0, 0 }, /* pdsch-MappingTypeA */
    { (ASN_TAG_CLASS_CONTEXT | (16 << 2)), 16, 0, 0 }, /* pdsch-MappingTypeB */
    { (ASN_TAG_CLASS_CONTEXT | (17 << 2)), 17, 0, 0 }, /* interleavingVRB-ToPRB-PDSCH */
    { (ASN_TAG_CLASS_CONTEXT | (18 << 2)), 18, 0, 0 }, /* interSlotFreqHopping-PUSCH */
    { (ASN_TAG_CLASS_CONTEXT | (19 << 2)), 19, 0, 0 }, /* type1-PUSCH-RepetitionMultiSlots */
    { (ASN_TAG_CLASS_CONTEXT | (20 << 2)), 20, 0, 0 }, /* type2-PUSCH-RepetitionMultiSlots */
    { (ASN_TAG_CLASS_CONTEXT | (21 << 2)), 21, 0, 0 }, /* pusch-RepetitionMultiSlots */
    { (ASN_TAG_CLASS_CONTEXT | (22 << 2)), 22, 0, 0 }, /* pdsch-RepetitionMultiSlots */
    { (ASN_TAG_CLASS_CONTEXT | (23 << 2)), 23, 0, 0 }, /* downlinkSPS */
    { (ASN_TAG_CLASS_CONTEXT | (24 << 2)), 24, 0, 0 }, /* configuredUL-GrantType1 */
    { (ASN_TAG_CLASS_CONTEXT | (25 << 2)), 25, 0, 0 }, /* configuredUL-GrantType2 */
    { (ASN_TAG_CLASS_CONTEXT | (26 << 2)), 26, 0, 0 }, /* pre-EmptIndication-DL */
    { (ASN_TAG_CLASS_CONTEXT | (27 << 2)), 27, 0, 0 }, /* cbg-TransIndication-DL */
    { (ASN_TAG_CLASS_CONTEXT | (28 << 2)), 28, 0, 0 }, /* cbg-TransIndication-UL */
    { (ASN_TAG_CLASS_CONTEXT | (29 << 2)), 29, 0, 0 }, /* cbg-FlushIndication-DL */
    { (ASN_TAG_CLASS_CONTEXT | (30 << 2)), 30, 0, 0 }, /* dynamicHARQ-ACK-CodeB-CBG-Retx-DL */
    { (ASN_TAG_CLASS_CONTEXT | (31 << 2)), 31, 0, 0 }, /* rateMatchingResrcSetSemi-Static */
    { (ASN_TAG_CLASS_CONTEXT | (32 << 2)), 32, 0, 0 }, /* rateMatchingResrcSetDynamic */
    { (ASN_TAG_CLASS_CONTEXT | (33 << 2)), 33, 0, 0 }, /* bwp-SwitchingDelay */
    { (ASN_TAG_CLASS_CONTEXT | (34 << 2)), 34, 0, 0 }, /* ext1 */
    { (ASN_TAG_CLASS_CONTEXT | (35 << 2)), 35, 0, 0 }, /* ext2 */
    { (ASN_TAG_CLASS_CONTEXT | (36 << 2)), 36, 0, 0 }, /* ext3 */
    { (ASN_TAG_CLASS_CONTEXT | (37 << 2)), 37, 0, 0 }, /* ext4 */
    { (ASN_TAG_CLASS_CONTEXT | (38 << 2)), 38, 0, 0 }, /* ext5 */
    { (ASN_TAG_CLASS_CONTEXT | (39 << 2)), 39, 0, 0 }, /* ext6 */
    { (ASN_TAG_CLASS_CONTEXT | (40 << 2)), 40, 0, 0 }, /* ext7 */
    { (ASN_TAG_CLASS_CONTEXT | (41 << 2)), 41, 0, 0 }, /* ext8 */
    { (ASN_TAG_CLASS_CONTEXT | (42 << 2)), 42, 0, 0 }, /* ext9 */
    { (ASN_TAG_CLASS_CONTEXT | (43 << 2)), 43, 0, 0 }, /* ext10 */
    { (ASN_TAG_CLASS_CONTEXT | (44 << 2)), 44, 0, 0 } /* ext11 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_Phy_ParametersCommon_specs_1 = {
	sizeof(struct NR_Phy_ParametersCommon),
	offsetof(struct NR_Phy_ParametersCommon, _asn_ctx),
	asn_MAP_NR_Phy_ParametersCommon_tag2el_1,
	45,	/* Count of tags in the map */
	asn_MAP_NR_Phy_ParametersCommon_oms_1,	/* Optional members */
	34, 11,	/* Root/Additions */
	34,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_Phy_ParametersCommon = {
	"Phy-ParametersCommon",
	"Phy-ParametersCommon",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_Phy_ParametersCommon_tags_1,
	sizeof(asn_DEF_NR_Phy_ParametersCommon_tags_1)
		/sizeof(asn_DEF_NR_Phy_ParametersCommon_tags_1[0]), /* 1 */
	asn_DEF_NR_Phy_ParametersCommon_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_Phy_ParametersCommon_tags_1)
		/sizeof(asn_DEF_NR_Phy_ParametersCommon_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_Phy_ParametersCommon_1,
	45,	/* Elements count */
	&asn_SPC_NR_Phy_ParametersCommon_specs_1	/* Additional specs */
};

