/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_PUSCH-PowerControl-v1610.h"

static int
memb_NR_olpc_ParameterSetDCI_0_1_r16_constraint_8(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 2L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_olpc_ParameterSetDCI_0_2_r16_constraint_8(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 2L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_sri_PUSCH_MappingToAddModList2_r17_constraint_12(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 16UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_sri_PUSCH_MappingToReleaseList2_r17_constraint_12(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 16UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_p0_PUSCH_SetList2_r17_constraint_12(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 16UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_dummy_constraint_12(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 64UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_pathlossReferenceRSToAddModListSizeExt_v1610_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 60UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_pathlossReferenceRSToReleaseListSizeExt_v1610_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 60UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_p0_PUSCH_SetList_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 16UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pathlossReferenceRSToAddModListSizeExt_v1610_constr_2 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 6,  6,  1,  60 }	/* (SIZE(1..60)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pathlossReferenceRSToReleaseListSizeExt_v1610_constr_4 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 6,  6,  1,  60 }	/* (SIZE(1..60)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_p0_PUSCH_SetList_r16_constr_6 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_olpc_ParameterSetDCI_0_1_r16_constr_9 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  1,  2 }	/* (1..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_olpc_ParameterSetDCI_0_2_r16_constr_10 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  1,  2 }	/* (1..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_sri_PUSCH_MappingToAddModList2_r17_constr_13 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_sri_PUSCH_MappingToReleaseList2_r17_constr_15 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_p0_PUSCH_SetList2_r17_constr_17 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dummy_constr_19 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 6,  6,  1,  64 }	/* (SIZE(1..64)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_sri_PUSCH_MappingToAddModList2_r17_constr_13 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_sri_PUSCH_MappingToReleaseList2_r17_constr_15 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_p0_PUSCH_SetList2_r17_constr_17 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_dummy_constr_19 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 6,  6,  1,  64 }	/* (SIZE(1..64)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_pathlossReferenceRSToAddModListSizeExt_v1610_constr_2 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 6,  6,  1,  60 }	/* (SIZE(1..60)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_pathlossReferenceRSToReleaseListSizeExt_v1610_constr_4 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 6,  6,  1,  60 }	/* (SIZE(1..60)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_p0_PUSCH_SetList_r16_constr_6 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static asn_TYPE_member_t asn_MBR_NR_pathlossReferenceRSToAddModListSizeExt_v1610_2[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_PUSCH_PathlossReferenceRS_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_pathlossReferenceRSToAddModListSizeExt_v1610_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_pathlossReferenceRSToAddModListSizeExt_v1610_specs_2 = {
	sizeof(struct NR_PUSCH_PowerControl_v1610__pathlossReferenceRSToAddModListSizeExt_v1610),
	offsetof(struct NR_PUSCH_PowerControl_v1610__pathlossReferenceRSToAddModListSizeExt_v1610, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pathlossReferenceRSToAddModListSizeExt_v1610_2 = {
	"pathlossReferenceRSToAddModListSizeExt-v1610",
	"pathlossReferenceRSToAddModListSizeExt-v1610",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_pathlossReferenceRSToAddModListSizeExt_v1610_tags_2,
	sizeof(asn_DEF_NR_pathlossReferenceRSToAddModListSizeExt_v1610_tags_2)
		/sizeof(asn_DEF_NR_pathlossReferenceRSToAddModListSizeExt_v1610_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_pathlossReferenceRSToAddModListSizeExt_v1610_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_pathlossReferenceRSToAddModListSizeExt_v1610_tags_2)
		/sizeof(asn_DEF_NR_pathlossReferenceRSToAddModListSizeExt_v1610_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pathlossReferenceRSToAddModListSizeExt_v1610_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_pathlossReferenceRSToAddModListSizeExt_v1610_2,
	1,	/* Single element */
	&asn_SPC_NR_pathlossReferenceRSToAddModListSizeExt_v1610_specs_2	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_pathlossReferenceRSToReleaseListSizeExt_v1610_4[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (2 << 2)),
		0,
		&asn_DEF_NR_PUSCH_PathlossReferenceRS_Id_v1610,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_pathlossReferenceRSToReleaseListSizeExt_v1610_tags_4[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_pathlossReferenceRSToReleaseListSizeExt_v1610_specs_4 = {
	sizeof(struct NR_PUSCH_PowerControl_v1610__pathlossReferenceRSToReleaseListSizeExt_v1610),
	offsetof(struct NR_PUSCH_PowerControl_v1610__pathlossReferenceRSToReleaseListSizeExt_v1610, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pathlossReferenceRSToReleaseListSizeExt_v1610_4 = {
	"pathlossReferenceRSToReleaseListSizeExt-v1610",
	"pathlossReferenceRSToReleaseListSizeExt-v1610",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_pathlossReferenceRSToReleaseListSizeExt_v1610_tags_4,
	sizeof(asn_DEF_NR_pathlossReferenceRSToReleaseListSizeExt_v1610_tags_4)
		/sizeof(asn_DEF_NR_pathlossReferenceRSToReleaseListSizeExt_v1610_tags_4[0]) - 1, /* 1 */
	asn_DEF_NR_pathlossReferenceRSToReleaseListSizeExt_v1610_tags_4,	/* Same as above */
	sizeof(asn_DEF_NR_pathlossReferenceRSToReleaseListSizeExt_v1610_tags_4)
		/sizeof(asn_DEF_NR_pathlossReferenceRSToReleaseListSizeExt_v1610_tags_4[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pathlossReferenceRSToReleaseListSizeExt_v1610_constr_4,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_pathlossReferenceRSToReleaseListSizeExt_v1610_4,
	1,	/* Single element */
	&asn_SPC_NR_pathlossReferenceRSToReleaseListSizeExt_v1610_specs_4	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_p0_PUSCH_SetList_r16_6[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_P0_PUSCH_Set_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_p0_PUSCH_SetList_r16_tags_6[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_p0_PUSCH_SetList_r16_specs_6 = {
	sizeof(struct NR_PUSCH_PowerControl_v1610__p0_PUSCH_SetList_r16),
	offsetof(struct NR_PUSCH_PowerControl_v1610__p0_PUSCH_SetList_r16, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_p0_PUSCH_SetList_r16_6 = {
	"p0-PUSCH-SetList-r16",
	"p0-PUSCH-SetList-r16",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_p0_PUSCH_SetList_r16_tags_6,
	sizeof(asn_DEF_NR_p0_PUSCH_SetList_r16_tags_6)
		/sizeof(asn_DEF_NR_p0_PUSCH_SetList_r16_tags_6[0]) - 1, /* 1 */
	asn_DEF_NR_p0_PUSCH_SetList_r16_tags_6,	/* Same as above */
	sizeof(asn_DEF_NR_p0_PUSCH_SetList_r16_tags_6)
		/sizeof(asn_DEF_NR_p0_PUSCH_SetList_r16_tags_6[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_p0_PUSCH_SetList_r16_constr_6,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_p0_PUSCH_SetList_r16_6,
	1,	/* Single element */
	&asn_SPC_NR_p0_PUSCH_SetList_r16_specs_6	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_olpc_ParameterSet_8[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_PUSCH_PowerControl_v1610__olpc_ParameterSet, olpc_ParameterSetDCI_0_1_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_olpc_ParameterSetDCI_0_1_r16_constr_9,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_olpc_ParameterSetDCI_0_1_r16_constraint_8
		},
		0, 0, /* No default value */
		"olpc-ParameterSetDCI-0-1-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PUSCH_PowerControl_v1610__olpc_ParameterSet, olpc_ParameterSetDCI_0_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_olpc_ParameterSetDCI_0_2_r16_constr_10,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_olpc_ParameterSetDCI_0_2_r16_constraint_8
		},
		0, 0, /* No default value */
		"olpc-ParameterSetDCI-0-2-r16"
		},
};
static const int asn_MAP_NR_olpc_ParameterSet_oms_8[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_olpc_ParameterSet_tags_8[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_olpc_ParameterSet_tag2el_8[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* olpc-ParameterSetDCI-0-1-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* olpc-ParameterSetDCI-0-2-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_olpc_ParameterSet_specs_8 = {
	sizeof(struct NR_PUSCH_PowerControl_v1610__olpc_ParameterSet),
	offsetof(struct NR_PUSCH_PowerControl_v1610__olpc_ParameterSet, _asn_ctx),
	asn_MAP_NR_olpc_ParameterSet_tag2el_8,
	2,	/* Count of tags in the map */
	asn_MAP_NR_olpc_ParameterSet_oms_8,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_olpc_ParameterSet_8 = {
	"olpc-ParameterSet",
	"olpc-ParameterSet",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_olpc_ParameterSet_tags_8,
	sizeof(asn_DEF_NR_olpc_ParameterSet_tags_8)
		/sizeof(asn_DEF_NR_olpc_ParameterSet_tags_8[0]) - 1, /* 1 */
	asn_DEF_NR_olpc_ParameterSet_tags_8,	/* Same as above */
	sizeof(asn_DEF_NR_olpc_ParameterSet_tags_8)
		/sizeof(asn_DEF_NR_olpc_ParameterSet_tags_8[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_olpc_ParameterSet_8,
	2,	/* Elements count */
	&asn_SPC_NR_olpc_ParameterSet_specs_8	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_sri_PUSCH_MappingToAddModList2_r17_13[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_SRI_PUSCH_PowerControl,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_sri_PUSCH_MappingToAddModList2_r17_tags_13[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_sri_PUSCH_MappingToAddModList2_r17_specs_13 = {
	sizeof(struct NR_PUSCH_PowerControl_v1610__ext1__sri_PUSCH_MappingToAddModList2_r17),
	offsetof(struct NR_PUSCH_PowerControl_v1610__ext1__sri_PUSCH_MappingToAddModList2_r17, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_sri_PUSCH_MappingToAddModList2_r17_13 = {
	"sri-PUSCH-MappingToAddModList2-r17",
	"sri-PUSCH-MappingToAddModList2-r17",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_sri_PUSCH_MappingToAddModList2_r17_tags_13,
	sizeof(asn_DEF_NR_sri_PUSCH_MappingToAddModList2_r17_tags_13)
		/sizeof(asn_DEF_NR_sri_PUSCH_MappingToAddModList2_r17_tags_13[0]) - 1, /* 1 */
	asn_DEF_NR_sri_PUSCH_MappingToAddModList2_r17_tags_13,	/* Same as above */
	sizeof(asn_DEF_NR_sri_PUSCH_MappingToAddModList2_r17_tags_13)
		/sizeof(asn_DEF_NR_sri_PUSCH_MappingToAddModList2_r17_tags_13[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_sri_PUSCH_MappingToAddModList2_r17_constr_13,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_sri_PUSCH_MappingToAddModList2_r17_13,
	1,	/* Single element */
	&asn_SPC_NR_sri_PUSCH_MappingToAddModList2_r17_specs_13	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_sri_PUSCH_MappingToReleaseList2_r17_15[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (2 << 2)),
		0,
		&asn_DEF_NR_SRI_PUSCH_PowerControlId,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_sri_PUSCH_MappingToReleaseList2_r17_tags_15[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_sri_PUSCH_MappingToReleaseList2_r17_specs_15 = {
	sizeof(struct NR_PUSCH_PowerControl_v1610__ext1__sri_PUSCH_MappingToReleaseList2_r17),
	offsetof(struct NR_PUSCH_PowerControl_v1610__ext1__sri_PUSCH_MappingToReleaseList2_r17, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_sri_PUSCH_MappingToReleaseList2_r17_15 = {
	"sri-PUSCH-MappingToReleaseList2-r17",
	"sri-PUSCH-MappingToReleaseList2-r17",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_sri_PUSCH_MappingToReleaseList2_r17_tags_15,
	sizeof(asn_DEF_NR_sri_PUSCH_MappingToReleaseList2_r17_tags_15)
		/sizeof(asn_DEF_NR_sri_PUSCH_MappingToReleaseList2_r17_tags_15[0]) - 1, /* 1 */
	asn_DEF_NR_sri_PUSCH_MappingToReleaseList2_r17_tags_15,	/* Same as above */
	sizeof(asn_DEF_NR_sri_PUSCH_MappingToReleaseList2_r17_tags_15)
		/sizeof(asn_DEF_NR_sri_PUSCH_MappingToReleaseList2_r17_tags_15[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_sri_PUSCH_MappingToReleaseList2_r17_constr_15,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_sri_PUSCH_MappingToReleaseList2_r17_15,
	1,	/* Single element */
	&asn_SPC_NR_sri_PUSCH_MappingToReleaseList2_r17_specs_15	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_p0_PUSCH_SetList2_r17_17[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_P0_PUSCH_Set_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_p0_PUSCH_SetList2_r17_tags_17[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_p0_PUSCH_SetList2_r17_specs_17 = {
	sizeof(struct NR_PUSCH_PowerControl_v1610__ext1__p0_PUSCH_SetList2_r17),
	offsetof(struct NR_PUSCH_PowerControl_v1610__ext1__p0_PUSCH_SetList2_r17, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_p0_PUSCH_SetList2_r17_17 = {
	"p0-PUSCH-SetList2-r17",
	"p0-PUSCH-SetList2-r17",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_p0_PUSCH_SetList2_r17_tags_17,
	sizeof(asn_DEF_NR_p0_PUSCH_SetList2_r17_tags_17)
		/sizeof(asn_DEF_NR_p0_PUSCH_SetList2_r17_tags_17[0]) - 1, /* 1 */
	asn_DEF_NR_p0_PUSCH_SetList2_r17_tags_17,	/* Same as above */
	sizeof(asn_DEF_NR_p0_PUSCH_SetList2_r17_tags_17)
		/sizeof(asn_DEF_NR_p0_PUSCH_SetList2_r17_tags_17[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_p0_PUSCH_SetList2_r17_constr_17,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_p0_PUSCH_SetList2_r17_17,
	1,	/* Single element */
	&asn_SPC_NR_p0_PUSCH_SetList2_r17_specs_17	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_dummy_19[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_DummyPathlossReferenceRS_v1710,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_dummy_tags_19[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_dummy_specs_19 = {
	sizeof(struct NR_PUSCH_PowerControl_v1610__ext1__dummy),
	offsetof(struct NR_PUSCH_PowerControl_v1610__ext1__dummy, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dummy_19 = {
	"dummy",
	"dummy",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_dummy_tags_19,
	sizeof(asn_DEF_NR_dummy_tags_19)
		/sizeof(asn_DEF_NR_dummy_tags_19[0]) - 1, /* 1 */
	asn_DEF_NR_dummy_tags_19,	/* Same as above */
	sizeof(asn_DEF_NR_dummy_tags_19)
		/sizeof(asn_DEF_NR_dummy_tags_19[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dummy_constr_19,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_dummy_19,
	1,	/* Single element */
	&asn_SPC_NR_dummy_specs_19	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext1_12[] = {
	{ ATF_POINTER, 4, offsetof(struct NR_PUSCH_PowerControl_v1610__ext1, sri_PUSCH_MappingToAddModList2_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_sri_PUSCH_MappingToAddModList2_r17_13,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_sri_PUSCH_MappingToAddModList2_r17_constr_13,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_sri_PUSCH_MappingToAddModList2_r17_constraint_12
		},
		0, 0, /* No default value */
		"sri-PUSCH-MappingToAddModList2-r17"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_PUSCH_PowerControl_v1610__ext1, sri_PUSCH_MappingToReleaseList2_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_sri_PUSCH_MappingToReleaseList2_r17_15,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_sri_PUSCH_MappingToReleaseList2_r17_constr_15,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_sri_PUSCH_MappingToReleaseList2_r17_constraint_12
		},
		0, 0, /* No default value */
		"sri-PUSCH-MappingToReleaseList2-r17"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_PUSCH_PowerControl_v1610__ext1, p0_PUSCH_SetList2_r17),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		0,
		&asn_DEF_NR_p0_PUSCH_SetList2_r17_17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_p0_PUSCH_SetList2_r17_constr_17,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_p0_PUSCH_SetList2_r17_constraint_12
		},
		0, 0, /* No default value */
		"p0-PUSCH-SetList2-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PUSCH_PowerControl_v1610__ext1, dummy),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		0,
		&asn_DEF_NR_dummy_19,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_dummy_constr_19,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_dummy_constraint_12
		},
		0, 0, /* No default value */
		"dummy"
		},
};
static const int asn_MAP_NR_ext1_oms_12[] = { 0, 1, 2, 3 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_12[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_12[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* sri-PUSCH-MappingToAddModList2-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* sri-PUSCH-MappingToReleaseList2-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* p0-PUSCH-SetList2-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 } /* dummy */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_12 = {
	sizeof(struct NR_PUSCH_PowerControl_v1610__ext1),
	offsetof(struct NR_PUSCH_PowerControl_v1610__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_12,
	4,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_12,	/* Optional members */
	4, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_12 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_12,
	sizeof(asn_DEF_NR_ext1_tags_12)
		/sizeof(asn_DEF_NR_ext1_tags_12[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_12,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_12)
		/sizeof(asn_DEF_NR_ext1_tags_12[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_12,
	4,	/* Elements count */
	&asn_SPC_NR_ext1_specs_12	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_PUSCH_PowerControl_v1610_1[] = {
	{ ATF_POINTER, 5, offsetof(struct NR_PUSCH_PowerControl_v1610, pathlossReferenceRSToAddModListSizeExt_v1610),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_pathlossReferenceRSToAddModListSizeExt_v1610_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_pathlossReferenceRSToAddModListSizeExt_v1610_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_pathlossReferenceRSToAddModListSizeExt_v1610_constraint_1
		},
		0, 0, /* No default value */
		"pathlossReferenceRSToAddModListSizeExt-v1610"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_PUSCH_PowerControl_v1610, pathlossReferenceRSToReleaseListSizeExt_v1610),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_pathlossReferenceRSToReleaseListSizeExt_v1610_4,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_pathlossReferenceRSToReleaseListSizeExt_v1610_constr_4,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_pathlossReferenceRSToReleaseListSizeExt_v1610_constraint_1
		},
		0, 0, /* No default value */
		"pathlossReferenceRSToReleaseListSizeExt-v1610"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_PUSCH_PowerControl_v1610, p0_PUSCH_SetList_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		0,
		&asn_DEF_NR_p0_PUSCH_SetList_r16_6,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_p0_PUSCH_SetList_r16_constr_6,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_p0_PUSCH_SetList_r16_constraint_1
		},
		0, 0, /* No default value */
		"p0-PUSCH-SetList-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_PUSCH_PowerControl_v1610, olpc_ParameterSet),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		0,
		&asn_DEF_NR_olpc_ParameterSet_8,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"olpc-ParameterSet"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PUSCH_PowerControl_v1610, ext1),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		0,
		&asn_DEF_NR_ext1_12,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
};
static const int asn_MAP_NR_PUSCH_PowerControl_v1610_oms_1[] = { 0, 1, 2, 3, 4 };
static const ber_tlv_tag_t asn_DEF_NR_PUSCH_PowerControl_v1610_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_PUSCH_PowerControl_v1610_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* pathlossReferenceRSToAddModListSizeExt-v1610 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* pathlossReferenceRSToReleaseListSizeExt-v1610 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* p0-PUSCH-SetList-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* olpc-ParameterSet */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 } /* ext1 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_PUSCH_PowerControl_v1610_specs_1 = {
	sizeof(struct NR_PUSCH_PowerControl_v1610),
	offsetof(struct NR_PUSCH_PowerControl_v1610, _asn_ctx),
	asn_MAP_NR_PUSCH_PowerControl_v1610_tag2el_1,
	5,	/* Count of tags in the map */
	asn_MAP_NR_PUSCH_PowerControl_v1610_oms_1,	/* Optional members */
	4, 1,	/* Root/Additions */
	4,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_PUSCH_PowerControl_v1610 = {
	"PUSCH-PowerControl-v1610",
	"PUSCH-PowerControl-v1610",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_PUSCH_PowerControl_v1610_tags_1,
	sizeof(asn_DEF_NR_PUSCH_PowerControl_v1610_tags_1)
		/sizeof(asn_DEF_NR_PUSCH_PowerControl_v1610_tags_1[0]), /* 1 */
	asn_DEF_NR_PUSCH_PowerControl_v1610_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_PUSCH_PowerControl_v1610_tags_1)
		/sizeof(asn_DEF_NR_PUSCH_PowerControl_v1610_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_PUSCH_PowerControl_v1610_1,
	5,	/* Elements count */
	&asn_SPC_NR_PUSCH_PowerControl_v1610_specs_1	/* Additional specs */
};

