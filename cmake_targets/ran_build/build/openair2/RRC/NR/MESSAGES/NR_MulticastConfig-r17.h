/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_MulticastConfig_r17_H_
#define	_NR_MulticastConfig_r17_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_MulticastConfig_r17__type1_Codebook_GenerationMode_r17 {
	NR_MulticastConfig_r17__type1_Codebook_GenerationMode_r17_mode1	= 0,
	NR_MulticastConfig_r17__type1_Codebook_GenerationMode_r17_mode2	= 1
} e_NR_MulticastConfig_r17__type1_Codebook_GenerationMode_r17;

/* Forward declarations */
struct NR_SetupRelease_PDSCH_HARQ_ACK_CodebookList_r16;

/* NR_MulticastConfig-r17 */
typedef struct NR_MulticastConfig_r17 {
	struct NR_SetupRelease_PDSCH_HARQ_ACK_CodebookList_r16	*pdsch_HARQ_ACK_CodebookListMulticast_r17;	/* OPTIONAL */
	long	*type1_Codebook_GenerationMode_r17;	/* OPTIONAL */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_MulticastConfig_r17_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_type1_Codebook_GenerationMode_r17_3;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_MulticastConfig_r17;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_MulticastConfig_r17_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_MulticastConfig_r17_1[2];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_SetupRelease.h"

#endif	/* _NR_MulticastConfig_r17_H_ */
#include <asn_internal.h>
