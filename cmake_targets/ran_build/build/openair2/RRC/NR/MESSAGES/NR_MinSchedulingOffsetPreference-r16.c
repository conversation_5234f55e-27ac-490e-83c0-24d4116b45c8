/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_MinSchedulingOffsetPreference-r16.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_preferredK0_SCS_15kHz_r16_constr_3 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_preferredK0_SCS_30kHz_r16_constr_8 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_preferredK0_SCS_60kHz_r16_constr_13 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_preferredK0_SCS_120kHz_r16_constr_18 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_preferredK2_SCS_15kHz_r16_constr_24 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_preferredK2_SCS_30kHz_r16_constr_29 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_preferredK2_SCS_60kHz_r16_constr_34 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_preferredK2_SCS_120kHz_r16_constr_39 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_preferredK0_SCS_15kHz_r16_value2enum_3[] = {
	{ 0,	3,	"sl1" },
	{ 1,	3,	"sl2" },
	{ 2,	3,	"sl4" },
	{ 3,	3,	"sl6" }
};
static const unsigned int asn_MAP_NR_preferredK0_SCS_15kHz_r16_enum2value_3[] = {
	0,	/* sl1(0) */
	1,	/* sl2(1) */
	2,	/* sl4(2) */
	3	/* sl6(3) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_preferredK0_SCS_15kHz_r16_specs_3 = {
	asn_MAP_NR_preferredK0_SCS_15kHz_r16_value2enum_3,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_preferredK0_SCS_15kHz_r16_enum2value_3,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_preferredK0_SCS_15kHz_r16_tags_3[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_preferredK0_SCS_15kHz_r16_3 = {
	"preferredK0-SCS-15kHz-r16",
	"preferredK0-SCS-15kHz-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_preferredK0_SCS_15kHz_r16_tags_3,
	sizeof(asn_DEF_NR_preferredK0_SCS_15kHz_r16_tags_3)
		/sizeof(asn_DEF_NR_preferredK0_SCS_15kHz_r16_tags_3[0]) - 1, /* 1 */
	asn_DEF_NR_preferredK0_SCS_15kHz_r16_tags_3,	/* Same as above */
	sizeof(asn_DEF_NR_preferredK0_SCS_15kHz_r16_tags_3)
		/sizeof(asn_DEF_NR_preferredK0_SCS_15kHz_r16_tags_3[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_preferredK0_SCS_15kHz_r16_constr_3,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_preferredK0_SCS_15kHz_r16_specs_3	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_preferredK0_SCS_30kHz_r16_value2enum_8[] = {
	{ 0,	3,	"sl1" },
	{ 1,	3,	"sl2" },
	{ 2,	3,	"sl4" },
	{ 3,	3,	"sl6" }
};
static const unsigned int asn_MAP_NR_preferredK0_SCS_30kHz_r16_enum2value_8[] = {
	0,	/* sl1(0) */
	1,	/* sl2(1) */
	2,	/* sl4(2) */
	3	/* sl6(3) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_preferredK0_SCS_30kHz_r16_specs_8 = {
	asn_MAP_NR_preferredK0_SCS_30kHz_r16_value2enum_8,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_preferredK0_SCS_30kHz_r16_enum2value_8,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_preferredK0_SCS_30kHz_r16_tags_8[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_preferredK0_SCS_30kHz_r16_8 = {
	"preferredK0-SCS-30kHz-r16",
	"preferredK0-SCS-30kHz-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_preferredK0_SCS_30kHz_r16_tags_8,
	sizeof(asn_DEF_NR_preferredK0_SCS_30kHz_r16_tags_8)
		/sizeof(asn_DEF_NR_preferredK0_SCS_30kHz_r16_tags_8[0]) - 1, /* 1 */
	asn_DEF_NR_preferredK0_SCS_30kHz_r16_tags_8,	/* Same as above */
	sizeof(asn_DEF_NR_preferredK0_SCS_30kHz_r16_tags_8)
		/sizeof(asn_DEF_NR_preferredK0_SCS_30kHz_r16_tags_8[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_preferredK0_SCS_30kHz_r16_constr_8,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_preferredK0_SCS_30kHz_r16_specs_8	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_preferredK0_SCS_60kHz_r16_value2enum_13[] = {
	{ 0,	3,	"sl2" },
	{ 1,	3,	"sl4" },
	{ 2,	3,	"sl8" },
	{ 3,	4,	"sl12" }
};
static const unsigned int asn_MAP_NR_preferredK0_SCS_60kHz_r16_enum2value_13[] = {
	3,	/* sl12(3) */
	0,	/* sl2(0) */
	1,	/* sl4(1) */
	2	/* sl8(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_preferredK0_SCS_60kHz_r16_specs_13 = {
	asn_MAP_NR_preferredK0_SCS_60kHz_r16_value2enum_13,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_preferredK0_SCS_60kHz_r16_enum2value_13,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_preferredK0_SCS_60kHz_r16_tags_13[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_preferredK0_SCS_60kHz_r16_13 = {
	"preferredK0-SCS-60kHz-r16",
	"preferredK0-SCS-60kHz-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_preferredK0_SCS_60kHz_r16_tags_13,
	sizeof(asn_DEF_NR_preferredK0_SCS_60kHz_r16_tags_13)
		/sizeof(asn_DEF_NR_preferredK0_SCS_60kHz_r16_tags_13[0]) - 1, /* 1 */
	asn_DEF_NR_preferredK0_SCS_60kHz_r16_tags_13,	/* Same as above */
	sizeof(asn_DEF_NR_preferredK0_SCS_60kHz_r16_tags_13)
		/sizeof(asn_DEF_NR_preferredK0_SCS_60kHz_r16_tags_13[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_preferredK0_SCS_60kHz_r16_constr_13,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_preferredK0_SCS_60kHz_r16_specs_13	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_preferredK0_SCS_120kHz_r16_value2enum_18[] = {
	{ 0,	3,	"sl2" },
	{ 1,	3,	"sl4" },
	{ 2,	3,	"sl8" },
	{ 3,	4,	"sl12" }
};
static const unsigned int asn_MAP_NR_preferredK0_SCS_120kHz_r16_enum2value_18[] = {
	3,	/* sl12(3) */
	0,	/* sl2(0) */
	1,	/* sl4(1) */
	2	/* sl8(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_preferredK0_SCS_120kHz_r16_specs_18 = {
	asn_MAP_NR_preferredK0_SCS_120kHz_r16_value2enum_18,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_preferredK0_SCS_120kHz_r16_enum2value_18,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_preferredK0_SCS_120kHz_r16_tags_18[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_preferredK0_SCS_120kHz_r16_18 = {
	"preferredK0-SCS-120kHz-r16",
	"preferredK0-SCS-120kHz-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_preferredK0_SCS_120kHz_r16_tags_18,
	sizeof(asn_DEF_NR_preferredK0_SCS_120kHz_r16_tags_18)
		/sizeof(asn_DEF_NR_preferredK0_SCS_120kHz_r16_tags_18[0]) - 1, /* 1 */
	asn_DEF_NR_preferredK0_SCS_120kHz_r16_tags_18,	/* Same as above */
	sizeof(asn_DEF_NR_preferredK0_SCS_120kHz_r16_tags_18)
		/sizeof(asn_DEF_NR_preferredK0_SCS_120kHz_r16_tags_18[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_preferredK0_SCS_120kHz_r16_constr_18,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_preferredK0_SCS_120kHz_r16_specs_18	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_preferredK0_r16_2[] = {
	{ ATF_POINTER, 4, offsetof(struct NR_MinSchedulingOffsetPreference_r16__preferredK0_r16, preferredK0_SCS_15kHz_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_preferredK0_SCS_15kHz_r16_3,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"preferredK0-SCS-15kHz-r16"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_MinSchedulingOffsetPreference_r16__preferredK0_r16, preferredK0_SCS_30kHz_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_preferredK0_SCS_30kHz_r16_8,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"preferredK0-SCS-30kHz-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_MinSchedulingOffsetPreference_r16__preferredK0_r16, preferredK0_SCS_60kHz_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_preferredK0_SCS_60kHz_r16_13,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"preferredK0-SCS-60kHz-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MinSchedulingOffsetPreference_r16__preferredK0_r16, preferredK0_SCS_120kHz_r16),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_preferredK0_SCS_120kHz_r16_18,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"preferredK0-SCS-120kHz-r16"
		},
};
static const int asn_MAP_NR_preferredK0_r16_oms_2[] = { 0, 1, 2, 3 };
static const ber_tlv_tag_t asn_DEF_NR_preferredK0_r16_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_preferredK0_r16_tag2el_2[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* preferredK0-SCS-15kHz-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* preferredK0-SCS-30kHz-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* preferredK0-SCS-60kHz-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 } /* preferredK0-SCS-120kHz-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_preferredK0_r16_specs_2 = {
	sizeof(struct NR_MinSchedulingOffsetPreference_r16__preferredK0_r16),
	offsetof(struct NR_MinSchedulingOffsetPreference_r16__preferredK0_r16, _asn_ctx),
	asn_MAP_NR_preferredK0_r16_tag2el_2,
	4,	/* Count of tags in the map */
	asn_MAP_NR_preferredK0_r16_oms_2,	/* Optional members */
	4, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_preferredK0_r16_2 = {
	"preferredK0-r16",
	"preferredK0-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_preferredK0_r16_tags_2,
	sizeof(asn_DEF_NR_preferredK0_r16_tags_2)
		/sizeof(asn_DEF_NR_preferredK0_r16_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_preferredK0_r16_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_preferredK0_r16_tags_2)
		/sizeof(asn_DEF_NR_preferredK0_r16_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_preferredK0_r16_2,
	4,	/* Elements count */
	&asn_SPC_NR_preferredK0_r16_specs_2	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_preferredK2_SCS_15kHz_r16_value2enum_24[] = {
	{ 0,	3,	"sl1" },
	{ 1,	3,	"sl2" },
	{ 2,	3,	"sl4" },
	{ 3,	3,	"sl6" }
};
static const unsigned int asn_MAP_NR_preferredK2_SCS_15kHz_r16_enum2value_24[] = {
	0,	/* sl1(0) */
	1,	/* sl2(1) */
	2,	/* sl4(2) */
	3	/* sl6(3) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_preferredK2_SCS_15kHz_r16_specs_24 = {
	asn_MAP_NR_preferredK2_SCS_15kHz_r16_value2enum_24,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_preferredK2_SCS_15kHz_r16_enum2value_24,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_preferredK2_SCS_15kHz_r16_tags_24[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_preferredK2_SCS_15kHz_r16_24 = {
	"preferredK2-SCS-15kHz-r16",
	"preferredK2-SCS-15kHz-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_preferredK2_SCS_15kHz_r16_tags_24,
	sizeof(asn_DEF_NR_preferredK2_SCS_15kHz_r16_tags_24)
		/sizeof(asn_DEF_NR_preferredK2_SCS_15kHz_r16_tags_24[0]) - 1, /* 1 */
	asn_DEF_NR_preferredK2_SCS_15kHz_r16_tags_24,	/* Same as above */
	sizeof(asn_DEF_NR_preferredK2_SCS_15kHz_r16_tags_24)
		/sizeof(asn_DEF_NR_preferredK2_SCS_15kHz_r16_tags_24[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_preferredK2_SCS_15kHz_r16_constr_24,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_preferredK2_SCS_15kHz_r16_specs_24	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_preferredK2_SCS_30kHz_r16_value2enum_29[] = {
	{ 0,	3,	"sl1" },
	{ 1,	3,	"sl2" },
	{ 2,	3,	"sl4" },
	{ 3,	3,	"sl6" }
};
static const unsigned int asn_MAP_NR_preferredK2_SCS_30kHz_r16_enum2value_29[] = {
	0,	/* sl1(0) */
	1,	/* sl2(1) */
	2,	/* sl4(2) */
	3	/* sl6(3) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_preferredK2_SCS_30kHz_r16_specs_29 = {
	asn_MAP_NR_preferredK2_SCS_30kHz_r16_value2enum_29,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_preferredK2_SCS_30kHz_r16_enum2value_29,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_preferredK2_SCS_30kHz_r16_tags_29[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_preferredK2_SCS_30kHz_r16_29 = {
	"preferredK2-SCS-30kHz-r16",
	"preferredK2-SCS-30kHz-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_preferredK2_SCS_30kHz_r16_tags_29,
	sizeof(asn_DEF_NR_preferredK2_SCS_30kHz_r16_tags_29)
		/sizeof(asn_DEF_NR_preferredK2_SCS_30kHz_r16_tags_29[0]) - 1, /* 1 */
	asn_DEF_NR_preferredK2_SCS_30kHz_r16_tags_29,	/* Same as above */
	sizeof(asn_DEF_NR_preferredK2_SCS_30kHz_r16_tags_29)
		/sizeof(asn_DEF_NR_preferredK2_SCS_30kHz_r16_tags_29[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_preferredK2_SCS_30kHz_r16_constr_29,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_preferredK2_SCS_30kHz_r16_specs_29	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_preferredK2_SCS_60kHz_r16_value2enum_34[] = {
	{ 0,	3,	"sl2" },
	{ 1,	3,	"sl4" },
	{ 2,	3,	"sl8" },
	{ 3,	4,	"sl12" }
};
static const unsigned int asn_MAP_NR_preferredK2_SCS_60kHz_r16_enum2value_34[] = {
	3,	/* sl12(3) */
	0,	/* sl2(0) */
	1,	/* sl4(1) */
	2	/* sl8(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_preferredK2_SCS_60kHz_r16_specs_34 = {
	asn_MAP_NR_preferredK2_SCS_60kHz_r16_value2enum_34,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_preferredK2_SCS_60kHz_r16_enum2value_34,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_preferredK2_SCS_60kHz_r16_tags_34[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_preferredK2_SCS_60kHz_r16_34 = {
	"preferredK2-SCS-60kHz-r16",
	"preferredK2-SCS-60kHz-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_preferredK2_SCS_60kHz_r16_tags_34,
	sizeof(asn_DEF_NR_preferredK2_SCS_60kHz_r16_tags_34)
		/sizeof(asn_DEF_NR_preferredK2_SCS_60kHz_r16_tags_34[0]) - 1, /* 1 */
	asn_DEF_NR_preferredK2_SCS_60kHz_r16_tags_34,	/* Same as above */
	sizeof(asn_DEF_NR_preferredK2_SCS_60kHz_r16_tags_34)
		/sizeof(asn_DEF_NR_preferredK2_SCS_60kHz_r16_tags_34[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_preferredK2_SCS_60kHz_r16_constr_34,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_preferredK2_SCS_60kHz_r16_specs_34	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_preferredK2_SCS_120kHz_r16_value2enum_39[] = {
	{ 0,	3,	"sl2" },
	{ 1,	3,	"sl4" },
	{ 2,	3,	"sl8" },
	{ 3,	4,	"sl12" }
};
static const unsigned int asn_MAP_NR_preferredK2_SCS_120kHz_r16_enum2value_39[] = {
	3,	/* sl12(3) */
	0,	/* sl2(0) */
	1,	/* sl4(1) */
	2	/* sl8(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_preferredK2_SCS_120kHz_r16_specs_39 = {
	asn_MAP_NR_preferredK2_SCS_120kHz_r16_value2enum_39,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_preferredK2_SCS_120kHz_r16_enum2value_39,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_preferredK2_SCS_120kHz_r16_tags_39[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_preferredK2_SCS_120kHz_r16_39 = {
	"preferredK2-SCS-120kHz-r16",
	"preferredK2-SCS-120kHz-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_preferredK2_SCS_120kHz_r16_tags_39,
	sizeof(asn_DEF_NR_preferredK2_SCS_120kHz_r16_tags_39)
		/sizeof(asn_DEF_NR_preferredK2_SCS_120kHz_r16_tags_39[0]) - 1, /* 1 */
	asn_DEF_NR_preferredK2_SCS_120kHz_r16_tags_39,	/* Same as above */
	sizeof(asn_DEF_NR_preferredK2_SCS_120kHz_r16_tags_39)
		/sizeof(asn_DEF_NR_preferredK2_SCS_120kHz_r16_tags_39[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_preferredK2_SCS_120kHz_r16_constr_39,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_preferredK2_SCS_120kHz_r16_specs_39	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_preferredK2_r16_23[] = {
	{ ATF_POINTER, 4, offsetof(struct NR_MinSchedulingOffsetPreference_r16__preferredK2_r16, preferredK2_SCS_15kHz_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_preferredK2_SCS_15kHz_r16_24,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"preferredK2-SCS-15kHz-r16"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_MinSchedulingOffsetPreference_r16__preferredK2_r16, preferredK2_SCS_30kHz_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_preferredK2_SCS_30kHz_r16_29,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"preferredK2-SCS-30kHz-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_MinSchedulingOffsetPreference_r16__preferredK2_r16, preferredK2_SCS_60kHz_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_preferredK2_SCS_60kHz_r16_34,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"preferredK2-SCS-60kHz-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MinSchedulingOffsetPreference_r16__preferredK2_r16, preferredK2_SCS_120kHz_r16),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_preferredK2_SCS_120kHz_r16_39,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"preferredK2-SCS-120kHz-r16"
		},
};
static const int asn_MAP_NR_preferredK2_r16_oms_23[] = { 0, 1, 2, 3 };
static const ber_tlv_tag_t asn_DEF_NR_preferredK2_r16_tags_23[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_preferredK2_r16_tag2el_23[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* preferredK2-SCS-15kHz-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* preferredK2-SCS-30kHz-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* preferredK2-SCS-60kHz-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 } /* preferredK2-SCS-120kHz-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_preferredK2_r16_specs_23 = {
	sizeof(struct NR_MinSchedulingOffsetPreference_r16__preferredK2_r16),
	offsetof(struct NR_MinSchedulingOffsetPreference_r16__preferredK2_r16, _asn_ctx),
	asn_MAP_NR_preferredK2_r16_tag2el_23,
	4,	/* Count of tags in the map */
	asn_MAP_NR_preferredK2_r16_oms_23,	/* Optional members */
	4, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_preferredK2_r16_23 = {
	"preferredK2-r16",
	"preferredK2-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_preferredK2_r16_tags_23,
	sizeof(asn_DEF_NR_preferredK2_r16_tags_23)
		/sizeof(asn_DEF_NR_preferredK2_r16_tags_23[0]) - 1, /* 1 */
	asn_DEF_NR_preferredK2_r16_tags_23,	/* Same as above */
	sizeof(asn_DEF_NR_preferredK2_r16_tags_23)
		/sizeof(asn_DEF_NR_preferredK2_r16_tags_23[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_preferredK2_r16_23,
	4,	/* Elements count */
	&asn_SPC_NR_preferredK2_r16_specs_23	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_MinSchedulingOffsetPreference_r16_1[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_MinSchedulingOffsetPreference_r16, preferredK0_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_preferredK0_r16_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"preferredK0-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MinSchedulingOffsetPreference_r16, preferredK2_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_preferredK2_r16_23,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"preferredK2-r16"
		},
};
static const int asn_MAP_NR_MinSchedulingOffsetPreference_r16_oms_1[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_MinSchedulingOffsetPreference_r16_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_MinSchedulingOffsetPreference_r16_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* preferredK0-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* preferredK2-r16 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_MinSchedulingOffsetPreference_r16_specs_1 = {
	sizeof(struct NR_MinSchedulingOffsetPreference_r16),
	offsetof(struct NR_MinSchedulingOffsetPreference_r16, _asn_ctx),
	asn_MAP_NR_MinSchedulingOffsetPreference_r16_tag2el_1,
	2,	/* Count of tags in the map */
	asn_MAP_NR_MinSchedulingOffsetPreference_r16_oms_1,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_MinSchedulingOffsetPreference_r16 = {
	"MinSchedulingOffsetPreference-r16",
	"MinSchedulingOffsetPreference-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_MinSchedulingOffsetPreference_r16_tags_1,
	sizeof(asn_DEF_NR_MinSchedulingOffsetPreference_r16_tags_1)
		/sizeof(asn_DEF_NR_MinSchedulingOffsetPreference_r16_tags_1[0]), /* 1 */
	asn_DEF_NR_MinSchedulingOffsetPreference_r16_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_MinSchedulingOffsetPreference_r16_tags_1)
		/sizeof(asn_DEF_NR_MinSchedulingOffsetPreference_r16_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_MinSchedulingOffsetPreference_r16_1,
	2,	/* Elements count */
	&asn_SPC_NR_MinSchedulingOffsetPreference_r16_specs_1	/* Additional specs */
};

