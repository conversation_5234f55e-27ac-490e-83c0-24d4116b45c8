/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_PUSCH-Config.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NativeInteger_constraint_12(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 274L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_harq_ProcessNumberSizeDCI_0_2_r16_constraint_43(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 4L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_numberOfBitsForRV_DCI_0_2_r16_constraint_43(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 2L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_maxRankDCI_0_2_r16_constraint_43(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 4L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_numberOfInvalidSymbolsForDL_UL_Switching_r16_constraint_43(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 4L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_sequenceOffsetForRV_r17_constraint_111(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 3L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_harq_ProcessNumberSizeDCI_0_2_v1700_constraint_111(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value == 5L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_harq_ProcessNumberSizeDCI_0_1_r17_constraint_111(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value == 5L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_mpe_ResourcePoolToAddModList_r17_constraint_111(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 64UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_mpe_ResourcePoolToReleaseList_r17_constraint_111(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 64UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_dataScramblingIdentityPUSCH_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 1023L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_frequencyHoppingOffsetLists_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 4UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_maxRank_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 4L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_txConfig_constr_3 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_frequencyHopping_constr_9 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_Member_constr_13 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 9,  9,  1,  274 }	/* (1..274) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_frequencyHoppingOffsetLists_constr_12 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 2,  2,  1,  4 }	/* (SIZE(1..4)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_resourceAllocation_constr_14 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pusch_AggregationFactor_constr_19 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_mcs_Table_constr_23 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_mcs_TableTransformPrecoder_constr_26 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_transformPrecoder_constr_29 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_codebookSubset_constr_32 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_rbg_Size_constr_37 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_tp_pi2BPSK_constr_40 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dmrs_SequenceInitializationDCI_0_2_r16_constr_47 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_antennaPortsFieldPresenceDCI_0_2_r16_constr_50 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pusch_RepTypeA_constr_55 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pusch_RepTypeB_constr_58 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_frequencyHoppingDCI_0_2_r16_constr_54 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_codebookSubsetDCI_0_2_r16_constr_62 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_invalidSymbolPatternIndicatorDCI_0_2_r16_constr_66 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_mcs_TableDCI_0_2_r16_constr_69 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_mcs_TableTransformPrecoderDCI_0_2_r16_constr_72 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_priorityIndicatorDCI_0_2_r16_constr_75 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pusch_RepTypeIndicatorDCI_0_2_r16_constr_77 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_resourceAllocationDCI_0_2_r16_constr_80 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_resourceAllocationType1GranularityDCI_0_2_r16_constr_84 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_invalidSymbolPatternIndicatorDCI_0_1_r16_constr_92 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_priorityIndicatorDCI_0_1_r16_constr_94 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pusch_RepTypeIndicatorDCI_0_1_r16_constr_96 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_frequencyHoppingDCI_0_1_r16_constr_99 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ul_FullPowerTransmission_r16_constr_105 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_harq_ProcessNumberSizeDCI_0_2_r16_constr_46 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  4 }	/* (0..4) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_numberOfBitsForRV_DCI_0_2_r16_constr_49 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_maxRankDCI_0_2_r16_constr_68 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  1,  4 }	/* (1..4) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_numberOfInvalidSymbolsForDL_UL_Switching_r16_constr_110 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  1,  4 }	/* (1..4) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_mappingPattern_r17_constr_117 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_secondTPCFieldDCI_0_1_r17_constr_120 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_secondTPCFieldDCI_0_2_r17_constr_122 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_availableSlotCounting_r17_constr_127 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_mpe_ResourcePoolToAddModList_r17_constr_132 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 6,  6,  1,  64 }	/* (SIZE(1..64)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_mpe_ResourcePoolToReleaseList_r17_constr_134 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 6,  6,  1,  64 }	/* (SIZE(1..64)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_sequenceOffsetForRV_r17_constr_124 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_harq_ProcessNumberSizeDCI_0_2_v1700_constr_130 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  5,  5 }	/* (5..5) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_harq_ProcessNumberSizeDCI_0_1_r17_constr_131 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  5,  5 }	/* (5..5) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_mpe_ResourcePoolToAddModList_r17_constr_132 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 6,  6,  1,  64 }	/* (SIZE(1..64)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_mpe_ResourcePoolToReleaseList_r17_constr_134 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 6,  6,  1,  64 }	/* (SIZE(1..64)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_dataScramblingIdentityPUSCH_constr_2 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 10,  10,  0,  1023 }	/* (0..1023) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_frequencyHoppingOffsetLists_constr_12 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 2,  2,  1,  4 }	/* (SIZE(1..4)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_maxRank_constr_36 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  1,  4 }	/* (1..4) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_txConfig_value2enum_3[] = {
	{ 0,	8,	"codebook" },
	{ 1,	11,	"nonCodebook" }
};
static const unsigned int asn_MAP_NR_txConfig_enum2value_3[] = {
	0,	/* codebook(0) */
	1	/* nonCodebook(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_txConfig_specs_3 = {
	asn_MAP_NR_txConfig_value2enum_3,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_txConfig_enum2value_3,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_txConfig_tags_3[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_txConfig_3 = {
	"txConfig",
	"txConfig",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_txConfig_tags_3,
	sizeof(asn_DEF_NR_txConfig_tags_3)
		/sizeof(asn_DEF_NR_txConfig_tags_3[0]) - 1, /* 1 */
	asn_DEF_NR_txConfig_tags_3,	/* Same as above */
	sizeof(asn_DEF_NR_txConfig_tags_3)
		/sizeof(asn_DEF_NR_txConfig_tags_3[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_txConfig_constr_3,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_txConfig_specs_3	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_frequencyHopping_value2enum_9[] = {
	{ 0,	9,	"intraSlot" },
	{ 1,	9,	"interSlot" }
};
static const unsigned int asn_MAP_NR_frequencyHopping_enum2value_9[] = {
	1,	/* interSlot(1) */
	0	/* intraSlot(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_frequencyHopping_specs_9 = {
	asn_MAP_NR_frequencyHopping_value2enum_9,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_frequencyHopping_enum2value_9,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_frequencyHopping_tags_9[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_frequencyHopping_9 = {
	"frequencyHopping",
	"frequencyHopping",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_frequencyHopping_tags_9,
	sizeof(asn_DEF_NR_frequencyHopping_tags_9)
		/sizeof(asn_DEF_NR_frequencyHopping_tags_9[0]) - 1, /* 1 */
	asn_DEF_NR_frequencyHopping_tags_9,	/* Same as above */
	sizeof(asn_DEF_NR_frequencyHopping_tags_9)
		/sizeof(asn_DEF_NR_frequencyHopping_tags_9[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_frequencyHopping_constr_9,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_frequencyHopping_specs_9	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_frequencyHoppingOffsetLists_12[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (2 << 2)),
		0,
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_Member_constr_13,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NativeInteger_constraint_12
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_frequencyHoppingOffsetLists_tags_12[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_frequencyHoppingOffsetLists_specs_12 = {
	sizeof(struct NR_PUSCH_Config__frequencyHoppingOffsetLists),
	offsetof(struct NR_PUSCH_Config__frequencyHoppingOffsetLists, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_frequencyHoppingOffsetLists_12 = {
	"frequencyHoppingOffsetLists",
	"frequencyHoppingOffsetLists",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_frequencyHoppingOffsetLists_tags_12,
	sizeof(asn_DEF_NR_frequencyHoppingOffsetLists_tags_12)
		/sizeof(asn_DEF_NR_frequencyHoppingOffsetLists_tags_12[0]) - 1, /* 1 */
	asn_DEF_NR_frequencyHoppingOffsetLists_tags_12,	/* Same as above */
	sizeof(asn_DEF_NR_frequencyHoppingOffsetLists_tags_12)
		/sizeof(asn_DEF_NR_frequencyHoppingOffsetLists_tags_12[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_frequencyHoppingOffsetLists_constr_12,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_frequencyHoppingOffsetLists_12,
	1,	/* Single element */
	&asn_SPC_NR_frequencyHoppingOffsetLists_specs_12	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_resourceAllocation_value2enum_14[] = {
	{ 0,	23,	"resourceAllocationType0" },
	{ 1,	23,	"resourceAllocationType1" },
	{ 2,	13,	"dynamicSwitch" }
};
static const unsigned int asn_MAP_NR_resourceAllocation_enum2value_14[] = {
	2,	/* dynamicSwitch(2) */
	0,	/* resourceAllocationType0(0) */
	1	/* resourceAllocationType1(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_resourceAllocation_specs_14 = {
	asn_MAP_NR_resourceAllocation_value2enum_14,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_resourceAllocation_enum2value_14,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_resourceAllocation_tags_14[] = {
	(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_resourceAllocation_14 = {
	"resourceAllocation",
	"resourceAllocation",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_resourceAllocation_tags_14,
	sizeof(asn_DEF_NR_resourceAllocation_tags_14)
		/sizeof(asn_DEF_NR_resourceAllocation_tags_14[0]) - 1, /* 1 */
	asn_DEF_NR_resourceAllocation_tags_14,	/* Same as above */
	sizeof(asn_DEF_NR_resourceAllocation_tags_14)
		/sizeof(asn_DEF_NR_resourceAllocation_tags_14[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_resourceAllocation_constr_14,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_resourceAllocation_specs_14	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pusch_AggregationFactor_value2enum_19[] = {
	{ 0,	2,	"n2" },
	{ 1,	2,	"n4" },
	{ 2,	2,	"n8" }
};
static const unsigned int asn_MAP_NR_pusch_AggregationFactor_enum2value_19[] = {
	0,	/* n2(0) */
	1,	/* n4(1) */
	2	/* n8(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pusch_AggregationFactor_specs_19 = {
	asn_MAP_NR_pusch_AggregationFactor_value2enum_19,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pusch_AggregationFactor_enum2value_19,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pusch_AggregationFactor_tags_19[] = {
	(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pusch_AggregationFactor_19 = {
	"pusch-AggregationFactor",
	"pusch-AggregationFactor",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pusch_AggregationFactor_tags_19,
	sizeof(asn_DEF_NR_pusch_AggregationFactor_tags_19)
		/sizeof(asn_DEF_NR_pusch_AggregationFactor_tags_19[0]) - 1, /* 1 */
	asn_DEF_NR_pusch_AggregationFactor_tags_19,	/* Same as above */
	sizeof(asn_DEF_NR_pusch_AggregationFactor_tags_19)
		/sizeof(asn_DEF_NR_pusch_AggregationFactor_tags_19[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pusch_AggregationFactor_constr_19,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pusch_AggregationFactor_specs_19	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_mcs_Table_value2enum_23[] = {
	{ 0,	6,	"qam256" },
	{ 1,	10,	"qam64LowSE" }
};
static const unsigned int asn_MAP_NR_mcs_Table_enum2value_23[] = {
	0,	/* qam256(0) */
	1	/* qam64LowSE(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_mcs_Table_specs_23 = {
	asn_MAP_NR_mcs_Table_value2enum_23,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_mcs_Table_enum2value_23,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_mcs_Table_tags_23[] = {
	(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mcs_Table_23 = {
	"mcs-Table",
	"mcs-Table",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_mcs_Table_tags_23,
	sizeof(asn_DEF_NR_mcs_Table_tags_23)
		/sizeof(asn_DEF_NR_mcs_Table_tags_23[0]) - 1, /* 1 */
	asn_DEF_NR_mcs_Table_tags_23,	/* Same as above */
	sizeof(asn_DEF_NR_mcs_Table_tags_23)
		/sizeof(asn_DEF_NR_mcs_Table_tags_23[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_mcs_Table_constr_23,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_mcs_Table_specs_23	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_mcs_TableTransformPrecoder_value2enum_26[] = {
	{ 0,	6,	"qam256" },
	{ 1,	10,	"qam64LowSE" }
};
static const unsigned int asn_MAP_NR_mcs_TableTransformPrecoder_enum2value_26[] = {
	0,	/* qam256(0) */
	1	/* qam64LowSE(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_mcs_TableTransformPrecoder_specs_26 = {
	asn_MAP_NR_mcs_TableTransformPrecoder_value2enum_26,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_mcs_TableTransformPrecoder_enum2value_26,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_mcs_TableTransformPrecoder_tags_26[] = {
	(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mcs_TableTransformPrecoder_26 = {
	"mcs-TableTransformPrecoder",
	"mcs-TableTransformPrecoder",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_mcs_TableTransformPrecoder_tags_26,
	sizeof(asn_DEF_NR_mcs_TableTransformPrecoder_tags_26)
		/sizeof(asn_DEF_NR_mcs_TableTransformPrecoder_tags_26[0]) - 1, /* 1 */
	asn_DEF_NR_mcs_TableTransformPrecoder_tags_26,	/* Same as above */
	sizeof(asn_DEF_NR_mcs_TableTransformPrecoder_tags_26)
		/sizeof(asn_DEF_NR_mcs_TableTransformPrecoder_tags_26[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_mcs_TableTransformPrecoder_constr_26,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_mcs_TableTransformPrecoder_specs_26	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_transformPrecoder_value2enum_29[] = {
	{ 0,	7,	"enabled" },
	{ 1,	8,	"disabled" }
};
static const unsigned int asn_MAP_NR_transformPrecoder_enum2value_29[] = {
	1,	/* disabled(1) */
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_transformPrecoder_specs_29 = {
	asn_MAP_NR_transformPrecoder_value2enum_29,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_transformPrecoder_enum2value_29,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_transformPrecoder_tags_29[] = {
	(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_transformPrecoder_29 = {
	"transformPrecoder",
	"transformPrecoder",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_transformPrecoder_tags_29,
	sizeof(asn_DEF_NR_transformPrecoder_tags_29)
		/sizeof(asn_DEF_NR_transformPrecoder_tags_29[0]) - 1, /* 1 */
	asn_DEF_NR_transformPrecoder_tags_29,	/* Same as above */
	sizeof(asn_DEF_NR_transformPrecoder_tags_29)
		/sizeof(asn_DEF_NR_transformPrecoder_tags_29[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_transformPrecoder_constr_29,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_transformPrecoder_specs_29	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_codebookSubset_value2enum_32[] = {
	{ 0,	29,	"fullyAndPartialAndNonCoherent" },
	{ 1,	21,	"partialAndNonCoherent" },
	{ 2,	11,	"nonCoherent" }
};
static const unsigned int asn_MAP_NR_codebookSubset_enum2value_32[] = {
	0,	/* fullyAndPartialAndNonCoherent(0) */
	2,	/* nonCoherent(2) */
	1	/* partialAndNonCoherent(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_codebookSubset_specs_32 = {
	asn_MAP_NR_codebookSubset_value2enum_32,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_codebookSubset_enum2value_32,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_codebookSubset_tags_32[] = {
	(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_codebookSubset_32 = {
	"codebookSubset",
	"codebookSubset",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_codebookSubset_tags_32,
	sizeof(asn_DEF_NR_codebookSubset_tags_32)
		/sizeof(asn_DEF_NR_codebookSubset_tags_32[0]) - 1, /* 1 */
	asn_DEF_NR_codebookSubset_tags_32,	/* Same as above */
	sizeof(asn_DEF_NR_codebookSubset_tags_32)
		/sizeof(asn_DEF_NR_codebookSubset_tags_32[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_codebookSubset_constr_32,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_codebookSubset_specs_32	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_rbg_Size_value2enum_37[] = {
	{ 0,	7,	"config2" }
};
static const unsigned int asn_MAP_NR_rbg_Size_enum2value_37[] = {
	0	/* config2(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_rbg_Size_specs_37 = {
	asn_MAP_NR_rbg_Size_value2enum_37,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_rbg_Size_enum2value_37,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_rbg_Size_tags_37[] = {
	(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_rbg_Size_37 = {
	"rbg-Size",
	"rbg-Size",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_rbg_Size_tags_37,
	sizeof(asn_DEF_NR_rbg_Size_tags_37)
		/sizeof(asn_DEF_NR_rbg_Size_tags_37[0]) - 1, /* 1 */
	asn_DEF_NR_rbg_Size_tags_37,	/* Same as above */
	sizeof(asn_DEF_NR_rbg_Size_tags_37)
		/sizeof(asn_DEF_NR_rbg_Size_tags_37[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_rbg_Size_constr_37,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_rbg_Size_specs_37	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_tp_pi2BPSK_value2enum_40[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_tp_pi2BPSK_enum2value_40[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_tp_pi2BPSK_specs_40 = {
	asn_MAP_NR_tp_pi2BPSK_value2enum_40,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_tp_pi2BPSK_enum2value_40,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_tp_pi2BPSK_tags_40[] = {
	(ASN_TAG_CLASS_CONTEXT | (17 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_tp_pi2BPSK_40 = {
	"tp-pi2BPSK",
	"tp-pi2BPSK",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_tp_pi2BPSK_tags_40,
	sizeof(asn_DEF_NR_tp_pi2BPSK_tags_40)
		/sizeof(asn_DEF_NR_tp_pi2BPSK_tags_40[0]) - 1, /* 1 */
	asn_DEF_NR_tp_pi2BPSK_tags_40,	/* Same as above */
	sizeof(asn_DEF_NR_tp_pi2BPSK_tags_40)
		/sizeof(asn_DEF_NR_tp_pi2BPSK_tags_40[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_tp_pi2BPSK_constr_40,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_tp_pi2BPSK_specs_40	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dmrs_SequenceInitializationDCI_0_2_r16_value2enum_47[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_dmrs_SequenceInitializationDCI_0_2_r16_enum2value_47[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dmrs_SequenceInitializationDCI_0_2_r16_specs_47 = {
	asn_MAP_NR_dmrs_SequenceInitializationDCI_0_2_r16_value2enum_47,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dmrs_SequenceInitializationDCI_0_2_r16_enum2value_47,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dmrs_SequenceInitializationDCI_0_2_r16_tags_47[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dmrs_SequenceInitializationDCI_0_2_r16_47 = {
	"dmrs-SequenceInitializationDCI-0-2-r16",
	"dmrs-SequenceInitializationDCI-0-2-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dmrs_SequenceInitializationDCI_0_2_r16_tags_47,
	sizeof(asn_DEF_NR_dmrs_SequenceInitializationDCI_0_2_r16_tags_47)
		/sizeof(asn_DEF_NR_dmrs_SequenceInitializationDCI_0_2_r16_tags_47[0]) - 1, /* 1 */
	asn_DEF_NR_dmrs_SequenceInitializationDCI_0_2_r16_tags_47,	/* Same as above */
	sizeof(asn_DEF_NR_dmrs_SequenceInitializationDCI_0_2_r16_tags_47)
		/sizeof(asn_DEF_NR_dmrs_SequenceInitializationDCI_0_2_r16_tags_47[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dmrs_SequenceInitializationDCI_0_2_r16_constr_47,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dmrs_SequenceInitializationDCI_0_2_r16_specs_47	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_antennaPortsFieldPresenceDCI_0_2_r16_value2enum_50[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_antennaPortsFieldPresenceDCI_0_2_r16_enum2value_50[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_antennaPortsFieldPresenceDCI_0_2_r16_specs_50 = {
	asn_MAP_NR_antennaPortsFieldPresenceDCI_0_2_r16_value2enum_50,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_antennaPortsFieldPresenceDCI_0_2_r16_enum2value_50,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_antennaPortsFieldPresenceDCI_0_2_r16_tags_50[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_antennaPortsFieldPresenceDCI_0_2_r16_50 = {
	"antennaPortsFieldPresenceDCI-0-2-r16",
	"antennaPortsFieldPresenceDCI-0-2-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_antennaPortsFieldPresenceDCI_0_2_r16_tags_50,
	sizeof(asn_DEF_NR_antennaPortsFieldPresenceDCI_0_2_r16_tags_50)
		/sizeof(asn_DEF_NR_antennaPortsFieldPresenceDCI_0_2_r16_tags_50[0]) - 1, /* 1 */
	asn_DEF_NR_antennaPortsFieldPresenceDCI_0_2_r16_tags_50,	/* Same as above */
	sizeof(asn_DEF_NR_antennaPortsFieldPresenceDCI_0_2_r16_tags_50)
		/sizeof(asn_DEF_NR_antennaPortsFieldPresenceDCI_0_2_r16_tags_50[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_antennaPortsFieldPresenceDCI_0_2_r16_constr_50,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_antennaPortsFieldPresenceDCI_0_2_r16_specs_50	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pusch_RepTypeA_value2enum_55[] = {
	{ 0,	9,	"intraSlot" },
	{ 1,	9,	"interSlot" }
};
static const unsigned int asn_MAP_NR_pusch_RepTypeA_enum2value_55[] = {
	1,	/* interSlot(1) */
	0	/* intraSlot(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pusch_RepTypeA_specs_55 = {
	asn_MAP_NR_pusch_RepTypeA_value2enum_55,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pusch_RepTypeA_enum2value_55,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pusch_RepTypeA_tags_55[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pusch_RepTypeA_55 = {
	"pusch-RepTypeA",
	"pusch-RepTypeA",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pusch_RepTypeA_tags_55,
	sizeof(asn_DEF_NR_pusch_RepTypeA_tags_55)
		/sizeof(asn_DEF_NR_pusch_RepTypeA_tags_55[0]) - 1, /* 1 */
	asn_DEF_NR_pusch_RepTypeA_tags_55,	/* Same as above */
	sizeof(asn_DEF_NR_pusch_RepTypeA_tags_55)
		/sizeof(asn_DEF_NR_pusch_RepTypeA_tags_55[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pusch_RepTypeA_constr_55,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pusch_RepTypeA_specs_55	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pusch_RepTypeB_value2enum_58[] = {
	{ 0,	15,	"interRepetition" },
	{ 1,	9,	"interSlot" }
};
static const unsigned int asn_MAP_NR_pusch_RepTypeB_enum2value_58[] = {
	0,	/* interRepetition(0) */
	1	/* interSlot(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pusch_RepTypeB_specs_58 = {
	asn_MAP_NR_pusch_RepTypeB_value2enum_58,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pusch_RepTypeB_enum2value_58,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pusch_RepTypeB_tags_58[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pusch_RepTypeB_58 = {
	"pusch-RepTypeB",
	"pusch-RepTypeB",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pusch_RepTypeB_tags_58,
	sizeof(asn_DEF_NR_pusch_RepTypeB_tags_58)
		/sizeof(asn_DEF_NR_pusch_RepTypeB_tags_58[0]) - 1, /* 1 */
	asn_DEF_NR_pusch_RepTypeB_tags_58,	/* Same as above */
	sizeof(asn_DEF_NR_pusch_RepTypeB_tags_58)
		/sizeof(asn_DEF_NR_pusch_RepTypeB_tags_58[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pusch_RepTypeB_constr_58,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pusch_RepTypeB_specs_58	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_frequencyHoppingDCI_0_2_r16_54[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PUSCH_Config__ext1__frequencyHoppingDCI_0_2_r16, choice.pusch_RepTypeA),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pusch_RepTypeA_55,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pusch-RepTypeA"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PUSCH_Config__ext1__frequencyHoppingDCI_0_2_r16, choice.pusch_RepTypeB),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pusch_RepTypeB_58,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pusch-RepTypeB"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_NR_frequencyHoppingDCI_0_2_r16_tag2el_54[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* pusch-RepTypeA */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* pusch-RepTypeB */
};
static asn_CHOICE_specifics_t asn_SPC_NR_frequencyHoppingDCI_0_2_r16_specs_54 = {
	sizeof(struct NR_PUSCH_Config__ext1__frequencyHoppingDCI_0_2_r16),
	offsetof(struct NR_PUSCH_Config__ext1__frequencyHoppingDCI_0_2_r16, _asn_ctx),
	offsetof(struct NR_PUSCH_Config__ext1__frequencyHoppingDCI_0_2_r16, present),
	sizeof(((struct NR_PUSCH_Config__ext1__frequencyHoppingDCI_0_2_r16 *)0)->present),
	asn_MAP_NR_frequencyHoppingDCI_0_2_r16_tag2el_54,
	2,	/* Count of tags in the map */
	0, 0,
	-1	/* Extensions start */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_frequencyHoppingDCI_0_2_r16_54 = {
	"frequencyHoppingDCI-0-2-r16",
	"frequencyHoppingDCI-0-2-r16",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_frequencyHoppingDCI_0_2_r16_constr_54,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		CHOICE_constraint
	},
	asn_MBR_NR_frequencyHoppingDCI_0_2_r16_54,
	2,	/* Elements count */
	&asn_SPC_NR_frequencyHoppingDCI_0_2_r16_specs_54	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_codebookSubsetDCI_0_2_r16_value2enum_62[] = {
	{ 0,	29,	"fullyAndPartialAndNonCoherent" },
	{ 1,	21,	"partialAndNonCoherent" },
	{ 2,	11,	"nonCoherent" }
};
static const unsigned int asn_MAP_NR_codebookSubsetDCI_0_2_r16_enum2value_62[] = {
	0,	/* fullyAndPartialAndNonCoherent(0) */
	2,	/* nonCoherent(2) */
	1	/* partialAndNonCoherent(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_codebookSubsetDCI_0_2_r16_specs_62 = {
	asn_MAP_NR_codebookSubsetDCI_0_2_r16_value2enum_62,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_codebookSubsetDCI_0_2_r16_enum2value_62,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_codebookSubsetDCI_0_2_r16_tags_62[] = {
	(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_codebookSubsetDCI_0_2_r16_62 = {
	"codebookSubsetDCI-0-2-r16",
	"codebookSubsetDCI-0-2-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_codebookSubsetDCI_0_2_r16_tags_62,
	sizeof(asn_DEF_NR_codebookSubsetDCI_0_2_r16_tags_62)
		/sizeof(asn_DEF_NR_codebookSubsetDCI_0_2_r16_tags_62[0]) - 1, /* 1 */
	asn_DEF_NR_codebookSubsetDCI_0_2_r16_tags_62,	/* Same as above */
	sizeof(asn_DEF_NR_codebookSubsetDCI_0_2_r16_tags_62)
		/sizeof(asn_DEF_NR_codebookSubsetDCI_0_2_r16_tags_62[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_codebookSubsetDCI_0_2_r16_constr_62,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_codebookSubsetDCI_0_2_r16_specs_62	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_invalidSymbolPatternIndicatorDCI_0_2_r16_value2enum_66[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_invalidSymbolPatternIndicatorDCI_0_2_r16_enum2value_66[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_invalidSymbolPatternIndicatorDCI_0_2_r16_specs_66 = {
	asn_MAP_NR_invalidSymbolPatternIndicatorDCI_0_2_r16_value2enum_66,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_invalidSymbolPatternIndicatorDCI_0_2_r16_enum2value_66,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_invalidSymbolPatternIndicatorDCI_0_2_r16_tags_66[] = {
	(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_invalidSymbolPatternIndicatorDCI_0_2_r16_66 = {
	"invalidSymbolPatternIndicatorDCI-0-2-r16",
	"invalidSymbolPatternIndicatorDCI-0-2-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_invalidSymbolPatternIndicatorDCI_0_2_r16_tags_66,
	sizeof(asn_DEF_NR_invalidSymbolPatternIndicatorDCI_0_2_r16_tags_66)
		/sizeof(asn_DEF_NR_invalidSymbolPatternIndicatorDCI_0_2_r16_tags_66[0]) - 1, /* 1 */
	asn_DEF_NR_invalidSymbolPatternIndicatorDCI_0_2_r16_tags_66,	/* Same as above */
	sizeof(asn_DEF_NR_invalidSymbolPatternIndicatorDCI_0_2_r16_tags_66)
		/sizeof(asn_DEF_NR_invalidSymbolPatternIndicatorDCI_0_2_r16_tags_66[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_invalidSymbolPatternIndicatorDCI_0_2_r16_constr_66,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_invalidSymbolPatternIndicatorDCI_0_2_r16_specs_66	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_mcs_TableDCI_0_2_r16_value2enum_69[] = {
	{ 0,	6,	"qam256" },
	{ 1,	10,	"qam64LowSE" }
};
static const unsigned int asn_MAP_NR_mcs_TableDCI_0_2_r16_enum2value_69[] = {
	0,	/* qam256(0) */
	1	/* qam64LowSE(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_mcs_TableDCI_0_2_r16_specs_69 = {
	asn_MAP_NR_mcs_TableDCI_0_2_r16_value2enum_69,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_mcs_TableDCI_0_2_r16_enum2value_69,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_mcs_TableDCI_0_2_r16_tags_69[] = {
	(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mcs_TableDCI_0_2_r16_69 = {
	"mcs-TableDCI-0-2-r16",
	"mcs-TableDCI-0-2-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_mcs_TableDCI_0_2_r16_tags_69,
	sizeof(asn_DEF_NR_mcs_TableDCI_0_2_r16_tags_69)
		/sizeof(asn_DEF_NR_mcs_TableDCI_0_2_r16_tags_69[0]) - 1, /* 1 */
	asn_DEF_NR_mcs_TableDCI_0_2_r16_tags_69,	/* Same as above */
	sizeof(asn_DEF_NR_mcs_TableDCI_0_2_r16_tags_69)
		/sizeof(asn_DEF_NR_mcs_TableDCI_0_2_r16_tags_69[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_mcs_TableDCI_0_2_r16_constr_69,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_mcs_TableDCI_0_2_r16_specs_69	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_mcs_TableTransformPrecoderDCI_0_2_r16_value2enum_72[] = {
	{ 0,	6,	"qam256" },
	{ 1,	10,	"qam64LowSE" }
};
static const unsigned int asn_MAP_NR_mcs_TableTransformPrecoderDCI_0_2_r16_enum2value_72[] = {
	0,	/* qam256(0) */
	1	/* qam64LowSE(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_mcs_TableTransformPrecoderDCI_0_2_r16_specs_72 = {
	asn_MAP_NR_mcs_TableTransformPrecoderDCI_0_2_r16_value2enum_72,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_mcs_TableTransformPrecoderDCI_0_2_r16_enum2value_72,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_mcs_TableTransformPrecoderDCI_0_2_r16_tags_72[] = {
	(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mcs_TableTransformPrecoderDCI_0_2_r16_72 = {
	"mcs-TableTransformPrecoderDCI-0-2-r16",
	"mcs-TableTransformPrecoderDCI-0-2-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_mcs_TableTransformPrecoderDCI_0_2_r16_tags_72,
	sizeof(asn_DEF_NR_mcs_TableTransformPrecoderDCI_0_2_r16_tags_72)
		/sizeof(asn_DEF_NR_mcs_TableTransformPrecoderDCI_0_2_r16_tags_72[0]) - 1, /* 1 */
	asn_DEF_NR_mcs_TableTransformPrecoderDCI_0_2_r16_tags_72,	/* Same as above */
	sizeof(asn_DEF_NR_mcs_TableTransformPrecoderDCI_0_2_r16_tags_72)
		/sizeof(asn_DEF_NR_mcs_TableTransformPrecoderDCI_0_2_r16_tags_72[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_mcs_TableTransformPrecoderDCI_0_2_r16_constr_72,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_mcs_TableTransformPrecoderDCI_0_2_r16_specs_72	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_priorityIndicatorDCI_0_2_r16_value2enum_75[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_priorityIndicatorDCI_0_2_r16_enum2value_75[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_priorityIndicatorDCI_0_2_r16_specs_75 = {
	asn_MAP_NR_priorityIndicatorDCI_0_2_r16_value2enum_75,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_priorityIndicatorDCI_0_2_r16_enum2value_75,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_priorityIndicatorDCI_0_2_r16_tags_75[] = {
	(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_priorityIndicatorDCI_0_2_r16_75 = {
	"priorityIndicatorDCI-0-2-r16",
	"priorityIndicatorDCI-0-2-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_priorityIndicatorDCI_0_2_r16_tags_75,
	sizeof(asn_DEF_NR_priorityIndicatorDCI_0_2_r16_tags_75)
		/sizeof(asn_DEF_NR_priorityIndicatorDCI_0_2_r16_tags_75[0]) - 1, /* 1 */
	asn_DEF_NR_priorityIndicatorDCI_0_2_r16_tags_75,	/* Same as above */
	sizeof(asn_DEF_NR_priorityIndicatorDCI_0_2_r16_tags_75)
		/sizeof(asn_DEF_NR_priorityIndicatorDCI_0_2_r16_tags_75[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_priorityIndicatorDCI_0_2_r16_constr_75,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_priorityIndicatorDCI_0_2_r16_specs_75	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pusch_RepTypeIndicatorDCI_0_2_r16_value2enum_77[] = {
	{ 0,	14,	"pusch-RepTypeA" },
	{ 1,	14,	"pusch-RepTypeB" }
};
static const unsigned int asn_MAP_NR_pusch_RepTypeIndicatorDCI_0_2_r16_enum2value_77[] = {
	0,	/* pusch-RepTypeA(0) */
	1	/* pusch-RepTypeB(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pusch_RepTypeIndicatorDCI_0_2_r16_specs_77 = {
	asn_MAP_NR_pusch_RepTypeIndicatorDCI_0_2_r16_value2enum_77,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pusch_RepTypeIndicatorDCI_0_2_r16_enum2value_77,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pusch_RepTypeIndicatorDCI_0_2_r16_tags_77[] = {
	(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pusch_RepTypeIndicatorDCI_0_2_r16_77 = {
	"pusch-RepTypeIndicatorDCI-0-2-r16",
	"pusch-RepTypeIndicatorDCI-0-2-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pusch_RepTypeIndicatorDCI_0_2_r16_tags_77,
	sizeof(asn_DEF_NR_pusch_RepTypeIndicatorDCI_0_2_r16_tags_77)
		/sizeof(asn_DEF_NR_pusch_RepTypeIndicatorDCI_0_2_r16_tags_77[0]) - 1, /* 1 */
	asn_DEF_NR_pusch_RepTypeIndicatorDCI_0_2_r16_tags_77,	/* Same as above */
	sizeof(asn_DEF_NR_pusch_RepTypeIndicatorDCI_0_2_r16_tags_77)
		/sizeof(asn_DEF_NR_pusch_RepTypeIndicatorDCI_0_2_r16_tags_77[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pusch_RepTypeIndicatorDCI_0_2_r16_constr_77,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pusch_RepTypeIndicatorDCI_0_2_r16_specs_77	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_resourceAllocationDCI_0_2_r16_value2enum_80[] = {
	{ 0,	23,	"resourceAllocationType0" },
	{ 1,	23,	"resourceAllocationType1" },
	{ 2,	13,	"dynamicSwitch" }
};
static const unsigned int asn_MAP_NR_resourceAllocationDCI_0_2_r16_enum2value_80[] = {
	2,	/* dynamicSwitch(2) */
	0,	/* resourceAllocationType0(0) */
	1	/* resourceAllocationType1(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_resourceAllocationDCI_0_2_r16_specs_80 = {
	asn_MAP_NR_resourceAllocationDCI_0_2_r16_value2enum_80,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_resourceAllocationDCI_0_2_r16_enum2value_80,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_resourceAllocationDCI_0_2_r16_tags_80[] = {
	(ASN_TAG_CLASS_CONTEXT | (17 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_resourceAllocationDCI_0_2_r16_80 = {
	"resourceAllocationDCI-0-2-r16",
	"resourceAllocationDCI-0-2-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_resourceAllocationDCI_0_2_r16_tags_80,
	sizeof(asn_DEF_NR_resourceAllocationDCI_0_2_r16_tags_80)
		/sizeof(asn_DEF_NR_resourceAllocationDCI_0_2_r16_tags_80[0]) - 1, /* 1 */
	asn_DEF_NR_resourceAllocationDCI_0_2_r16_tags_80,	/* Same as above */
	sizeof(asn_DEF_NR_resourceAllocationDCI_0_2_r16_tags_80)
		/sizeof(asn_DEF_NR_resourceAllocationDCI_0_2_r16_tags_80[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_resourceAllocationDCI_0_2_r16_constr_80,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_resourceAllocationDCI_0_2_r16_specs_80	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_resourceAllocationType1GranularityDCI_0_2_r16_value2enum_84[] = {
	{ 0,	2,	"n2" },
	{ 1,	2,	"n4" },
	{ 2,	2,	"n8" },
	{ 3,	3,	"n16" }
};
static const unsigned int asn_MAP_NR_resourceAllocationType1GranularityDCI_0_2_r16_enum2value_84[] = {
	3,	/* n16(3) */
	0,	/* n2(0) */
	1,	/* n4(1) */
	2	/* n8(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_resourceAllocationType1GranularityDCI_0_2_r16_specs_84 = {
	asn_MAP_NR_resourceAllocationType1GranularityDCI_0_2_r16_value2enum_84,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_resourceAllocationType1GranularityDCI_0_2_r16_enum2value_84,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_resourceAllocationType1GranularityDCI_0_2_r16_tags_84[] = {
	(ASN_TAG_CLASS_CONTEXT | (18 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_resourceAllocationType1GranularityDCI_0_2_r16_84 = {
	"resourceAllocationType1GranularityDCI-0-2-r16",
	"resourceAllocationType1GranularityDCI-0-2-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_resourceAllocationType1GranularityDCI_0_2_r16_tags_84,
	sizeof(asn_DEF_NR_resourceAllocationType1GranularityDCI_0_2_r16_tags_84)
		/sizeof(asn_DEF_NR_resourceAllocationType1GranularityDCI_0_2_r16_tags_84[0]) - 1, /* 1 */
	asn_DEF_NR_resourceAllocationType1GranularityDCI_0_2_r16_tags_84,	/* Same as above */
	sizeof(asn_DEF_NR_resourceAllocationType1GranularityDCI_0_2_r16_tags_84)
		/sizeof(asn_DEF_NR_resourceAllocationType1GranularityDCI_0_2_r16_tags_84[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_resourceAllocationType1GranularityDCI_0_2_r16_constr_84,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_resourceAllocationType1GranularityDCI_0_2_r16_specs_84	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_invalidSymbolPatternIndicatorDCI_0_1_r16_value2enum_92[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_invalidSymbolPatternIndicatorDCI_0_1_r16_enum2value_92[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_invalidSymbolPatternIndicatorDCI_0_1_r16_specs_92 = {
	asn_MAP_NR_invalidSymbolPatternIndicatorDCI_0_1_r16_value2enum_92,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_invalidSymbolPatternIndicatorDCI_0_1_r16_enum2value_92,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_invalidSymbolPatternIndicatorDCI_0_1_r16_tags_92[] = {
	(ASN_TAG_CLASS_CONTEXT | (22 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_invalidSymbolPatternIndicatorDCI_0_1_r16_92 = {
	"invalidSymbolPatternIndicatorDCI-0-1-r16",
	"invalidSymbolPatternIndicatorDCI-0-1-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_invalidSymbolPatternIndicatorDCI_0_1_r16_tags_92,
	sizeof(asn_DEF_NR_invalidSymbolPatternIndicatorDCI_0_1_r16_tags_92)
		/sizeof(asn_DEF_NR_invalidSymbolPatternIndicatorDCI_0_1_r16_tags_92[0]) - 1, /* 1 */
	asn_DEF_NR_invalidSymbolPatternIndicatorDCI_0_1_r16_tags_92,	/* Same as above */
	sizeof(asn_DEF_NR_invalidSymbolPatternIndicatorDCI_0_1_r16_tags_92)
		/sizeof(asn_DEF_NR_invalidSymbolPatternIndicatorDCI_0_1_r16_tags_92[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_invalidSymbolPatternIndicatorDCI_0_1_r16_constr_92,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_invalidSymbolPatternIndicatorDCI_0_1_r16_specs_92	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_priorityIndicatorDCI_0_1_r16_value2enum_94[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_priorityIndicatorDCI_0_1_r16_enum2value_94[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_priorityIndicatorDCI_0_1_r16_specs_94 = {
	asn_MAP_NR_priorityIndicatorDCI_0_1_r16_value2enum_94,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_priorityIndicatorDCI_0_1_r16_enum2value_94,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_priorityIndicatorDCI_0_1_r16_tags_94[] = {
	(ASN_TAG_CLASS_CONTEXT | (23 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_priorityIndicatorDCI_0_1_r16_94 = {
	"priorityIndicatorDCI-0-1-r16",
	"priorityIndicatorDCI-0-1-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_priorityIndicatorDCI_0_1_r16_tags_94,
	sizeof(asn_DEF_NR_priorityIndicatorDCI_0_1_r16_tags_94)
		/sizeof(asn_DEF_NR_priorityIndicatorDCI_0_1_r16_tags_94[0]) - 1, /* 1 */
	asn_DEF_NR_priorityIndicatorDCI_0_1_r16_tags_94,	/* Same as above */
	sizeof(asn_DEF_NR_priorityIndicatorDCI_0_1_r16_tags_94)
		/sizeof(asn_DEF_NR_priorityIndicatorDCI_0_1_r16_tags_94[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_priorityIndicatorDCI_0_1_r16_constr_94,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_priorityIndicatorDCI_0_1_r16_specs_94	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pusch_RepTypeIndicatorDCI_0_1_r16_value2enum_96[] = {
	{ 0,	14,	"pusch-RepTypeA" },
	{ 1,	14,	"pusch-RepTypeB" }
};
static const unsigned int asn_MAP_NR_pusch_RepTypeIndicatorDCI_0_1_r16_enum2value_96[] = {
	0,	/* pusch-RepTypeA(0) */
	1	/* pusch-RepTypeB(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pusch_RepTypeIndicatorDCI_0_1_r16_specs_96 = {
	asn_MAP_NR_pusch_RepTypeIndicatorDCI_0_1_r16_value2enum_96,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pusch_RepTypeIndicatorDCI_0_1_r16_enum2value_96,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pusch_RepTypeIndicatorDCI_0_1_r16_tags_96[] = {
	(ASN_TAG_CLASS_CONTEXT | (24 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pusch_RepTypeIndicatorDCI_0_1_r16_96 = {
	"pusch-RepTypeIndicatorDCI-0-1-r16",
	"pusch-RepTypeIndicatorDCI-0-1-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pusch_RepTypeIndicatorDCI_0_1_r16_tags_96,
	sizeof(asn_DEF_NR_pusch_RepTypeIndicatorDCI_0_1_r16_tags_96)
		/sizeof(asn_DEF_NR_pusch_RepTypeIndicatorDCI_0_1_r16_tags_96[0]) - 1, /* 1 */
	asn_DEF_NR_pusch_RepTypeIndicatorDCI_0_1_r16_tags_96,	/* Same as above */
	sizeof(asn_DEF_NR_pusch_RepTypeIndicatorDCI_0_1_r16_tags_96)
		/sizeof(asn_DEF_NR_pusch_RepTypeIndicatorDCI_0_1_r16_tags_96[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pusch_RepTypeIndicatorDCI_0_1_r16_constr_96,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pusch_RepTypeIndicatorDCI_0_1_r16_specs_96	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_frequencyHoppingDCI_0_1_r16_value2enum_99[] = {
	{ 0,	15,	"interRepetition" },
	{ 1,	9,	"interSlot" }
};
static const unsigned int asn_MAP_NR_frequencyHoppingDCI_0_1_r16_enum2value_99[] = {
	0,	/* interRepetition(0) */
	1	/* interSlot(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_frequencyHoppingDCI_0_1_r16_specs_99 = {
	asn_MAP_NR_frequencyHoppingDCI_0_1_r16_value2enum_99,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_frequencyHoppingDCI_0_1_r16_enum2value_99,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_frequencyHoppingDCI_0_1_r16_tags_99[] = {
	(ASN_TAG_CLASS_CONTEXT | (25 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_frequencyHoppingDCI_0_1_r16_99 = {
	"frequencyHoppingDCI-0-1-r16",
	"frequencyHoppingDCI-0-1-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_frequencyHoppingDCI_0_1_r16_tags_99,
	sizeof(asn_DEF_NR_frequencyHoppingDCI_0_1_r16_tags_99)
		/sizeof(asn_DEF_NR_frequencyHoppingDCI_0_1_r16_tags_99[0]) - 1, /* 1 */
	asn_DEF_NR_frequencyHoppingDCI_0_1_r16_tags_99,	/* Same as above */
	sizeof(asn_DEF_NR_frequencyHoppingDCI_0_1_r16_tags_99)
		/sizeof(asn_DEF_NR_frequencyHoppingDCI_0_1_r16_tags_99[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_frequencyHoppingDCI_0_1_r16_constr_99,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_frequencyHoppingDCI_0_1_r16_specs_99	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_ul_FullPowerTransmission_r16_value2enum_105[] = {
	{ 0,	9,	"fullpower" },
	{ 1,	14,	"fullpowerMode1" },
	{ 2,	14,	"fullpowerMode2" }
};
static const unsigned int asn_MAP_NR_ul_FullPowerTransmission_r16_enum2value_105[] = {
	0,	/* fullpower(0) */
	1,	/* fullpowerMode1(1) */
	2	/* fullpowerMode2(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ul_FullPowerTransmission_r16_specs_105 = {
	asn_MAP_NR_ul_FullPowerTransmission_r16_value2enum_105,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ul_FullPowerTransmission_r16_enum2value_105,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ul_FullPowerTransmission_r16_tags_105[] = {
	(ASN_TAG_CLASS_CONTEXT | (29 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ul_FullPowerTransmission_r16_105 = {
	"ul-FullPowerTransmission-r16",
	"ul-FullPowerTransmission-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ul_FullPowerTransmission_r16_tags_105,
	sizeof(asn_DEF_NR_ul_FullPowerTransmission_r16_tags_105)
		/sizeof(asn_DEF_NR_ul_FullPowerTransmission_r16_tags_105[0]) - 1, /* 1 */
	asn_DEF_NR_ul_FullPowerTransmission_r16_tags_105,	/* Same as above */
	sizeof(asn_DEF_NR_ul_FullPowerTransmission_r16_tags_105)
		/sizeof(asn_DEF_NR_ul_FullPowerTransmission_r16_tags_105[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ul_FullPowerTransmission_r16_constr_105,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ul_FullPowerTransmission_r16_specs_105	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext1_43[] = {
	{ ATF_POINTER, 32, offsetof(struct NR_PUSCH_Config__ext1, minimumSchedulingOffsetK2_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_MinSchedulingOffsetK2_Values_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"minimumSchedulingOffsetK2-r16"
		},
	{ ATF_POINTER, 31, offsetof(struct NR_PUSCH_Config__ext1, ul_AccessConfigListDCI_0_1_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_UL_AccessConfigListDCI_0_1_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ul-AccessConfigListDCI-0-1-r16"
		},
	{ ATF_POINTER, 30, offsetof(struct NR_PUSCH_Config__ext1, harq_ProcessNumberSizeDCI_0_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_harq_ProcessNumberSizeDCI_0_2_r16_constr_46,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_harq_ProcessNumberSizeDCI_0_2_r16_constraint_43
		},
		0, 0, /* No default value */
		"harq-ProcessNumberSizeDCI-0-2-r16"
		},
	{ ATF_POINTER, 29, offsetof(struct NR_PUSCH_Config__ext1, dmrs_SequenceInitializationDCI_0_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dmrs_SequenceInitializationDCI_0_2_r16_47,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dmrs-SequenceInitializationDCI-0-2-r16"
		},
	{ ATF_POINTER, 28, offsetof(struct NR_PUSCH_Config__ext1, numberOfBitsForRV_DCI_0_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_numberOfBitsForRV_DCI_0_2_r16_constr_49,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_numberOfBitsForRV_DCI_0_2_r16_constraint_43
		},
		0, 0, /* No default value */
		"numberOfBitsForRV-DCI-0-2-r16"
		},
	{ ATF_POINTER, 27, offsetof(struct NR_PUSCH_Config__ext1, antennaPortsFieldPresenceDCI_0_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_antennaPortsFieldPresenceDCI_0_2_r16_50,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"antennaPortsFieldPresenceDCI-0-2-r16"
		},
	{ ATF_POINTER, 26, offsetof(struct NR_PUSCH_Config__ext1, dmrs_UplinkForPUSCH_MappingTypeA_DCI_0_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_DMRS_UplinkConfig,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dmrs-UplinkForPUSCH-MappingTypeA-DCI-0-2-r16"
		},
	{ ATF_POINTER, 25, offsetof(struct NR_PUSCH_Config__ext1, dmrs_UplinkForPUSCH_MappingTypeB_DCI_0_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_DMRS_UplinkConfig,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dmrs-UplinkForPUSCH-MappingTypeB-DCI-0-2-r16"
		},
	{ ATF_POINTER, 24, offsetof(struct NR_PUSCH_Config__ext1, frequencyHoppingDCI_0_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_frequencyHoppingDCI_0_2_r16_54,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"frequencyHoppingDCI-0-2-r16"
		},
	{ ATF_POINTER, 23, offsetof(struct NR_PUSCH_Config__ext1, frequencyHoppingOffsetListsDCI_0_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_FrequencyHoppingOffsetListsDCI_0_2_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"frequencyHoppingOffsetListsDCI-0-2-r16"
		},
	{ ATF_POINTER, 22, offsetof(struct NR_PUSCH_Config__ext1, codebookSubsetDCI_0_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_codebookSubsetDCI_0_2_r16_62,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"codebookSubsetDCI-0-2-r16"
		},
	{ ATF_POINTER, 21, offsetof(struct NR_PUSCH_Config__ext1, invalidSymbolPatternIndicatorDCI_0_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_invalidSymbolPatternIndicatorDCI_0_2_r16_66,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"invalidSymbolPatternIndicatorDCI-0-2-r16"
		},
	{ ATF_POINTER, 20, offsetof(struct NR_PUSCH_Config__ext1, maxRankDCI_0_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_maxRankDCI_0_2_r16_constr_68,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_maxRankDCI_0_2_r16_constraint_43
		},
		0, 0, /* No default value */
		"maxRankDCI-0-2-r16"
		},
	{ ATF_POINTER, 19, offsetof(struct NR_PUSCH_Config__ext1, mcs_TableDCI_0_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_mcs_TableDCI_0_2_r16_69,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mcs-TableDCI-0-2-r16"
		},
	{ ATF_POINTER, 18, offsetof(struct NR_PUSCH_Config__ext1, mcs_TableTransformPrecoderDCI_0_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_mcs_TableTransformPrecoderDCI_0_2_r16_72,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mcs-TableTransformPrecoderDCI-0-2-r16"
		},
	{ ATF_POINTER, 17, offsetof(struct NR_PUSCH_Config__ext1, priorityIndicatorDCI_0_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_priorityIndicatorDCI_0_2_r16_75,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"priorityIndicatorDCI-0-2-r16"
		},
	{ ATF_POINTER, 16, offsetof(struct NR_PUSCH_Config__ext1, pusch_RepTypeIndicatorDCI_0_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pusch_RepTypeIndicatorDCI_0_2_r16_77,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pusch-RepTypeIndicatorDCI-0-2-r16"
		},
	{ ATF_POINTER, 15, offsetof(struct NR_PUSCH_Config__ext1, resourceAllocationDCI_0_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (17 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_resourceAllocationDCI_0_2_r16_80,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"resourceAllocationDCI-0-2-r16"
		},
	{ ATF_POINTER, 14, offsetof(struct NR_PUSCH_Config__ext1, resourceAllocationType1GranularityDCI_0_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (18 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_resourceAllocationType1GranularityDCI_0_2_r16_84,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"resourceAllocationType1GranularityDCI-0-2-r16"
		},
	{ ATF_POINTER, 13, offsetof(struct NR_PUSCH_Config__ext1, uci_OnPUSCH_ListDCI_0_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (19 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_UCI_OnPUSCH_ListDCI_0_2_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"uci-OnPUSCH-ListDCI-0-2-r16"
		},
	{ ATF_POINTER, 12, offsetof(struct NR_PUSCH_Config__ext1, pusch_TimeDomainAllocationListDCI_0_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (20 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_PUSCH_TimeDomainResourceAllocationList_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pusch-TimeDomainAllocationListDCI-0-2-r16"
		},
	{ ATF_POINTER, 11, offsetof(struct NR_PUSCH_Config__ext1, pusch_TimeDomainAllocationListDCI_0_1_r16),
		(ASN_TAG_CLASS_CONTEXT | (21 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_PUSCH_TimeDomainResourceAllocationList_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pusch-TimeDomainAllocationListDCI-0-1-r16"
		},
	{ ATF_POINTER, 10, offsetof(struct NR_PUSCH_Config__ext1, invalidSymbolPatternIndicatorDCI_0_1_r16),
		(ASN_TAG_CLASS_CONTEXT | (22 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_invalidSymbolPatternIndicatorDCI_0_1_r16_92,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"invalidSymbolPatternIndicatorDCI-0-1-r16"
		},
	{ ATF_POINTER, 9, offsetof(struct NR_PUSCH_Config__ext1, priorityIndicatorDCI_0_1_r16),
		(ASN_TAG_CLASS_CONTEXT | (23 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_priorityIndicatorDCI_0_1_r16_94,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"priorityIndicatorDCI-0-1-r16"
		},
	{ ATF_POINTER, 8, offsetof(struct NR_PUSCH_Config__ext1, pusch_RepTypeIndicatorDCI_0_1_r16),
		(ASN_TAG_CLASS_CONTEXT | (24 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pusch_RepTypeIndicatorDCI_0_1_r16_96,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pusch-RepTypeIndicatorDCI-0-1-r16"
		},
	{ ATF_POINTER, 7, offsetof(struct NR_PUSCH_Config__ext1, frequencyHoppingDCI_0_1_r16),
		(ASN_TAG_CLASS_CONTEXT | (25 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_frequencyHoppingDCI_0_1_r16_99,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"frequencyHoppingDCI-0-1-r16"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_PUSCH_Config__ext1, uci_OnPUSCH_ListDCI_0_1_r16),
		(ASN_TAG_CLASS_CONTEXT | (26 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_UCI_OnPUSCH_ListDCI_0_1_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"uci-OnPUSCH-ListDCI-0-1-r16"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_PUSCH_Config__ext1, invalidSymbolPattern_r16),
		(ASN_TAG_CLASS_CONTEXT | (27 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_InvalidSymbolPattern_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"invalidSymbolPattern-r16"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_PUSCH_Config__ext1, pusch_PowerControl_v1610),
		(ASN_TAG_CLASS_CONTEXT | (28 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_PUSCH_PowerControl_v1610,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pusch-PowerControl-v1610"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_PUSCH_Config__ext1, ul_FullPowerTransmission_r16),
		(ASN_TAG_CLASS_CONTEXT | (29 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ul_FullPowerTransmission_r16_105,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ul-FullPowerTransmission-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_PUSCH_Config__ext1, pusch_TimeDomainAllocationListForMultiPUSCH_r16),
		(ASN_TAG_CLASS_CONTEXT | (30 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_PUSCH_TimeDomainResourceAllocationList_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pusch-TimeDomainAllocationListForMultiPUSCH-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PUSCH_Config__ext1, numberOfInvalidSymbolsForDL_UL_Switching_r16),
		(ASN_TAG_CLASS_CONTEXT | (31 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_numberOfInvalidSymbolsForDL_UL_Switching_r16_constr_110,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_numberOfInvalidSymbolsForDL_UL_Switching_r16_constraint_43
		},
		0, 0, /* No default value */
		"numberOfInvalidSymbolsForDL-UL-Switching-r16"
		},
};
static const int asn_MAP_NR_ext1_oms_43[] = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_43[] = {
	(ASN_TAG_CLASS_CONTEXT | (18 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_43[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* minimumSchedulingOffsetK2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* ul-AccessConfigListDCI-0-1-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* harq-ProcessNumberSizeDCI-0-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* dmrs-SequenceInitializationDCI-0-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* numberOfBitsForRV-DCI-0-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* antennaPortsFieldPresenceDCI-0-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* dmrs-UplinkForPUSCH-MappingTypeA-DCI-0-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* dmrs-UplinkForPUSCH-MappingTypeB-DCI-0-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* frequencyHoppingDCI-0-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* frequencyHoppingOffsetListsDCI-0-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 }, /* codebookSubsetDCI-0-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (11 << 2)), 11, 0, 0 }, /* invalidSymbolPatternIndicatorDCI-0-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (12 << 2)), 12, 0, 0 }, /* maxRankDCI-0-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (13 << 2)), 13, 0, 0 }, /* mcs-TableDCI-0-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (14 << 2)), 14, 0, 0 }, /* mcs-TableTransformPrecoderDCI-0-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (15 << 2)), 15, 0, 0 }, /* priorityIndicatorDCI-0-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (16 << 2)), 16, 0, 0 }, /* pusch-RepTypeIndicatorDCI-0-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (17 << 2)), 17, 0, 0 }, /* resourceAllocationDCI-0-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (18 << 2)), 18, 0, 0 }, /* resourceAllocationType1GranularityDCI-0-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (19 << 2)), 19, 0, 0 }, /* uci-OnPUSCH-ListDCI-0-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (20 << 2)), 20, 0, 0 }, /* pusch-TimeDomainAllocationListDCI-0-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (21 << 2)), 21, 0, 0 }, /* pusch-TimeDomainAllocationListDCI-0-1-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (22 << 2)), 22, 0, 0 }, /* invalidSymbolPatternIndicatorDCI-0-1-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (23 << 2)), 23, 0, 0 }, /* priorityIndicatorDCI-0-1-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (24 << 2)), 24, 0, 0 }, /* pusch-RepTypeIndicatorDCI-0-1-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (25 << 2)), 25, 0, 0 }, /* frequencyHoppingDCI-0-1-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (26 << 2)), 26, 0, 0 }, /* uci-OnPUSCH-ListDCI-0-1-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (27 << 2)), 27, 0, 0 }, /* invalidSymbolPattern-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (28 << 2)), 28, 0, 0 }, /* pusch-PowerControl-v1610 */
    { (ASN_TAG_CLASS_CONTEXT | (29 << 2)), 29, 0, 0 }, /* ul-FullPowerTransmission-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (30 << 2)), 30, 0, 0 }, /* pusch-TimeDomainAllocationListForMultiPUSCH-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (31 << 2)), 31, 0, 0 } /* numberOfInvalidSymbolsForDL-UL-Switching-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_43 = {
	sizeof(struct NR_PUSCH_Config__ext1),
	offsetof(struct NR_PUSCH_Config__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_43,
	32,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_43,	/* Optional members */
	32, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_43 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_43,
	sizeof(asn_DEF_NR_ext1_tags_43)
		/sizeof(asn_DEF_NR_ext1_tags_43[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_43,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_43)
		/sizeof(asn_DEF_NR_ext1_tags_43[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_43,
	32,	/* Elements count */
	&asn_SPC_NR_ext1_specs_43	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_mappingPattern_r17_value2enum_117[] = {
	{ 0,	13,	"cyclicMapping" },
	{ 1,	17,	"sequentialMapping" }
};
static const unsigned int asn_MAP_NR_mappingPattern_r17_enum2value_117[] = {
	0,	/* cyclicMapping(0) */
	1	/* sequentialMapping(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_mappingPattern_r17_specs_117 = {
	asn_MAP_NR_mappingPattern_r17_value2enum_117,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_mappingPattern_r17_enum2value_117,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_mappingPattern_r17_tags_117[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mappingPattern_r17_117 = {
	"mappingPattern-r17",
	"mappingPattern-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_mappingPattern_r17_tags_117,
	sizeof(asn_DEF_NR_mappingPattern_r17_tags_117)
		/sizeof(asn_DEF_NR_mappingPattern_r17_tags_117[0]) - 1, /* 1 */
	asn_DEF_NR_mappingPattern_r17_tags_117,	/* Same as above */
	sizeof(asn_DEF_NR_mappingPattern_r17_tags_117)
		/sizeof(asn_DEF_NR_mappingPattern_r17_tags_117[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_mappingPattern_r17_constr_117,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_mappingPattern_r17_specs_117	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_secondTPCFieldDCI_0_1_r17_value2enum_120[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_secondTPCFieldDCI_0_1_r17_enum2value_120[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_secondTPCFieldDCI_0_1_r17_specs_120 = {
	asn_MAP_NR_secondTPCFieldDCI_0_1_r17_value2enum_120,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_secondTPCFieldDCI_0_1_r17_enum2value_120,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_secondTPCFieldDCI_0_1_r17_tags_120[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_secondTPCFieldDCI_0_1_r17_120 = {
	"secondTPCFieldDCI-0-1-r17",
	"secondTPCFieldDCI-0-1-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_secondTPCFieldDCI_0_1_r17_tags_120,
	sizeof(asn_DEF_NR_secondTPCFieldDCI_0_1_r17_tags_120)
		/sizeof(asn_DEF_NR_secondTPCFieldDCI_0_1_r17_tags_120[0]) - 1, /* 1 */
	asn_DEF_NR_secondTPCFieldDCI_0_1_r17_tags_120,	/* Same as above */
	sizeof(asn_DEF_NR_secondTPCFieldDCI_0_1_r17_tags_120)
		/sizeof(asn_DEF_NR_secondTPCFieldDCI_0_1_r17_tags_120[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_secondTPCFieldDCI_0_1_r17_constr_120,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_secondTPCFieldDCI_0_1_r17_specs_120	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_secondTPCFieldDCI_0_2_r17_value2enum_122[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_secondTPCFieldDCI_0_2_r17_enum2value_122[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_secondTPCFieldDCI_0_2_r17_specs_122 = {
	asn_MAP_NR_secondTPCFieldDCI_0_2_r17_value2enum_122,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_secondTPCFieldDCI_0_2_r17_enum2value_122,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_secondTPCFieldDCI_0_2_r17_tags_122[] = {
	(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_secondTPCFieldDCI_0_2_r17_122 = {
	"secondTPCFieldDCI-0-2-r17",
	"secondTPCFieldDCI-0-2-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_secondTPCFieldDCI_0_2_r17_tags_122,
	sizeof(asn_DEF_NR_secondTPCFieldDCI_0_2_r17_tags_122)
		/sizeof(asn_DEF_NR_secondTPCFieldDCI_0_2_r17_tags_122[0]) - 1, /* 1 */
	asn_DEF_NR_secondTPCFieldDCI_0_2_r17_tags_122,	/* Same as above */
	sizeof(asn_DEF_NR_secondTPCFieldDCI_0_2_r17_tags_122)
		/sizeof(asn_DEF_NR_secondTPCFieldDCI_0_2_r17_tags_122[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_secondTPCFieldDCI_0_2_r17_constr_122,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_secondTPCFieldDCI_0_2_r17_specs_122	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_availableSlotCounting_r17_value2enum_127[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_availableSlotCounting_r17_enum2value_127[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_availableSlotCounting_r17_specs_127 = {
	asn_MAP_NR_availableSlotCounting_r17_value2enum_127,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_availableSlotCounting_r17_enum2value_127,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_availableSlotCounting_r17_tags_127[] = {
	(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_availableSlotCounting_r17_127 = {
	"availableSlotCounting-r17",
	"availableSlotCounting-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_availableSlotCounting_r17_tags_127,
	sizeof(asn_DEF_NR_availableSlotCounting_r17_tags_127)
		/sizeof(asn_DEF_NR_availableSlotCounting_r17_tags_127[0]) - 1, /* 1 */
	asn_DEF_NR_availableSlotCounting_r17_tags_127,	/* Same as above */
	sizeof(asn_DEF_NR_availableSlotCounting_r17_tags_127)
		/sizeof(asn_DEF_NR_availableSlotCounting_r17_tags_127[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_availableSlotCounting_r17_constr_127,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_availableSlotCounting_r17_specs_127	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_mpe_ResourcePoolToAddModList_r17_132[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_MPE_Resource_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_mpe_ResourcePoolToAddModList_r17_tags_132[] = {
	(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_mpe_ResourcePoolToAddModList_r17_specs_132 = {
	sizeof(struct NR_PUSCH_Config__ext2__mpe_ResourcePoolToAddModList_r17),
	offsetof(struct NR_PUSCH_Config__ext2__mpe_ResourcePoolToAddModList_r17, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mpe_ResourcePoolToAddModList_r17_132 = {
	"mpe-ResourcePoolToAddModList-r17",
	"mpe-ResourcePoolToAddModList-r17",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_mpe_ResourcePoolToAddModList_r17_tags_132,
	sizeof(asn_DEF_NR_mpe_ResourcePoolToAddModList_r17_tags_132)
		/sizeof(asn_DEF_NR_mpe_ResourcePoolToAddModList_r17_tags_132[0]) - 1, /* 1 */
	asn_DEF_NR_mpe_ResourcePoolToAddModList_r17_tags_132,	/* Same as above */
	sizeof(asn_DEF_NR_mpe_ResourcePoolToAddModList_r17_tags_132)
		/sizeof(asn_DEF_NR_mpe_ResourcePoolToAddModList_r17_tags_132[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_mpe_ResourcePoolToAddModList_r17_constr_132,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_mpe_ResourcePoolToAddModList_r17_132,
	1,	/* Single element */
	&asn_SPC_NR_mpe_ResourcePoolToAddModList_r17_specs_132	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_mpe_ResourcePoolToReleaseList_r17_134[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (2 << 2)),
		0,
		&asn_DEF_NR_MPE_ResourceId_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_mpe_ResourcePoolToReleaseList_r17_tags_134[] = {
	(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_mpe_ResourcePoolToReleaseList_r17_specs_134 = {
	sizeof(struct NR_PUSCH_Config__ext2__mpe_ResourcePoolToReleaseList_r17),
	offsetof(struct NR_PUSCH_Config__ext2__mpe_ResourcePoolToReleaseList_r17, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mpe_ResourcePoolToReleaseList_r17_134 = {
	"mpe-ResourcePoolToReleaseList-r17",
	"mpe-ResourcePoolToReleaseList-r17",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_mpe_ResourcePoolToReleaseList_r17_tags_134,
	sizeof(asn_DEF_NR_mpe_ResourcePoolToReleaseList_r17_tags_134)
		/sizeof(asn_DEF_NR_mpe_ResourcePoolToReleaseList_r17_tags_134[0]) - 1, /* 1 */
	asn_DEF_NR_mpe_ResourcePoolToReleaseList_r17_tags_134,	/* Same as above */
	sizeof(asn_DEF_NR_mpe_ResourcePoolToReleaseList_r17_tags_134)
		/sizeof(asn_DEF_NR_mpe_ResourcePoolToReleaseList_r17_tags_134[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_mpe_ResourcePoolToReleaseList_r17_constr_134,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_mpe_ResourcePoolToReleaseList_r17_134,
	1,	/* Single element */
	&asn_SPC_NR_mpe_ResourcePoolToReleaseList_r17_specs_134	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext2_111[] = {
	{ ATF_POINTER, 17, offsetof(struct NR_PUSCH_Config__ext2, ul_AccessConfigListDCI_0_2_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_UL_AccessConfigListDCI_0_2_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ul-AccessConfigListDCI-0-2-r17"
		},
	{ ATF_POINTER, 16, offsetof(struct NR_PUSCH_Config__ext2, betaOffsetsCrossPri0_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_BetaOffsetsCrossPriSel_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"betaOffsetsCrossPri0-r17"
		},
	{ ATF_POINTER, 15, offsetof(struct NR_PUSCH_Config__ext2, betaOffsetsCrossPri1_r17),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_BetaOffsetsCrossPriSel_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"betaOffsetsCrossPri1-r17"
		},
	{ ATF_POINTER, 14, offsetof(struct NR_PUSCH_Config__ext2, betaOffsetsCrossPri0DCI_0_2_r17),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_BetaOffsetsCrossPriSelDCI_0_2_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"betaOffsetsCrossPri0DCI-0-2-r17"
		},
	{ ATF_POINTER, 13, offsetof(struct NR_PUSCH_Config__ext2, betaOffsetsCrossPri1DCI_0_2_r17),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_BetaOffsetsCrossPriSelDCI_0_2_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"betaOffsetsCrossPri1DCI-0-2-r17"
		},
	{ ATF_POINTER, 12, offsetof(struct NR_PUSCH_Config__ext2, mappingPattern_r17),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_mappingPattern_r17_117,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mappingPattern-r17"
		},
	{ ATF_POINTER, 11, offsetof(struct NR_PUSCH_Config__ext2, secondTPCFieldDCI_0_1_r17),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_secondTPCFieldDCI_0_1_r17_120,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"secondTPCFieldDCI-0-1-r17"
		},
	{ ATF_POINTER, 10, offsetof(struct NR_PUSCH_Config__ext2, secondTPCFieldDCI_0_2_r17),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_secondTPCFieldDCI_0_2_r17_122,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"secondTPCFieldDCI-0-2-r17"
		},
	{ ATF_POINTER, 9, offsetof(struct NR_PUSCH_Config__ext2, sequenceOffsetForRV_r17),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_sequenceOffsetForRV_r17_constr_124,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_sequenceOffsetForRV_r17_constraint_111
		},
		0, 0, /* No default value */
		"sequenceOffsetForRV-r17"
		},
	{ ATF_POINTER, 8, offsetof(struct NR_PUSCH_Config__ext2, ul_AccessConfigListDCI_0_1_r17),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_UL_AccessConfigListDCI_0_1_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ul-AccessConfigListDCI-0-1-r17"
		},
	{ ATF_POINTER, 7, offsetof(struct NR_PUSCH_Config__ext2, minimumSchedulingOffsetK2_r17),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_MinSchedulingOffsetK2_Values_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"minimumSchedulingOffsetK2-r17"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_PUSCH_Config__ext2, availableSlotCounting_r17),
		(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_availableSlotCounting_r17_127,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"availableSlotCounting-r17"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_PUSCH_Config__ext2, dmrs_BundlingPUSCH_Config_r17),
		(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_DMRS_BundlingPUSCH_Config_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dmrs-BundlingPUSCH-Config-r17"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_PUSCH_Config__ext2, harq_ProcessNumberSizeDCI_0_2_v1700),
		(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_harq_ProcessNumberSizeDCI_0_2_v1700_constr_130,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_harq_ProcessNumberSizeDCI_0_2_v1700_constraint_111
		},
		0, 0, /* No default value */
		"harq-ProcessNumberSizeDCI-0-2-v1700"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_PUSCH_Config__ext2, harq_ProcessNumberSizeDCI_0_1_r17),
		(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_harq_ProcessNumberSizeDCI_0_1_r17_constr_131,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_harq_ProcessNumberSizeDCI_0_1_r17_constraint_111
		},
		0, 0, /* No default value */
		"harq-ProcessNumberSizeDCI-0-1-r17"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_PUSCH_Config__ext2, mpe_ResourcePoolToAddModList_r17),
		(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
		0,
		&asn_DEF_NR_mpe_ResourcePoolToAddModList_r17_132,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_mpe_ResourcePoolToAddModList_r17_constr_132,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_mpe_ResourcePoolToAddModList_r17_constraint_111
		},
		0, 0, /* No default value */
		"mpe-ResourcePoolToAddModList-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PUSCH_Config__ext2, mpe_ResourcePoolToReleaseList_r17),
		(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
		0,
		&asn_DEF_NR_mpe_ResourcePoolToReleaseList_r17_134,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_mpe_ResourcePoolToReleaseList_r17_constr_134,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_mpe_ResourcePoolToReleaseList_r17_constraint_111
		},
		0, 0, /* No default value */
		"mpe-ResourcePoolToReleaseList-r17"
		},
};
static const int asn_MAP_NR_ext2_oms_111[] = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16 };
static const ber_tlv_tag_t asn_DEF_NR_ext2_tags_111[] = {
	(ASN_TAG_CLASS_CONTEXT | (19 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext2_tag2el_111[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* ul-AccessConfigListDCI-0-2-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* betaOffsetsCrossPri0-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* betaOffsetsCrossPri1-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* betaOffsetsCrossPri0DCI-0-2-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* betaOffsetsCrossPri1DCI-0-2-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* mappingPattern-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* secondTPCFieldDCI-0-1-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* secondTPCFieldDCI-0-2-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* sequenceOffsetForRV-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* ul-AccessConfigListDCI-0-1-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 }, /* minimumSchedulingOffsetK2-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (11 << 2)), 11, 0, 0 }, /* availableSlotCounting-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (12 << 2)), 12, 0, 0 }, /* dmrs-BundlingPUSCH-Config-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (13 << 2)), 13, 0, 0 }, /* harq-ProcessNumberSizeDCI-0-2-v1700 */
    { (ASN_TAG_CLASS_CONTEXT | (14 << 2)), 14, 0, 0 }, /* harq-ProcessNumberSizeDCI-0-1-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (15 << 2)), 15, 0, 0 }, /* mpe-ResourcePoolToAddModList-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (16 << 2)), 16, 0, 0 } /* mpe-ResourcePoolToReleaseList-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext2_specs_111 = {
	sizeof(struct NR_PUSCH_Config__ext2),
	offsetof(struct NR_PUSCH_Config__ext2, _asn_ctx),
	asn_MAP_NR_ext2_tag2el_111,
	17,	/* Count of tags in the map */
	asn_MAP_NR_ext2_oms_111,	/* Optional members */
	17, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext2_111 = {
	"ext2",
	"ext2",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext2_tags_111,
	sizeof(asn_DEF_NR_ext2_tags_111)
		/sizeof(asn_DEF_NR_ext2_tags_111[0]) - 1, /* 1 */
	asn_DEF_NR_ext2_tags_111,	/* Same as above */
	sizeof(asn_DEF_NR_ext2_tags_111)
		/sizeof(asn_DEF_NR_ext2_tags_111[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext2_111,
	17,	/* Elements count */
	&asn_SPC_NR_ext2_specs_111	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_PUSCH_Config_1[] = {
	{ ATF_POINTER, 7, offsetof(struct NR_PUSCH_Config, dataScramblingIdentityPUSCH),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_dataScramblingIdentityPUSCH_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_dataScramblingIdentityPUSCH_constraint_1
		},
		0, 0, /* No default value */
		"dataScramblingIdentityPUSCH"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_PUSCH_Config, txConfig),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_txConfig_3,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"txConfig"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_PUSCH_Config, dmrs_UplinkForPUSCH_MappingTypeA),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_DMRS_UplinkConfig,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dmrs-UplinkForPUSCH-MappingTypeA"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_PUSCH_Config, dmrs_UplinkForPUSCH_MappingTypeB),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_DMRS_UplinkConfig,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dmrs-UplinkForPUSCH-MappingTypeB"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_PUSCH_Config, pusch_PowerControl),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_PUSCH_PowerControl,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pusch-PowerControl"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_PUSCH_Config, frequencyHopping),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_frequencyHopping_9,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"frequencyHopping"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PUSCH_Config, frequencyHoppingOffsetLists),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		0,
		&asn_DEF_NR_frequencyHoppingOffsetLists_12,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_frequencyHoppingOffsetLists_constr_12,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_frequencyHoppingOffsetLists_constraint_1
		},
		0, 0, /* No default value */
		"frequencyHoppingOffsetLists"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PUSCH_Config, resourceAllocation),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_resourceAllocation_14,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"resourceAllocation"
		},
	{ ATF_POINTER, 12, offsetof(struct NR_PUSCH_Config, pusch_TimeDomainAllocationList),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_PUSCH_TimeDomainResourceAllocationList,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pusch-TimeDomainAllocationList"
		},
	{ ATF_POINTER, 11, offsetof(struct NR_PUSCH_Config, pusch_AggregationFactor),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pusch_AggregationFactor_19,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pusch-AggregationFactor"
		},
	{ ATF_POINTER, 10, offsetof(struct NR_PUSCH_Config, mcs_Table),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_mcs_Table_23,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mcs-Table"
		},
	{ ATF_POINTER, 9, offsetof(struct NR_PUSCH_Config, mcs_TableTransformPrecoder),
		(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_mcs_TableTransformPrecoder_26,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mcs-TableTransformPrecoder"
		},
	{ ATF_POINTER, 8, offsetof(struct NR_PUSCH_Config, transformPrecoder),
		(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_transformPrecoder_29,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"transformPrecoder"
		},
	{ ATF_POINTER, 7, offsetof(struct NR_PUSCH_Config, codebookSubset),
		(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_codebookSubset_32,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"codebookSubset"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_PUSCH_Config, maxRank),
		(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_maxRank_constr_36,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_maxRank_constraint_1
		},
		0, 0, /* No default value */
		"maxRank"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_PUSCH_Config, rbg_Size),
		(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_rbg_Size_37,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"rbg-Size"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_PUSCH_Config, uci_OnPUSCH),
		(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_UCI_OnPUSCH,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"uci-OnPUSCH"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_PUSCH_Config, tp_pi2BPSK),
		(ASN_TAG_CLASS_CONTEXT | (17 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_tp_pi2BPSK_40,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"tp-pi2BPSK"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_PUSCH_Config, ext1),
		(ASN_TAG_CLASS_CONTEXT | (18 << 2)),
		0,
		&asn_DEF_NR_ext1_43,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PUSCH_Config, ext2),
		(ASN_TAG_CLASS_CONTEXT | (19 << 2)),
		0,
		&asn_DEF_NR_ext2_111,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext2"
		},
};
static const int asn_MAP_NR_PUSCH_Config_oms_1[] = { 0, 1, 2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19 };
static const ber_tlv_tag_t asn_DEF_NR_PUSCH_Config_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_PUSCH_Config_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* dataScramblingIdentityPUSCH */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* txConfig */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* dmrs-UplinkForPUSCH-MappingTypeA */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* dmrs-UplinkForPUSCH-MappingTypeB */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* pusch-PowerControl */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* frequencyHopping */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* frequencyHoppingOffsetLists */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* resourceAllocation */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* pusch-TimeDomainAllocationList */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* pusch-AggregationFactor */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 }, /* mcs-Table */
    { (ASN_TAG_CLASS_CONTEXT | (11 << 2)), 11, 0, 0 }, /* mcs-TableTransformPrecoder */
    { (ASN_TAG_CLASS_CONTEXT | (12 << 2)), 12, 0, 0 }, /* transformPrecoder */
    { (ASN_TAG_CLASS_CONTEXT | (13 << 2)), 13, 0, 0 }, /* codebookSubset */
    { (ASN_TAG_CLASS_CONTEXT | (14 << 2)), 14, 0, 0 }, /* maxRank */
    { (ASN_TAG_CLASS_CONTEXT | (15 << 2)), 15, 0, 0 }, /* rbg-Size */
    { (ASN_TAG_CLASS_CONTEXT | (16 << 2)), 16, 0, 0 }, /* uci-OnPUSCH */
    { (ASN_TAG_CLASS_CONTEXT | (17 << 2)), 17, 0, 0 }, /* tp-pi2BPSK */
    { (ASN_TAG_CLASS_CONTEXT | (18 << 2)), 18, 0, 0 }, /* ext1 */
    { (ASN_TAG_CLASS_CONTEXT | (19 << 2)), 19, 0, 0 } /* ext2 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_PUSCH_Config_specs_1 = {
	sizeof(struct NR_PUSCH_Config),
	offsetof(struct NR_PUSCH_Config, _asn_ctx),
	asn_MAP_NR_PUSCH_Config_tag2el_1,
	20,	/* Count of tags in the map */
	asn_MAP_NR_PUSCH_Config_oms_1,	/* Optional members */
	17, 2,	/* Root/Additions */
	18,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_PUSCH_Config = {
	"PUSCH-Config",
	"PUSCH-Config",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_PUSCH_Config_tags_1,
	sizeof(asn_DEF_NR_PUSCH_Config_tags_1)
		/sizeof(asn_DEF_NR_PUSCH_Config_tags_1[0]), /* 1 */
	asn_DEF_NR_PUSCH_Config_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_PUSCH_Config_tags_1)
		/sizeof(asn_DEF_NR_PUSCH_Config_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_PUSCH_Config_1,
	20,	/* Elements count */
	&asn_SPC_NR_PUSCH_Config_specs_1	/* Additional specs */
};

