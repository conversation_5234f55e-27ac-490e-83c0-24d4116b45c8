/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_MeasResultSuccessHONR-r17.h"

static asn_TYPE_member_t asn_MBR_NR_cellResults_r17_3[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_MeasResultSuccessHONR_r17__measResult_r17__cellResults_r17, resultsSSB_Cell_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_MeasQuantityResults,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"resultsSSB-Cell-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MeasResultSuccessHONR_r17__measResult_r17__cellResults_r17, resultsCSI_RS_Cell_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_MeasQuantityResults,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"resultsCSI-RS-Cell-r17"
		},
};
static const int asn_MAP_NR_cellResults_r17_oms_3[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_cellResults_r17_tags_3[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_cellResults_r17_tag2el_3[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* resultsSSB-Cell-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* resultsCSI-RS-Cell-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_cellResults_r17_specs_3 = {
	sizeof(struct NR_MeasResultSuccessHONR_r17__measResult_r17__cellResults_r17),
	offsetof(struct NR_MeasResultSuccessHONR_r17__measResult_r17__cellResults_r17, _asn_ctx),
	asn_MAP_NR_cellResults_r17_tag2el_3,
	2,	/* Count of tags in the map */
	asn_MAP_NR_cellResults_r17_oms_3,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_cellResults_r17_3 = {
	"cellResults-r17",
	"cellResults-r17",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_cellResults_r17_tags_3,
	sizeof(asn_DEF_NR_cellResults_r17_tags_3)
		/sizeof(asn_DEF_NR_cellResults_r17_tags_3[0]) - 1, /* 1 */
	asn_DEF_NR_cellResults_r17_tags_3,	/* Same as above */
	sizeof(asn_DEF_NR_cellResults_r17_tags_3)
		/sizeof(asn_DEF_NR_cellResults_r17_tags_3[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_cellResults_r17_3,
	2,	/* Elements count */
	&asn_SPC_NR_cellResults_r17_specs_3	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_rsIndexResults_r17_6[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_MeasResultSuccessHONR_r17__measResult_r17__rsIndexResults_r17, resultsSSB_Indexes_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ResultsPerSSB_IndexList,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"resultsSSB-Indexes-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MeasResultSuccessHONR_r17__measResult_r17__rsIndexResults_r17, resultsCSI_RS_Indexes_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ResultsPerCSI_RS_IndexList,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"resultsCSI-RS-Indexes-r17"
		},
};
static const int asn_MAP_NR_rsIndexResults_r17_oms_6[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_rsIndexResults_r17_tags_6[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_rsIndexResults_r17_tag2el_6[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* resultsSSB-Indexes-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* resultsCSI-RS-Indexes-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_rsIndexResults_r17_specs_6 = {
	sizeof(struct NR_MeasResultSuccessHONR_r17__measResult_r17__rsIndexResults_r17),
	offsetof(struct NR_MeasResultSuccessHONR_r17__measResult_r17__rsIndexResults_r17, _asn_ctx),
	asn_MAP_NR_rsIndexResults_r17_tag2el_6,
	2,	/* Count of tags in the map */
	asn_MAP_NR_rsIndexResults_r17_oms_6,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_rsIndexResults_r17_6 = {
	"rsIndexResults-r17",
	"rsIndexResults-r17",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_rsIndexResults_r17_tags_6,
	sizeof(asn_DEF_NR_rsIndexResults_r17_tags_6)
		/sizeof(asn_DEF_NR_rsIndexResults_r17_tags_6[0]) - 1, /* 1 */
	asn_DEF_NR_rsIndexResults_r17_tags_6,	/* Same as above */
	sizeof(asn_DEF_NR_rsIndexResults_r17_tags_6)
		/sizeof(asn_DEF_NR_rsIndexResults_r17_tags_6[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_rsIndexResults_r17_6,
	2,	/* Elements count */
	&asn_SPC_NR_rsIndexResults_r17_specs_6	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_measResult_r17_2[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_MeasResultSuccessHONR_r17__measResult_r17, cellResults_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_cellResults_r17_3,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"cellResults-r17"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_MeasResultSuccessHONR_r17__measResult_r17, rsIndexResults_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_rsIndexResults_r17_6,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"rsIndexResults-r17"
		},
};
static const ber_tlv_tag_t asn_DEF_NR_measResult_r17_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_measResult_r17_tag2el_2[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* cellResults-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* rsIndexResults-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_measResult_r17_specs_2 = {
	sizeof(struct NR_MeasResultSuccessHONR_r17__measResult_r17),
	offsetof(struct NR_MeasResultSuccessHONR_r17__measResult_r17, _asn_ctx),
	asn_MAP_NR_measResult_r17_tag2el_2,
	2,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_measResult_r17_2 = {
	"measResult-r17",
	"measResult-r17",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_measResult_r17_tags_2,
	sizeof(asn_DEF_NR_measResult_r17_tags_2)
		/sizeof(asn_DEF_NR_measResult_r17_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_measResult_r17_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_measResult_r17_tags_2)
		/sizeof(asn_DEF_NR_measResult_r17_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_measResult_r17_2,
	2,	/* Elements count */
	&asn_SPC_NR_measResult_r17_specs_2	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_MeasResultSuccessHONR_r17_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_MeasResultSuccessHONR_r17, measResult_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_measResult_r17_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"measResult-r17"
		},
};
static const ber_tlv_tag_t asn_DEF_NR_MeasResultSuccessHONR_r17_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_MeasResultSuccessHONR_r17_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* measResult-r17 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_MeasResultSuccessHONR_r17_specs_1 = {
	sizeof(struct NR_MeasResultSuccessHONR_r17),
	offsetof(struct NR_MeasResultSuccessHONR_r17, _asn_ctx),
	asn_MAP_NR_MeasResultSuccessHONR_r17_tag2el_1,
	1,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_MeasResultSuccessHONR_r17 = {
	"MeasResultSuccessHONR-r17",
	"MeasResultSuccessHONR-r17",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_MeasResultSuccessHONR_r17_tags_1,
	sizeof(asn_DEF_NR_MeasResultSuccessHONR_r17_tags_1)
		/sizeof(asn_DEF_NR_MeasResultSuccessHONR_r17_tags_1[0]), /* 1 */
	asn_DEF_NR_MeasResultSuccessHONR_r17_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_MeasResultSuccessHONR_r17_tags_1)
		/sizeof(asn_DEF_NR_MeasResultSuccessHONR_r17_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_MeasResultSuccessHONR_r17_1,
	1,	/* Elements count */
	&asn_SPC_NR_MeasResultSuccessHONR_r17_specs_1	/* Additional specs */
};

