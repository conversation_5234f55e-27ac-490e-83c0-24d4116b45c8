/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_MeasResultNR_H_
#define	_NR_MeasResultNR_H_


#include <asn_application.h>

/* Including external dependencies */
#include "NR_PhysCellId.h"
#include <constr_SEQUENCE.h>
#include <NativeEnumerated.h>
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include "NR_TimeBetweenEvent-r17.h"

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_MeasResultNR__ext2__choCandidate_r17 {
	NR_MeasResultNR__ext2__choCandidate_r17_true	= 0
} e_NR_MeasResultNR__ext2__choCandidate_r17;
typedef enum NR_MeasResultNR__ext2__triggeredEvent_r17__firstTriggeredEvent {
	NR_MeasResultNR__ext2__triggeredEvent_r17__firstTriggeredEvent_condFirstEvent	= 0,
	NR_MeasResultNR__ext2__triggeredEvent_r17__firstTriggeredEvent_condSecondEvent	= 1
} e_NR_MeasResultNR__ext2__triggeredEvent_r17__firstTriggeredEvent;

/* Forward declarations */
struct NR_MeasQuantityResults;
struct NR_ResultsPerSSB_IndexList;
struct NR_ResultsPerCSI_RS_IndexList;
struct NR_CGI_InfoNR;
struct NR_CondTriggerConfig_r16;

/* NR_MeasResultNR */
typedef struct NR_MeasResultNR {
	NR_PhysCellId_t	*physCellId;	/* OPTIONAL */
	struct NR_MeasResultNR__measResult {
		struct NR_MeasResultNR__measResult__cellResults {
			struct NR_MeasQuantityResults	*resultsSSB_Cell;	/* OPTIONAL */
			struct NR_MeasQuantityResults	*resultsCSI_RS_Cell;	/* OPTIONAL */
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} cellResults;
		struct NR_MeasResultNR__measResult__rsIndexResults {
			struct NR_ResultsPerSSB_IndexList	*resultsSSB_Indexes;	/* OPTIONAL */
			struct NR_ResultsPerCSI_RS_IndexList	*resultsCSI_RS_Indexes;	/* OPTIONAL */
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *rsIndexResults;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} measResult;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	struct NR_MeasResultNR__ext1 {
		struct NR_CGI_InfoNR	*cgi_Info;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext1;
	struct NR_MeasResultNR__ext2 {
		long	*choCandidate_r17;	/* OPTIONAL */
		struct NR_MeasResultNR__ext2__choConfig_r17 {
			A_SEQUENCE_OF(struct NR_CondTriggerConfig_r16) list;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *choConfig_r17;
		struct NR_MeasResultNR__ext2__triggeredEvent_r17 {
			NR_TimeBetweenEvent_r17_t	*timeBetweenEvents_r17;	/* OPTIONAL */
			long	*firstTriggeredEvent;	/* OPTIONAL */
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *triggeredEvent_r17;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext2;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_MeasResultNR_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_choCandidate_r17_14;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_firstTriggeredEvent_20;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_MeasResultNR;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_MeasResultNR_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_MeasResultNR_1[4];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_MeasQuantityResults.h"
#include "NR_ResultsPerSSB-IndexList.h"
#include "NR_ResultsPerCSI-RS-IndexList.h"
#include "NR_CGI-InfoNR.h"
#include "NR_CondTriggerConfig-r16.h"

#endif	/* _NR_MeasResultNR_H_ */
#include <asn_internal.h>
