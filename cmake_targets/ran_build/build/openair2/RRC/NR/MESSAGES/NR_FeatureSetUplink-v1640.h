/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_FeatureSetUplink_v1640_H_
#define	_NR_FeatureSetUplink_v1640_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_FeatureSetUplink_v1640__offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16__scs_15kHz_r16 {
	NR_FeatureSetUplink_v1640__offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16__scs_15kHz_r16_set1	= 0,
	NR_FeatureSetUplink_v1640__offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16__scs_15kHz_r16_set2	= 1,
	NR_FeatureSetUplink_v1640__offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16__scs_15kHz_r16_set3	= 2
} e_NR_FeatureSetUplink_v1640__offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16__scs_15kHz_r16;
typedef enum NR_FeatureSetUplink_v1640__offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16__scs_30kHz_r16 {
	NR_FeatureSetUplink_v1640__offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16__scs_30kHz_r16_set1	= 0,
	NR_FeatureSetUplink_v1640__offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16__scs_30kHz_r16_set2	= 1,
	NR_FeatureSetUplink_v1640__offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16__scs_30kHz_r16_set3	= 2
} e_NR_FeatureSetUplink_v1640__offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16__scs_30kHz_r16;
typedef enum NR_FeatureSetUplink_v1640__offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16__scs_60kHz_r16 {
	NR_FeatureSetUplink_v1640__offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16__scs_60kHz_r16_set1	= 0,
	NR_FeatureSetUplink_v1640__offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16__scs_60kHz_r16_set2	= 1,
	NR_FeatureSetUplink_v1640__offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16__scs_60kHz_r16_set3	= 2
} e_NR_FeatureSetUplink_v1640__offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16__scs_60kHz_r16;

/* Forward declarations */
struct NR_SubSlot_Config_r16;

/* NR_FeatureSetUplink-v1640 */
typedef struct NR_FeatureSetUplink_v1640 {
	struct NR_SubSlot_Config_r16	*twoHARQ_ACK_Codebook_type1_r16;	/* OPTIONAL */
	struct NR_SubSlot_Config_r16	*twoHARQ_ACK_Codebook_type2_r16;	/* OPTIONAL */
	struct NR_FeatureSetUplink_v1640__offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16 {
		long	*scs_15kHz_r16;	/* OPTIONAL */
		long	*scs_30kHz_r16;	/* OPTIONAL */
		long	*scs_60kHz_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *offsetSRS_CB_PUSCH_PDCCH_MonitorAnyOccWithSpanGap_fr1_r16;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_FeatureSetUplink_v1640_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_15kHz_r16_5;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_30kHz_r16_9;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_60kHz_r16_13;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_FeatureSetUplink_v1640;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_FeatureSetUplink_v1640_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_FeatureSetUplink_v1640_1[3];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_SubSlot-Config-r16.h"

#endif	/* _NR_FeatureSetUplink_v1640_H_ */
#include <asn_internal.h>
