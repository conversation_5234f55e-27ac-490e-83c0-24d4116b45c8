/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_FeatureSetUplink_v1710_H_
#define	_NR_FeatureSetUplink_v1710_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_FeatureSetUplink_v1710__mTRP_PUSCH_TypeA_CB_r17 {
	NR_FeatureSetUplink_v1710__mTRP_PUSCH_TypeA_CB_r17_n1	= 0,
	NR_FeatureSetUplink_v1710__mTRP_PUSCH_TypeA_CB_r17_n2	= 1,
	NR_FeatureSetUplink_v1710__mTRP_PUSCH_TypeA_CB_r17_n4	= 2
} e_NR_FeatureSetUplink_v1710__mTRP_PUSCH_TypeA_CB_r17;
typedef enum NR_FeatureSetUplink_v1710__mTRP_PUSCH_RepetitionTypeA_r17 {
	NR_FeatureSetUplink_v1710__mTRP_PUSCH_RepetitionTypeA_r17_n1	= 0,
	NR_FeatureSetUplink_v1710__mTRP_PUSCH_RepetitionTypeA_r17_n2	= 1,
	NR_FeatureSetUplink_v1710__mTRP_PUSCH_RepetitionTypeA_r17_n3	= 2,
	NR_FeatureSetUplink_v1710__mTRP_PUSCH_RepetitionTypeA_r17_n4	= 3
} e_NR_FeatureSetUplink_v1710__mTRP_PUSCH_RepetitionTypeA_r17;
typedef enum NR_FeatureSetUplink_v1710__mTRP_PUCCH_IntraSlot_r17 {
	NR_FeatureSetUplink_v1710__mTRP_PUCCH_IntraSlot_r17_pf0_2	= 0,
	NR_FeatureSetUplink_v1710__mTRP_PUCCH_IntraSlot_r17_pf1_3_4	= 1,
	NR_FeatureSetUplink_v1710__mTRP_PUCCH_IntraSlot_r17_pf0_4	= 2
} e_NR_FeatureSetUplink_v1710__mTRP_PUCCH_IntraSlot_r17;
typedef enum NR_FeatureSetUplink_v1710__srs_AntennaSwitching2SP_1Periodic_r17 {
	NR_FeatureSetUplink_v1710__srs_AntennaSwitching2SP_1Periodic_r17_supported	= 0
} e_NR_FeatureSetUplink_v1710__srs_AntennaSwitching2SP_1Periodic_r17;
typedef enum NR_FeatureSetUplink_v1710__srs_ExtensionAperiodicSRS_r17 {
	NR_FeatureSetUplink_v1710__srs_ExtensionAperiodicSRS_r17_supported	= 0
} e_NR_FeatureSetUplink_v1710__srs_ExtensionAperiodicSRS_r17;
typedef enum NR_FeatureSetUplink_v1710__srs_OneAP_SRS_r17 {
	NR_FeatureSetUplink_v1710__srs_OneAP_SRS_r17_supported	= 0
} e_NR_FeatureSetUplink_v1710__srs_OneAP_SRS_r17;
typedef enum NR_FeatureSetUplink_v1710__ue_PowerClassPerBandPerBC_r17 {
	NR_FeatureSetUplink_v1710__ue_PowerClassPerBandPerBC_r17_pc1dot5	= 0,
	NR_FeatureSetUplink_v1710__ue_PowerClassPerBandPerBC_r17_pc2	= 1,
	NR_FeatureSetUplink_v1710__ue_PowerClassPerBandPerBC_r17_pc3	= 2
} e_NR_FeatureSetUplink_v1710__ue_PowerClassPerBandPerBC_r17;
typedef enum NR_FeatureSetUplink_v1710__tx_Support_UL_GapFR2_r17 {
	NR_FeatureSetUplink_v1710__tx_Support_UL_GapFR2_r17_supported	= 0
} e_NR_FeatureSetUplink_v1710__tx_Support_UL_GapFR2_r17;

/* NR_FeatureSetUplink-v1710 */
typedef struct NR_FeatureSetUplink_v1710 {
	long	*mTRP_PUSCH_TypeA_CB_r17;	/* OPTIONAL */
	long	*mTRP_PUSCH_RepetitionTypeA_r17;	/* OPTIONAL */
	long	*mTRP_PUCCH_IntraSlot_r17;	/* OPTIONAL */
	long	*srs_AntennaSwitching2SP_1Periodic_r17;	/* OPTIONAL */
	long	*srs_ExtensionAperiodicSRS_r17;	/* OPTIONAL */
	long	*srs_OneAP_SRS_r17;	/* OPTIONAL */
	long	*ue_PowerClassPerBandPerBC_r17;	/* OPTIONAL */
	long	*tx_Support_UL_GapFR2_r17;	/* OPTIONAL */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_FeatureSetUplink_v1710_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mTRP_PUSCH_TypeA_CB_r17_2;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mTRP_PUSCH_RepetitionTypeA_r17_6;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mTRP_PUCCH_IntraSlot_r17_11;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_srs_AntennaSwitching2SP_1Periodic_r17_15;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_srs_ExtensionAperiodicSRS_r17_17;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_srs_OneAP_SRS_r17_19;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_ue_PowerClassPerBandPerBC_r17_21;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_tx_Support_UL_GapFR2_r17_25;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_FeatureSetUplink_v1710;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_FeatureSetUplink_v1710_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_FeatureSetUplink_v1710_1[8];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_FeatureSetUplink_v1710_H_ */
#include <asn_internal.h>
