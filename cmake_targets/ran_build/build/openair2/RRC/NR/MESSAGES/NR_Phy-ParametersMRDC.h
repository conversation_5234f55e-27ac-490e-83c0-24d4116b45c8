/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_Phy_ParametersMRDC_H_
#define	_NR_Phy_ParametersMRDC_H_


#include <asn_application.h>

/* Including external dependencies */
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include <constr_SEQUENCE.h>
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_Phy_ParametersMRDC__ext2__tdd_PCellUL_TX_AllUL_Subframe_r16 {
	NR_Phy_ParametersMRDC__ext2__tdd_PCellUL_TX_AllUL_Subframe_r16_supported	= 0
} e_NR_Phy_ParametersMRDC__ext2__tdd_PCellUL_TX_AllUL_Subframe_r16;
typedef enum NR_Phy_ParametersMRDC__ext2__fdd_PCellUL_TX_AllUL_Subframe_r16 {
	NR_Phy_ParametersMRDC__ext2__fdd_PCellUL_TX_AllUL_Subframe_r16_supported	= 0
} e_NR_Phy_ParametersMRDC__ext2__fdd_PCellUL_TX_AllUL_Subframe_r16;

/* Forward declarations */
struct NR_NAICS_Capability_Entry;
struct NR_CarrierAggregationVariant;

/* NR_Phy-ParametersMRDC */
typedef struct NR_Phy_ParametersMRDC {
	struct NR_Phy_ParametersMRDC__naics_Capability_List {
		A_SEQUENCE_OF(struct NR_NAICS_Capability_Entry) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *naics_Capability_List;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	struct NR_Phy_ParametersMRDC__ext1 {
		struct NR_CarrierAggregationVariant	*spCellPlacement;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext1;
	struct NR_Phy_ParametersMRDC__ext2 {
		long	*tdd_PCellUL_TX_AllUL_Subframe_r16;	/* OPTIONAL */
		long	*fdd_PCellUL_TX_AllUL_Subframe_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext2;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_Phy_ParametersMRDC_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_tdd_PCellUL_TX_AllUL_Subframe_r16_8;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_fdd_PCellUL_TX_AllUL_Subframe_r16_10;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_Phy_ParametersMRDC;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_Phy_ParametersMRDC_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_Phy_ParametersMRDC_1[3];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_NAICS-Capability-Entry.h"
#include "NR_CarrierAggregationVariant.h"

#endif	/* _NR_Phy_ParametersMRDC_H_ */
#include <asn_internal.h>
