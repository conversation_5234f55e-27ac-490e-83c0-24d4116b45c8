/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_MsgA-PUSCH-Resource-r16.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_msgA_MCS_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 15L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_nrofSlotsMsgA_PUSCH_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 4L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_msgA_PUSCH_TimeDomainOffset_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 32L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_msgA_PUSCH_TimeDomainAllocation_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 16L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_startSymbolAndLengthMsgA_PO_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 127L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_guardPeriodMsgA_PUSCH_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 3L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_guardBandMsgA_PUSCH_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 1L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_frequencyStartMsgA_PUSCH_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 274L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_nrofPRBs_PerMsgA_PO_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 32L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_msgA_HoppingBits_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	const BIT_STRING_t *st = (const BIT_STRING_t *)sptr;
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	if(st->size > 0) {
		/* Size in bits */
		size = 8 * st->size - (st->bits_unused & 0x07);
	} else {
		size = 0;
	}
	
	if((size == 2UL)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_nrofDMRS_Sequences_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 2L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_interlaceIndexFirstPO_MsgA_PUSCH_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 10L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_nrofInterlacesPerMsgA_PO_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 10L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_nrofMsgA_PO_PerSlot_r16_constr_4 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_mappingTypeMsgA_PUSCH_r16_constr_12 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_nrofMsgA_PO_FDM_r16_constr_19 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_msgA_IntraSlotFrequencyHopping_r16_constr_24 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_msgA_Alpha_r16_constr_29 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  7 }	/* (0..7) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_msgA_MCS_r16_constr_2 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  15 }	/* (0..15) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_nrofSlotsMsgA_PUSCH_r16_constr_3 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  1,  4 }	/* (1..4) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_msgA_PUSCH_TimeDomainOffset_r16_constr_9 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 5,  5,  1,  32 }	/* (1..32) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_msgA_PUSCH_TimeDomainAllocation_r16_constr_10 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (1..16) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_startSymbolAndLengthMsgA_PO_r16_constr_11 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 7,  7,  0,  127 }	/* (0..127) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_guardPeriodMsgA_PUSCH_r16_constr_15 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_guardBandMsgA_PUSCH_r16_constr_16 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_frequencyStartMsgA_PUSCH_r16_constr_17 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 9,  9,  0,  274 }	/* (0..274) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_nrofPRBs_PerMsgA_PO_r16_constr_18 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 5,  5,  1,  32 }	/* (1..32) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_msgA_HoppingBits_r16_constr_26 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 0,  0,  2,  2 }	/* (SIZE(2..2)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_nrofDMRS_Sequences_r16_constr_28 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  1,  2 }	/* (1..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_interlaceIndexFirstPO_MsgA_PUSCH_r16_constr_38 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  1,  10 }	/* (1..10) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_nrofInterlacesPerMsgA_PO_r16_constr_39 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  1,  10 }	/* (1..10) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_nrofMsgA_PO_PerSlot_r16_value2enum_4[] = {
	{ 0,	3,	"one" },
	{ 1,	3,	"two" },
	{ 2,	5,	"three" },
	{ 3,	3,	"six" }
};
static const unsigned int asn_MAP_NR_nrofMsgA_PO_PerSlot_r16_enum2value_4[] = {
	0,	/* one(0) */
	3,	/* six(3) */
	2,	/* three(2) */
	1	/* two(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_nrofMsgA_PO_PerSlot_r16_specs_4 = {
	asn_MAP_NR_nrofMsgA_PO_PerSlot_r16_value2enum_4,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_nrofMsgA_PO_PerSlot_r16_enum2value_4,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_nrofMsgA_PO_PerSlot_r16_tags_4[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_nrofMsgA_PO_PerSlot_r16_4 = {
	"nrofMsgA-PO-PerSlot-r16",
	"nrofMsgA-PO-PerSlot-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_nrofMsgA_PO_PerSlot_r16_tags_4,
	sizeof(asn_DEF_NR_nrofMsgA_PO_PerSlot_r16_tags_4)
		/sizeof(asn_DEF_NR_nrofMsgA_PO_PerSlot_r16_tags_4[0]) - 1, /* 1 */
	asn_DEF_NR_nrofMsgA_PO_PerSlot_r16_tags_4,	/* Same as above */
	sizeof(asn_DEF_NR_nrofMsgA_PO_PerSlot_r16_tags_4)
		/sizeof(asn_DEF_NR_nrofMsgA_PO_PerSlot_r16_tags_4[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_nrofMsgA_PO_PerSlot_r16_constr_4,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_nrofMsgA_PO_PerSlot_r16_specs_4	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_mappingTypeMsgA_PUSCH_r16_value2enum_12[] = {
	{ 0,	5,	"typeA" },
	{ 1,	5,	"typeB" }
};
static const unsigned int asn_MAP_NR_mappingTypeMsgA_PUSCH_r16_enum2value_12[] = {
	0,	/* typeA(0) */
	1	/* typeB(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_mappingTypeMsgA_PUSCH_r16_specs_12 = {
	asn_MAP_NR_mappingTypeMsgA_PUSCH_r16_value2enum_12,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_mappingTypeMsgA_PUSCH_r16_enum2value_12,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_mappingTypeMsgA_PUSCH_r16_tags_12[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mappingTypeMsgA_PUSCH_r16_12 = {
	"mappingTypeMsgA-PUSCH-r16",
	"mappingTypeMsgA-PUSCH-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_mappingTypeMsgA_PUSCH_r16_tags_12,
	sizeof(asn_DEF_NR_mappingTypeMsgA_PUSCH_r16_tags_12)
		/sizeof(asn_DEF_NR_mappingTypeMsgA_PUSCH_r16_tags_12[0]) - 1, /* 1 */
	asn_DEF_NR_mappingTypeMsgA_PUSCH_r16_tags_12,	/* Same as above */
	sizeof(asn_DEF_NR_mappingTypeMsgA_PUSCH_r16_tags_12)
		/sizeof(asn_DEF_NR_mappingTypeMsgA_PUSCH_r16_tags_12[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_mappingTypeMsgA_PUSCH_r16_constr_12,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_mappingTypeMsgA_PUSCH_r16_specs_12	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_nrofMsgA_PO_FDM_r16_value2enum_19[] = {
	{ 0,	3,	"one" },
	{ 1,	3,	"two" },
	{ 2,	4,	"four" },
	{ 3,	5,	"eight" }
};
static const unsigned int asn_MAP_NR_nrofMsgA_PO_FDM_r16_enum2value_19[] = {
	3,	/* eight(3) */
	2,	/* four(2) */
	0,	/* one(0) */
	1	/* two(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_nrofMsgA_PO_FDM_r16_specs_19 = {
	asn_MAP_NR_nrofMsgA_PO_FDM_r16_value2enum_19,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_nrofMsgA_PO_FDM_r16_enum2value_19,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_nrofMsgA_PO_FDM_r16_tags_19[] = {
	(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_nrofMsgA_PO_FDM_r16_19 = {
	"nrofMsgA-PO-FDM-r16",
	"nrofMsgA-PO-FDM-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_nrofMsgA_PO_FDM_r16_tags_19,
	sizeof(asn_DEF_NR_nrofMsgA_PO_FDM_r16_tags_19)
		/sizeof(asn_DEF_NR_nrofMsgA_PO_FDM_r16_tags_19[0]) - 1, /* 1 */
	asn_DEF_NR_nrofMsgA_PO_FDM_r16_tags_19,	/* Same as above */
	sizeof(asn_DEF_NR_nrofMsgA_PO_FDM_r16_tags_19)
		/sizeof(asn_DEF_NR_nrofMsgA_PO_FDM_r16_tags_19[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_nrofMsgA_PO_FDM_r16_constr_19,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_nrofMsgA_PO_FDM_r16_specs_19	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_msgA_IntraSlotFrequencyHopping_r16_value2enum_24[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_msgA_IntraSlotFrequencyHopping_r16_enum2value_24[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_msgA_IntraSlotFrequencyHopping_r16_specs_24 = {
	asn_MAP_NR_msgA_IntraSlotFrequencyHopping_r16_value2enum_24,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_msgA_IntraSlotFrequencyHopping_r16_enum2value_24,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_msgA_IntraSlotFrequencyHopping_r16_tags_24[] = {
	(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_msgA_IntraSlotFrequencyHopping_r16_24 = {
	"msgA-IntraSlotFrequencyHopping-r16",
	"msgA-IntraSlotFrequencyHopping-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_msgA_IntraSlotFrequencyHopping_r16_tags_24,
	sizeof(asn_DEF_NR_msgA_IntraSlotFrequencyHopping_r16_tags_24)
		/sizeof(asn_DEF_NR_msgA_IntraSlotFrequencyHopping_r16_tags_24[0]) - 1, /* 1 */
	asn_DEF_NR_msgA_IntraSlotFrequencyHopping_r16_tags_24,	/* Same as above */
	sizeof(asn_DEF_NR_msgA_IntraSlotFrequencyHopping_r16_tags_24)
		/sizeof(asn_DEF_NR_msgA_IntraSlotFrequencyHopping_r16_tags_24[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_msgA_IntraSlotFrequencyHopping_r16_constr_24,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_msgA_IntraSlotFrequencyHopping_r16_specs_24	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_msgA_Alpha_r16_value2enum_29[] = {
	{ 0,	6,	"alpha0" },
	{ 1,	7,	"alpha04" },
	{ 2,	7,	"alpha05" },
	{ 3,	7,	"alpha06" },
	{ 4,	7,	"alpha07" },
	{ 5,	7,	"alpha08" },
	{ 6,	7,	"alpha09" },
	{ 7,	6,	"alpha1" }
};
static const unsigned int asn_MAP_NR_msgA_Alpha_r16_enum2value_29[] = {
	0,	/* alpha0(0) */
	1,	/* alpha04(1) */
	2,	/* alpha05(2) */
	3,	/* alpha06(3) */
	4,	/* alpha07(4) */
	5,	/* alpha08(5) */
	6,	/* alpha09(6) */
	7	/* alpha1(7) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_msgA_Alpha_r16_specs_29 = {
	asn_MAP_NR_msgA_Alpha_r16_value2enum_29,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_msgA_Alpha_r16_enum2value_29,	/* N => "tag"; sorted by N */
	8,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_msgA_Alpha_r16_tags_29[] = {
	(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_msgA_Alpha_r16_29 = {
	"msgA-Alpha-r16",
	"msgA-Alpha-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_msgA_Alpha_r16_tags_29,
	sizeof(asn_DEF_NR_msgA_Alpha_r16_tags_29)
		/sizeof(asn_DEF_NR_msgA_Alpha_r16_tags_29[0]) - 1, /* 1 */
	asn_DEF_NR_msgA_Alpha_r16_tags_29,	/* Same as above */
	sizeof(asn_DEF_NR_msgA_Alpha_r16_tags_29)
		/sizeof(asn_DEF_NR_msgA_Alpha_r16_tags_29[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_msgA_Alpha_r16_constr_29,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_msgA_Alpha_r16_specs_29	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_MsgA_PUSCH_Resource_r16_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_MsgA_PUSCH_Resource_r16, msgA_MCS_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_msgA_MCS_r16_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_msgA_MCS_r16_constraint_1
		},
		0, 0, /* No default value */
		"msgA-MCS-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_MsgA_PUSCH_Resource_r16, nrofSlotsMsgA_PUSCH_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_nrofSlotsMsgA_PUSCH_r16_constr_3,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_nrofSlotsMsgA_PUSCH_r16_constraint_1
		},
		0, 0, /* No default value */
		"nrofSlotsMsgA-PUSCH-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_MsgA_PUSCH_Resource_r16, nrofMsgA_PO_PerSlot_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_nrofMsgA_PO_PerSlot_r16_4,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"nrofMsgA-PO-PerSlot-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_MsgA_PUSCH_Resource_r16, msgA_PUSCH_TimeDomainOffset_r16),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_msgA_PUSCH_TimeDomainOffset_r16_constr_9,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_msgA_PUSCH_TimeDomainOffset_r16_constraint_1
		},
		0, 0, /* No default value */
		"msgA-PUSCH-TimeDomainOffset-r16"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_MsgA_PUSCH_Resource_r16, msgA_PUSCH_TimeDomainAllocation_r16),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_msgA_PUSCH_TimeDomainAllocation_r16_constr_10,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_msgA_PUSCH_TimeDomainAllocation_r16_constraint_1
		},
		0, 0, /* No default value */
		"msgA-PUSCH-TimeDomainAllocation-r16"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_MsgA_PUSCH_Resource_r16, startSymbolAndLengthMsgA_PO_r16),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_startSymbolAndLengthMsgA_PO_r16_constr_11,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_startSymbolAndLengthMsgA_PO_r16_constraint_1
		},
		0, 0, /* No default value */
		"startSymbolAndLengthMsgA-PO-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_MsgA_PUSCH_Resource_r16, mappingTypeMsgA_PUSCH_r16),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_mappingTypeMsgA_PUSCH_r16_12,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mappingTypeMsgA-PUSCH-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MsgA_PUSCH_Resource_r16, guardPeriodMsgA_PUSCH_r16),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_guardPeriodMsgA_PUSCH_r16_constr_15,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_guardPeriodMsgA_PUSCH_r16_constraint_1
		},
		0, 0, /* No default value */
		"guardPeriodMsgA-PUSCH-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_MsgA_PUSCH_Resource_r16, guardBandMsgA_PUSCH_r16),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_guardBandMsgA_PUSCH_r16_constr_16,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_guardBandMsgA_PUSCH_r16_constraint_1
		},
		0, 0, /* No default value */
		"guardBandMsgA-PUSCH-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_MsgA_PUSCH_Resource_r16, frequencyStartMsgA_PUSCH_r16),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_frequencyStartMsgA_PUSCH_r16_constr_17,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_frequencyStartMsgA_PUSCH_r16_constraint_1
		},
		0, 0, /* No default value */
		"frequencyStartMsgA-PUSCH-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_MsgA_PUSCH_Resource_r16, nrofPRBs_PerMsgA_PO_r16),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_nrofPRBs_PerMsgA_PO_r16_constr_18,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_nrofPRBs_PerMsgA_PO_r16_constraint_1
		},
		0, 0, /* No default value */
		"nrofPRBs-PerMsgA-PO-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_MsgA_PUSCH_Resource_r16, nrofMsgA_PO_FDM_r16),
		(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_nrofMsgA_PO_FDM_r16_19,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"nrofMsgA-PO-FDM-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_MsgA_PUSCH_Resource_r16, msgA_IntraSlotFrequencyHopping_r16),
		(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_msgA_IntraSlotFrequencyHopping_r16_24,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"msgA-IntraSlotFrequencyHopping-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MsgA_PUSCH_Resource_r16, msgA_HoppingBits_r16),
		(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_BIT_STRING,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_msgA_HoppingBits_r16_constr_26,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_msgA_HoppingBits_r16_constraint_1
		},
		0, 0, /* No default value */
		"msgA-HoppingBits-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_MsgA_PUSCH_Resource_r16, msgA_DMRS_Config_r16),
		(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_MsgA_DMRS_Config_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"msgA-DMRS-Config-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_MsgA_PUSCH_Resource_r16, nrofDMRS_Sequences_r16),
		(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_nrofDMRS_Sequences_r16_constr_28,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_nrofDMRS_Sequences_r16_constraint_1
		},
		0, 0, /* No default value */
		"nrofDMRS-Sequences-r16"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_MsgA_PUSCH_Resource_r16, msgA_Alpha_r16),
		(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_msgA_Alpha_r16_29,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"msgA-Alpha-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_MsgA_PUSCH_Resource_r16, interlaceIndexFirstPO_MsgA_PUSCH_r16),
		(ASN_TAG_CLASS_CONTEXT | (17 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_interlaceIndexFirstPO_MsgA_PUSCH_r16_constr_38,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_interlaceIndexFirstPO_MsgA_PUSCH_r16_constraint_1
		},
		0, 0, /* No default value */
		"interlaceIndexFirstPO-MsgA-PUSCH-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MsgA_PUSCH_Resource_r16, nrofInterlacesPerMsgA_PO_r16),
		(ASN_TAG_CLASS_CONTEXT | (18 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_nrofInterlacesPerMsgA_PO_r16_constr_39,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_nrofInterlacesPerMsgA_PO_r16_constraint_1
		},
		0, 0, /* No default value */
		"nrofInterlacesPerMsgA-PO-r16"
		},
};
static const int asn_MAP_NR_MsgA_PUSCH_Resource_r16_oms_1[] = { 4, 5, 6, 7, 12, 13, 16, 17, 18 };
static const ber_tlv_tag_t asn_DEF_NR_MsgA_PUSCH_Resource_r16_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_MsgA_PUSCH_Resource_r16_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* msgA-MCS-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* nrofSlotsMsgA-PUSCH-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* nrofMsgA-PO-PerSlot-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* msgA-PUSCH-TimeDomainOffset-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* msgA-PUSCH-TimeDomainAllocation-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* startSymbolAndLengthMsgA-PO-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* mappingTypeMsgA-PUSCH-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* guardPeriodMsgA-PUSCH-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* guardBandMsgA-PUSCH-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* frequencyStartMsgA-PUSCH-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 }, /* nrofPRBs-PerMsgA-PO-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (11 << 2)), 11, 0, 0 }, /* nrofMsgA-PO-FDM-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (12 << 2)), 12, 0, 0 }, /* msgA-IntraSlotFrequencyHopping-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (13 << 2)), 13, 0, 0 }, /* msgA-HoppingBits-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (14 << 2)), 14, 0, 0 }, /* msgA-DMRS-Config-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (15 << 2)), 15, 0, 0 }, /* nrofDMRS-Sequences-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (16 << 2)), 16, 0, 0 }, /* msgA-Alpha-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (17 << 2)), 17, 0, 0 }, /* interlaceIndexFirstPO-MsgA-PUSCH-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (18 << 2)), 18, 0, 0 } /* nrofInterlacesPerMsgA-PO-r16 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_MsgA_PUSCH_Resource_r16_specs_1 = {
	sizeof(struct NR_MsgA_PUSCH_Resource_r16),
	offsetof(struct NR_MsgA_PUSCH_Resource_r16, _asn_ctx),
	asn_MAP_NR_MsgA_PUSCH_Resource_r16_tag2el_1,
	19,	/* Count of tags in the map */
	asn_MAP_NR_MsgA_PUSCH_Resource_r16_oms_1,	/* Optional members */
	9, 0,	/* Root/Additions */
	19,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_MsgA_PUSCH_Resource_r16 = {
	"MsgA-PUSCH-Resource-r16",
	"MsgA-PUSCH-Resource-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_MsgA_PUSCH_Resource_r16_tags_1,
	sizeof(asn_DEF_NR_MsgA_PUSCH_Resource_r16_tags_1)
		/sizeof(asn_DEF_NR_MsgA_PUSCH_Resource_r16_tags_1[0]), /* 1 */
	asn_DEF_NR_MsgA_PUSCH_Resource_r16_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_MsgA_PUSCH_Resource_r16_tags_1)
		/sizeof(asn_DEF_NR_MsgA_PUSCH_Resource_r16_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_MsgA_PUSCH_Resource_r16_1,
	19,	/* Elements count */
	&asn_SPC_NR_MsgA_PUSCH_Resource_r16_specs_1	/* Additional specs */
};

