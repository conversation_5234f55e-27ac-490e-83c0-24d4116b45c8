/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_FrequencyInfoDL_H_
#define	_NR_FrequencyInfoDL_H_


#include <asn_application.h>

/* Including external dependencies */
#include "NR_ARFCN-ValueNR.h"
#include "NR_MultiFrequencyBandListNR.h"
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct NR_SCS_SpecificCarrier;

/* NR_FrequencyInfoDL */
typedef struct NR_FrequencyInfoDL {
	NR_ARFCN_ValueNR_t	*absoluteFrequencySSB;	/* OPTIONAL */
	NR_MultiFrequencyBandListNR_t	 frequencyBandList;
	NR_ARFCN_ValueNR_t	 absoluteFrequencyPointA;
	struct NR_FrequencyInfoDL__scs_SpecificCarrierList {
		A_SEQUENCE_OF(struct NR_SCS_SpecificCarrier) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} scs_SpecificCarrierList;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_FrequencyInfoDL_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_NR_FrequencyInfoDL;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_FrequencyInfoDL_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_FrequencyInfoDL_1[4];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_SCS-SpecificCarrier.h"

#endif	/* _NR_FrequencyInfoDL_H_ */
#include <asn_internal.h>
