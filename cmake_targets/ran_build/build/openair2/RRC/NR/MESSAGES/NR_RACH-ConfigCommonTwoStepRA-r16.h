/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_RACH_ConfigCommonTwoStepRA_r16_H_
#define	_NR_RACH_ConfigCommonTwoStepRA_r16_H_


#include <asn_application.h>

/* Including external dependencies */
#include "NR_RACH-ConfigGenericTwoStepRA-r16.h"
#include <NativeInteger.h>
#include <NativeEnumerated.h>
#include "NR_RSRP-Range.h"
#include "NR_SubcarrierSpacing.h"
#include <constr_CHOICE.h>
#include "NR_RA-Prioritization.h"
#include <BIT_STRING.h>
#include <constr_SEQUENCE.h>
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16_PR {
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16_PR_NOTHING,	/* No components present */
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16_PR_oneEighth,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16_PR_oneFourth,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16_PR_oneHalf,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16_PR_one,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16_PR_two,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16_PR_four,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16_PR_eight,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16_PR_sixteen
} NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16_PR;
typedef enum NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneEighth {
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneEighth_n4	= 0,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneEighth_n8	= 1,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneEighth_n12	= 2,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneEighth_n16	= 3,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneEighth_n20	= 4,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneEighth_n24	= 5,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneEighth_n28	= 6,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneEighth_n32	= 7,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneEighth_n36	= 8,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneEighth_n40	= 9,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneEighth_n44	= 10,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneEighth_n48	= 11,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneEighth_n52	= 12,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneEighth_n56	= 13,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneEighth_n60	= 14,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneEighth_n64	= 15
} e_NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneEighth;
typedef enum NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneFourth {
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneFourth_n4	= 0,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneFourth_n8	= 1,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneFourth_n12	= 2,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneFourth_n16	= 3,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneFourth_n20	= 4,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneFourth_n24	= 5,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneFourth_n28	= 6,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneFourth_n32	= 7,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneFourth_n36	= 8,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneFourth_n40	= 9,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneFourth_n44	= 10,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneFourth_n48	= 11,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneFourth_n52	= 12,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneFourth_n56	= 13,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneFourth_n60	= 14,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneFourth_n64	= 15
} e_NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneFourth;
typedef enum NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneHalf {
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneHalf_n4	= 0,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneHalf_n8	= 1,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneHalf_n12	= 2,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneHalf_n16	= 3,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneHalf_n20	= 4,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneHalf_n24	= 5,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneHalf_n28	= 6,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneHalf_n32	= 7,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneHalf_n36	= 8,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneHalf_n40	= 9,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneHalf_n44	= 10,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneHalf_n48	= 11,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneHalf_n52	= 12,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneHalf_n56	= 13,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneHalf_n60	= 14,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneHalf_n64	= 15
} e_NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__oneHalf;
typedef enum NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__one {
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__one_n4	= 0,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__one_n8	= 1,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__one_n12	= 2,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__one_n16	= 3,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__one_n20	= 4,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__one_n24	= 5,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__one_n28	= 6,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__one_n32	= 7,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__one_n36	= 8,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__one_n40	= 9,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__one_n44	= 10,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__one_n48	= 11,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__one_n52	= 12,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__one_n56	= 13,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__one_n60	= 14,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__one_n64	= 15
} e_NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__one;
typedef enum NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__two {
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__two_n4	= 0,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__two_n8	= 1,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__two_n12	= 2,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__two_n16	= 3,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__two_n20	= 4,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__two_n24	= 5,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__two_n28	= 6,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__two_n32	= 7
} e_NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16__two;
typedef enum NR_RACH_ConfigCommonTwoStepRA_r16__msgA_PRACH_RootSequenceIndex_r16_PR {
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_PRACH_RootSequenceIndex_r16_PR_NOTHING,	/* No components present */
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_PRACH_RootSequenceIndex_r16_PR_l839,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_PRACH_RootSequenceIndex_r16_PR_l139,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_PRACH_RootSequenceIndex_r16_PR_l571,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_PRACH_RootSequenceIndex_r16_PR_l1151
} NR_RACH_ConfigCommonTwoStepRA_r16__msgA_PRACH_RootSequenceIndex_r16_PR;
typedef enum NR_RACH_ConfigCommonTwoStepRA_r16__msgA_TransMax_r16 {
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_TransMax_r16_n1	= 0,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_TransMax_r16_n2	= 1,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_TransMax_r16_n4	= 2,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_TransMax_r16_n6	= 3,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_TransMax_r16_n8	= 4,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_TransMax_r16_n10	= 5,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_TransMax_r16_n20	= 6,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_TransMax_r16_n50	= 7,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_TransMax_r16_n100	= 8,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_TransMax_r16_n200	= 9
} e_NR_RACH_ConfigCommonTwoStepRA_r16__msgA_TransMax_r16;
typedef enum NR_RACH_ConfigCommonTwoStepRA_r16__msgA_RestrictedSetConfig_r16 {
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_RestrictedSetConfig_r16_unrestrictedSet	= 0,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_RestrictedSetConfig_r16_restrictedSetTypeA	= 1,
	NR_RACH_ConfigCommonTwoStepRA_r16__msgA_RestrictedSetConfig_r16_restrictedSetTypeB	= 2
} e_NR_RACH_ConfigCommonTwoStepRA_r16__msgA_RestrictedSetConfig_r16;
typedef enum NR_RACH_ConfigCommonTwoStepRA_r16__ra_ContentionResolutionTimer_r16 {
	NR_RACH_ConfigCommonTwoStepRA_r16__ra_ContentionResolutionTimer_r16_sf8	= 0,
	NR_RACH_ConfigCommonTwoStepRA_r16__ra_ContentionResolutionTimer_r16_sf16	= 1,
	NR_RACH_ConfigCommonTwoStepRA_r16__ra_ContentionResolutionTimer_r16_sf24	= 2,
	NR_RACH_ConfigCommonTwoStepRA_r16__ra_ContentionResolutionTimer_r16_sf32	= 3,
	NR_RACH_ConfigCommonTwoStepRA_r16__ra_ContentionResolutionTimer_r16_sf40	= 4,
	NR_RACH_ConfigCommonTwoStepRA_r16__ra_ContentionResolutionTimer_r16_sf48	= 5,
	NR_RACH_ConfigCommonTwoStepRA_r16__ra_ContentionResolutionTimer_r16_sf56	= 6,
	NR_RACH_ConfigCommonTwoStepRA_r16__ra_ContentionResolutionTimer_r16_sf64	= 7
} e_NR_RACH_ConfigCommonTwoStepRA_r16__ra_ContentionResolutionTimer_r16;

/* Forward declarations */
struct NR_GroupB_ConfiguredTwoStepRA_r16;
struct NR_RA_PrioritizationForSlicing_r17;
struct NR_FeatureCombinationPreambles_r17;

/* NR_RACH-ConfigCommonTwoStepRA-r16 */
typedef struct NR_RACH_ConfigCommonTwoStepRA_r16 {
	NR_RACH_ConfigGenericTwoStepRA_r16_t	 rach_ConfigGenericTwoStepRA_r16;
	long	*msgA_TotalNumberOfRA_Preambles_r16;	/* OPTIONAL */
	struct NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16 {
		NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16_PR present;
		union NR_RACH_ConfigCommonTwoStepRA_r16__NR_msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16_u {
			long	 oneEighth;
			long	 oneFourth;
			long	 oneHalf;
			long	 one;
			long	 two;
			long	 four;
			long	 eight;
			long	 sixteen;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16;
	long	*msgA_CB_PreamblesPerSSB_PerSharedRO_r16;	/* OPTIONAL */
	long	*msgA_SSB_SharedRO_MaskIndex_r16;	/* OPTIONAL */
	struct NR_GroupB_ConfiguredTwoStepRA_r16	*groupB_ConfiguredTwoStepRA_r16;	/* OPTIONAL */
	struct NR_RACH_ConfigCommonTwoStepRA_r16__msgA_PRACH_RootSequenceIndex_r16 {
		NR_RACH_ConfigCommonTwoStepRA_r16__msgA_PRACH_RootSequenceIndex_r16_PR present;
		union NR_RACH_ConfigCommonTwoStepRA_r16__NR_msgA_PRACH_RootSequenceIndex_r16_u {
			long	 l839;
			long	 l139;
			long	 l571;
			long	 l1151;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *msgA_PRACH_RootSequenceIndex_r16;
	long	*msgA_TransMax_r16;	/* OPTIONAL */
	NR_RSRP_Range_t	*msgA_RSRP_Threshold_r16;	/* OPTIONAL */
	NR_RSRP_Range_t	*msgA_RSRP_ThresholdSSB_r16;	/* OPTIONAL */
	NR_SubcarrierSpacing_t	*msgA_SubcarrierSpacing_r16;	/* OPTIONAL */
	long	*msgA_RestrictedSetConfig_r16;	/* OPTIONAL */
	struct NR_RACH_ConfigCommonTwoStepRA_r16__ra_PrioritizationForAccessIdentityTwoStep_r16 {
		NR_RA_Prioritization_t	 ra_Prioritization_r16;
		BIT_STRING_t	 ra_PrioritizationForAI_r16;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ra_PrioritizationForAccessIdentityTwoStep_r16;
	long	*ra_ContentionResolutionTimer_r16;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	struct NR_RACH_ConfigCommonTwoStepRA_r16__ext1 {
		struct NR_RA_PrioritizationForSlicing_r17	*ra_PrioritizationForSlicingTwoStep_r17;	/* OPTIONAL */
		struct NR_RACH_ConfigCommonTwoStepRA_r16__ext1__featureCombinationPreamblesList_r17 {
			A_SEQUENCE_OF(struct NR_FeatureCombinationPreambles_r17) list;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *featureCombinationPreamblesList_r17;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext1;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_RACH_ConfigCommonTwoStepRA_r16_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_oneEighth_5;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_oneFourth_22;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_oneHalf_39;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_one_56;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_two_73;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_msgA_TransMax_r16_93;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_msgA_RestrictedSetConfig_r16_107;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_ra_ContentionResolutionTimer_r16_114;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_RACH_ConfigCommonTwoStepRA_r16;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_RACH_ConfigCommonTwoStepRA_r16_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_RACH_ConfigCommonTwoStepRA_r16_1[15];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_GroupB-ConfiguredTwoStepRA-r16.h"
#include "NR_RA-PrioritizationForSlicing-r17.h"
#include "NR_FeatureCombinationPreambles-r17.h"

#endif	/* _NR_RACH_ConfigCommonTwoStepRA_r16_H_ */
#include <asn_internal.h>
