/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_MeasResultNR.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_choConfig_r17_constraint_13(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 2UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_choCandidate_r17_constr_14 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_choConfig_r17_constr_16 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 1,  1,  1,  2 }	/* (SIZE(1..2)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_firstTriggeredEvent_constr_20 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_choConfig_r17_constr_16 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 1,  1,  1,  2 }	/* (SIZE(1..2)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static asn_TYPE_member_t asn_MBR_NR_cellResults_4[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_MeasResultNR__measResult__cellResults, resultsSSB_Cell),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_MeasQuantityResults,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"resultsSSB-Cell"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MeasResultNR__measResult__cellResults, resultsCSI_RS_Cell),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_MeasQuantityResults,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"resultsCSI-RS-Cell"
		},
};
static const int asn_MAP_NR_cellResults_oms_4[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_cellResults_tags_4[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_cellResults_tag2el_4[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* resultsSSB-Cell */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* resultsCSI-RS-Cell */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_cellResults_specs_4 = {
	sizeof(struct NR_MeasResultNR__measResult__cellResults),
	offsetof(struct NR_MeasResultNR__measResult__cellResults, _asn_ctx),
	asn_MAP_NR_cellResults_tag2el_4,
	2,	/* Count of tags in the map */
	asn_MAP_NR_cellResults_oms_4,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_cellResults_4 = {
	"cellResults",
	"cellResults",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_cellResults_tags_4,
	sizeof(asn_DEF_NR_cellResults_tags_4)
		/sizeof(asn_DEF_NR_cellResults_tags_4[0]) - 1, /* 1 */
	asn_DEF_NR_cellResults_tags_4,	/* Same as above */
	sizeof(asn_DEF_NR_cellResults_tags_4)
		/sizeof(asn_DEF_NR_cellResults_tags_4[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_cellResults_4,
	2,	/* Elements count */
	&asn_SPC_NR_cellResults_specs_4	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_rsIndexResults_7[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_MeasResultNR__measResult__rsIndexResults, resultsSSB_Indexes),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ResultsPerSSB_IndexList,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"resultsSSB-Indexes"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MeasResultNR__measResult__rsIndexResults, resultsCSI_RS_Indexes),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ResultsPerCSI_RS_IndexList,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"resultsCSI-RS-Indexes"
		},
};
static const int asn_MAP_NR_rsIndexResults_oms_7[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_rsIndexResults_tags_7[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_rsIndexResults_tag2el_7[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* resultsSSB-Indexes */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* resultsCSI-RS-Indexes */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_rsIndexResults_specs_7 = {
	sizeof(struct NR_MeasResultNR__measResult__rsIndexResults),
	offsetof(struct NR_MeasResultNR__measResult__rsIndexResults, _asn_ctx),
	asn_MAP_NR_rsIndexResults_tag2el_7,
	2,	/* Count of tags in the map */
	asn_MAP_NR_rsIndexResults_oms_7,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_rsIndexResults_7 = {
	"rsIndexResults",
	"rsIndexResults",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_rsIndexResults_tags_7,
	sizeof(asn_DEF_NR_rsIndexResults_tags_7)
		/sizeof(asn_DEF_NR_rsIndexResults_tags_7[0]) - 1, /* 1 */
	asn_DEF_NR_rsIndexResults_tags_7,	/* Same as above */
	sizeof(asn_DEF_NR_rsIndexResults_tags_7)
		/sizeof(asn_DEF_NR_rsIndexResults_tags_7[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_rsIndexResults_7,
	2,	/* Elements count */
	&asn_SPC_NR_rsIndexResults_specs_7	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_measResult_3[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_MeasResultNR__measResult, cellResults),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_cellResults_4,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"cellResults"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MeasResultNR__measResult, rsIndexResults),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_rsIndexResults_7,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"rsIndexResults"
		},
};
static const int asn_MAP_NR_measResult_oms_3[] = { 1 };
static const ber_tlv_tag_t asn_DEF_NR_measResult_tags_3[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_measResult_tag2el_3[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* cellResults */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* rsIndexResults */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_measResult_specs_3 = {
	sizeof(struct NR_MeasResultNR__measResult),
	offsetof(struct NR_MeasResultNR__measResult, _asn_ctx),
	asn_MAP_NR_measResult_tag2el_3,
	2,	/* Count of tags in the map */
	asn_MAP_NR_measResult_oms_3,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_measResult_3 = {
	"measResult",
	"measResult",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_measResult_tags_3,
	sizeof(asn_DEF_NR_measResult_tags_3)
		/sizeof(asn_DEF_NR_measResult_tags_3[0]) - 1, /* 1 */
	asn_DEF_NR_measResult_tags_3,	/* Same as above */
	sizeof(asn_DEF_NR_measResult_tags_3)
		/sizeof(asn_DEF_NR_measResult_tags_3[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_measResult_3,
	2,	/* Elements count */
	&asn_SPC_NR_measResult_specs_3	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext1_11[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_MeasResultNR__ext1, cgi_Info),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_CGI_InfoNR,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"cgi-Info"
		},
};
static const int asn_MAP_NR_ext1_oms_11[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_11[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_11[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* cgi-Info */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_11 = {
	sizeof(struct NR_MeasResultNR__ext1),
	offsetof(struct NR_MeasResultNR__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_11,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_11,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_11 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_11,
	sizeof(asn_DEF_NR_ext1_tags_11)
		/sizeof(asn_DEF_NR_ext1_tags_11[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_11,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_11)
		/sizeof(asn_DEF_NR_ext1_tags_11[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_11,
	1,	/* Elements count */
	&asn_SPC_NR_ext1_specs_11	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_choCandidate_r17_value2enum_14[] = {
	{ 0,	4,	"true" }
};
static const unsigned int asn_MAP_NR_choCandidate_r17_enum2value_14[] = {
	0	/* true(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_choCandidate_r17_specs_14 = {
	asn_MAP_NR_choCandidate_r17_value2enum_14,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_choCandidate_r17_enum2value_14,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_choCandidate_r17_tags_14[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_choCandidate_r17_14 = {
	"choCandidate-r17",
	"choCandidate-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_choCandidate_r17_tags_14,
	sizeof(asn_DEF_NR_choCandidate_r17_tags_14)
		/sizeof(asn_DEF_NR_choCandidate_r17_tags_14[0]) - 1, /* 1 */
	asn_DEF_NR_choCandidate_r17_tags_14,	/* Same as above */
	sizeof(asn_DEF_NR_choCandidate_r17_tags_14)
		/sizeof(asn_DEF_NR_choCandidate_r17_tags_14[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_choCandidate_r17_constr_14,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_choCandidate_r17_specs_14	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_choConfig_r17_16[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_CondTriggerConfig_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_choConfig_r17_tags_16[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_choConfig_r17_specs_16 = {
	sizeof(struct NR_MeasResultNR__ext2__choConfig_r17),
	offsetof(struct NR_MeasResultNR__ext2__choConfig_r17, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_choConfig_r17_16 = {
	"choConfig-r17",
	"choConfig-r17",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_choConfig_r17_tags_16,
	sizeof(asn_DEF_NR_choConfig_r17_tags_16)
		/sizeof(asn_DEF_NR_choConfig_r17_tags_16[0]) - 1, /* 1 */
	asn_DEF_NR_choConfig_r17_tags_16,	/* Same as above */
	sizeof(asn_DEF_NR_choConfig_r17_tags_16)
		/sizeof(asn_DEF_NR_choConfig_r17_tags_16[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_choConfig_r17_constr_16,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_choConfig_r17_16,
	1,	/* Single element */
	&asn_SPC_NR_choConfig_r17_specs_16	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_firstTriggeredEvent_value2enum_20[] = {
	{ 0,	14,	"condFirstEvent" },
	{ 1,	15,	"condSecondEvent" }
};
static const unsigned int asn_MAP_NR_firstTriggeredEvent_enum2value_20[] = {
	0,	/* condFirstEvent(0) */
	1	/* condSecondEvent(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_firstTriggeredEvent_specs_20 = {
	asn_MAP_NR_firstTriggeredEvent_value2enum_20,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_firstTriggeredEvent_enum2value_20,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_firstTriggeredEvent_tags_20[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_firstTriggeredEvent_20 = {
	"firstTriggeredEvent",
	"firstTriggeredEvent",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_firstTriggeredEvent_tags_20,
	sizeof(asn_DEF_NR_firstTriggeredEvent_tags_20)
		/sizeof(asn_DEF_NR_firstTriggeredEvent_tags_20[0]) - 1, /* 1 */
	asn_DEF_NR_firstTriggeredEvent_tags_20,	/* Same as above */
	sizeof(asn_DEF_NR_firstTriggeredEvent_tags_20)
		/sizeof(asn_DEF_NR_firstTriggeredEvent_tags_20[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_firstTriggeredEvent_constr_20,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_firstTriggeredEvent_specs_20	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_triggeredEvent_r17_18[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_MeasResultNR__ext2__triggeredEvent_r17, timeBetweenEvents_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_TimeBetweenEvent_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"timeBetweenEvents-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MeasResultNR__ext2__triggeredEvent_r17, firstTriggeredEvent),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_firstTriggeredEvent_20,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"firstTriggeredEvent"
		},
};
static const int asn_MAP_NR_triggeredEvent_r17_oms_18[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_triggeredEvent_r17_tags_18[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_triggeredEvent_r17_tag2el_18[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* timeBetweenEvents-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* firstTriggeredEvent */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_triggeredEvent_r17_specs_18 = {
	sizeof(struct NR_MeasResultNR__ext2__triggeredEvent_r17),
	offsetof(struct NR_MeasResultNR__ext2__triggeredEvent_r17, _asn_ctx),
	asn_MAP_NR_triggeredEvent_r17_tag2el_18,
	2,	/* Count of tags in the map */
	asn_MAP_NR_triggeredEvent_r17_oms_18,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_triggeredEvent_r17_18 = {
	"triggeredEvent-r17",
	"triggeredEvent-r17",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_triggeredEvent_r17_tags_18,
	sizeof(asn_DEF_NR_triggeredEvent_r17_tags_18)
		/sizeof(asn_DEF_NR_triggeredEvent_r17_tags_18[0]) - 1, /* 1 */
	asn_DEF_NR_triggeredEvent_r17_tags_18,	/* Same as above */
	sizeof(asn_DEF_NR_triggeredEvent_r17_tags_18)
		/sizeof(asn_DEF_NR_triggeredEvent_r17_tags_18[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_triggeredEvent_r17_18,
	2,	/* Elements count */
	&asn_SPC_NR_triggeredEvent_r17_specs_18	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext2_13[] = {
	{ ATF_POINTER, 3, offsetof(struct NR_MeasResultNR__ext2, choCandidate_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_choCandidate_r17_14,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"choCandidate-r17"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_MeasResultNR__ext2, choConfig_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_choConfig_r17_16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_choConfig_r17_constr_16,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_choConfig_r17_constraint_13
		},
		0, 0, /* No default value */
		"choConfig-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MeasResultNR__ext2, triggeredEvent_r17),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		0,
		&asn_DEF_NR_triggeredEvent_r17_18,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"triggeredEvent-r17"
		},
};
static const int asn_MAP_NR_ext2_oms_13[] = { 0, 1, 2 };
static const ber_tlv_tag_t asn_DEF_NR_ext2_tags_13[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext2_tag2el_13[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* choCandidate-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* choConfig-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* triggeredEvent-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext2_specs_13 = {
	sizeof(struct NR_MeasResultNR__ext2),
	offsetof(struct NR_MeasResultNR__ext2, _asn_ctx),
	asn_MAP_NR_ext2_tag2el_13,
	3,	/* Count of tags in the map */
	asn_MAP_NR_ext2_oms_13,	/* Optional members */
	3, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext2_13 = {
	"ext2",
	"ext2",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext2_tags_13,
	sizeof(asn_DEF_NR_ext2_tags_13)
		/sizeof(asn_DEF_NR_ext2_tags_13[0]) - 1, /* 1 */
	asn_DEF_NR_ext2_tags_13,	/* Same as above */
	sizeof(asn_DEF_NR_ext2_tags_13)
		/sizeof(asn_DEF_NR_ext2_tags_13[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext2_13,
	3,	/* Elements count */
	&asn_SPC_NR_ext2_specs_13	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_MeasResultNR_1[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_MeasResultNR, physCellId),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_PhysCellId,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"physCellId"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_MeasResultNR, measResult),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_measResult_3,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"measResult"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_MeasResultNR, ext1),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		0,
		&asn_DEF_NR_ext1_11,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MeasResultNR, ext2),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		0,
		&asn_DEF_NR_ext2_13,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext2"
		},
};
static const int asn_MAP_NR_MeasResultNR_oms_1[] = { 0, 2, 3 };
static const ber_tlv_tag_t asn_DEF_NR_MeasResultNR_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_MeasResultNR_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* physCellId */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* measResult */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* ext1 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 } /* ext2 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_MeasResultNR_specs_1 = {
	sizeof(struct NR_MeasResultNR),
	offsetof(struct NR_MeasResultNR, _asn_ctx),
	asn_MAP_NR_MeasResultNR_tag2el_1,
	4,	/* Count of tags in the map */
	asn_MAP_NR_MeasResultNR_oms_1,	/* Optional members */
	1, 2,	/* Root/Additions */
	2,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_MeasResultNR = {
	"MeasResultNR",
	"MeasResultNR",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_MeasResultNR_tags_1,
	sizeof(asn_DEF_NR_MeasResultNR_tags_1)
		/sizeof(asn_DEF_NR_MeasResultNR_tags_1[0]), /* 1 */
	asn_DEF_NR_MeasResultNR_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_MeasResultNR_tags_1)
		/sizeof(asn_DEF_NR_MeasResultNR_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_MeasResultNR_1,
	4,	/* Elements count */
	&asn_SPC_NR_MeasResultNR_specs_1	/* Additional specs */
};

