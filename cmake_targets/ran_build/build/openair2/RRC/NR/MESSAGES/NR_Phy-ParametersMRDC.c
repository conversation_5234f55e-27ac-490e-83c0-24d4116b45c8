/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_Phy-ParametersMRDC.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_naics_Capability_List_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 8UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_naics_Capability_List_constr_2 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (SIZE(1..8)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_tdd_PCellUL_TX_AllUL_Subframe_r16_constr_8 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_fdd_PCellUL_TX_AllUL_Subframe_r16_constr_10 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_naics_Capability_List_constr_2 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (SIZE(1..8)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static asn_TYPE_member_t asn_MBR_NR_naics_Capability_List_2[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_NAICS_Capability_Entry,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_naics_Capability_List_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_naics_Capability_List_specs_2 = {
	sizeof(struct NR_Phy_ParametersMRDC__naics_Capability_List),
	offsetof(struct NR_Phy_ParametersMRDC__naics_Capability_List, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_naics_Capability_List_2 = {
	"naics-Capability-List",
	"naics-Capability-List",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_naics_Capability_List_tags_2,
	sizeof(asn_DEF_NR_naics_Capability_List_tags_2)
		/sizeof(asn_DEF_NR_naics_Capability_List_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_naics_Capability_List_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_naics_Capability_List_tags_2)
		/sizeof(asn_DEF_NR_naics_Capability_List_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_naics_Capability_List_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_naics_Capability_List_2,
	1,	/* Single element */
	&asn_SPC_NR_naics_Capability_List_specs_2	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext1_5[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersMRDC__ext1, spCellPlacement),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_CarrierAggregationVariant,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"spCellPlacement"
		},
};
static const int asn_MAP_NR_ext1_oms_5[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_5[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_5[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* spCellPlacement */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_5 = {
	sizeof(struct NR_Phy_ParametersMRDC__ext1),
	offsetof(struct NR_Phy_ParametersMRDC__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_5,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_5,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_5 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_5,
	sizeof(asn_DEF_NR_ext1_tags_5)
		/sizeof(asn_DEF_NR_ext1_tags_5[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_5,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_5)
		/sizeof(asn_DEF_NR_ext1_tags_5[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_5,
	1,	/* Elements count */
	&asn_SPC_NR_ext1_specs_5	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_tdd_PCellUL_TX_AllUL_Subframe_r16_value2enum_8[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_tdd_PCellUL_TX_AllUL_Subframe_r16_enum2value_8[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_tdd_PCellUL_TX_AllUL_Subframe_r16_specs_8 = {
	asn_MAP_NR_tdd_PCellUL_TX_AllUL_Subframe_r16_value2enum_8,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_tdd_PCellUL_TX_AllUL_Subframe_r16_enum2value_8,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_tdd_PCellUL_TX_AllUL_Subframe_r16_tags_8[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_tdd_PCellUL_TX_AllUL_Subframe_r16_8 = {
	"tdd-PCellUL-TX-AllUL-Subframe-r16",
	"tdd-PCellUL-TX-AllUL-Subframe-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_tdd_PCellUL_TX_AllUL_Subframe_r16_tags_8,
	sizeof(asn_DEF_NR_tdd_PCellUL_TX_AllUL_Subframe_r16_tags_8)
		/sizeof(asn_DEF_NR_tdd_PCellUL_TX_AllUL_Subframe_r16_tags_8[0]) - 1, /* 1 */
	asn_DEF_NR_tdd_PCellUL_TX_AllUL_Subframe_r16_tags_8,	/* Same as above */
	sizeof(asn_DEF_NR_tdd_PCellUL_TX_AllUL_Subframe_r16_tags_8)
		/sizeof(asn_DEF_NR_tdd_PCellUL_TX_AllUL_Subframe_r16_tags_8[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_tdd_PCellUL_TX_AllUL_Subframe_r16_constr_8,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_tdd_PCellUL_TX_AllUL_Subframe_r16_specs_8	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_fdd_PCellUL_TX_AllUL_Subframe_r16_value2enum_10[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_fdd_PCellUL_TX_AllUL_Subframe_r16_enum2value_10[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_fdd_PCellUL_TX_AllUL_Subframe_r16_specs_10 = {
	asn_MAP_NR_fdd_PCellUL_TX_AllUL_Subframe_r16_value2enum_10,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_fdd_PCellUL_TX_AllUL_Subframe_r16_enum2value_10,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_fdd_PCellUL_TX_AllUL_Subframe_r16_tags_10[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_fdd_PCellUL_TX_AllUL_Subframe_r16_10 = {
	"fdd-PCellUL-TX-AllUL-Subframe-r16",
	"fdd-PCellUL-TX-AllUL-Subframe-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_fdd_PCellUL_TX_AllUL_Subframe_r16_tags_10,
	sizeof(asn_DEF_NR_fdd_PCellUL_TX_AllUL_Subframe_r16_tags_10)
		/sizeof(asn_DEF_NR_fdd_PCellUL_TX_AllUL_Subframe_r16_tags_10[0]) - 1, /* 1 */
	asn_DEF_NR_fdd_PCellUL_TX_AllUL_Subframe_r16_tags_10,	/* Same as above */
	sizeof(asn_DEF_NR_fdd_PCellUL_TX_AllUL_Subframe_r16_tags_10)
		/sizeof(asn_DEF_NR_fdd_PCellUL_TX_AllUL_Subframe_r16_tags_10[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_fdd_PCellUL_TX_AllUL_Subframe_r16_constr_10,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_fdd_PCellUL_TX_AllUL_Subframe_r16_specs_10	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext2_7[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_Phy_ParametersMRDC__ext2, tdd_PCellUL_TX_AllUL_Subframe_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_tdd_PCellUL_TX_AllUL_Subframe_r16_8,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"tdd-PCellUL-TX-AllUL-Subframe-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersMRDC__ext2, fdd_PCellUL_TX_AllUL_Subframe_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_fdd_PCellUL_TX_AllUL_Subframe_r16_10,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"fdd-PCellUL-TX-AllUL-Subframe-r16"
		},
};
static const int asn_MAP_NR_ext2_oms_7[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_ext2_tags_7[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext2_tag2el_7[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* tdd-PCellUL-TX-AllUL-Subframe-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* fdd-PCellUL-TX-AllUL-Subframe-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext2_specs_7 = {
	sizeof(struct NR_Phy_ParametersMRDC__ext2),
	offsetof(struct NR_Phy_ParametersMRDC__ext2, _asn_ctx),
	asn_MAP_NR_ext2_tag2el_7,
	2,	/* Count of tags in the map */
	asn_MAP_NR_ext2_oms_7,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext2_7 = {
	"ext2",
	"ext2",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext2_tags_7,
	sizeof(asn_DEF_NR_ext2_tags_7)
		/sizeof(asn_DEF_NR_ext2_tags_7[0]) - 1, /* 1 */
	asn_DEF_NR_ext2_tags_7,	/* Same as above */
	sizeof(asn_DEF_NR_ext2_tags_7)
		/sizeof(asn_DEF_NR_ext2_tags_7[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext2_7,
	2,	/* Elements count */
	&asn_SPC_NR_ext2_specs_7	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_Phy_ParametersMRDC_1[] = {
	{ ATF_POINTER, 3, offsetof(struct NR_Phy_ParametersMRDC, naics_Capability_List),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_naics_Capability_List_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_naics_Capability_List_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_naics_Capability_List_constraint_1
		},
		0, 0, /* No default value */
		"naics-Capability-List"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_Phy_ParametersMRDC, ext1),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_ext1_5,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersMRDC, ext2),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		0,
		&asn_DEF_NR_ext2_7,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext2"
		},
};
static const int asn_MAP_NR_Phy_ParametersMRDC_oms_1[] = { 0, 1, 2 };
static const ber_tlv_tag_t asn_DEF_NR_Phy_ParametersMRDC_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_Phy_ParametersMRDC_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* naics-Capability-List */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* ext1 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* ext2 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_Phy_ParametersMRDC_specs_1 = {
	sizeof(struct NR_Phy_ParametersMRDC),
	offsetof(struct NR_Phy_ParametersMRDC, _asn_ctx),
	asn_MAP_NR_Phy_ParametersMRDC_tag2el_1,
	3,	/* Count of tags in the map */
	asn_MAP_NR_Phy_ParametersMRDC_oms_1,	/* Optional members */
	1, 2,	/* Root/Additions */
	1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_Phy_ParametersMRDC = {
	"Phy-ParametersMRDC",
	"Phy-ParametersMRDC",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_Phy_ParametersMRDC_tags_1,
	sizeof(asn_DEF_NR_Phy_ParametersMRDC_tags_1)
		/sizeof(asn_DEF_NR_Phy_ParametersMRDC_tags_1[0]), /* 1 */
	asn_DEF_NR_Phy_ParametersMRDC_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_Phy_ParametersMRDC_tags_1)
		/sizeof(asn_DEF_NR_Phy_ParametersMRDC_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_Phy_ParametersMRDC_1,
	3,	/* Elements count */
	&asn_SPC_NR_Phy_ParametersMRDC_specs_1	/* Additional specs */
};

