/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_MobilityFromNRCommand_IEs_H_
#define	_NR_MobilityFromNRCommand_IEs_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <OCTET_STRING.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_MobilityFromNRCommand_IEs__targetRAT_Type {
	NR_MobilityFromNRCommand_IEs__targetRAT_Type_eutra	= 0,
	NR_MobilityFromNRCommand_IEs__targetRAT_Type_utra_fdd_v1610	= 1,
	NR_MobilityFromNRCommand_IEs__targetRAT_Type_spare2	= 2,
	NR_MobilityFromNRCommand_IEs__targetRAT_Type_spare1	= 3
	/*
	 * Enumeration is extensible
	 */
} e_NR_MobilityFromNRCommand_IEs__targetRAT_Type;

/* Forward declarations */
struct NR_MobilityFromNRCommand_v1610_IEs;

/* NR_MobilityFromNRCommand-IEs */
typedef struct NR_MobilityFromNRCommand_IEs {
	long	 targetRAT_Type;
	OCTET_STRING_t	 targetRAT_MessageContainer;
	OCTET_STRING_t	*nas_SecurityParamFromNR;	/* OPTIONAL */
	OCTET_STRING_t	*lateNonCriticalExtension;	/* OPTIONAL */
	struct NR_MobilityFromNRCommand_v1610_IEs	*nonCriticalExtension;	/* OPTIONAL */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_MobilityFromNRCommand_IEs_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_targetRAT_Type_2;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_MobilityFromNRCommand_IEs;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_MobilityFromNRCommand_IEs_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_MobilityFromNRCommand_IEs_1[5];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_MobilityFromNRCommand-v1610-IEs.h"

#endif	/* _NR_MobilityFromNRCommand_IEs_H_ */
#include <asn_internal.h>
