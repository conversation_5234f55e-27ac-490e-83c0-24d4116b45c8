/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "PC5-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_MAC_ParametersSidelink_r17_H_
#define	_NR_MAC_ParametersSidelink_r17_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_MAC_ParametersSidelink_r17__drx_OnSidelink_r17 {
	NR_MAC_ParametersSidelink_r17__drx_OnSidelink_r17_supported	= 0
} e_NR_MAC_ParametersSidelink_r17__drx_OnSidelink_r17;

/* NR_MAC-ParametersSidelink-r17 */
typedef struct NR_MAC_ParametersSidelink_r17 {
	long	*drx_OnSidelink_r17;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_MAC_ParametersSidelink_r17_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_drx_OnSidelink_r17_2;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_MAC_ParametersSidelink_r17;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_MAC_ParametersSidelink_r17_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_MAC_ParametersSidelink_r17_1[1];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_MAC_ParametersSidelink_r17_H_ */
#include <asn_internal.h>
