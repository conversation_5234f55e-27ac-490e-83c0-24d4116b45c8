/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_RACH-ConfigGenericTwoStepRA-r16.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_msgA_PRACH_ConfigurationIndex_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 262L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_msgA_RO_FrequencyStart_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 274L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_msgA_ZeroCorrelationZoneConfig_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 15L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_msgA_PreambleReceivedTargetPower_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= -202L && value <= -60L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_msgA_RO_FDM_r16_constr_3 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_msgA_PreamblePowerRampingStep_r16_constr_10 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_msgB_ResponseWindow_r16_constr_16 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  9 }	/* (0..9) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_preambleTransMax_r16_constr_27 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  10 }	/* (0..10) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_msgB_ResponseWindow_v1700_constr_41 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  5 }	/* (0..5) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_msgA_PRACH_ConfigurationIndex_r16_constr_2 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 9,  9,  0,  262 }	/* (0..262) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_msgA_RO_FrequencyStart_r16_constr_8 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 9,  9,  0,  274 }	/* (0..274) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_msgA_ZeroCorrelationZoneConfig_r16_constr_9 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  15 }	/* (0..15) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_msgA_PreambleReceivedTargetPower_r16_constr_15 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 8,  8, -202, -60 }	/* (-202..-60) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_msgA_RO_FDM_r16_value2enum_3[] = {
	{ 0,	3,	"one" },
	{ 1,	3,	"two" },
	{ 2,	4,	"four" },
	{ 3,	5,	"eight" }
};
static const unsigned int asn_MAP_NR_msgA_RO_FDM_r16_enum2value_3[] = {
	3,	/* eight(3) */
	2,	/* four(2) */
	0,	/* one(0) */
	1	/* two(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_msgA_RO_FDM_r16_specs_3 = {
	asn_MAP_NR_msgA_RO_FDM_r16_value2enum_3,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_msgA_RO_FDM_r16_enum2value_3,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_msgA_RO_FDM_r16_tags_3[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_msgA_RO_FDM_r16_3 = {
	"msgA-RO-FDM-r16",
	"msgA-RO-FDM-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_msgA_RO_FDM_r16_tags_3,
	sizeof(asn_DEF_NR_msgA_RO_FDM_r16_tags_3)
		/sizeof(asn_DEF_NR_msgA_RO_FDM_r16_tags_3[0]) - 1, /* 1 */
	asn_DEF_NR_msgA_RO_FDM_r16_tags_3,	/* Same as above */
	sizeof(asn_DEF_NR_msgA_RO_FDM_r16_tags_3)
		/sizeof(asn_DEF_NR_msgA_RO_FDM_r16_tags_3[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_msgA_RO_FDM_r16_constr_3,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_msgA_RO_FDM_r16_specs_3	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_msgA_PreamblePowerRampingStep_r16_value2enum_10[] = {
	{ 0,	3,	"dB0" },
	{ 1,	3,	"dB2" },
	{ 2,	3,	"dB4" },
	{ 3,	3,	"dB6" }
};
static const unsigned int asn_MAP_NR_msgA_PreamblePowerRampingStep_r16_enum2value_10[] = {
	0,	/* dB0(0) */
	1,	/* dB2(1) */
	2,	/* dB4(2) */
	3	/* dB6(3) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_msgA_PreamblePowerRampingStep_r16_specs_10 = {
	asn_MAP_NR_msgA_PreamblePowerRampingStep_r16_value2enum_10,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_msgA_PreamblePowerRampingStep_r16_enum2value_10,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_msgA_PreamblePowerRampingStep_r16_tags_10[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_msgA_PreamblePowerRampingStep_r16_10 = {
	"msgA-PreamblePowerRampingStep-r16",
	"msgA-PreamblePowerRampingStep-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_msgA_PreamblePowerRampingStep_r16_tags_10,
	sizeof(asn_DEF_NR_msgA_PreamblePowerRampingStep_r16_tags_10)
		/sizeof(asn_DEF_NR_msgA_PreamblePowerRampingStep_r16_tags_10[0]) - 1, /* 1 */
	asn_DEF_NR_msgA_PreamblePowerRampingStep_r16_tags_10,	/* Same as above */
	sizeof(asn_DEF_NR_msgA_PreamblePowerRampingStep_r16_tags_10)
		/sizeof(asn_DEF_NR_msgA_PreamblePowerRampingStep_r16_tags_10[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_msgA_PreamblePowerRampingStep_r16_constr_10,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_msgA_PreamblePowerRampingStep_r16_specs_10	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_msgB_ResponseWindow_r16_value2enum_16[] = {
	{ 0,	3,	"sl1" },
	{ 1,	3,	"sl2" },
	{ 2,	3,	"sl4" },
	{ 3,	3,	"sl8" },
	{ 4,	4,	"sl10" },
	{ 5,	4,	"sl20" },
	{ 6,	4,	"sl40" },
	{ 7,	4,	"sl80" },
	{ 8,	5,	"sl160" },
	{ 9,	5,	"sl320" }
};
static const unsigned int asn_MAP_NR_msgB_ResponseWindow_r16_enum2value_16[] = {
	0,	/* sl1(0) */
	4,	/* sl10(4) */
	8,	/* sl160(8) */
	1,	/* sl2(1) */
	5,	/* sl20(5) */
	9,	/* sl320(9) */
	2,	/* sl4(2) */
	6,	/* sl40(6) */
	3,	/* sl8(3) */
	7	/* sl80(7) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_msgB_ResponseWindow_r16_specs_16 = {
	asn_MAP_NR_msgB_ResponseWindow_r16_value2enum_16,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_msgB_ResponseWindow_r16_enum2value_16,	/* N => "tag"; sorted by N */
	10,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_msgB_ResponseWindow_r16_tags_16[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_msgB_ResponseWindow_r16_16 = {
	"msgB-ResponseWindow-r16",
	"msgB-ResponseWindow-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_msgB_ResponseWindow_r16_tags_16,
	sizeof(asn_DEF_NR_msgB_ResponseWindow_r16_tags_16)
		/sizeof(asn_DEF_NR_msgB_ResponseWindow_r16_tags_16[0]) - 1, /* 1 */
	asn_DEF_NR_msgB_ResponseWindow_r16_tags_16,	/* Same as above */
	sizeof(asn_DEF_NR_msgB_ResponseWindow_r16_tags_16)
		/sizeof(asn_DEF_NR_msgB_ResponseWindow_r16_tags_16[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_msgB_ResponseWindow_r16_constr_16,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_msgB_ResponseWindow_r16_specs_16	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_preambleTransMax_r16_value2enum_27[] = {
	{ 0,	2,	"n3" },
	{ 1,	2,	"n4" },
	{ 2,	2,	"n5" },
	{ 3,	2,	"n6" },
	{ 4,	2,	"n7" },
	{ 5,	2,	"n8" },
	{ 6,	3,	"n10" },
	{ 7,	3,	"n20" },
	{ 8,	3,	"n50" },
	{ 9,	4,	"n100" },
	{ 10,	4,	"n200" }
};
static const unsigned int asn_MAP_NR_preambleTransMax_r16_enum2value_27[] = {
	6,	/* n10(6) */
	9,	/* n100(9) */
	7,	/* n20(7) */
	10,	/* n200(10) */
	0,	/* n3(0) */
	1,	/* n4(1) */
	2,	/* n5(2) */
	8,	/* n50(8) */
	3,	/* n6(3) */
	4,	/* n7(4) */
	5	/* n8(5) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_preambleTransMax_r16_specs_27 = {
	asn_MAP_NR_preambleTransMax_r16_value2enum_27,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_preambleTransMax_r16_enum2value_27,	/* N => "tag"; sorted by N */
	11,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_preambleTransMax_r16_tags_27[] = {
	(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_preambleTransMax_r16_27 = {
	"preambleTransMax-r16",
	"preambleTransMax-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_preambleTransMax_r16_tags_27,
	sizeof(asn_DEF_NR_preambleTransMax_r16_tags_27)
		/sizeof(asn_DEF_NR_preambleTransMax_r16_tags_27[0]) - 1, /* 1 */
	asn_DEF_NR_preambleTransMax_r16_tags_27,	/* Same as above */
	sizeof(asn_DEF_NR_preambleTransMax_r16_tags_27)
		/sizeof(asn_DEF_NR_preambleTransMax_r16_tags_27[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_preambleTransMax_r16_constr_27,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_preambleTransMax_r16_specs_27	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_msgB_ResponseWindow_v1700_value2enum_41[] = {
	{ 0,	5,	"sl240" },
	{ 1,	5,	"sl640" },
	{ 2,	5,	"sl960" },
	{ 3,	6,	"sl1280" },
	{ 4,	6,	"sl1920" },
	{ 5,	6,	"sl2560" }
};
static const unsigned int asn_MAP_NR_msgB_ResponseWindow_v1700_enum2value_41[] = {
	3,	/* sl1280(3) */
	4,	/* sl1920(4) */
	0,	/* sl240(0) */
	5,	/* sl2560(5) */
	1,	/* sl640(1) */
	2	/* sl960(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_msgB_ResponseWindow_v1700_specs_41 = {
	asn_MAP_NR_msgB_ResponseWindow_v1700_value2enum_41,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_msgB_ResponseWindow_v1700_enum2value_41,	/* N => "tag"; sorted by N */
	6,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_msgB_ResponseWindow_v1700_tags_41[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_msgB_ResponseWindow_v1700_41 = {
	"msgB-ResponseWindow-v1700",
	"msgB-ResponseWindow-v1700",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_msgB_ResponseWindow_v1700_tags_41,
	sizeof(asn_DEF_NR_msgB_ResponseWindow_v1700_tags_41)
		/sizeof(asn_DEF_NR_msgB_ResponseWindow_v1700_tags_41[0]) - 1, /* 1 */
	asn_DEF_NR_msgB_ResponseWindow_v1700_tags_41,	/* Same as above */
	sizeof(asn_DEF_NR_msgB_ResponseWindow_v1700_tags_41)
		/sizeof(asn_DEF_NR_msgB_ResponseWindow_v1700_tags_41[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_msgB_ResponseWindow_v1700_constr_41,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_msgB_ResponseWindow_v1700_specs_41	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext1_40[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_RACH_ConfigGenericTwoStepRA_r16__ext1, msgB_ResponseWindow_v1700),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_msgB_ResponseWindow_v1700_41,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"msgB-ResponseWindow-v1700"
		},
};
static const int asn_MAP_NR_ext1_oms_40[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_40[] = {
	(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_40[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* msgB-ResponseWindow-v1700 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_40 = {
	sizeof(struct NR_RACH_ConfigGenericTwoStepRA_r16__ext1),
	offsetof(struct NR_RACH_ConfigGenericTwoStepRA_r16__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_40,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_40,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_40 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_40,
	sizeof(asn_DEF_NR_ext1_tags_40)
		/sizeof(asn_DEF_NR_ext1_tags_40[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_40,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_40)
		/sizeof(asn_DEF_NR_ext1_tags_40[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_40,
	1,	/* Elements count */
	&asn_SPC_NR_ext1_specs_40	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_RACH_ConfigGenericTwoStepRA_r16_1[] = {
	{ ATF_POINTER, 9, offsetof(struct NR_RACH_ConfigGenericTwoStepRA_r16, msgA_PRACH_ConfigurationIndex_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_msgA_PRACH_ConfigurationIndex_r16_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_msgA_PRACH_ConfigurationIndex_r16_constraint_1
		},
		0, 0, /* No default value */
		"msgA-PRACH-ConfigurationIndex-r16"
		},
	{ ATF_POINTER, 8, offsetof(struct NR_RACH_ConfigGenericTwoStepRA_r16, msgA_RO_FDM_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_msgA_RO_FDM_r16_3,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"msgA-RO-FDM-r16"
		},
	{ ATF_POINTER, 7, offsetof(struct NR_RACH_ConfigGenericTwoStepRA_r16, msgA_RO_FrequencyStart_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_msgA_RO_FrequencyStart_r16_constr_8,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_msgA_RO_FrequencyStart_r16_constraint_1
		},
		0, 0, /* No default value */
		"msgA-RO-FrequencyStart-r16"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_RACH_ConfigGenericTwoStepRA_r16, msgA_ZeroCorrelationZoneConfig_r16),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_msgA_ZeroCorrelationZoneConfig_r16_constr_9,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_msgA_ZeroCorrelationZoneConfig_r16_constraint_1
		},
		0, 0, /* No default value */
		"msgA-ZeroCorrelationZoneConfig-r16"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_RACH_ConfigGenericTwoStepRA_r16, msgA_PreamblePowerRampingStep_r16),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_msgA_PreamblePowerRampingStep_r16_10,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"msgA-PreamblePowerRampingStep-r16"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_RACH_ConfigGenericTwoStepRA_r16, msgA_PreambleReceivedTargetPower_r16),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_msgA_PreambleReceivedTargetPower_r16_constr_15,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_msgA_PreambleReceivedTargetPower_r16_constraint_1
		},
		0, 0, /* No default value */
		"msgA-PreambleReceivedTargetPower-r16"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_RACH_ConfigGenericTwoStepRA_r16, msgB_ResponseWindow_r16),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_msgB_ResponseWindow_r16_16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"msgB-ResponseWindow-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_RACH_ConfigGenericTwoStepRA_r16, preambleTransMax_r16),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_preambleTransMax_r16_27,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"preambleTransMax-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RACH_ConfigGenericTwoStepRA_r16, ext1),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		0,
		&asn_DEF_NR_ext1_40,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
};
static const int asn_MAP_NR_RACH_ConfigGenericTwoStepRA_r16_oms_1[] = { 0, 1, 2, 3, 4, 5, 6, 7, 8 };
static const ber_tlv_tag_t asn_DEF_NR_RACH_ConfigGenericTwoStepRA_r16_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_RACH_ConfigGenericTwoStepRA_r16_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* msgA-PRACH-ConfigurationIndex-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* msgA-RO-FDM-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* msgA-RO-FrequencyStart-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* msgA-ZeroCorrelationZoneConfig-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* msgA-PreamblePowerRampingStep-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* msgA-PreambleReceivedTargetPower-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* msgB-ResponseWindow-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* preambleTransMax-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 } /* ext1 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_RACH_ConfigGenericTwoStepRA_r16_specs_1 = {
	sizeof(struct NR_RACH_ConfigGenericTwoStepRA_r16),
	offsetof(struct NR_RACH_ConfigGenericTwoStepRA_r16, _asn_ctx),
	asn_MAP_NR_RACH_ConfigGenericTwoStepRA_r16_tag2el_1,
	9,	/* Count of tags in the map */
	asn_MAP_NR_RACH_ConfigGenericTwoStepRA_r16_oms_1,	/* Optional members */
	8, 1,	/* Root/Additions */
	8,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_RACH_ConfigGenericTwoStepRA_r16 = {
	"RACH-ConfigGenericTwoStepRA-r16",
	"RACH-ConfigGenericTwoStepRA-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_RACH_ConfigGenericTwoStepRA_r16_tags_1,
	sizeof(asn_DEF_NR_RACH_ConfigGenericTwoStepRA_r16_tags_1)
		/sizeof(asn_DEF_NR_RACH_ConfigGenericTwoStepRA_r16_tags_1[0]), /* 1 */
	asn_DEF_NR_RACH_ConfigGenericTwoStepRA_r16_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_RACH_ConfigGenericTwoStepRA_r16_tags_1)
		/sizeof(asn_DEF_NR_RACH_ConfigGenericTwoStepRA_r16_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_RACH_ConfigGenericTwoStepRA_r16_1,
	9,	/* Elements count */
	&asn_SPC_NR_RACH_ConfigGenericTwoStepRA_r16_specs_1	/* Additional specs */
};

