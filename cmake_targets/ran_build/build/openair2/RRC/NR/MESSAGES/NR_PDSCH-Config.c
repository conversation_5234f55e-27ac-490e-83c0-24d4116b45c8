/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_PDSCH-Config.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_aperiodicZP_CSI_RS_ResourceSetsToAddModListDCI_1_2_r16_constraint_64(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 16UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_aperiodicZP_CSI_RS_ResourceSetsToReleaseListDCI_1_2_r16_constraint_64(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 16UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_harq_ProcessNumberSizeDCI_1_2_r16_constraint_64(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 4L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_numberOfBitsForRV_DCI_1_2_r16_constraint_64(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 2L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_dataScramblingIdentityPDSCH2_r16_constraint_64(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 1023L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_dl_OrJointTCI_StateToAddModList_r17_constraint_134(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 128UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_dl_OrJointTCI_StateToReleaseList_r17_constraint_134(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 128UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_harq_ProcessNumberSizeDCI_1_2_v1700_constraint_122(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 5L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_harq_ProcessNumberSizeDCI_1_1_r17_constraint_122(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value == 5L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_sizeDCI_4_2_r17_constraint_122(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 20L && value <= 140L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_dataScramblingIdentityPDSCH_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 1023L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_tci_StatesToAddModList_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 128UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_tci_StatesToReleaseList_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 128UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_rateMatchPatternToAddModList_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 4UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_rateMatchPatternToReleaseList_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 4UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_zp_CSI_RS_ResourceToAddModList_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 32UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_zp_CSI_RS_ResourceToReleaseList_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 32UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_aperiodic_ZP_CSI_RS_ResourceSetsToAddModList_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 16UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_aperiodic_ZP_CSI_RS_ResourceSetsToReleaseList_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 16UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_sp_ZP_CSI_RS_ResourceSetsToAddModList_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 16UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_sp_ZP_CSI_RS_ResourceSetsToReleaseList_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 16UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_tci_StatesToAddModList_constr_5 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 7,  7,  1,  128 }	/* (SIZE(1..128)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_tci_StatesToReleaseList_constr_7 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 7,  7,  1,  128 }	/* (SIZE(1..128)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_vrb_ToPRB_Interleaver_constr_9 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_resourceAllocation_constr_12 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pdsch_AggregationFactor_constr_17 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_rateMatchPatternToAddModList_constr_21 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 2,  2,  1,  4 }	/* (SIZE(1..4)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_rateMatchPatternToReleaseList_constr_23 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 2,  2,  1,  4 }	/* (SIZE(1..4)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_rbg_Size_constr_27 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_mcs_Table_constr_30 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_maxNrofCodeWordsScheduledByDCI_constr_33 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_bundleSize_constr_38 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_bundleSizeSet1_constr_42 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_bundleSizeSet2_constr_47 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_prb_BundlingType_constr_36 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_zp_CSI_RS_ResourceToAddModList_constr_50 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 5,  5,  1,  32 }	/* (SIZE(1..32)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_zp_CSI_RS_ResourceToReleaseList_constr_52 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 5,  5,  1,  32 }	/* (SIZE(1..32)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_aperiodic_ZP_CSI_RS_ResourceSetsToAddModList_constr_54 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_aperiodic_ZP_CSI_RS_ResourceSetsToReleaseList_constr_56 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_sp_ZP_CSI_RS_ResourceSetsToAddModList_constr_58 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_sp_ZP_CSI_RS_ResourceSetsToReleaseList_constr_60 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_antennaPortsFieldPresenceDCI_1_2_r16_constr_67 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_aperiodicZP_CSI_RS_ResourceSetsToAddModListDCI_1_2_r16_constr_69 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_aperiodicZP_CSI_RS_ResourceSetsToReleaseListDCI_1_2_r16_constr_71 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dmrs_SequenceInitializationDCI_1_2_r16_constr_75 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_mcs_TableDCI_1_2_r16_constr_78 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_bundleSize_r16_constr_85 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_bundleSizeSet1_r16_constr_89 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_bundleSizeSet2_r16_constr_94 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_prb_BundlingTypeDCI_1_2_r16_constr_83 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_priorityIndicatorDCI_1_2_r16_constr_97 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_resourceAllocationType1GranularityDCI_1_2_r16_constr_101 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_vrb_ToPRB_InterleaverDCI_1_2_r16_constr_106 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_referenceOfSLIVDCI_1_2_r16_constr_109 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_resourceAllocationDCI_1_2_r16_constr_111 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_priorityIndicatorDCI_1_1_r16_constr_115 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_aperiodicZP_CSI_RS_ResourceSetsToAddModListDCI_1_2_r16_constr_69 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_aperiodicZP_CSI_RS_ResourceSetsToReleaseListDCI_1_2_r16_constr_71 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_harq_ProcessNumberSizeDCI_1_2_r16_constr_77 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  4 }	/* (0..4) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_numberOfBitsForRV_DCI_1_2_r16_constr_81 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_dataScramblingIdentityPDSCH2_r16_constr_117 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 10,  10,  0,  1023 }	/* (0..1023) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pdsch_HARQ_ACK_OneShotFeedbackDCI_1_2_r17_constr_123 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pdsch_HARQ_ACK_EnhType3DCI_1_2_r17_constr_125 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pdsch_HARQ_ACK_EnhType3DCI_Field_1_2_r17_constr_127 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pdsch_HARQ_ACK_RetxDCI_1_2_r17_constr_129 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pucch_sSCellDynDCI_1_2_r17_constr_131 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dl_OrJointTCI_StateToAddModList_r17_constr_135 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 7,  7,  1,  128 }	/* (SIZE(1..128)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dl_OrJointTCI_StateToReleaseList_r17_constr_137 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 7,  7,  1,  128 }	/* (SIZE(1..128)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_dl_OrJointTCI_StateToAddModList_r17_constr_135 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 7,  7,  1,  128 }	/* (SIZE(1..128)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_dl_OrJointTCI_StateToReleaseList_r17_constr_137 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 7,  7,  1,  128 }	/* (SIZE(1..128)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dl_OrJointTCI_StateList_r17_constr_133 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_beamAppTime_r17_constr_140 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  15 }	/* (0..15) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dmrs_FD_OCC_DisabledForRank1_PDSCH_r17_constr_158 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_mcs_Table_r17_constr_163 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_mcs_TableDCI_1_2_r17_constr_165 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_xOverheadMulticast_r17_constr_167 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_priorityIndicatorDCI_4_2_r17_constr_171 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_harq_ProcessNumberSizeDCI_1_2_v1700_constr_161 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  5 }	/* (0..5) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_harq_ProcessNumberSizeDCI_1_1_r17_constr_162 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  5,  5 }	/* (5..5) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_sizeDCI_4_2_r17_constr_173 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 7,  7,  20,  140 }	/* (20..140) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_dataScramblingIdentityPDSCH_constr_2 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 10,  10,  0,  1023 }	/* (0..1023) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_tci_StatesToAddModList_constr_5 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 7,  7,  1,  128 }	/* (SIZE(1..128)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_tci_StatesToReleaseList_constr_7 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 7,  7,  1,  128 }	/* (SIZE(1..128)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_rateMatchPatternToAddModList_constr_21 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 2,  2,  1,  4 }	/* (SIZE(1..4)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_rateMatchPatternToReleaseList_constr_23 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 2,  2,  1,  4 }	/* (SIZE(1..4)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_zp_CSI_RS_ResourceToAddModList_constr_50 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 5,  5,  1,  32 }	/* (SIZE(1..32)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_zp_CSI_RS_ResourceToReleaseList_constr_52 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 5,  5,  1,  32 }	/* (SIZE(1..32)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_aperiodic_ZP_CSI_RS_ResourceSetsToAddModList_constr_54 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_aperiodic_ZP_CSI_RS_ResourceSetsToReleaseList_constr_56 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_sp_ZP_CSI_RS_ResourceSetsToAddModList_constr_58 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_sp_ZP_CSI_RS_ResourceSetsToReleaseList_constr_60 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (SIZE(1..16)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static asn_TYPE_member_t asn_MBR_NR_tci_StatesToAddModList_5[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_TCI_State,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_tci_StatesToAddModList_tags_5[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_tci_StatesToAddModList_specs_5 = {
	sizeof(struct NR_PDSCH_Config__tci_StatesToAddModList),
	offsetof(struct NR_PDSCH_Config__tci_StatesToAddModList, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_tci_StatesToAddModList_5 = {
	"tci-StatesToAddModList",
	"tci-StatesToAddModList",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_tci_StatesToAddModList_tags_5,
	sizeof(asn_DEF_NR_tci_StatesToAddModList_tags_5)
		/sizeof(asn_DEF_NR_tci_StatesToAddModList_tags_5[0]) - 1, /* 1 */
	asn_DEF_NR_tci_StatesToAddModList_tags_5,	/* Same as above */
	sizeof(asn_DEF_NR_tci_StatesToAddModList_tags_5)
		/sizeof(asn_DEF_NR_tci_StatesToAddModList_tags_5[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_tci_StatesToAddModList_constr_5,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_tci_StatesToAddModList_5,
	1,	/* Single element */
	&asn_SPC_NR_tci_StatesToAddModList_specs_5	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_tci_StatesToReleaseList_7[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (2 << 2)),
		0,
		&asn_DEF_NR_TCI_StateId,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_tci_StatesToReleaseList_tags_7[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_tci_StatesToReleaseList_specs_7 = {
	sizeof(struct NR_PDSCH_Config__tci_StatesToReleaseList),
	offsetof(struct NR_PDSCH_Config__tci_StatesToReleaseList, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_tci_StatesToReleaseList_7 = {
	"tci-StatesToReleaseList",
	"tci-StatesToReleaseList",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_tci_StatesToReleaseList_tags_7,
	sizeof(asn_DEF_NR_tci_StatesToReleaseList_tags_7)
		/sizeof(asn_DEF_NR_tci_StatesToReleaseList_tags_7[0]) - 1, /* 1 */
	asn_DEF_NR_tci_StatesToReleaseList_tags_7,	/* Same as above */
	sizeof(asn_DEF_NR_tci_StatesToReleaseList_tags_7)
		/sizeof(asn_DEF_NR_tci_StatesToReleaseList_tags_7[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_tci_StatesToReleaseList_constr_7,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_tci_StatesToReleaseList_7,
	1,	/* Single element */
	&asn_SPC_NR_tci_StatesToReleaseList_specs_7	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_vrb_ToPRB_Interleaver_value2enum_9[] = {
	{ 0,	2,	"n2" },
	{ 1,	2,	"n4" }
};
static const unsigned int asn_MAP_NR_vrb_ToPRB_Interleaver_enum2value_9[] = {
	0,	/* n2(0) */
	1	/* n4(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_vrb_ToPRB_Interleaver_specs_9 = {
	asn_MAP_NR_vrb_ToPRB_Interleaver_value2enum_9,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_vrb_ToPRB_Interleaver_enum2value_9,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_vrb_ToPRB_Interleaver_tags_9[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_vrb_ToPRB_Interleaver_9 = {
	"vrb-ToPRB-Interleaver",
	"vrb-ToPRB-Interleaver",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_vrb_ToPRB_Interleaver_tags_9,
	sizeof(asn_DEF_NR_vrb_ToPRB_Interleaver_tags_9)
		/sizeof(asn_DEF_NR_vrb_ToPRB_Interleaver_tags_9[0]) - 1, /* 1 */
	asn_DEF_NR_vrb_ToPRB_Interleaver_tags_9,	/* Same as above */
	sizeof(asn_DEF_NR_vrb_ToPRB_Interleaver_tags_9)
		/sizeof(asn_DEF_NR_vrb_ToPRB_Interleaver_tags_9[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_vrb_ToPRB_Interleaver_constr_9,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_vrb_ToPRB_Interleaver_specs_9	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_resourceAllocation_value2enum_12[] = {
	{ 0,	23,	"resourceAllocationType0" },
	{ 1,	23,	"resourceAllocationType1" },
	{ 2,	13,	"dynamicSwitch" }
};
static const unsigned int asn_MAP_NR_resourceAllocation_enum2value_12[] = {
	2,	/* dynamicSwitch(2) */
	0,	/* resourceAllocationType0(0) */
	1	/* resourceAllocationType1(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_resourceAllocation_specs_12 = {
	asn_MAP_NR_resourceAllocation_value2enum_12,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_resourceAllocation_enum2value_12,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_resourceAllocation_tags_12[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_resourceAllocation_12 = {
	"resourceAllocation",
	"resourceAllocation",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_resourceAllocation_tags_12,
	sizeof(asn_DEF_NR_resourceAllocation_tags_12)
		/sizeof(asn_DEF_NR_resourceAllocation_tags_12[0]) - 1, /* 1 */
	asn_DEF_NR_resourceAllocation_tags_12,	/* Same as above */
	sizeof(asn_DEF_NR_resourceAllocation_tags_12)
		/sizeof(asn_DEF_NR_resourceAllocation_tags_12[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_resourceAllocation_constr_12,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_resourceAllocation_specs_12	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pdsch_AggregationFactor_value2enum_17[] = {
	{ 0,	2,	"n2" },
	{ 1,	2,	"n4" },
	{ 2,	2,	"n8" }
};
static const unsigned int asn_MAP_NR_pdsch_AggregationFactor_enum2value_17[] = {
	0,	/* n2(0) */
	1,	/* n4(1) */
	2	/* n8(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pdsch_AggregationFactor_specs_17 = {
	asn_MAP_NR_pdsch_AggregationFactor_value2enum_17,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pdsch_AggregationFactor_enum2value_17,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pdsch_AggregationFactor_tags_17[] = {
	(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pdsch_AggregationFactor_17 = {
	"pdsch-AggregationFactor",
	"pdsch-AggregationFactor",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pdsch_AggregationFactor_tags_17,
	sizeof(asn_DEF_NR_pdsch_AggregationFactor_tags_17)
		/sizeof(asn_DEF_NR_pdsch_AggregationFactor_tags_17[0]) - 1, /* 1 */
	asn_DEF_NR_pdsch_AggregationFactor_tags_17,	/* Same as above */
	sizeof(asn_DEF_NR_pdsch_AggregationFactor_tags_17)
		/sizeof(asn_DEF_NR_pdsch_AggregationFactor_tags_17[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pdsch_AggregationFactor_constr_17,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pdsch_AggregationFactor_specs_17	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_rateMatchPatternToAddModList_21[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_RateMatchPattern,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_rateMatchPatternToAddModList_tags_21[] = {
	(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_rateMatchPatternToAddModList_specs_21 = {
	sizeof(struct NR_PDSCH_Config__rateMatchPatternToAddModList),
	offsetof(struct NR_PDSCH_Config__rateMatchPatternToAddModList, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_rateMatchPatternToAddModList_21 = {
	"rateMatchPatternToAddModList",
	"rateMatchPatternToAddModList",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_rateMatchPatternToAddModList_tags_21,
	sizeof(asn_DEF_NR_rateMatchPatternToAddModList_tags_21)
		/sizeof(asn_DEF_NR_rateMatchPatternToAddModList_tags_21[0]) - 1, /* 1 */
	asn_DEF_NR_rateMatchPatternToAddModList_tags_21,	/* Same as above */
	sizeof(asn_DEF_NR_rateMatchPatternToAddModList_tags_21)
		/sizeof(asn_DEF_NR_rateMatchPatternToAddModList_tags_21[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_rateMatchPatternToAddModList_constr_21,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_rateMatchPatternToAddModList_21,
	1,	/* Single element */
	&asn_SPC_NR_rateMatchPatternToAddModList_specs_21	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_rateMatchPatternToReleaseList_23[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (2 << 2)),
		0,
		&asn_DEF_NR_RateMatchPatternId,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_rateMatchPatternToReleaseList_tags_23[] = {
	(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_rateMatchPatternToReleaseList_specs_23 = {
	sizeof(struct NR_PDSCH_Config__rateMatchPatternToReleaseList),
	offsetof(struct NR_PDSCH_Config__rateMatchPatternToReleaseList, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_rateMatchPatternToReleaseList_23 = {
	"rateMatchPatternToReleaseList",
	"rateMatchPatternToReleaseList",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_rateMatchPatternToReleaseList_tags_23,
	sizeof(asn_DEF_NR_rateMatchPatternToReleaseList_tags_23)
		/sizeof(asn_DEF_NR_rateMatchPatternToReleaseList_tags_23[0]) - 1, /* 1 */
	asn_DEF_NR_rateMatchPatternToReleaseList_tags_23,	/* Same as above */
	sizeof(asn_DEF_NR_rateMatchPatternToReleaseList_tags_23)
		/sizeof(asn_DEF_NR_rateMatchPatternToReleaseList_tags_23[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_rateMatchPatternToReleaseList_constr_23,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_rateMatchPatternToReleaseList_23,
	1,	/* Single element */
	&asn_SPC_NR_rateMatchPatternToReleaseList_specs_23	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_rbg_Size_value2enum_27[] = {
	{ 0,	7,	"config1" },
	{ 1,	7,	"config2" }
};
static const unsigned int asn_MAP_NR_rbg_Size_enum2value_27[] = {
	0,	/* config1(0) */
	1	/* config2(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_rbg_Size_specs_27 = {
	asn_MAP_NR_rbg_Size_value2enum_27,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_rbg_Size_enum2value_27,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_rbg_Size_tags_27[] = {
	(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_rbg_Size_27 = {
	"rbg-Size",
	"rbg-Size",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_rbg_Size_tags_27,
	sizeof(asn_DEF_NR_rbg_Size_tags_27)
		/sizeof(asn_DEF_NR_rbg_Size_tags_27[0]) - 1, /* 1 */
	asn_DEF_NR_rbg_Size_tags_27,	/* Same as above */
	sizeof(asn_DEF_NR_rbg_Size_tags_27)
		/sizeof(asn_DEF_NR_rbg_Size_tags_27[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_rbg_Size_constr_27,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_rbg_Size_specs_27	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_mcs_Table_value2enum_30[] = {
	{ 0,	6,	"qam256" },
	{ 1,	10,	"qam64LowSE" }
};
static const unsigned int asn_MAP_NR_mcs_Table_enum2value_30[] = {
	0,	/* qam256(0) */
	1	/* qam64LowSE(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_mcs_Table_specs_30 = {
	asn_MAP_NR_mcs_Table_value2enum_30,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_mcs_Table_enum2value_30,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_mcs_Table_tags_30[] = {
	(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mcs_Table_30 = {
	"mcs-Table",
	"mcs-Table",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_mcs_Table_tags_30,
	sizeof(asn_DEF_NR_mcs_Table_tags_30)
		/sizeof(asn_DEF_NR_mcs_Table_tags_30[0]) - 1, /* 1 */
	asn_DEF_NR_mcs_Table_tags_30,	/* Same as above */
	sizeof(asn_DEF_NR_mcs_Table_tags_30)
		/sizeof(asn_DEF_NR_mcs_Table_tags_30[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_mcs_Table_constr_30,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_mcs_Table_specs_30	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_maxNrofCodeWordsScheduledByDCI_value2enum_33[] = {
	{ 0,	2,	"n1" },
	{ 1,	2,	"n2" }
};
static const unsigned int asn_MAP_NR_maxNrofCodeWordsScheduledByDCI_enum2value_33[] = {
	0,	/* n1(0) */
	1	/* n2(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_maxNrofCodeWordsScheduledByDCI_specs_33 = {
	asn_MAP_NR_maxNrofCodeWordsScheduledByDCI_value2enum_33,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_maxNrofCodeWordsScheduledByDCI_enum2value_33,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_maxNrofCodeWordsScheduledByDCI_tags_33[] = {
	(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_maxNrofCodeWordsScheduledByDCI_33 = {
	"maxNrofCodeWordsScheduledByDCI",
	"maxNrofCodeWordsScheduledByDCI",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_maxNrofCodeWordsScheduledByDCI_tags_33,
	sizeof(asn_DEF_NR_maxNrofCodeWordsScheduledByDCI_tags_33)
		/sizeof(asn_DEF_NR_maxNrofCodeWordsScheduledByDCI_tags_33[0]) - 1, /* 1 */
	asn_DEF_NR_maxNrofCodeWordsScheduledByDCI_tags_33,	/* Same as above */
	sizeof(asn_DEF_NR_maxNrofCodeWordsScheduledByDCI_tags_33)
		/sizeof(asn_DEF_NR_maxNrofCodeWordsScheduledByDCI_tags_33[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_maxNrofCodeWordsScheduledByDCI_constr_33,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_maxNrofCodeWordsScheduledByDCI_specs_33	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_bundleSize_value2enum_38[] = {
	{ 0,	2,	"n4" },
	{ 1,	8,	"wideband" }
};
static const unsigned int asn_MAP_NR_bundleSize_enum2value_38[] = {
	0,	/* n4(0) */
	1	/* wideband(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_bundleSize_specs_38 = {
	asn_MAP_NR_bundleSize_value2enum_38,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_bundleSize_enum2value_38,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_bundleSize_tags_38[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_bundleSize_38 = {
	"bundleSize",
	"bundleSize",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_bundleSize_tags_38,
	sizeof(asn_DEF_NR_bundleSize_tags_38)
		/sizeof(asn_DEF_NR_bundleSize_tags_38[0]) - 1, /* 1 */
	asn_DEF_NR_bundleSize_tags_38,	/* Same as above */
	sizeof(asn_DEF_NR_bundleSize_tags_38)
		/sizeof(asn_DEF_NR_bundleSize_tags_38[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_bundleSize_constr_38,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_bundleSize_specs_38	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_staticBundling_37[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_PDSCH_Config__prb_BundlingType__staticBundling, bundleSize),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_bundleSize_38,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"bundleSize"
		},
};
static const int asn_MAP_NR_staticBundling_oms_37[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_staticBundling_tags_37[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_staticBundling_tag2el_37[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* bundleSize */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_staticBundling_specs_37 = {
	sizeof(struct NR_PDSCH_Config__prb_BundlingType__staticBundling),
	offsetof(struct NR_PDSCH_Config__prb_BundlingType__staticBundling, _asn_ctx),
	asn_MAP_NR_staticBundling_tag2el_37,
	1,	/* Count of tags in the map */
	asn_MAP_NR_staticBundling_oms_37,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_staticBundling_37 = {
	"staticBundling",
	"staticBundling",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_staticBundling_tags_37,
	sizeof(asn_DEF_NR_staticBundling_tags_37)
		/sizeof(asn_DEF_NR_staticBundling_tags_37[0]) - 1, /* 1 */
	asn_DEF_NR_staticBundling_tags_37,	/* Same as above */
	sizeof(asn_DEF_NR_staticBundling_tags_37)
		/sizeof(asn_DEF_NR_staticBundling_tags_37[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_staticBundling_37,
	1,	/* Elements count */
	&asn_SPC_NR_staticBundling_specs_37	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_bundleSizeSet1_value2enum_42[] = {
	{ 0,	2,	"n4" },
	{ 1,	8,	"wideband" },
	{ 2,	11,	"n2-wideband" },
	{ 3,	11,	"n4-wideband" }
};
static const unsigned int asn_MAP_NR_bundleSizeSet1_enum2value_42[] = {
	2,	/* n2-wideband(2) */
	0,	/* n4(0) */
	3,	/* n4-wideband(3) */
	1	/* wideband(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_bundleSizeSet1_specs_42 = {
	asn_MAP_NR_bundleSizeSet1_value2enum_42,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_bundleSizeSet1_enum2value_42,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_bundleSizeSet1_tags_42[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_bundleSizeSet1_42 = {
	"bundleSizeSet1",
	"bundleSizeSet1",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_bundleSizeSet1_tags_42,
	sizeof(asn_DEF_NR_bundleSizeSet1_tags_42)
		/sizeof(asn_DEF_NR_bundleSizeSet1_tags_42[0]) - 1, /* 1 */
	asn_DEF_NR_bundleSizeSet1_tags_42,	/* Same as above */
	sizeof(asn_DEF_NR_bundleSizeSet1_tags_42)
		/sizeof(asn_DEF_NR_bundleSizeSet1_tags_42[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_bundleSizeSet1_constr_42,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_bundleSizeSet1_specs_42	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_bundleSizeSet2_value2enum_47[] = {
	{ 0,	2,	"n4" },
	{ 1,	8,	"wideband" }
};
static const unsigned int asn_MAP_NR_bundleSizeSet2_enum2value_47[] = {
	0,	/* n4(0) */
	1	/* wideband(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_bundleSizeSet2_specs_47 = {
	asn_MAP_NR_bundleSizeSet2_value2enum_47,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_bundleSizeSet2_enum2value_47,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_bundleSizeSet2_tags_47[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_bundleSizeSet2_47 = {
	"bundleSizeSet2",
	"bundleSizeSet2",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_bundleSizeSet2_tags_47,
	sizeof(asn_DEF_NR_bundleSizeSet2_tags_47)
		/sizeof(asn_DEF_NR_bundleSizeSet2_tags_47[0]) - 1, /* 1 */
	asn_DEF_NR_bundleSizeSet2_tags_47,	/* Same as above */
	sizeof(asn_DEF_NR_bundleSizeSet2_tags_47)
		/sizeof(asn_DEF_NR_bundleSizeSet2_tags_47[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_bundleSizeSet2_constr_47,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_bundleSizeSet2_specs_47	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_dynamicBundling_41[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_PDSCH_Config__prb_BundlingType__dynamicBundling, bundleSizeSet1),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_bundleSizeSet1_42,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"bundleSizeSet1"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PDSCH_Config__prb_BundlingType__dynamicBundling, bundleSizeSet2),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_bundleSizeSet2_47,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"bundleSizeSet2"
		},
};
static const int asn_MAP_NR_dynamicBundling_oms_41[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_dynamicBundling_tags_41[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_dynamicBundling_tag2el_41[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* bundleSizeSet1 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* bundleSizeSet2 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_dynamicBundling_specs_41 = {
	sizeof(struct NR_PDSCH_Config__prb_BundlingType__dynamicBundling),
	offsetof(struct NR_PDSCH_Config__prb_BundlingType__dynamicBundling, _asn_ctx),
	asn_MAP_NR_dynamicBundling_tag2el_41,
	2,	/* Count of tags in the map */
	asn_MAP_NR_dynamicBundling_oms_41,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dynamicBundling_41 = {
	"dynamicBundling",
	"dynamicBundling",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_dynamicBundling_tags_41,
	sizeof(asn_DEF_NR_dynamicBundling_tags_41)
		/sizeof(asn_DEF_NR_dynamicBundling_tags_41[0]) - 1, /* 1 */
	asn_DEF_NR_dynamicBundling_tags_41,	/* Same as above */
	sizeof(asn_DEF_NR_dynamicBundling_tags_41)
		/sizeof(asn_DEF_NR_dynamicBundling_tags_41[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_dynamicBundling_41,
	2,	/* Elements count */
	&asn_SPC_NR_dynamicBundling_specs_41	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_prb_BundlingType_36[] = {
	{ ATF_POINTER, 0, offsetof(struct NR_PDSCH_Config__prb_BundlingType, choice.staticBundling),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_staticBundling_37,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"staticBundling"
		},
	{ ATF_POINTER, 0, offsetof(struct NR_PDSCH_Config__prb_BundlingType, choice.dynamicBundling),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_dynamicBundling_41,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dynamicBundling"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_NR_prb_BundlingType_tag2el_36[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* staticBundling */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* dynamicBundling */
};
static asn_CHOICE_specifics_t asn_SPC_NR_prb_BundlingType_specs_36 = {
	sizeof(struct NR_PDSCH_Config__prb_BundlingType),
	offsetof(struct NR_PDSCH_Config__prb_BundlingType, _asn_ctx),
	offsetof(struct NR_PDSCH_Config__prb_BundlingType, present),
	sizeof(((struct NR_PDSCH_Config__prb_BundlingType *)0)->present),
	asn_MAP_NR_prb_BundlingType_tag2el_36,
	2,	/* Count of tags in the map */
	0, 0,
	-1	/* Extensions start */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_prb_BundlingType_36 = {
	"prb-BundlingType",
	"prb-BundlingType",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_prb_BundlingType_constr_36,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		CHOICE_constraint
	},
	asn_MBR_NR_prb_BundlingType_36,
	2,	/* Elements count */
	&asn_SPC_NR_prb_BundlingType_specs_36	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_zp_CSI_RS_ResourceToAddModList_50[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_ZP_CSI_RS_Resource,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_zp_CSI_RS_ResourceToAddModList_tags_50[] = {
	(ASN_TAG_CLASS_CONTEXT | (17 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_zp_CSI_RS_ResourceToAddModList_specs_50 = {
	sizeof(struct NR_PDSCH_Config__zp_CSI_RS_ResourceToAddModList),
	offsetof(struct NR_PDSCH_Config__zp_CSI_RS_ResourceToAddModList, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_zp_CSI_RS_ResourceToAddModList_50 = {
	"zp-CSI-RS-ResourceToAddModList",
	"zp-CSI-RS-ResourceToAddModList",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_zp_CSI_RS_ResourceToAddModList_tags_50,
	sizeof(asn_DEF_NR_zp_CSI_RS_ResourceToAddModList_tags_50)
		/sizeof(asn_DEF_NR_zp_CSI_RS_ResourceToAddModList_tags_50[0]) - 1, /* 1 */
	asn_DEF_NR_zp_CSI_RS_ResourceToAddModList_tags_50,	/* Same as above */
	sizeof(asn_DEF_NR_zp_CSI_RS_ResourceToAddModList_tags_50)
		/sizeof(asn_DEF_NR_zp_CSI_RS_ResourceToAddModList_tags_50[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_zp_CSI_RS_ResourceToAddModList_constr_50,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_zp_CSI_RS_ResourceToAddModList_50,
	1,	/* Single element */
	&asn_SPC_NR_zp_CSI_RS_ResourceToAddModList_specs_50	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_zp_CSI_RS_ResourceToReleaseList_52[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (2 << 2)),
		0,
		&asn_DEF_NR_ZP_CSI_RS_ResourceId,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_zp_CSI_RS_ResourceToReleaseList_tags_52[] = {
	(ASN_TAG_CLASS_CONTEXT | (18 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_zp_CSI_RS_ResourceToReleaseList_specs_52 = {
	sizeof(struct NR_PDSCH_Config__zp_CSI_RS_ResourceToReleaseList),
	offsetof(struct NR_PDSCH_Config__zp_CSI_RS_ResourceToReleaseList, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_zp_CSI_RS_ResourceToReleaseList_52 = {
	"zp-CSI-RS-ResourceToReleaseList",
	"zp-CSI-RS-ResourceToReleaseList",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_zp_CSI_RS_ResourceToReleaseList_tags_52,
	sizeof(asn_DEF_NR_zp_CSI_RS_ResourceToReleaseList_tags_52)
		/sizeof(asn_DEF_NR_zp_CSI_RS_ResourceToReleaseList_tags_52[0]) - 1, /* 1 */
	asn_DEF_NR_zp_CSI_RS_ResourceToReleaseList_tags_52,	/* Same as above */
	sizeof(asn_DEF_NR_zp_CSI_RS_ResourceToReleaseList_tags_52)
		/sizeof(asn_DEF_NR_zp_CSI_RS_ResourceToReleaseList_tags_52[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_zp_CSI_RS_ResourceToReleaseList_constr_52,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_zp_CSI_RS_ResourceToReleaseList_52,
	1,	/* Single element */
	&asn_SPC_NR_zp_CSI_RS_ResourceToReleaseList_specs_52	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_aperiodic_ZP_CSI_RS_ResourceSetsToAddModList_54[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_ZP_CSI_RS_ResourceSet,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_aperiodic_ZP_CSI_RS_ResourceSetsToAddModList_tags_54[] = {
	(ASN_TAG_CLASS_CONTEXT | (19 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_aperiodic_ZP_CSI_RS_ResourceSetsToAddModList_specs_54 = {
	sizeof(struct NR_PDSCH_Config__aperiodic_ZP_CSI_RS_ResourceSetsToAddModList),
	offsetof(struct NR_PDSCH_Config__aperiodic_ZP_CSI_RS_ResourceSetsToAddModList, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_aperiodic_ZP_CSI_RS_ResourceSetsToAddModList_54 = {
	"aperiodic-ZP-CSI-RS-ResourceSetsToAddModList",
	"aperiodic-ZP-CSI-RS-ResourceSetsToAddModList",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_aperiodic_ZP_CSI_RS_ResourceSetsToAddModList_tags_54,
	sizeof(asn_DEF_NR_aperiodic_ZP_CSI_RS_ResourceSetsToAddModList_tags_54)
		/sizeof(asn_DEF_NR_aperiodic_ZP_CSI_RS_ResourceSetsToAddModList_tags_54[0]) - 1, /* 1 */
	asn_DEF_NR_aperiodic_ZP_CSI_RS_ResourceSetsToAddModList_tags_54,	/* Same as above */
	sizeof(asn_DEF_NR_aperiodic_ZP_CSI_RS_ResourceSetsToAddModList_tags_54)
		/sizeof(asn_DEF_NR_aperiodic_ZP_CSI_RS_ResourceSetsToAddModList_tags_54[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_aperiodic_ZP_CSI_RS_ResourceSetsToAddModList_constr_54,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_aperiodic_ZP_CSI_RS_ResourceSetsToAddModList_54,
	1,	/* Single element */
	&asn_SPC_NR_aperiodic_ZP_CSI_RS_ResourceSetsToAddModList_specs_54	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_aperiodic_ZP_CSI_RS_ResourceSetsToReleaseList_56[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (2 << 2)),
		0,
		&asn_DEF_NR_ZP_CSI_RS_ResourceSetId,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_aperiodic_ZP_CSI_RS_ResourceSetsToReleaseList_tags_56[] = {
	(ASN_TAG_CLASS_CONTEXT | (20 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_aperiodic_ZP_CSI_RS_ResourceSetsToReleaseList_specs_56 = {
	sizeof(struct NR_PDSCH_Config__aperiodic_ZP_CSI_RS_ResourceSetsToReleaseList),
	offsetof(struct NR_PDSCH_Config__aperiodic_ZP_CSI_RS_ResourceSetsToReleaseList, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_aperiodic_ZP_CSI_RS_ResourceSetsToReleaseList_56 = {
	"aperiodic-ZP-CSI-RS-ResourceSetsToReleaseList",
	"aperiodic-ZP-CSI-RS-ResourceSetsToReleaseList",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_aperiodic_ZP_CSI_RS_ResourceSetsToReleaseList_tags_56,
	sizeof(asn_DEF_NR_aperiodic_ZP_CSI_RS_ResourceSetsToReleaseList_tags_56)
		/sizeof(asn_DEF_NR_aperiodic_ZP_CSI_RS_ResourceSetsToReleaseList_tags_56[0]) - 1, /* 1 */
	asn_DEF_NR_aperiodic_ZP_CSI_RS_ResourceSetsToReleaseList_tags_56,	/* Same as above */
	sizeof(asn_DEF_NR_aperiodic_ZP_CSI_RS_ResourceSetsToReleaseList_tags_56)
		/sizeof(asn_DEF_NR_aperiodic_ZP_CSI_RS_ResourceSetsToReleaseList_tags_56[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_aperiodic_ZP_CSI_RS_ResourceSetsToReleaseList_constr_56,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_aperiodic_ZP_CSI_RS_ResourceSetsToReleaseList_56,
	1,	/* Single element */
	&asn_SPC_NR_aperiodic_ZP_CSI_RS_ResourceSetsToReleaseList_specs_56	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_sp_ZP_CSI_RS_ResourceSetsToAddModList_58[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_ZP_CSI_RS_ResourceSet,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_sp_ZP_CSI_RS_ResourceSetsToAddModList_tags_58[] = {
	(ASN_TAG_CLASS_CONTEXT | (21 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_sp_ZP_CSI_RS_ResourceSetsToAddModList_specs_58 = {
	sizeof(struct NR_PDSCH_Config__sp_ZP_CSI_RS_ResourceSetsToAddModList),
	offsetof(struct NR_PDSCH_Config__sp_ZP_CSI_RS_ResourceSetsToAddModList, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_sp_ZP_CSI_RS_ResourceSetsToAddModList_58 = {
	"sp-ZP-CSI-RS-ResourceSetsToAddModList",
	"sp-ZP-CSI-RS-ResourceSetsToAddModList",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_sp_ZP_CSI_RS_ResourceSetsToAddModList_tags_58,
	sizeof(asn_DEF_NR_sp_ZP_CSI_RS_ResourceSetsToAddModList_tags_58)
		/sizeof(asn_DEF_NR_sp_ZP_CSI_RS_ResourceSetsToAddModList_tags_58[0]) - 1, /* 1 */
	asn_DEF_NR_sp_ZP_CSI_RS_ResourceSetsToAddModList_tags_58,	/* Same as above */
	sizeof(asn_DEF_NR_sp_ZP_CSI_RS_ResourceSetsToAddModList_tags_58)
		/sizeof(asn_DEF_NR_sp_ZP_CSI_RS_ResourceSetsToAddModList_tags_58[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_sp_ZP_CSI_RS_ResourceSetsToAddModList_constr_58,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_sp_ZP_CSI_RS_ResourceSetsToAddModList_58,
	1,	/* Single element */
	&asn_SPC_NR_sp_ZP_CSI_RS_ResourceSetsToAddModList_specs_58	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_sp_ZP_CSI_RS_ResourceSetsToReleaseList_60[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (2 << 2)),
		0,
		&asn_DEF_NR_ZP_CSI_RS_ResourceSetId,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_sp_ZP_CSI_RS_ResourceSetsToReleaseList_tags_60[] = {
	(ASN_TAG_CLASS_CONTEXT | (22 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_sp_ZP_CSI_RS_ResourceSetsToReleaseList_specs_60 = {
	sizeof(struct NR_PDSCH_Config__sp_ZP_CSI_RS_ResourceSetsToReleaseList),
	offsetof(struct NR_PDSCH_Config__sp_ZP_CSI_RS_ResourceSetsToReleaseList, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_sp_ZP_CSI_RS_ResourceSetsToReleaseList_60 = {
	"sp-ZP-CSI-RS-ResourceSetsToReleaseList",
	"sp-ZP-CSI-RS-ResourceSetsToReleaseList",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_sp_ZP_CSI_RS_ResourceSetsToReleaseList_tags_60,
	sizeof(asn_DEF_NR_sp_ZP_CSI_RS_ResourceSetsToReleaseList_tags_60)
		/sizeof(asn_DEF_NR_sp_ZP_CSI_RS_ResourceSetsToReleaseList_tags_60[0]) - 1, /* 1 */
	asn_DEF_NR_sp_ZP_CSI_RS_ResourceSetsToReleaseList_tags_60,	/* Same as above */
	sizeof(asn_DEF_NR_sp_ZP_CSI_RS_ResourceSetsToReleaseList_tags_60)
		/sizeof(asn_DEF_NR_sp_ZP_CSI_RS_ResourceSetsToReleaseList_tags_60[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_sp_ZP_CSI_RS_ResourceSetsToReleaseList_constr_60,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_sp_ZP_CSI_RS_ResourceSetsToReleaseList_60,
	1,	/* Single element */
	&asn_SPC_NR_sp_ZP_CSI_RS_ResourceSetsToReleaseList_specs_60	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_antennaPortsFieldPresenceDCI_1_2_r16_value2enum_67[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_antennaPortsFieldPresenceDCI_1_2_r16_enum2value_67[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_antennaPortsFieldPresenceDCI_1_2_r16_specs_67 = {
	asn_MAP_NR_antennaPortsFieldPresenceDCI_1_2_r16_value2enum_67,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_antennaPortsFieldPresenceDCI_1_2_r16_enum2value_67,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_antennaPortsFieldPresenceDCI_1_2_r16_tags_67[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_antennaPortsFieldPresenceDCI_1_2_r16_67 = {
	"antennaPortsFieldPresenceDCI-1-2-r16",
	"antennaPortsFieldPresenceDCI-1-2-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_antennaPortsFieldPresenceDCI_1_2_r16_tags_67,
	sizeof(asn_DEF_NR_antennaPortsFieldPresenceDCI_1_2_r16_tags_67)
		/sizeof(asn_DEF_NR_antennaPortsFieldPresenceDCI_1_2_r16_tags_67[0]) - 1, /* 1 */
	asn_DEF_NR_antennaPortsFieldPresenceDCI_1_2_r16_tags_67,	/* Same as above */
	sizeof(asn_DEF_NR_antennaPortsFieldPresenceDCI_1_2_r16_tags_67)
		/sizeof(asn_DEF_NR_antennaPortsFieldPresenceDCI_1_2_r16_tags_67[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_antennaPortsFieldPresenceDCI_1_2_r16_constr_67,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_antennaPortsFieldPresenceDCI_1_2_r16_specs_67	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_aperiodicZP_CSI_RS_ResourceSetsToAddModListDCI_1_2_r16_69[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_ZP_CSI_RS_ResourceSet,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_aperiodicZP_CSI_RS_ResourceSetsToAddModListDCI_1_2_r16_tags_69[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_aperiodicZP_CSI_RS_ResourceSetsToAddModListDCI_1_2_r16_specs_69 = {
	sizeof(struct NR_PDSCH_Config__ext1__aperiodicZP_CSI_RS_ResourceSetsToAddModListDCI_1_2_r16),
	offsetof(struct NR_PDSCH_Config__ext1__aperiodicZP_CSI_RS_ResourceSetsToAddModListDCI_1_2_r16, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_aperiodicZP_CSI_RS_ResourceSetsToAddModListDCI_1_2_r16_69 = {
	"aperiodicZP-CSI-RS-ResourceSetsToAddModListDCI-1-2-r16",
	"aperiodicZP-CSI-RS-ResourceSetsToAddModListDCI-1-2-r16",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_aperiodicZP_CSI_RS_ResourceSetsToAddModListDCI_1_2_r16_tags_69,
	sizeof(asn_DEF_NR_aperiodicZP_CSI_RS_ResourceSetsToAddModListDCI_1_2_r16_tags_69)
		/sizeof(asn_DEF_NR_aperiodicZP_CSI_RS_ResourceSetsToAddModListDCI_1_2_r16_tags_69[0]) - 1, /* 1 */
	asn_DEF_NR_aperiodicZP_CSI_RS_ResourceSetsToAddModListDCI_1_2_r16_tags_69,	/* Same as above */
	sizeof(asn_DEF_NR_aperiodicZP_CSI_RS_ResourceSetsToAddModListDCI_1_2_r16_tags_69)
		/sizeof(asn_DEF_NR_aperiodicZP_CSI_RS_ResourceSetsToAddModListDCI_1_2_r16_tags_69[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_aperiodicZP_CSI_RS_ResourceSetsToAddModListDCI_1_2_r16_constr_69,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_aperiodicZP_CSI_RS_ResourceSetsToAddModListDCI_1_2_r16_69,
	1,	/* Single element */
	&asn_SPC_NR_aperiodicZP_CSI_RS_ResourceSetsToAddModListDCI_1_2_r16_specs_69	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_aperiodicZP_CSI_RS_ResourceSetsToReleaseListDCI_1_2_r16_71[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (2 << 2)),
		0,
		&asn_DEF_NR_ZP_CSI_RS_ResourceSetId,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_aperiodicZP_CSI_RS_ResourceSetsToReleaseListDCI_1_2_r16_tags_71[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_aperiodicZP_CSI_RS_ResourceSetsToReleaseListDCI_1_2_r16_specs_71 = {
	sizeof(struct NR_PDSCH_Config__ext1__aperiodicZP_CSI_RS_ResourceSetsToReleaseListDCI_1_2_r16),
	offsetof(struct NR_PDSCH_Config__ext1__aperiodicZP_CSI_RS_ResourceSetsToReleaseListDCI_1_2_r16, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_aperiodicZP_CSI_RS_ResourceSetsToReleaseListDCI_1_2_r16_71 = {
	"aperiodicZP-CSI-RS-ResourceSetsToReleaseListDCI-1-2-r16",
	"aperiodicZP-CSI-RS-ResourceSetsToReleaseListDCI-1-2-r16",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_aperiodicZP_CSI_RS_ResourceSetsToReleaseListDCI_1_2_r16_tags_71,
	sizeof(asn_DEF_NR_aperiodicZP_CSI_RS_ResourceSetsToReleaseListDCI_1_2_r16_tags_71)
		/sizeof(asn_DEF_NR_aperiodicZP_CSI_RS_ResourceSetsToReleaseListDCI_1_2_r16_tags_71[0]) - 1, /* 1 */
	asn_DEF_NR_aperiodicZP_CSI_RS_ResourceSetsToReleaseListDCI_1_2_r16_tags_71,	/* Same as above */
	sizeof(asn_DEF_NR_aperiodicZP_CSI_RS_ResourceSetsToReleaseListDCI_1_2_r16_tags_71)
		/sizeof(asn_DEF_NR_aperiodicZP_CSI_RS_ResourceSetsToReleaseListDCI_1_2_r16_tags_71[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_aperiodicZP_CSI_RS_ResourceSetsToReleaseListDCI_1_2_r16_constr_71,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_aperiodicZP_CSI_RS_ResourceSetsToReleaseListDCI_1_2_r16_71,
	1,	/* Single element */
	&asn_SPC_NR_aperiodicZP_CSI_RS_ResourceSetsToReleaseListDCI_1_2_r16_specs_71	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dmrs_SequenceInitializationDCI_1_2_r16_value2enum_75[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_dmrs_SequenceInitializationDCI_1_2_r16_enum2value_75[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dmrs_SequenceInitializationDCI_1_2_r16_specs_75 = {
	asn_MAP_NR_dmrs_SequenceInitializationDCI_1_2_r16_value2enum_75,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dmrs_SequenceInitializationDCI_1_2_r16_enum2value_75,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dmrs_SequenceInitializationDCI_1_2_r16_tags_75[] = {
	(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dmrs_SequenceInitializationDCI_1_2_r16_75 = {
	"dmrs-SequenceInitializationDCI-1-2-r16",
	"dmrs-SequenceInitializationDCI-1-2-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dmrs_SequenceInitializationDCI_1_2_r16_tags_75,
	sizeof(asn_DEF_NR_dmrs_SequenceInitializationDCI_1_2_r16_tags_75)
		/sizeof(asn_DEF_NR_dmrs_SequenceInitializationDCI_1_2_r16_tags_75[0]) - 1, /* 1 */
	asn_DEF_NR_dmrs_SequenceInitializationDCI_1_2_r16_tags_75,	/* Same as above */
	sizeof(asn_DEF_NR_dmrs_SequenceInitializationDCI_1_2_r16_tags_75)
		/sizeof(asn_DEF_NR_dmrs_SequenceInitializationDCI_1_2_r16_tags_75[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dmrs_SequenceInitializationDCI_1_2_r16_constr_75,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dmrs_SequenceInitializationDCI_1_2_r16_specs_75	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_mcs_TableDCI_1_2_r16_value2enum_78[] = {
	{ 0,	6,	"qam256" },
	{ 1,	10,	"qam64LowSE" }
};
static const unsigned int asn_MAP_NR_mcs_TableDCI_1_2_r16_enum2value_78[] = {
	0,	/* qam256(0) */
	1	/* qam64LowSE(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_mcs_TableDCI_1_2_r16_specs_78 = {
	asn_MAP_NR_mcs_TableDCI_1_2_r16_value2enum_78,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_mcs_TableDCI_1_2_r16_enum2value_78,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_mcs_TableDCI_1_2_r16_tags_78[] = {
	(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mcs_TableDCI_1_2_r16_78 = {
	"mcs-TableDCI-1-2-r16",
	"mcs-TableDCI-1-2-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_mcs_TableDCI_1_2_r16_tags_78,
	sizeof(asn_DEF_NR_mcs_TableDCI_1_2_r16_tags_78)
		/sizeof(asn_DEF_NR_mcs_TableDCI_1_2_r16_tags_78[0]) - 1, /* 1 */
	asn_DEF_NR_mcs_TableDCI_1_2_r16_tags_78,	/* Same as above */
	sizeof(asn_DEF_NR_mcs_TableDCI_1_2_r16_tags_78)
		/sizeof(asn_DEF_NR_mcs_TableDCI_1_2_r16_tags_78[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_mcs_TableDCI_1_2_r16_constr_78,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_mcs_TableDCI_1_2_r16_specs_78	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_bundleSize_r16_value2enum_85[] = {
	{ 0,	2,	"n4" },
	{ 1,	8,	"wideband" }
};
static const unsigned int asn_MAP_NR_bundleSize_r16_enum2value_85[] = {
	0,	/* n4(0) */
	1	/* wideband(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_bundleSize_r16_specs_85 = {
	asn_MAP_NR_bundleSize_r16_value2enum_85,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_bundleSize_r16_enum2value_85,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_bundleSize_r16_tags_85[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_bundleSize_r16_85 = {
	"bundleSize-r16",
	"bundleSize-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_bundleSize_r16_tags_85,
	sizeof(asn_DEF_NR_bundleSize_r16_tags_85)
		/sizeof(asn_DEF_NR_bundleSize_r16_tags_85[0]) - 1, /* 1 */
	asn_DEF_NR_bundleSize_r16_tags_85,	/* Same as above */
	sizeof(asn_DEF_NR_bundleSize_r16_tags_85)
		/sizeof(asn_DEF_NR_bundleSize_r16_tags_85[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_bundleSize_r16_constr_85,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_bundleSize_r16_specs_85	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_staticBundling_r16_84[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16__staticBundling_r16, bundleSize_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_bundleSize_r16_85,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"bundleSize-r16"
		},
};
static const int asn_MAP_NR_staticBundling_r16_oms_84[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_staticBundling_r16_tags_84[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_staticBundling_r16_tag2el_84[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* bundleSize-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_staticBundling_r16_specs_84 = {
	sizeof(struct NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16__staticBundling_r16),
	offsetof(struct NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16__staticBundling_r16, _asn_ctx),
	asn_MAP_NR_staticBundling_r16_tag2el_84,
	1,	/* Count of tags in the map */
	asn_MAP_NR_staticBundling_r16_oms_84,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_staticBundling_r16_84 = {
	"staticBundling-r16",
	"staticBundling-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_staticBundling_r16_tags_84,
	sizeof(asn_DEF_NR_staticBundling_r16_tags_84)
		/sizeof(asn_DEF_NR_staticBundling_r16_tags_84[0]) - 1, /* 1 */
	asn_DEF_NR_staticBundling_r16_tags_84,	/* Same as above */
	sizeof(asn_DEF_NR_staticBundling_r16_tags_84)
		/sizeof(asn_DEF_NR_staticBundling_r16_tags_84[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_staticBundling_r16_84,
	1,	/* Elements count */
	&asn_SPC_NR_staticBundling_r16_specs_84	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_bundleSizeSet1_r16_value2enum_89[] = {
	{ 0,	2,	"n4" },
	{ 1,	8,	"wideband" },
	{ 2,	11,	"n2-wideband" },
	{ 3,	11,	"n4-wideband" }
};
static const unsigned int asn_MAP_NR_bundleSizeSet1_r16_enum2value_89[] = {
	2,	/* n2-wideband(2) */
	0,	/* n4(0) */
	3,	/* n4-wideband(3) */
	1	/* wideband(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_bundleSizeSet1_r16_specs_89 = {
	asn_MAP_NR_bundleSizeSet1_r16_value2enum_89,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_bundleSizeSet1_r16_enum2value_89,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_bundleSizeSet1_r16_tags_89[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_bundleSizeSet1_r16_89 = {
	"bundleSizeSet1-r16",
	"bundleSizeSet1-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_bundleSizeSet1_r16_tags_89,
	sizeof(asn_DEF_NR_bundleSizeSet1_r16_tags_89)
		/sizeof(asn_DEF_NR_bundleSizeSet1_r16_tags_89[0]) - 1, /* 1 */
	asn_DEF_NR_bundleSizeSet1_r16_tags_89,	/* Same as above */
	sizeof(asn_DEF_NR_bundleSizeSet1_r16_tags_89)
		/sizeof(asn_DEF_NR_bundleSizeSet1_r16_tags_89[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_bundleSizeSet1_r16_constr_89,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_bundleSizeSet1_r16_specs_89	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_bundleSizeSet2_r16_value2enum_94[] = {
	{ 0,	2,	"n4" },
	{ 1,	8,	"wideband" }
};
static const unsigned int asn_MAP_NR_bundleSizeSet2_r16_enum2value_94[] = {
	0,	/* n4(0) */
	1	/* wideband(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_bundleSizeSet2_r16_specs_94 = {
	asn_MAP_NR_bundleSizeSet2_r16_value2enum_94,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_bundleSizeSet2_r16_enum2value_94,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_bundleSizeSet2_r16_tags_94[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_bundleSizeSet2_r16_94 = {
	"bundleSizeSet2-r16",
	"bundleSizeSet2-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_bundleSizeSet2_r16_tags_94,
	sizeof(asn_DEF_NR_bundleSizeSet2_r16_tags_94)
		/sizeof(asn_DEF_NR_bundleSizeSet2_r16_tags_94[0]) - 1, /* 1 */
	asn_DEF_NR_bundleSizeSet2_r16_tags_94,	/* Same as above */
	sizeof(asn_DEF_NR_bundleSizeSet2_r16_tags_94)
		/sizeof(asn_DEF_NR_bundleSizeSet2_r16_tags_94[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_bundleSizeSet2_r16_constr_94,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_bundleSizeSet2_r16_specs_94	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_dynamicBundling_r16_88[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16__dynamicBundling_r16, bundleSizeSet1_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_bundleSizeSet1_r16_89,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"bundleSizeSet1-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16__dynamicBundling_r16, bundleSizeSet2_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_bundleSizeSet2_r16_94,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"bundleSizeSet2-r16"
		},
};
static const int asn_MAP_NR_dynamicBundling_r16_oms_88[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_dynamicBundling_r16_tags_88[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_dynamicBundling_r16_tag2el_88[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* bundleSizeSet1-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* bundleSizeSet2-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_dynamicBundling_r16_specs_88 = {
	sizeof(struct NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16__dynamicBundling_r16),
	offsetof(struct NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16__dynamicBundling_r16, _asn_ctx),
	asn_MAP_NR_dynamicBundling_r16_tag2el_88,
	2,	/* Count of tags in the map */
	asn_MAP_NR_dynamicBundling_r16_oms_88,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dynamicBundling_r16_88 = {
	"dynamicBundling-r16",
	"dynamicBundling-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_dynamicBundling_r16_tags_88,
	sizeof(asn_DEF_NR_dynamicBundling_r16_tags_88)
		/sizeof(asn_DEF_NR_dynamicBundling_r16_tags_88[0]) - 1, /* 1 */
	asn_DEF_NR_dynamicBundling_r16_tags_88,	/* Same as above */
	sizeof(asn_DEF_NR_dynamicBundling_r16_tags_88)
		/sizeof(asn_DEF_NR_dynamicBundling_r16_tags_88[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_dynamicBundling_r16_88,
	2,	/* Elements count */
	&asn_SPC_NR_dynamicBundling_r16_specs_88	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_prb_BundlingTypeDCI_1_2_r16_83[] = {
	{ ATF_POINTER, 0, offsetof(struct NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16, choice.staticBundling_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_staticBundling_r16_84,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"staticBundling-r16"
		},
	{ ATF_POINTER, 0, offsetof(struct NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16, choice.dynamicBundling_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_dynamicBundling_r16_88,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dynamicBundling-r16"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_NR_prb_BundlingTypeDCI_1_2_r16_tag2el_83[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* staticBundling-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* dynamicBundling-r16 */
};
static asn_CHOICE_specifics_t asn_SPC_NR_prb_BundlingTypeDCI_1_2_r16_specs_83 = {
	sizeof(struct NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16),
	offsetof(struct NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16, _asn_ctx),
	offsetof(struct NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16, present),
	sizeof(((struct NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16 *)0)->present),
	asn_MAP_NR_prb_BundlingTypeDCI_1_2_r16_tag2el_83,
	2,	/* Count of tags in the map */
	0, 0,
	-1	/* Extensions start */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_prb_BundlingTypeDCI_1_2_r16_83 = {
	"prb-BundlingTypeDCI-1-2-r16",
	"prb-BundlingTypeDCI-1-2-r16",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_prb_BundlingTypeDCI_1_2_r16_constr_83,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		CHOICE_constraint
	},
	asn_MBR_NR_prb_BundlingTypeDCI_1_2_r16_83,
	2,	/* Elements count */
	&asn_SPC_NR_prb_BundlingTypeDCI_1_2_r16_specs_83	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_priorityIndicatorDCI_1_2_r16_value2enum_97[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_priorityIndicatorDCI_1_2_r16_enum2value_97[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_priorityIndicatorDCI_1_2_r16_specs_97 = {
	asn_MAP_NR_priorityIndicatorDCI_1_2_r16_value2enum_97,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_priorityIndicatorDCI_1_2_r16_enum2value_97,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_priorityIndicatorDCI_1_2_r16_tags_97[] = {
	(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_priorityIndicatorDCI_1_2_r16_97 = {
	"priorityIndicatorDCI-1-2-r16",
	"priorityIndicatorDCI-1-2-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_priorityIndicatorDCI_1_2_r16_tags_97,
	sizeof(asn_DEF_NR_priorityIndicatorDCI_1_2_r16_tags_97)
		/sizeof(asn_DEF_NR_priorityIndicatorDCI_1_2_r16_tags_97[0]) - 1, /* 1 */
	asn_DEF_NR_priorityIndicatorDCI_1_2_r16_tags_97,	/* Same as above */
	sizeof(asn_DEF_NR_priorityIndicatorDCI_1_2_r16_tags_97)
		/sizeof(asn_DEF_NR_priorityIndicatorDCI_1_2_r16_tags_97[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_priorityIndicatorDCI_1_2_r16_constr_97,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_priorityIndicatorDCI_1_2_r16_specs_97	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_resourceAllocationType1GranularityDCI_1_2_r16_value2enum_101[] = {
	{ 0,	2,	"n2" },
	{ 1,	2,	"n4" },
	{ 2,	2,	"n8" },
	{ 3,	3,	"n16" }
};
static const unsigned int asn_MAP_NR_resourceAllocationType1GranularityDCI_1_2_r16_enum2value_101[] = {
	3,	/* n16(3) */
	0,	/* n2(0) */
	1,	/* n4(1) */
	2	/* n8(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_resourceAllocationType1GranularityDCI_1_2_r16_specs_101 = {
	asn_MAP_NR_resourceAllocationType1GranularityDCI_1_2_r16_value2enum_101,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_resourceAllocationType1GranularityDCI_1_2_r16_enum2value_101,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_resourceAllocationType1GranularityDCI_1_2_r16_tags_101[] = {
	(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_resourceAllocationType1GranularityDCI_1_2_r16_101 = {
	"resourceAllocationType1GranularityDCI-1-2-r16",
	"resourceAllocationType1GranularityDCI-1-2-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_resourceAllocationType1GranularityDCI_1_2_r16_tags_101,
	sizeof(asn_DEF_NR_resourceAllocationType1GranularityDCI_1_2_r16_tags_101)
		/sizeof(asn_DEF_NR_resourceAllocationType1GranularityDCI_1_2_r16_tags_101[0]) - 1, /* 1 */
	asn_DEF_NR_resourceAllocationType1GranularityDCI_1_2_r16_tags_101,	/* Same as above */
	sizeof(asn_DEF_NR_resourceAllocationType1GranularityDCI_1_2_r16_tags_101)
		/sizeof(asn_DEF_NR_resourceAllocationType1GranularityDCI_1_2_r16_tags_101[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_resourceAllocationType1GranularityDCI_1_2_r16_constr_101,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_resourceAllocationType1GranularityDCI_1_2_r16_specs_101	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_vrb_ToPRB_InterleaverDCI_1_2_r16_value2enum_106[] = {
	{ 0,	2,	"n2" },
	{ 1,	2,	"n4" }
};
static const unsigned int asn_MAP_NR_vrb_ToPRB_InterleaverDCI_1_2_r16_enum2value_106[] = {
	0,	/* n2(0) */
	1	/* n4(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_vrb_ToPRB_InterleaverDCI_1_2_r16_specs_106 = {
	asn_MAP_NR_vrb_ToPRB_InterleaverDCI_1_2_r16_value2enum_106,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_vrb_ToPRB_InterleaverDCI_1_2_r16_enum2value_106,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_vrb_ToPRB_InterleaverDCI_1_2_r16_tags_106[] = {
	(ASN_TAG_CLASS_CONTEXT | (17 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_vrb_ToPRB_InterleaverDCI_1_2_r16_106 = {
	"vrb-ToPRB-InterleaverDCI-1-2-r16",
	"vrb-ToPRB-InterleaverDCI-1-2-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_vrb_ToPRB_InterleaverDCI_1_2_r16_tags_106,
	sizeof(asn_DEF_NR_vrb_ToPRB_InterleaverDCI_1_2_r16_tags_106)
		/sizeof(asn_DEF_NR_vrb_ToPRB_InterleaverDCI_1_2_r16_tags_106[0]) - 1, /* 1 */
	asn_DEF_NR_vrb_ToPRB_InterleaverDCI_1_2_r16_tags_106,	/* Same as above */
	sizeof(asn_DEF_NR_vrb_ToPRB_InterleaverDCI_1_2_r16_tags_106)
		/sizeof(asn_DEF_NR_vrb_ToPRB_InterleaverDCI_1_2_r16_tags_106[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_vrb_ToPRB_InterleaverDCI_1_2_r16_constr_106,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_vrb_ToPRB_InterleaverDCI_1_2_r16_specs_106	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_referenceOfSLIVDCI_1_2_r16_value2enum_109[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_referenceOfSLIVDCI_1_2_r16_enum2value_109[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_referenceOfSLIVDCI_1_2_r16_specs_109 = {
	asn_MAP_NR_referenceOfSLIVDCI_1_2_r16_value2enum_109,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_referenceOfSLIVDCI_1_2_r16_enum2value_109,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_referenceOfSLIVDCI_1_2_r16_tags_109[] = {
	(ASN_TAG_CLASS_CONTEXT | (18 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_referenceOfSLIVDCI_1_2_r16_109 = {
	"referenceOfSLIVDCI-1-2-r16",
	"referenceOfSLIVDCI-1-2-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_referenceOfSLIVDCI_1_2_r16_tags_109,
	sizeof(asn_DEF_NR_referenceOfSLIVDCI_1_2_r16_tags_109)
		/sizeof(asn_DEF_NR_referenceOfSLIVDCI_1_2_r16_tags_109[0]) - 1, /* 1 */
	asn_DEF_NR_referenceOfSLIVDCI_1_2_r16_tags_109,	/* Same as above */
	sizeof(asn_DEF_NR_referenceOfSLIVDCI_1_2_r16_tags_109)
		/sizeof(asn_DEF_NR_referenceOfSLIVDCI_1_2_r16_tags_109[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_referenceOfSLIVDCI_1_2_r16_constr_109,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_referenceOfSLIVDCI_1_2_r16_specs_109	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_resourceAllocationDCI_1_2_r16_value2enum_111[] = {
	{ 0,	23,	"resourceAllocationType0" },
	{ 1,	23,	"resourceAllocationType1" },
	{ 2,	13,	"dynamicSwitch" }
};
static const unsigned int asn_MAP_NR_resourceAllocationDCI_1_2_r16_enum2value_111[] = {
	2,	/* dynamicSwitch(2) */
	0,	/* resourceAllocationType0(0) */
	1	/* resourceAllocationType1(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_resourceAllocationDCI_1_2_r16_specs_111 = {
	asn_MAP_NR_resourceAllocationDCI_1_2_r16_value2enum_111,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_resourceAllocationDCI_1_2_r16_enum2value_111,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_resourceAllocationDCI_1_2_r16_tags_111[] = {
	(ASN_TAG_CLASS_CONTEXT | (19 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_resourceAllocationDCI_1_2_r16_111 = {
	"resourceAllocationDCI-1-2-r16",
	"resourceAllocationDCI-1-2-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_resourceAllocationDCI_1_2_r16_tags_111,
	sizeof(asn_DEF_NR_resourceAllocationDCI_1_2_r16_tags_111)
		/sizeof(asn_DEF_NR_resourceAllocationDCI_1_2_r16_tags_111[0]) - 1, /* 1 */
	asn_DEF_NR_resourceAllocationDCI_1_2_r16_tags_111,	/* Same as above */
	sizeof(asn_DEF_NR_resourceAllocationDCI_1_2_r16_tags_111)
		/sizeof(asn_DEF_NR_resourceAllocationDCI_1_2_r16_tags_111[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_resourceAllocationDCI_1_2_r16_constr_111,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_resourceAllocationDCI_1_2_r16_specs_111	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_priorityIndicatorDCI_1_1_r16_value2enum_115[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_priorityIndicatorDCI_1_1_r16_enum2value_115[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_priorityIndicatorDCI_1_1_r16_specs_115 = {
	asn_MAP_NR_priorityIndicatorDCI_1_1_r16_value2enum_115,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_priorityIndicatorDCI_1_1_r16_enum2value_115,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_priorityIndicatorDCI_1_1_r16_tags_115[] = {
	(ASN_TAG_CLASS_CONTEXT | (20 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_priorityIndicatorDCI_1_1_r16_115 = {
	"priorityIndicatorDCI-1-1-r16",
	"priorityIndicatorDCI-1-1-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_priorityIndicatorDCI_1_1_r16_tags_115,
	sizeof(asn_DEF_NR_priorityIndicatorDCI_1_1_r16_tags_115)
		/sizeof(asn_DEF_NR_priorityIndicatorDCI_1_1_r16_tags_115[0]) - 1, /* 1 */
	asn_DEF_NR_priorityIndicatorDCI_1_1_r16_tags_115,	/* Same as above */
	sizeof(asn_DEF_NR_priorityIndicatorDCI_1_1_r16_tags_115)
		/sizeof(asn_DEF_NR_priorityIndicatorDCI_1_1_r16_tags_115[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_priorityIndicatorDCI_1_1_r16_constr_115,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_priorityIndicatorDCI_1_1_r16_specs_115	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext1_64[] = {
	{ ATF_POINTER, 24, offsetof(struct NR_PDSCH_Config__ext1, maxMIMO_Layers_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_MaxMIMO_LayersDL_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"maxMIMO-Layers-r16"
		},
	{ ATF_POINTER, 23, offsetof(struct NR_PDSCH_Config__ext1, minimumSchedulingOffsetK0_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_MinSchedulingOffsetK0_Values_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"minimumSchedulingOffsetK0-r16"
		},
	{ ATF_POINTER, 22, offsetof(struct NR_PDSCH_Config__ext1, antennaPortsFieldPresenceDCI_1_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_antennaPortsFieldPresenceDCI_1_2_r16_67,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"antennaPortsFieldPresenceDCI-1-2-r16"
		},
	{ ATF_POINTER, 21, offsetof(struct NR_PDSCH_Config__ext1, aperiodicZP_CSI_RS_ResourceSetsToAddModListDCI_1_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		0,
		&asn_DEF_NR_aperiodicZP_CSI_RS_ResourceSetsToAddModListDCI_1_2_r16_69,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_aperiodicZP_CSI_RS_ResourceSetsToAddModListDCI_1_2_r16_constr_69,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_aperiodicZP_CSI_RS_ResourceSetsToAddModListDCI_1_2_r16_constraint_64
		},
		0, 0, /* No default value */
		"aperiodicZP-CSI-RS-ResourceSetsToAddModListDCI-1-2-r16"
		},
	{ ATF_POINTER, 20, offsetof(struct NR_PDSCH_Config__ext1, aperiodicZP_CSI_RS_ResourceSetsToReleaseListDCI_1_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		0,
		&asn_DEF_NR_aperiodicZP_CSI_RS_ResourceSetsToReleaseListDCI_1_2_r16_71,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_aperiodicZP_CSI_RS_ResourceSetsToReleaseListDCI_1_2_r16_constr_71,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_aperiodicZP_CSI_RS_ResourceSetsToReleaseListDCI_1_2_r16_constraint_64
		},
		0, 0, /* No default value */
		"aperiodicZP-CSI-RS-ResourceSetsToReleaseListDCI-1-2-r16"
		},
	{ ATF_POINTER, 19, offsetof(struct NR_PDSCH_Config__ext1, dmrs_DownlinkForPDSCH_MappingTypeA_DCI_1_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_DMRS_DownlinkConfig,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dmrs-DownlinkForPDSCH-MappingTypeA-DCI-1-2-r16"
		},
	{ ATF_POINTER, 18, offsetof(struct NR_PDSCH_Config__ext1, dmrs_DownlinkForPDSCH_MappingTypeB_DCI_1_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_DMRS_DownlinkConfig,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dmrs-DownlinkForPDSCH-MappingTypeB-DCI-1-2-r16"
		},
	{ ATF_POINTER, 17, offsetof(struct NR_PDSCH_Config__ext1, dmrs_SequenceInitializationDCI_1_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dmrs_SequenceInitializationDCI_1_2_r16_75,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dmrs-SequenceInitializationDCI-1-2-r16"
		},
	{ ATF_POINTER, 16, offsetof(struct NR_PDSCH_Config__ext1, harq_ProcessNumberSizeDCI_1_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_harq_ProcessNumberSizeDCI_1_2_r16_constr_77,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_harq_ProcessNumberSizeDCI_1_2_r16_constraint_64
		},
		0, 0, /* No default value */
		"harq-ProcessNumberSizeDCI-1-2-r16"
		},
	{ ATF_POINTER, 15, offsetof(struct NR_PDSCH_Config__ext1, mcs_TableDCI_1_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_mcs_TableDCI_1_2_r16_78,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mcs-TableDCI-1-2-r16"
		},
	{ ATF_POINTER, 14, offsetof(struct NR_PDSCH_Config__ext1, numberOfBitsForRV_DCI_1_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_numberOfBitsForRV_DCI_1_2_r16_constr_81,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_numberOfBitsForRV_DCI_1_2_r16_constraint_64
		},
		0, 0, /* No default value */
		"numberOfBitsForRV-DCI-1-2-r16"
		},
	{ ATF_POINTER, 13, offsetof(struct NR_PDSCH_Config__ext1, pdsch_TimeDomainAllocationListDCI_1_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_PDSCH_TimeDomainResourceAllocationList_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pdsch-TimeDomainAllocationListDCI-1-2-r16"
		},
	{ ATF_POINTER, 12, offsetof(struct NR_PDSCH_Config__ext1, prb_BundlingTypeDCI_1_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_prb_BundlingTypeDCI_1_2_r16_83,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"prb-BundlingTypeDCI-1-2-r16"
		},
	{ ATF_POINTER, 11, offsetof(struct NR_PDSCH_Config__ext1, priorityIndicatorDCI_1_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_priorityIndicatorDCI_1_2_r16_97,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"priorityIndicatorDCI-1-2-r16"
		},
	{ ATF_POINTER, 10, offsetof(struct NR_PDSCH_Config__ext1, rateMatchPatternGroup1DCI_1_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_RateMatchPatternGroup,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"rateMatchPatternGroup1DCI-1-2-r16"
		},
	{ ATF_POINTER, 9, offsetof(struct NR_PDSCH_Config__ext1, rateMatchPatternGroup2DCI_1_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_RateMatchPatternGroup,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"rateMatchPatternGroup2DCI-1-2-r16"
		},
	{ ATF_POINTER, 8, offsetof(struct NR_PDSCH_Config__ext1, resourceAllocationType1GranularityDCI_1_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_resourceAllocationType1GranularityDCI_1_2_r16_101,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"resourceAllocationType1GranularityDCI-1-2-r16"
		},
	{ ATF_POINTER, 7, offsetof(struct NR_PDSCH_Config__ext1, vrb_ToPRB_InterleaverDCI_1_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (17 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_vrb_ToPRB_InterleaverDCI_1_2_r16_106,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"vrb-ToPRB-InterleaverDCI-1-2-r16"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_PDSCH_Config__ext1, referenceOfSLIVDCI_1_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (18 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_referenceOfSLIVDCI_1_2_r16_109,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"referenceOfSLIVDCI-1-2-r16"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_PDSCH_Config__ext1, resourceAllocationDCI_1_2_r16),
		(ASN_TAG_CLASS_CONTEXT | (19 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_resourceAllocationDCI_1_2_r16_111,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"resourceAllocationDCI-1-2-r16"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_PDSCH_Config__ext1, priorityIndicatorDCI_1_1_r16),
		(ASN_TAG_CLASS_CONTEXT | (20 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_priorityIndicatorDCI_1_1_r16_115,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"priorityIndicatorDCI-1-1-r16"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_PDSCH_Config__ext1, dataScramblingIdentityPDSCH2_r16),
		(ASN_TAG_CLASS_CONTEXT | (21 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_dataScramblingIdentityPDSCH2_r16_constr_117,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_dataScramblingIdentityPDSCH2_r16_constraint_64
		},
		0, 0, /* No default value */
		"dataScramblingIdentityPDSCH2-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_PDSCH_Config__ext1, pdsch_TimeDomainAllocationList_r16),
		(ASN_TAG_CLASS_CONTEXT | (22 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_PDSCH_TimeDomainResourceAllocationList_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pdsch-TimeDomainAllocationList-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PDSCH_Config__ext1, repetitionSchemeConfig_r16),
		(ASN_TAG_CLASS_CONTEXT | (23 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_RepetitionSchemeConfig_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"repetitionSchemeConfig-r16"
		},
};
static const int asn_MAP_NR_ext1_oms_64[] = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_64[] = {
	(ASN_TAG_CLASS_CONTEXT | (24 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_64[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* maxMIMO-Layers-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* minimumSchedulingOffsetK0-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* antennaPortsFieldPresenceDCI-1-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* aperiodicZP-CSI-RS-ResourceSetsToAddModListDCI-1-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* aperiodicZP-CSI-RS-ResourceSetsToReleaseListDCI-1-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* dmrs-DownlinkForPDSCH-MappingTypeA-DCI-1-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* dmrs-DownlinkForPDSCH-MappingTypeB-DCI-1-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* dmrs-SequenceInitializationDCI-1-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* harq-ProcessNumberSizeDCI-1-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* mcs-TableDCI-1-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 }, /* numberOfBitsForRV-DCI-1-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (11 << 2)), 11, 0, 0 }, /* pdsch-TimeDomainAllocationListDCI-1-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (12 << 2)), 12, 0, 0 }, /* prb-BundlingTypeDCI-1-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (13 << 2)), 13, 0, 0 }, /* priorityIndicatorDCI-1-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (14 << 2)), 14, 0, 0 }, /* rateMatchPatternGroup1DCI-1-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (15 << 2)), 15, 0, 0 }, /* rateMatchPatternGroup2DCI-1-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (16 << 2)), 16, 0, 0 }, /* resourceAllocationType1GranularityDCI-1-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (17 << 2)), 17, 0, 0 }, /* vrb-ToPRB-InterleaverDCI-1-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (18 << 2)), 18, 0, 0 }, /* referenceOfSLIVDCI-1-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (19 << 2)), 19, 0, 0 }, /* resourceAllocationDCI-1-2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (20 << 2)), 20, 0, 0 }, /* priorityIndicatorDCI-1-1-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (21 << 2)), 21, 0, 0 }, /* dataScramblingIdentityPDSCH2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (22 << 2)), 22, 0, 0 }, /* pdsch-TimeDomainAllocationList-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (23 << 2)), 23, 0, 0 } /* repetitionSchemeConfig-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_64 = {
	sizeof(struct NR_PDSCH_Config__ext1),
	offsetof(struct NR_PDSCH_Config__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_64,
	24,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_64,	/* Optional members */
	24, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_64 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_64,
	sizeof(asn_DEF_NR_ext1_tags_64)
		/sizeof(asn_DEF_NR_ext1_tags_64[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_64,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_64)
		/sizeof(asn_DEF_NR_ext1_tags_64[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_64,
	24,	/* Elements count */
	&asn_SPC_NR_ext1_specs_64	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext2_120[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_PDSCH_Config__ext2, repetitionSchemeConfig_v1630),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_RepetitionSchemeConfig_v1630,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"repetitionSchemeConfig-v1630"
		},
};
static const int asn_MAP_NR_ext2_oms_120[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext2_tags_120[] = {
	(ASN_TAG_CLASS_CONTEXT | (25 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext2_tag2el_120[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* repetitionSchemeConfig-v1630 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext2_specs_120 = {
	sizeof(struct NR_PDSCH_Config__ext2),
	offsetof(struct NR_PDSCH_Config__ext2, _asn_ctx),
	asn_MAP_NR_ext2_tag2el_120,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext2_oms_120,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext2_120 = {
	"ext2",
	"ext2",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext2_tags_120,
	sizeof(asn_DEF_NR_ext2_tags_120)
		/sizeof(asn_DEF_NR_ext2_tags_120[0]) - 1, /* 1 */
	asn_DEF_NR_ext2_tags_120,	/* Same as above */
	sizeof(asn_DEF_NR_ext2_tags_120)
		/sizeof(asn_DEF_NR_ext2_tags_120[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext2_120,
	1,	/* Elements count */
	&asn_SPC_NR_ext2_specs_120	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pdsch_HARQ_ACK_OneShotFeedbackDCI_1_2_r17_value2enum_123[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_pdsch_HARQ_ACK_OneShotFeedbackDCI_1_2_r17_enum2value_123[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pdsch_HARQ_ACK_OneShotFeedbackDCI_1_2_r17_specs_123 = {
	asn_MAP_NR_pdsch_HARQ_ACK_OneShotFeedbackDCI_1_2_r17_value2enum_123,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pdsch_HARQ_ACK_OneShotFeedbackDCI_1_2_r17_enum2value_123,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pdsch_HARQ_ACK_OneShotFeedbackDCI_1_2_r17_tags_123[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pdsch_HARQ_ACK_OneShotFeedbackDCI_1_2_r17_123 = {
	"pdsch-HARQ-ACK-OneShotFeedbackDCI-1-2-r17",
	"pdsch-HARQ-ACK-OneShotFeedbackDCI-1-2-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pdsch_HARQ_ACK_OneShotFeedbackDCI_1_2_r17_tags_123,
	sizeof(asn_DEF_NR_pdsch_HARQ_ACK_OneShotFeedbackDCI_1_2_r17_tags_123)
		/sizeof(asn_DEF_NR_pdsch_HARQ_ACK_OneShotFeedbackDCI_1_2_r17_tags_123[0]) - 1, /* 1 */
	asn_DEF_NR_pdsch_HARQ_ACK_OneShotFeedbackDCI_1_2_r17_tags_123,	/* Same as above */
	sizeof(asn_DEF_NR_pdsch_HARQ_ACK_OneShotFeedbackDCI_1_2_r17_tags_123)
		/sizeof(asn_DEF_NR_pdsch_HARQ_ACK_OneShotFeedbackDCI_1_2_r17_tags_123[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pdsch_HARQ_ACK_OneShotFeedbackDCI_1_2_r17_constr_123,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pdsch_HARQ_ACK_OneShotFeedbackDCI_1_2_r17_specs_123	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pdsch_HARQ_ACK_EnhType3DCI_1_2_r17_value2enum_125[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_pdsch_HARQ_ACK_EnhType3DCI_1_2_r17_enum2value_125[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pdsch_HARQ_ACK_EnhType3DCI_1_2_r17_specs_125 = {
	asn_MAP_NR_pdsch_HARQ_ACK_EnhType3DCI_1_2_r17_value2enum_125,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pdsch_HARQ_ACK_EnhType3DCI_1_2_r17_enum2value_125,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pdsch_HARQ_ACK_EnhType3DCI_1_2_r17_tags_125[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pdsch_HARQ_ACK_EnhType3DCI_1_2_r17_125 = {
	"pdsch-HARQ-ACK-EnhType3DCI-1-2-r17",
	"pdsch-HARQ-ACK-EnhType3DCI-1-2-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pdsch_HARQ_ACK_EnhType3DCI_1_2_r17_tags_125,
	sizeof(asn_DEF_NR_pdsch_HARQ_ACK_EnhType3DCI_1_2_r17_tags_125)
		/sizeof(asn_DEF_NR_pdsch_HARQ_ACK_EnhType3DCI_1_2_r17_tags_125[0]) - 1, /* 1 */
	asn_DEF_NR_pdsch_HARQ_ACK_EnhType3DCI_1_2_r17_tags_125,	/* Same as above */
	sizeof(asn_DEF_NR_pdsch_HARQ_ACK_EnhType3DCI_1_2_r17_tags_125)
		/sizeof(asn_DEF_NR_pdsch_HARQ_ACK_EnhType3DCI_1_2_r17_tags_125[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pdsch_HARQ_ACK_EnhType3DCI_1_2_r17_constr_125,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pdsch_HARQ_ACK_EnhType3DCI_1_2_r17_specs_125	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pdsch_HARQ_ACK_EnhType3DCI_Field_1_2_r17_value2enum_127[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_pdsch_HARQ_ACK_EnhType3DCI_Field_1_2_r17_enum2value_127[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pdsch_HARQ_ACK_EnhType3DCI_Field_1_2_r17_specs_127 = {
	asn_MAP_NR_pdsch_HARQ_ACK_EnhType3DCI_Field_1_2_r17_value2enum_127,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pdsch_HARQ_ACK_EnhType3DCI_Field_1_2_r17_enum2value_127,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pdsch_HARQ_ACK_EnhType3DCI_Field_1_2_r17_tags_127[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pdsch_HARQ_ACK_EnhType3DCI_Field_1_2_r17_127 = {
	"pdsch-HARQ-ACK-EnhType3DCI-Field-1-2-r17",
	"pdsch-HARQ-ACK-EnhType3DCI-Field-1-2-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pdsch_HARQ_ACK_EnhType3DCI_Field_1_2_r17_tags_127,
	sizeof(asn_DEF_NR_pdsch_HARQ_ACK_EnhType3DCI_Field_1_2_r17_tags_127)
		/sizeof(asn_DEF_NR_pdsch_HARQ_ACK_EnhType3DCI_Field_1_2_r17_tags_127[0]) - 1, /* 1 */
	asn_DEF_NR_pdsch_HARQ_ACK_EnhType3DCI_Field_1_2_r17_tags_127,	/* Same as above */
	sizeof(asn_DEF_NR_pdsch_HARQ_ACK_EnhType3DCI_Field_1_2_r17_tags_127)
		/sizeof(asn_DEF_NR_pdsch_HARQ_ACK_EnhType3DCI_Field_1_2_r17_tags_127[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pdsch_HARQ_ACK_EnhType3DCI_Field_1_2_r17_constr_127,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pdsch_HARQ_ACK_EnhType3DCI_Field_1_2_r17_specs_127	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pdsch_HARQ_ACK_RetxDCI_1_2_r17_value2enum_129[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_pdsch_HARQ_ACK_RetxDCI_1_2_r17_enum2value_129[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pdsch_HARQ_ACK_RetxDCI_1_2_r17_specs_129 = {
	asn_MAP_NR_pdsch_HARQ_ACK_RetxDCI_1_2_r17_value2enum_129,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pdsch_HARQ_ACK_RetxDCI_1_2_r17_enum2value_129,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pdsch_HARQ_ACK_RetxDCI_1_2_r17_tags_129[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pdsch_HARQ_ACK_RetxDCI_1_2_r17_129 = {
	"pdsch-HARQ-ACK-RetxDCI-1-2-r17",
	"pdsch-HARQ-ACK-RetxDCI-1-2-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pdsch_HARQ_ACK_RetxDCI_1_2_r17_tags_129,
	sizeof(asn_DEF_NR_pdsch_HARQ_ACK_RetxDCI_1_2_r17_tags_129)
		/sizeof(asn_DEF_NR_pdsch_HARQ_ACK_RetxDCI_1_2_r17_tags_129[0]) - 1, /* 1 */
	asn_DEF_NR_pdsch_HARQ_ACK_RetxDCI_1_2_r17_tags_129,	/* Same as above */
	sizeof(asn_DEF_NR_pdsch_HARQ_ACK_RetxDCI_1_2_r17_tags_129)
		/sizeof(asn_DEF_NR_pdsch_HARQ_ACK_RetxDCI_1_2_r17_tags_129[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pdsch_HARQ_ACK_RetxDCI_1_2_r17_constr_129,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pdsch_HARQ_ACK_RetxDCI_1_2_r17_specs_129	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pucch_sSCellDynDCI_1_2_r17_value2enum_131[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_pucch_sSCellDynDCI_1_2_r17_enum2value_131[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pucch_sSCellDynDCI_1_2_r17_specs_131 = {
	asn_MAP_NR_pucch_sSCellDynDCI_1_2_r17_value2enum_131,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pucch_sSCellDynDCI_1_2_r17_enum2value_131,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pucch_sSCellDynDCI_1_2_r17_tags_131[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pucch_sSCellDynDCI_1_2_r17_131 = {
	"pucch-sSCellDynDCI-1-2-r17",
	"pucch-sSCellDynDCI-1-2-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pucch_sSCellDynDCI_1_2_r17_tags_131,
	sizeof(asn_DEF_NR_pucch_sSCellDynDCI_1_2_r17_tags_131)
		/sizeof(asn_DEF_NR_pucch_sSCellDynDCI_1_2_r17_tags_131[0]) - 1, /* 1 */
	asn_DEF_NR_pucch_sSCellDynDCI_1_2_r17_tags_131,	/* Same as above */
	sizeof(asn_DEF_NR_pucch_sSCellDynDCI_1_2_r17_tags_131)
		/sizeof(asn_DEF_NR_pucch_sSCellDynDCI_1_2_r17_tags_131[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pucch_sSCellDynDCI_1_2_r17_constr_131,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pucch_sSCellDynDCI_1_2_r17_specs_131	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_dl_OrJointTCI_StateToAddModList_r17_135[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_TCI_State,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_dl_OrJointTCI_StateToAddModList_r17_tags_135[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_dl_OrJointTCI_StateToAddModList_r17_specs_135 = {
	sizeof(struct NR_PDSCH_Config__ext3__dl_OrJointTCI_StateList_r17__explicitlist__dl_OrJointTCI_StateToAddModList_r17),
	offsetof(struct NR_PDSCH_Config__ext3__dl_OrJointTCI_StateList_r17__explicitlist__dl_OrJointTCI_StateToAddModList_r17, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dl_OrJointTCI_StateToAddModList_r17_135 = {
	"dl-OrJointTCI-StateToAddModList-r17",
	"dl-OrJointTCI-StateToAddModList-r17",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_dl_OrJointTCI_StateToAddModList_r17_tags_135,
	sizeof(asn_DEF_NR_dl_OrJointTCI_StateToAddModList_r17_tags_135)
		/sizeof(asn_DEF_NR_dl_OrJointTCI_StateToAddModList_r17_tags_135[0]) - 1, /* 1 */
	asn_DEF_NR_dl_OrJointTCI_StateToAddModList_r17_tags_135,	/* Same as above */
	sizeof(asn_DEF_NR_dl_OrJointTCI_StateToAddModList_r17_tags_135)
		/sizeof(asn_DEF_NR_dl_OrJointTCI_StateToAddModList_r17_tags_135[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dl_OrJointTCI_StateToAddModList_r17_constr_135,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_dl_OrJointTCI_StateToAddModList_r17_135,
	1,	/* Single element */
	&asn_SPC_NR_dl_OrJointTCI_StateToAddModList_r17_specs_135	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_dl_OrJointTCI_StateToReleaseList_r17_137[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (2 << 2)),
		0,
		&asn_DEF_NR_TCI_StateId,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_dl_OrJointTCI_StateToReleaseList_r17_tags_137[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_dl_OrJointTCI_StateToReleaseList_r17_specs_137 = {
	sizeof(struct NR_PDSCH_Config__ext3__dl_OrJointTCI_StateList_r17__explicitlist__dl_OrJointTCI_StateToReleaseList_r17),
	offsetof(struct NR_PDSCH_Config__ext3__dl_OrJointTCI_StateList_r17__explicitlist__dl_OrJointTCI_StateToReleaseList_r17, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dl_OrJointTCI_StateToReleaseList_r17_137 = {
	"dl-OrJointTCI-StateToReleaseList-r17",
	"dl-OrJointTCI-StateToReleaseList-r17",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_dl_OrJointTCI_StateToReleaseList_r17_tags_137,
	sizeof(asn_DEF_NR_dl_OrJointTCI_StateToReleaseList_r17_tags_137)
		/sizeof(asn_DEF_NR_dl_OrJointTCI_StateToReleaseList_r17_tags_137[0]) - 1, /* 1 */
	asn_DEF_NR_dl_OrJointTCI_StateToReleaseList_r17_tags_137,	/* Same as above */
	sizeof(asn_DEF_NR_dl_OrJointTCI_StateToReleaseList_r17_tags_137)
		/sizeof(asn_DEF_NR_dl_OrJointTCI_StateToReleaseList_r17_tags_137[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dl_OrJointTCI_StateToReleaseList_r17_constr_137,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_dl_OrJointTCI_StateToReleaseList_r17_137,
	1,	/* Single element */
	&asn_SPC_NR_dl_OrJointTCI_StateToReleaseList_r17_specs_137	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_explicitlist_134[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_PDSCH_Config__ext3__dl_OrJointTCI_StateList_r17__explicitlist, dl_OrJointTCI_StateToAddModList_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_dl_OrJointTCI_StateToAddModList_r17_135,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_dl_OrJointTCI_StateToAddModList_r17_constr_135,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_dl_OrJointTCI_StateToAddModList_r17_constraint_134
		},
		0, 0, /* No default value */
		"dl-OrJointTCI-StateToAddModList-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PDSCH_Config__ext3__dl_OrJointTCI_StateList_r17__explicitlist, dl_OrJointTCI_StateToReleaseList_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_dl_OrJointTCI_StateToReleaseList_r17_137,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_dl_OrJointTCI_StateToReleaseList_r17_constr_137,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_dl_OrJointTCI_StateToReleaseList_r17_constraint_134
		},
		0, 0, /* No default value */
		"dl-OrJointTCI-StateToReleaseList-r17"
		},
};
static const int asn_MAP_NR_explicitlist_oms_134[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_explicitlist_tags_134[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_explicitlist_tag2el_134[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* dl-OrJointTCI-StateToAddModList-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* dl-OrJointTCI-StateToReleaseList-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_explicitlist_specs_134 = {
	sizeof(struct NR_PDSCH_Config__ext3__dl_OrJointTCI_StateList_r17__explicitlist),
	offsetof(struct NR_PDSCH_Config__ext3__dl_OrJointTCI_StateList_r17__explicitlist, _asn_ctx),
	asn_MAP_NR_explicitlist_tag2el_134,
	2,	/* Count of tags in the map */
	asn_MAP_NR_explicitlist_oms_134,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_explicitlist_134 = {
	"explicitlist",
	"explicitlist",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_explicitlist_tags_134,
	sizeof(asn_DEF_NR_explicitlist_tags_134)
		/sizeof(asn_DEF_NR_explicitlist_tags_134[0]) - 1, /* 1 */
	asn_DEF_NR_explicitlist_tags_134,	/* Same as above */
	sizeof(asn_DEF_NR_explicitlist_tags_134)
		/sizeof(asn_DEF_NR_explicitlist_tags_134[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_explicitlist_134,
	2,	/* Elements count */
	&asn_SPC_NR_explicitlist_specs_134	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_dl_OrJointTCI_StateList_r17_133[] = {
	{ ATF_POINTER, 0, offsetof(struct NR_PDSCH_Config__ext3__dl_OrJointTCI_StateList_r17, choice.explicitlist),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_explicitlist_134,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"explicitlist"
		},
	{ ATF_POINTER, 0, offsetof(struct NR_PDSCH_Config__ext3__dl_OrJointTCI_StateList_r17, choice.unifiedTCI_StateRef_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ServingCellAndBWP_Id_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"unifiedTCI-StateRef-r17"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_NR_dl_OrJointTCI_StateList_r17_tag2el_133[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* explicitlist */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* unifiedTCI-StateRef-r17 */
};
static asn_CHOICE_specifics_t asn_SPC_NR_dl_OrJointTCI_StateList_r17_specs_133 = {
	sizeof(struct NR_PDSCH_Config__ext3__dl_OrJointTCI_StateList_r17),
	offsetof(struct NR_PDSCH_Config__ext3__dl_OrJointTCI_StateList_r17, _asn_ctx),
	offsetof(struct NR_PDSCH_Config__ext3__dl_OrJointTCI_StateList_r17, present),
	sizeof(((struct NR_PDSCH_Config__ext3__dl_OrJointTCI_StateList_r17 *)0)->present),
	asn_MAP_NR_dl_OrJointTCI_StateList_r17_tag2el_133,
	2,	/* Count of tags in the map */
	0, 0,
	-1	/* Extensions start */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dl_OrJointTCI_StateList_r17_133 = {
	"dl-OrJointTCI-StateList-r17",
	"dl-OrJointTCI-StateList-r17",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dl_OrJointTCI_StateList_r17_constr_133,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		CHOICE_constraint
	},
	asn_MBR_NR_dl_OrJointTCI_StateList_r17_133,
	2,	/* Elements count */
	&asn_SPC_NR_dl_OrJointTCI_StateList_r17_specs_133	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_beamAppTime_r17_value2enum_140[] = {
	{ 0,	2,	"n1" },
	{ 1,	2,	"n2" },
	{ 2,	2,	"n4" },
	{ 3,	2,	"n7" },
	{ 4,	3,	"n14" },
	{ 5,	3,	"n28" },
	{ 6,	3,	"n42" },
	{ 7,	3,	"n56" },
	{ 8,	3,	"n70" },
	{ 9,	3,	"n84" },
	{ 10,	3,	"n98" },
	{ 11,	4,	"n112" },
	{ 12,	4,	"n224" },
	{ 13,	4,	"n336" },
	{ 14,	6,	"spare2" },
	{ 15,	6,	"spare1" }
};
static const unsigned int asn_MAP_NR_beamAppTime_r17_enum2value_140[] = {
	0,	/* n1(0) */
	11,	/* n112(11) */
	4,	/* n14(4) */
	1,	/* n2(1) */
	12,	/* n224(12) */
	5,	/* n28(5) */
	13,	/* n336(13) */
	2,	/* n4(2) */
	6,	/* n42(6) */
	7,	/* n56(7) */
	3,	/* n7(3) */
	8,	/* n70(8) */
	9,	/* n84(9) */
	10,	/* n98(10) */
	15,	/* spare1(15) */
	14	/* spare2(14) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_beamAppTime_r17_specs_140 = {
	asn_MAP_NR_beamAppTime_r17_value2enum_140,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_beamAppTime_r17_enum2value_140,	/* N => "tag"; sorted by N */
	16,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_beamAppTime_r17_tags_140[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_beamAppTime_r17_140 = {
	"beamAppTime-r17",
	"beamAppTime-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_beamAppTime_r17_tags_140,
	sizeof(asn_DEF_NR_beamAppTime_r17_tags_140)
		/sizeof(asn_DEF_NR_beamAppTime_r17_tags_140[0]) - 1, /* 1 */
	asn_DEF_NR_beamAppTime_r17_tags_140,	/* Same as above */
	sizeof(asn_DEF_NR_beamAppTime_r17_tags_140)
		/sizeof(asn_DEF_NR_beamAppTime_r17_tags_140[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_beamAppTime_r17_constr_140,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_beamAppTime_r17_specs_140	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dmrs_FD_OCC_DisabledForRank1_PDSCH_r17_value2enum_158[] = {
	{ 0,	4,	"true" }
};
static const unsigned int asn_MAP_NR_dmrs_FD_OCC_DisabledForRank1_PDSCH_r17_enum2value_158[] = {
	0	/* true(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dmrs_FD_OCC_DisabledForRank1_PDSCH_r17_specs_158 = {
	asn_MAP_NR_dmrs_FD_OCC_DisabledForRank1_PDSCH_r17_value2enum_158,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dmrs_FD_OCC_DisabledForRank1_PDSCH_r17_enum2value_158,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dmrs_FD_OCC_DisabledForRank1_PDSCH_r17_tags_158[] = {
	(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dmrs_FD_OCC_DisabledForRank1_PDSCH_r17_158 = {
	"dmrs-FD-OCC-DisabledForRank1-PDSCH-r17",
	"dmrs-FD-OCC-DisabledForRank1-PDSCH-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dmrs_FD_OCC_DisabledForRank1_PDSCH_r17_tags_158,
	sizeof(asn_DEF_NR_dmrs_FD_OCC_DisabledForRank1_PDSCH_r17_tags_158)
		/sizeof(asn_DEF_NR_dmrs_FD_OCC_DisabledForRank1_PDSCH_r17_tags_158[0]) - 1, /* 1 */
	asn_DEF_NR_dmrs_FD_OCC_DisabledForRank1_PDSCH_r17_tags_158,	/* Same as above */
	sizeof(asn_DEF_NR_dmrs_FD_OCC_DisabledForRank1_PDSCH_r17_tags_158)
		/sizeof(asn_DEF_NR_dmrs_FD_OCC_DisabledForRank1_PDSCH_r17_tags_158[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dmrs_FD_OCC_DisabledForRank1_PDSCH_r17_constr_158,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dmrs_FD_OCC_DisabledForRank1_PDSCH_r17_specs_158	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_mcs_Table_r17_value2enum_163[] = {
	{ 0,	7,	"qam1024" }
};
static const unsigned int asn_MAP_NR_mcs_Table_r17_enum2value_163[] = {
	0	/* qam1024(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_mcs_Table_r17_specs_163 = {
	asn_MAP_NR_mcs_Table_r17_value2enum_163,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_mcs_Table_r17_enum2value_163,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_mcs_Table_r17_tags_163[] = {
	(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mcs_Table_r17_163 = {
	"mcs-Table-r17",
	"mcs-Table-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_mcs_Table_r17_tags_163,
	sizeof(asn_DEF_NR_mcs_Table_r17_tags_163)
		/sizeof(asn_DEF_NR_mcs_Table_r17_tags_163[0]) - 1, /* 1 */
	asn_DEF_NR_mcs_Table_r17_tags_163,	/* Same as above */
	sizeof(asn_DEF_NR_mcs_Table_r17_tags_163)
		/sizeof(asn_DEF_NR_mcs_Table_r17_tags_163[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_mcs_Table_r17_constr_163,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_mcs_Table_r17_specs_163	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_mcs_TableDCI_1_2_r17_value2enum_165[] = {
	{ 0,	7,	"qam1024" }
};
static const unsigned int asn_MAP_NR_mcs_TableDCI_1_2_r17_enum2value_165[] = {
	0	/* qam1024(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_mcs_TableDCI_1_2_r17_specs_165 = {
	asn_MAP_NR_mcs_TableDCI_1_2_r17_value2enum_165,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_mcs_TableDCI_1_2_r17_enum2value_165,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_mcs_TableDCI_1_2_r17_tags_165[] = {
	(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mcs_TableDCI_1_2_r17_165 = {
	"mcs-TableDCI-1-2-r17",
	"mcs-TableDCI-1-2-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_mcs_TableDCI_1_2_r17_tags_165,
	sizeof(asn_DEF_NR_mcs_TableDCI_1_2_r17_tags_165)
		/sizeof(asn_DEF_NR_mcs_TableDCI_1_2_r17_tags_165[0]) - 1, /* 1 */
	asn_DEF_NR_mcs_TableDCI_1_2_r17_tags_165,	/* Same as above */
	sizeof(asn_DEF_NR_mcs_TableDCI_1_2_r17_tags_165)
		/sizeof(asn_DEF_NR_mcs_TableDCI_1_2_r17_tags_165[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_mcs_TableDCI_1_2_r17_constr_165,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_mcs_TableDCI_1_2_r17_specs_165	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_xOverheadMulticast_r17_value2enum_167[] = {
	{ 0,	4,	"xOh6" },
	{ 1,	5,	"xOh12" },
	{ 2,	5,	"xOh18" }
};
static const unsigned int asn_MAP_NR_xOverheadMulticast_r17_enum2value_167[] = {
	1,	/* xOh12(1) */
	2,	/* xOh18(2) */
	0	/* xOh6(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_xOverheadMulticast_r17_specs_167 = {
	asn_MAP_NR_xOverheadMulticast_r17_value2enum_167,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_xOverheadMulticast_r17_enum2value_167,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_xOverheadMulticast_r17_tags_167[] = {
	(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_xOverheadMulticast_r17_167 = {
	"xOverheadMulticast-r17",
	"xOverheadMulticast-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_xOverheadMulticast_r17_tags_167,
	sizeof(asn_DEF_NR_xOverheadMulticast_r17_tags_167)
		/sizeof(asn_DEF_NR_xOverheadMulticast_r17_tags_167[0]) - 1, /* 1 */
	asn_DEF_NR_xOverheadMulticast_r17_tags_167,	/* Same as above */
	sizeof(asn_DEF_NR_xOverheadMulticast_r17_tags_167)
		/sizeof(asn_DEF_NR_xOverheadMulticast_r17_tags_167[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_xOverheadMulticast_r17_constr_167,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_xOverheadMulticast_r17_specs_167	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_priorityIndicatorDCI_4_2_r17_value2enum_171[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_priorityIndicatorDCI_4_2_r17_enum2value_171[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_priorityIndicatorDCI_4_2_r17_specs_171 = {
	asn_MAP_NR_priorityIndicatorDCI_4_2_r17_value2enum_171,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_priorityIndicatorDCI_4_2_r17_enum2value_171,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_priorityIndicatorDCI_4_2_r17_tags_171[] = {
	(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_priorityIndicatorDCI_4_2_r17_171 = {
	"priorityIndicatorDCI-4-2-r17",
	"priorityIndicatorDCI-4-2-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_priorityIndicatorDCI_4_2_r17_tags_171,
	sizeof(asn_DEF_NR_priorityIndicatorDCI_4_2_r17_tags_171)
		/sizeof(asn_DEF_NR_priorityIndicatorDCI_4_2_r17_tags_171[0]) - 1, /* 1 */
	asn_DEF_NR_priorityIndicatorDCI_4_2_r17_tags_171,	/* Same as above */
	sizeof(asn_DEF_NR_priorityIndicatorDCI_4_2_r17_tags_171)
		/sizeof(asn_DEF_NR_priorityIndicatorDCI_4_2_r17_tags_171[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_priorityIndicatorDCI_4_2_r17_constr_171,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_priorityIndicatorDCI_4_2_r17_specs_171	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext3_122[] = {
	{ ATF_POINTER, 17, offsetof(struct NR_PDSCH_Config__ext3, pdsch_HARQ_ACK_OneShotFeedbackDCI_1_2_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pdsch_HARQ_ACK_OneShotFeedbackDCI_1_2_r17_123,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pdsch-HARQ-ACK-OneShotFeedbackDCI-1-2-r17"
		},
	{ ATF_POINTER, 16, offsetof(struct NR_PDSCH_Config__ext3, pdsch_HARQ_ACK_EnhType3DCI_1_2_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pdsch_HARQ_ACK_EnhType3DCI_1_2_r17_125,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pdsch-HARQ-ACK-EnhType3DCI-1-2-r17"
		},
	{ ATF_POINTER, 15, offsetof(struct NR_PDSCH_Config__ext3, pdsch_HARQ_ACK_EnhType3DCI_Field_1_2_r17),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pdsch_HARQ_ACK_EnhType3DCI_Field_1_2_r17_127,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pdsch-HARQ-ACK-EnhType3DCI-Field-1-2-r17"
		},
	{ ATF_POINTER, 14, offsetof(struct NR_PDSCH_Config__ext3, pdsch_HARQ_ACK_RetxDCI_1_2_r17),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pdsch_HARQ_ACK_RetxDCI_1_2_r17_129,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pdsch-HARQ-ACK-RetxDCI-1-2-r17"
		},
	{ ATF_POINTER, 13, offsetof(struct NR_PDSCH_Config__ext3, pucch_sSCellDynDCI_1_2_r17),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pucch_sSCellDynDCI_1_2_r17_131,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pucch-sSCellDynDCI-1-2-r17"
		},
	{ ATF_POINTER, 12, offsetof(struct NR_PDSCH_Config__ext3, dl_OrJointTCI_StateList_r17),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_dl_OrJointTCI_StateList_r17_133,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dl-OrJointTCI-StateList-r17"
		},
	{ ATF_POINTER, 11, offsetof(struct NR_PDSCH_Config__ext3, beamAppTime_r17),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_beamAppTime_r17_140,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"beamAppTime-r17"
		},
	{ ATF_POINTER, 10, offsetof(struct NR_PDSCH_Config__ext3, dummy),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_Dummy_TDRA_List,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dummy"
		},
	{ ATF_POINTER, 9, offsetof(struct NR_PDSCH_Config__ext3, dmrs_FD_OCC_DisabledForRank1_PDSCH_r17),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dmrs_FD_OCC_DisabledForRank1_PDSCH_r17_158,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dmrs-FD-OCC-DisabledForRank1-PDSCH-r17"
		},
	{ ATF_POINTER, 8, offsetof(struct NR_PDSCH_Config__ext3, minimumSchedulingOffsetK0_r17),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_MinSchedulingOffsetK0_Values_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"minimumSchedulingOffsetK0-r17"
		},
	{ ATF_POINTER, 7, offsetof(struct NR_PDSCH_Config__ext3, harq_ProcessNumberSizeDCI_1_2_v1700),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_harq_ProcessNumberSizeDCI_1_2_v1700_constr_161,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_harq_ProcessNumberSizeDCI_1_2_v1700_constraint_122
		},
		0, 0, /* No default value */
		"harq-ProcessNumberSizeDCI-1-2-v1700"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_PDSCH_Config__ext3, harq_ProcessNumberSizeDCI_1_1_r17),
		(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_harq_ProcessNumberSizeDCI_1_1_r17_constr_162,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_harq_ProcessNumberSizeDCI_1_1_r17_constraint_122
		},
		0, 0, /* No default value */
		"harq-ProcessNumberSizeDCI-1-1-r17"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_PDSCH_Config__ext3, mcs_Table_r17),
		(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_mcs_Table_r17_163,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mcs-Table-r17"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_PDSCH_Config__ext3, mcs_TableDCI_1_2_r17),
		(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_mcs_TableDCI_1_2_r17_165,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mcs-TableDCI-1-2-r17"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_PDSCH_Config__ext3, xOverheadMulticast_r17),
		(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_xOverheadMulticast_r17_167,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"xOverheadMulticast-r17"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_PDSCH_Config__ext3, priorityIndicatorDCI_4_2_r17),
		(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_priorityIndicatorDCI_4_2_r17_171,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"priorityIndicatorDCI-4-2-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PDSCH_Config__ext3, sizeDCI_4_2_r17),
		(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_sizeDCI_4_2_r17_constr_173,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_sizeDCI_4_2_r17_constraint_122
		},
		0, 0, /* No default value */
		"sizeDCI-4-2-r17"
		},
};
static const int asn_MAP_NR_ext3_oms_122[] = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16 };
static const ber_tlv_tag_t asn_DEF_NR_ext3_tags_122[] = {
	(ASN_TAG_CLASS_CONTEXT | (26 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext3_tag2el_122[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* pdsch-HARQ-ACK-OneShotFeedbackDCI-1-2-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* pdsch-HARQ-ACK-EnhType3DCI-1-2-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* pdsch-HARQ-ACK-EnhType3DCI-Field-1-2-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* pdsch-HARQ-ACK-RetxDCI-1-2-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* pucch-sSCellDynDCI-1-2-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* dl-OrJointTCI-StateList-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* beamAppTime-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* dummy */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* dmrs-FD-OCC-DisabledForRank1-PDSCH-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* minimumSchedulingOffsetK0-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 }, /* harq-ProcessNumberSizeDCI-1-2-v1700 */
    { (ASN_TAG_CLASS_CONTEXT | (11 << 2)), 11, 0, 0 }, /* harq-ProcessNumberSizeDCI-1-1-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (12 << 2)), 12, 0, 0 }, /* mcs-Table-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (13 << 2)), 13, 0, 0 }, /* mcs-TableDCI-1-2-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (14 << 2)), 14, 0, 0 }, /* xOverheadMulticast-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (15 << 2)), 15, 0, 0 }, /* priorityIndicatorDCI-4-2-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (16 << 2)), 16, 0, 0 } /* sizeDCI-4-2-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext3_specs_122 = {
	sizeof(struct NR_PDSCH_Config__ext3),
	offsetof(struct NR_PDSCH_Config__ext3, _asn_ctx),
	asn_MAP_NR_ext3_tag2el_122,
	17,	/* Count of tags in the map */
	asn_MAP_NR_ext3_oms_122,	/* Optional members */
	17, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext3_122 = {
	"ext3",
	"ext3",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext3_tags_122,
	sizeof(asn_DEF_NR_ext3_tags_122)
		/sizeof(asn_DEF_NR_ext3_tags_122[0]) - 1, /* 1 */
	asn_DEF_NR_ext3_tags_122,	/* Same as above */
	sizeof(asn_DEF_NR_ext3_tags_122)
		/sizeof(asn_DEF_NR_ext3_tags_122[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext3_122,
	17,	/* Elements count */
	&asn_SPC_NR_ext3_specs_122	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext4_174[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_PDSCH_Config__ext4, pdsch_TimeDomainAllocationListForMultiPDSCH_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_MultiPDSCH_TDRA_List_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pdsch-TimeDomainAllocationListForMultiPDSCH-r17"
		},
};
static const int asn_MAP_NR_ext4_oms_174[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext4_tags_174[] = {
	(ASN_TAG_CLASS_CONTEXT | (27 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext4_tag2el_174[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* pdsch-TimeDomainAllocationListForMultiPDSCH-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext4_specs_174 = {
	sizeof(struct NR_PDSCH_Config__ext4),
	offsetof(struct NR_PDSCH_Config__ext4, _asn_ctx),
	asn_MAP_NR_ext4_tag2el_174,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext4_oms_174,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext4_174 = {
	"ext4",
	"ext4",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext4_tags_174,
	sizeof(asn_DEF_NR_ext4_tags_174)
		/sizeof(asn_DEF_NR_ext4_tags_174[0]) - 1, /* 1 */
	asn_DEF_NR_ext4_tags_174,	/* Same as above */
	sizeof(asn_DEF_NR_ext4_tags_174)
		/sizeof(asn_DEF_NR_ext4_tags_174[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext4_174,
	1,	/* Elements count */
	&asn_SPC_NR_ext4_specs_174	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_PDSCH_Config_1[] = {
	{ ATF_POINTER, 6, offsetof(struct NR_PDSCH_Config, dataScramblingIdentityPDSCH),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_dataScramblingIdentityPDSCH_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_dataScramblingIdentityPDSCH_constraint_1
		},
		0, 0, /* No default value */
		"dataScramblingIdentityPDSCH"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_PDSCH_Config, dmrs_DownlinkForPDSCH_MappingTypeA),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_DMRS_DownlinkConfig,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dmrs-DownlinkForPDSCH-MappingTypeA"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_PDSCH_Config, dmrs_DownlinkForPDSCH_MappingTypeB),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_DMRS_DownlinkConfig,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dmrs-DownlinkForPDSCH-MappingTypeB"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_PDSCH_Config, tci_StatesToAddModList),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		0,
		&asn_DEF_NR_tci_StatesToAddModList_5,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_tci_StatesToAddModList_constr_5,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_tci_StatesToAddModList_constraint_1
		},
		0, 0, /* No default value */
		"tci-StatesToAddModList"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_PDSCH_Config, tci_StatesToReleaseList),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		0,
		&asn_DEF_NR_tci_StatesToReleaseList_7,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_tci_StatesToReleaseList_constr_7,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_tci_StatesToReleaseList_constraint_1
		},
		0, 0, /* No default value */
		"tci-StatesToReleaseList"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PDSCH_Config, vrb_ToPRB_Interleaver),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_vrb_ToPRB_Interleaver_9,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"vrb-ToPRB-Interleaver"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PDSCH_Config, resourceAllocation),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_resourceAllocation_12,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"resourceAllocation"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_PDSCH_Config, pdsch_TimeDomainAllocationList),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_PDSCH_TimeDomainResourceAllocationList,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pdsch-TimeDomainAllocationList"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_PDSCH_Config, pdsch_AggregationFactor),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pdsch_AggregationFactor_17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pdsch-AggregationFactor"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_PDSCH_Config, rateMatchPatternToAddModList),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		0,
		&asn_DEF_NR_rateMatchPatternToAddModList_21,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_rateMatchPatternToAddModList_constr_21,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_rateMatchPatternToAddModList_constraint_1
		},
		0, 0, /* No default value */
		"rateMatchPatternToAddModList"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_PDSCH_Config, rateMatchPatternToReleaseList),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		0,
		&asn_DEF_NR_rateMatchPatternToReleaseList_23,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_rateMatchPatternToReleaseList_constr_23,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_rateMatchPatternToReleaseList_constraint_1
		},
		0, 0, /* No default value */
		"rateMatchPatternToReleaseList"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_PDSCH_Config, rateMatchPatternGroup1),
		(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_RateMatchPatternGroup,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"rateMatchPatternGroup1"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PDSCH_Config, rateMatchPatternGroup2),
		(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_RateMatchPatternGroup,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"rateMatchPatternGroup2"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PDSCH_Config, rbg_Size),
		(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_rbg_Size_27,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"rbg-Size"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_PDSCH_Config, mcs_Table),
		(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_mcs_Table_30,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mcs-Table"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PDSCH_Config, maxNrofCodeWordsScheduledByDCI),
		(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_maxNrofCodeWordsScheduledByDCI_33,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"maxNrofCodeWordsScheduledByDCI"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_PDSCH_Config, prb_BundlingType),
		(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_prb_BundlingType_36,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"prb-BundlingType"
		},
	{ ATF_POINTER, 11, offsetof(struct NR_PDSCH_Config, zp_CSI_RS_ResourceToAddModList),
		(ASN_TAG_CLASS_CONTEXT | (17 << 2)),
		0,
		&asn_DEF_NR_zp_CSI_RS_ResourceToAddModList_50,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_zp_CSI_RS_ResourceToAddModList_constr_50,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_zp_CSI_RS_ResourceToAddModList_constraint_1
		},
		0, 0, /* No default value */
		"zp-CSI-RS-ResourceToAddModList"
		},
	{ ATF_POINTER, 10, offsetof(struct NR_PDSCH_Config, zp_CSI_RS_ResourceToReleaseList),
		(ASN_TAG_CLASS_CONTEXT | (18 << 2)),
		0,
		&asn_DEF_NR_zp_CSI_RS_ResourceToReleaseList_52,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_zp_CSI_RS_ResourceToReleaseList_constr_52,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_zp_CSI_RS_ResourceToReleaseList_constraint_1
		},
		0, 0, /* No default value */
		"zp-CSI-RS-ResourceToReleaseList"
		},
	{ ATF_POINTER, 9, offsetof(struct NR_PDSCH_Config, aperiodic_ZP_CSI_RS_ResourceSetsToAddModList),
		(ASN_TAG_CLASS_CONTEXT | (19 << 2)),
		0,
		&asn_DEF_NR_aperiodic_ZP_CSI_RS_ResourceSetsToAddModList_54,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_aperiodic_ZP_CSI_RS_ResourceSetsToAddModList_constr_54,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_aperiodic_ZP_CSI_RS_ResourceSetsToAddModList_constraint_1
		},
		0, 0, /* No default value */
		"aperiodic-ZP-CSI-RS-ResourceSetsToAddModList"
		},
	{ ATF_POINTER, 8, offsetof(struct NR_PDSCH_Config, aperiodic_ZP_CSI_RS_ResourceSetsToReleaseList),
		(ASN_TAG_CLASS_CONTEXT | (20 << 2)),
		0,
		&asn_DEF_NR_aperiodic_ZP_CSI_RS_ResourceSetsToReleaseList_56,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_aperiodic_ZP_CSI_RS_ResourceSetsToReleaseList_constr_56,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_aperiodic_ZP_CSI_RS_ResourceSetsToReleaseList_constraint_1
		},
		0, 0, /* No default value */
		"aperiodic-ZP-CSI-RS-ResourceSetsToReleaseList"
		},
	{ ATF_POINTER, 7, offsetof(struct NR_PDSCH_Config, sp_ZP_CSI_RS_ResourceSetsToAddModList),
		(ASN_TAG_CLASS_CONTEXT | (21 << 2)),
		0,
		&asn_DEF_NR_sp_ZP_CSI_RS_ResourceSetsToAddModList_58,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_sp_ZP_CSI_RS_ResourceSetsToAddModList_constr_58,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_sp_ZP_CSI_RS_ResourceSetsToAddModList_constraint_1
		},
		0, 0, /* No default value */
		"sp-ZP-CSI-RS-ResourceSetsToAddModList"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_PDSCH_Config, sp_ZP_CSI_RS_ResourceSetsToReleaseList),
		(ASN_TAG_CLASS_CONTEXT | (22 << 2)),
		0,
		&asn_DEF_NR_sp_ZP_CSI_RS_ResourceSetsToReleaseList_60,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_sp_ZP_CSI_RS_ResourceSetsToReleaseList_constr_60,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_sp_ZP_CSI_RS_ResourceSetsToReleaseList_constraint_1
		},
		0, 0, /* No default value */
		"sp-ZP-CSI-RS-ResourceSetsToReleaseList"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_PDSCH_Config, p_ZP_CSI_RS_ResourceSet),
		(ASN_TAG_CLASS_CONTEXT | (23 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_SetupRelease_ZP_CSI_RS_ResourceSet,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"p-ZP-CSI-RS-ResourceSet"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_PDSCH_Config, ext1),
		(ASN_TAG_CLASS_CONTEXT | (24 << 2)),
		0,
		&asn_DEF_NR_ext1_64,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_PDSCH_Config, ext2),
		(ASN_TAG_CLASS_CONTEXT | (25 << 2)),
		0,
		&asn_DEF_NR_ext2_120,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext2"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_PDSCH_Config, ext3),
		(ASN_TAG_CLASS_CONTEXT | (26 << 2)),
		0,
		&asn_DEF_NR_ext3_122,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext3"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_PDSCH_Config, ext4),
		(ASN_TAG_CLASS_CONTEXT | (27 << 2)),
		0,
		&asn_DEF_NR_ext4_174,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext4"
		},
};
static const int asn_MAP_NR_PDSCH_Config_oms_1[] = { 0, 1, 2, 3, 4, 5, 7, 8, 9, 10, 11, 12, 14, 15, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27 };
static const ber_tlv_tag_t asn_DEF_NR_PDSCH_Config_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_PDSCH_Config_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* dataScramblingIdentityPDSCH */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* dmrs-DownlinkForPDSCH-MappingTypeA */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* dmrs-DownlinkForPDSCH-MappingTypeB */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* tci-StatesToAddModList */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* tci-StatesToReleaseList */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* vrb-ToPRB-Interleaver */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* resourceAllocation */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* pdsch-TimeDomainAllocationList */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* pdsch-AggregationFactor */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* rateMatchPatternToAddModList */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 }, /* rateMatchPatternToReleaseList */
    { (ASN_TAG_CLASS_CONTEXT | (11 << 2)), 11, 0, 0 }, /* rateMatchPatternGroup1 */
    { (ASN_TAG_CLASS_CONTEXT | (12 << 2)), 12, 0, 0 }, /* rateMatchPatternGroup2 */
    { (ASN_TAG_CLASS_CONTEXT | (13 << 2)), 13, 0, 0 }, /* rbg-Size */
    { (ASN_TAG_CLASS_CONTEXT | (14 << 2)), 14, 0, 0 }, /* mcs-Table */
    { (ASN_TAG_CLASS_CONTEXT | (15 << 2)), 15, 0, 0 }, /* maxNrofCodeWordsScheduledByDCI */
    { (ASN_TAG_CLASS_CONTEXT | (16 << 2)), 16, 0, 0 }, /* prb-BundlingType */
    { (ASN_TAG_CLASS_CONTEXT | (17 << 2)), 17, 0, 0 }, /* zp-CSI-RS-ResourceToAddModList */
    { (ASN_TAG_CLASS_CONTEXT | (18 << 2)), 18, 0, 0 }, /* zp-CSI-RS-ResourceToReleaseList */
    { (ASN_TAG_CLASS_CONTEXT | (19 << 2)), 19, 0, 0 }, /* aperiodic-ZP-CSI-RS-ResourceSetsToAddModList */
    { (ASN_TAG_CLASS_CONTEXT | (20 << 2)), 20, 0, 0 }, /* aperiodic-ZP-CSI-RS-ResourceSetsToReleaseList */
    { (ASN_TAG_CLASS_CONTEXT | (21 << 2)), 21, 0, 0 }, /* sp-ZP-CSI-RS-ResourceSetsToAddModList */
    { (ASN_TAG_CLASS_CONTEXT | (22 << 2)), 22, 0, 0 }, /* sp-ZP-CSI-RS-ResourceSetsToReleaseList */
    { (ASN_TAG_CLASS_CONTEXT | (23 << 2)), 23, 0, 0 }, /* p-ZP-CSI-RS-ResourceSet */
    { (ASN_TAG_CLASS_CONTEXT | (24 << 2)), 24, 0, 0 }, /* ext1 */
    { (ASN_TAG_CLASS_CONTEXT | (25 << 2)), 25, 0, 0 }, /* ext2 */
    { (ASN_TAG_CLASS_CONTEXT | (26 << 2)), 26, 0, 0 }, /* ext3 */
    { (ASN_TAG_CLASS_CONTEXT | (27 << 2)), 27, 0, 0 } /* ext4 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_PDSCH_Config_specs_1 = {
	sizeof(struct NR_PDSCH_Config),
	offsetof(struct NR_PDSCH_Config, _asn_ctx),
	asn_MAP_NR_PDSCH_Config_tag2el_1,
	28,	/* Count of tags in the map */
	asn_MAP_NR_PDSCH_Config_oms_1,	/* Optional members */
	21, 4,	/* Root/Additions */
	24,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_PDSCH_Config = {
	"PDSCH-Config",
	"PDSCH-Config",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_PDSCH_Config_tags_1,
	sizeof(asn_DEF_NR_PDSCH_Config_tags_1)
		/sizeof(asn_DEF_NR_PDSCH_Config_tags_1[0]), /* 1 */
	asn_DEF_NR_PDSCH_Config_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_PDSCH_Config_tags_1)
		/sizeof(asn_DEF_NR_PDSCH_Config_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_PDSCH_Config_1,
	28,	/* Elements count */
	&asn_SPC_NR_PDSCH_Config_specs_1	/* Additional specs */
};

