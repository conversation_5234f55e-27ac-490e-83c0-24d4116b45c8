/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_LBT_FailureRecoveryConfig_r16_H_
#define	_NR_LBT_FailureRecoveryConfig_r16_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_LBT_FailureRecoveryConfig_r16__lbt_FailureInstanceMaxCount_r16 {
	NR_LBT_FailureRecoveryConfig_r16__lbt_FailureInstanceMaxCount_r16_n4	= 0,
	NR_LBT_FailureRecoveryConfig_r16__lbt_FailureInstanceMaxCount_r16_n8	= 1,
	NR_LBT_FailureRecoveryConfig_r16__lbt_FailureInstanceMaxCount_r16_n16	= 2,
	NR_LBT_FailureRecoveryConfig_r16__lbt_FailureInstanceMaxCount_r16_n32	= 3,
	NR_LBT_FailureRecoveryConfig_r16__lbt_FailureInstanceMaxCount_r16_n64	= 4,
	NR_LBT_FailureRecoveryConfig_r16__lbt_FailureInstanceMaxCount_r16_n128	= 5
} e_NR_LBT_FailureRecoveryConfig_r16__lbt_FailureInstanceMaxCount_r16;
typedef enum NR_LBT_FailureRecoveryConfig_r16__lbt_FailureDetectionTimer_r16 {
	NR_LBT_FailureRecoveryConfig_r16__lbt_FailureDetectionTimer_r16_ms10	= 0,
	NR_LBT_FailureRecoveryConfig_r16__lbt_FailureDetectionTimer_r16_ms20	= 1,
	NR_LBT_FailureRecoveryConfig_r16__lbt_FailureDetectionTimer_r16_ms40	= 2,
	NR_LBT_FailureRecoveryConfig_r16__lbt_FailureDetectionTimer_r16_ms80	= 3,
	NR_LBT_FailureRecoveryConfig_r16__lbt_FailureDetectionTimer_r16_ms160	= 4,
	NR_LBT_FailureRecoveryConfig_r16__lbt_FailureDetectionTimer_r16_ms320	= 5
} e_NR_LBT_FailureRecoveryConfig_r16__lbt_FailureDetectionTimer_r16;

/* NR_LBT-FailureRecoveryConfig-r16 */
typedef struct NR_LBT_FailureRecoveryConfig_r16 {
	long	 lbt_FailureInstanceMaxCount_r16;
	long	 lbt_FailureDetectionTimer_r16;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_LBT_FailureRecoveryConfig_r16_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_lbt_FailureInstanceMaxCount_r16_2;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_lbt_FailureDetectionTimer_r16_9;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_LBT_FailureRecoveryConfig_r16;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_LBT_FailureRecoveryConfig_r16_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_LBT_FailureRecoveryConfig_r16_1[2];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_LBT_FailureRecoveryConfig_r16_H_ */
#include <asn_internal.h>
