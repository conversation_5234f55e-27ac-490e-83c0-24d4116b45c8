/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_MsgA-DMRS-Config-r16.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_msgA_PUSCH_DMRS_CDM_Group_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 1L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_msgA_PUSCH_NrofPorts_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 1L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_msgA_ScramblingID0_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 65535L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_msgA_ScramblingID1_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 65535L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_msgA_DMRS_AdditionalPosition_r16_constr_2 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_msgA_MaxLength_r16_constr_6 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_msgA_PUSCH_DMRS_CDM_Group_r16_constr_8 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_msgA_PUSCH_NrofPorts_r16_constr_9 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_msgA_ScramblingID0_r16_constr_10 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (0..65535) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_msgA_ScramblingID1_r16_constr_11 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (0..65535) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_msgA_DMRS_AdditionalPosition_r16_value2enum_2[] = {
	{ 0,	4,	"pos0" },
	{ 1,	4,	"pos1" },
	{ 2,	4,	"pos3" }
};
static const unsigned int asn_MAP_NR_msgA_DMRS_AdditionalPosition_r16_enum2value_2[] = {
	0,	/* pos0(0) */
	1,	/* pos1(1) */
	2	/* pos3(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_msgA_DMRS_AdditionalPosition_r16_specs_2 = {
	asn_MAP_NR_msgA_DMRS_AdditionalPosition_r16_value2enum_2,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_msgA_DMRS_AdditionalPosition_r16_enum2value_2,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_msgA_DMRS_AdditionalPosition_r16_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_msgA_DMRS_AdditionalPosition_r16_2 = {
	"msgA-DMRS-AdditionalPosition-r16",
	"msgA-DMRS-AdditionalPosition-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_msgA_DMRS_AdditionalPosition_r16_tags_2,
	sizeof(asn_DEF_NR_msgA_DMRS_AdditionalPosition_r16_tags_2)
		/sizeof(asn_DEF_NR_msgA_DMRS_AdditionalPosition_r16_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_msgA_DMRS_AdditionalPosition_r16_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_msgA_DMRS_AdditionalPosition_r16_tags_2)
		/sizeof(asn_DEF_NR_msgA_DMRS_AdditionalPosition_r16_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_msgA_DMRS_AdditionalPosition_r16_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_msgA_DMRS_AdditionalPosition_r16_specs_2	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_msgA_MaxLength_r16_value2enum_6[] = {
	{ 0,	4,	"len2" }
};
static const unsigned int asn_MAP_NR_msgA_MaxLength_r16_enum2value_6[] = {
	0	/* len2(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_msgA_MaxLength_r16_specs_6 = {
	asn_MAP_NR_msgA_MaxLength_r16_value2enum_6,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_msgA_MaxLength_r16_enum2value_6,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_msgA_MaxLength_r16_tags_6[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_msgA_MaxLength_r16_6 = {
	"msgA-MaxLength-r16",
	"msgA-MaxLength-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_msgA_MaxLength_r16_tags_6,
	sizeof(asn_DEF_NR_msgA_MaxLength_r16_tags_6)
		/sizeof(asn_DEF_NR_msgA_MaxLength_r16_tags_6[0]) - 1, /* 1 */
	asn_DEF_NR_msgA_MaxLength_r16_tags_6,	/* Same as above */
	sizeof(asn_DEF_NR_msgA_MaxLength_r16_tags_6)
		/sizeof(asn_DEF_NR_msgA_MaxLength_r16_tags_6[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_msgA_MaxLength_r16_constr_6,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_msgA_MaxLength_r16_specs_6	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_MsgA_DMRS_Config_r16_1[] = {
	{ ATF_POINTER, 6, offsetof(struct NR_MsgA_DMRS_Config_r16, msgA_DMRS_AdditionalPosition_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_msgA_DMRS_AdditionalPosition_r16_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"msgA-DMRS-AdditionalPosition-r16"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_MsgA_DMRS_Config_r16, msgA_MaxLength_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_msgA_MaxLength_r16_6,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"msgA-MaxLength-r16"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_MsgA_DMRS_Config_r16, msgA_PUSCH_DMRS_CDM_Group_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_msgA_PUSCH_DMRS_CDM_Group_r16_constr_8,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_msgA_PUSCH_DMRS_CDM_Group_r16_constraint_1
		},
		0, 0, /* No default value */
		"msgA-PUSCH-DMRS-CDM-Group-r16"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_MsgA_DMRS_Config_r16, msgA_PUSCH_NrofPorts_r16),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_msgA_PUSCH_NrofPorts_r16_constr_9,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_msgA_PUSCH_NrofPorts_r16_constraint_1
		},
		0, 0, /* No default value */
		"msgA-PUSCH-NrofPorts-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_MsgA_DMRS_Config_r16, msgA_ScramblingID0_r16),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_msgA_ScramblingID0_r16_constr_10,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_msgA_ScramblingID0_r16_constraint_1
		},
		0, 0, /* No default value */
		"msgA-ScramblingID0-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MsgA_DMRS_Config_r16, msgA_ScramblingID1_r16),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_msgA_ScramblingID1_r16_constr_11,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_msgA_ScramblingID1_r16_constraint_1
		},
		0, 0, /* No default value */
		"msgA-ScramblingID1-r16"
		},
};
static const int asn_MAP_NR_MsgA_DMRS_Config_r16_oms_1[] = { 0, 1, 2, 3, 4, 5 };
static const ber_tlv_tag_t asn_DEF_NR_MsgA_DMRS_Config_r16_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_MsgA_DMRS_Config_r16_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* msgA-DMRS-AdditionalPosition-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* msgA-MaxLength-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* msgA-PUSCH-DMRS-CDM-Group-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* msgA-PUSCH-NrofPorts-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* msgA-ScramblingID0-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 } /* msgA-ScramblingID1-r16 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_MsgA_DMRS_Config_r16_specs_1 = {
	sizeof(struct NR_MsgA_DMRS_Config_r16),
	offsetof(struct NR_MsgA_DMRS_Config_r16, _asn_ctx),
	asn_MAP_NR_MsgA_DMRS_Config_r16_tag2el_1,
	6,	/* Count of tags in the map */
	asn_MAP_NR_MsgA_DMRS_Config_r16_oms_1,	/* Optional members */
	6, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_MsgA_DMRS_Config_r16 = {
	"MsgA-DMRS-Config-r16",
	"MsgA-DMRS-Config-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_MsgA_DMRS_Config_r16_tags_1,
	sizeof(asn_DEF_NR_MsgA_DMRS_Config_r16_tags_1)
		/sizeof(asn_DEF_NR_MsgA_DMRS_Config_r16_tags_1[0]), /* 1 */
	asn_DEF_NR_MsgA_DMRS_Config_r16_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_MsgA_DMRS_Config_r16_tags_1)
		/sizeof(asn_DEF_NR_MsgA_DMRS_Config_r16_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_MsgA_DMRS_Config_r16_1,
	6,	/* Elements count */
	&asn_SPC_NR_MsgA_DMRS_Config_r16_specs_1	/* Additional specs */
};

