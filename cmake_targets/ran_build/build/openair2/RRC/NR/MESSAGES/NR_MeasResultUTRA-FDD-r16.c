/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_MeasResultUTRA-FDD-r16.h"

static int
memb_NR_utra_FDD_RSCP_r16_constraint_3(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= -5L && value <= 91L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_utra_FDD_EcN0_r16_constraint_3(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 49L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_utra_FDD_RSCP_r16_constr_4 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 7,  7, -5,  91 }	/* (-5..91) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_utra_FDD_EcN0_r16_constr_5 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 6,  6,  0,  49 }	/* (0..49) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static asn_TYPE_member_t asn_MBR_NR_measResult_r16_3[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_MeasResultUTRA_FDD_r16__measResult_r16, utra_FDD_RSCP_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_utra_FDD_RSCP_r16_constr_4,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_utra_FDD_RSCP_r16_constraint_3
		},
		0, 0, /* No default value */
		"utra-FDD-RSCP-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MeasResultUTRA_FDD_r16__measResult_r16, utra_FDD_EcN0_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_utra_FDD_EcN0_r16_constr_5,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_utra_FDD_EcN0_r16_constraint_3
		},
		0, 0, /* No default value */
		"utra-FDD-EcN0-r16"
		},
};
static const int asn_MAP_NR_measResult_r16_oms_3[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_measResult_r16_tags_3[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_measResult_r16_tag2el_3[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* utra-FDD-RSCP-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* utra-FDD-EcN0-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_measResult_r16_specs_3 = {
	sizeof(struct NR_MeasResultUTRA_FDD_r16__measResult_r16),
	offsetof(struct NR_MeasResultUTRA_FDD_r16__measResult_r16, _asn_ctx),
	asn_MAP_NR_measResult_r16_tag2el_3,
	2,	/* Count of tags in the map */
	asn_MAP_NR_measResult_r16_oms_3,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_measResult_r16_3 = {
	"measResult-r16",
	"measResult-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_measResult_r16_tags_3,
	sizeof(asn_DEF_NR_measResult_r16_tags_3)
		/sizeof(asn_DEF_NR_measResult_r16_tags_3[0]) - 1, /* 1 */
	asn_DEF_NR_measResult_r16_tags_3,	/* Same as above */
	sizeof(asn_DEF_NR_measResult_r16_tags_3)
		/sizeof(asn_DEF_NR_measResult_r16_tags_3[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_measResult_r16_3,
	2,	/* Elements count */
	&asn_SPC_NR_measResult_r16_specs_3	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_MeasResultUTRA_FDD_r16_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_MeasResultUTRA_FDD_r16, physCellId_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_PhysCellIdUTRA_FDD_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"physCellId-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_MeasResultUTRA_FDD_r16, measResult_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_measResult_r16_3,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"measResult-r16"
		},
};
static const ber_tlv_tag_t asn_DEF_NR_MeasResultUTRA_FDD_r16_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_MeasResultUTRA_FDD_r16_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* physCellId-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* measResult-r16 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_MeasResultUTRA_FDD_r16_specs_1 = {
	sizeof(struct NR_MeasResultUTRA_FDD_r16),
	offsetof(struct NR_MeasResultUTRA_FDD_r16, _asn_ctx),
	asn_MAP_NR_MeasResultUTRA_FDD_r16_tag2el_1,
	2,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_MeasResultUTRA_FDD_r16 = {
	"MeasResultUTRA-FDD-r16",
	"MeasResultUTRA-FDD-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_MeasResultUTRA_FDD_r16_tags_1,
	sizeof(asn_DEF_NR_MeasResultUTRA_FDD_r16_tags_1)
		/sizeof(asn_DEF_NR_MeasResultUTRA_FDD_r16_tags_1[0]), /* 1 */
	asn_DEF_NR_MeasResultUTRA_FDD_r16_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_MeasResultUTRA_FDD_r16_tags_1)
		/sizeof(asn_DEF_NR_MeasResultUTRA_FDD_r16_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_MeasResultUTRA_FDD_r16_1,
	2,	/* Elements count */
	&asn_SPC_NR_MeasResultUTRA_FDD_r16_specs_1	/* Additional specs */
};

