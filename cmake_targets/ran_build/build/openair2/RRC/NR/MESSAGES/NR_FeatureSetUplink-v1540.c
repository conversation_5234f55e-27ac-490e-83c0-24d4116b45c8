/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_FeatureSetUplink-v1540.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_zeroSlotOffsetAperiodicSRS_constr_2 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pa_PhaseDiscontinuityImpacts_constr_4 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pusch_SeparationWithGap_constr_6 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ul_MCS_TableAlt_DynamicIndication_constr_12 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_zeroSlotOffsetAperiodicSRS_value2enum_2[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_zeroSlotOffsetAperiodicSRS_enum2value_2[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_zeroSlotOffsetAperiodicSRS_specs_2 = {
	asn_MAP_NR_zeroSlotOffsetAperiodicSRS_value2enum_2,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_zeroSlotOffsetAperiodicSRS_enum2value_2,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_zeroSlotOffsetAperiodicSRS_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_zeroSlotOffsetAperiodicSRS_2 = {
	"zeroSlotOffsetAperiodicSRS",
	"zeroSlotOffsetAperiodicSRS",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_zeroSlotOffsetAperiodicSRS_tags_2,
	sizeof(asn_DEF_NR_zeroSlotOffsetAperiodicSRS_tags_2)
		/sizeof(asn_DEF_NR_zeroSlotOffsetAperiodicSRS_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_zeroSlotOffsetAperiodicSRS_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_zeroSlotOffsetAperiodicSRS_tags_2)
		/sizeof(asn_DEF_NR_zeroSlotOffsetAperiodicSRS_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_zeroSlotOffsetAperiodicSRS_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_zeroSlotOffsetAperiodicSRS_specs_2	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pa_PhaseDiscontinuityImpacts_value2enum_4[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_pa_PhaseDiscontinuityImpacts_enum2value_4[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pa_PhaseDiscontinuityImpacts_specs_4 = {
	asn_MAP_NR_pa_PhaseDiscontinuityImpacts_value2enum_4,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pa_PhaseDiscontinuityImpacts_enum2value_4,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pa_PhaseDiscontinuityImpacts_tags_4[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pa_PhaseDiscontinuityImpacts_4 = {
	"pa-PhaseDiscontinuityImpacts",
	"pa-PhaseDiscontinuityImpacts",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pa_PhaseDiscontinuityImpacts_tags_4,
	sizeof(asn_DEF_NR_pa_PhaseDiscontinuityImpacts_tags_4)
		/sizeof(asn_DEF_NR_pa_PhaseDiscontinuityImpacts_tags_4[0]) - 1, /* 1 */
	asn_DEF_NR_pa_PhaseDiscontinuityImpacts_tags_4,	/* Same as above */
	sizeof(asn_DEF_NR_pa_PhaseDiscontinuityImpacts_tags_4)
		/sizeof(asn_DEF_NR_pa_PhaseDiscontinuityImpacts_tags_4[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pa_PhaseDiscontinuityImpacts_constr_4,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pa_PhaseDiscontinuityImpacts_specs_4	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pusch_SeparationWithGap_value2enum_6[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_pusch_SeparationWithGap_enum2value_6[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pusch_SeparationWithGap_specs_6 = {
	asn_MAP_NR_pusch_SeparationWithGap_value2enum_6,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pusch_SeparationWithGap_enum2value_6,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pusch_SeparationWithGap_tags_6[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pusch_SeparationWithGap_6 = {
	"pusch-SeparationWithGap",
	"pusch-SeparationWithGap",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pusch_SeparationWithGap_tags_6,
	sizeof(asn_DEF_NR_pusch_SeparationWithGap_tags_6)
		/sizeof(asn_DEF_NR_pusch_SeparationWithGap_tags_6[0]) - 1, /* 1 */
	asn_DEF_NR_pusch_SeparationWithGap_tags_6,	/* Same as above */
	sizeof(asn_DEF_NR_pusch_SeparationWithGap_tags_6)
		/sizeof(asn_DEF_NR_pusch_SeparationWithGap_tags_6[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pusch_SeparationWithGap_constr_6,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pusch_SeparationWithGap_specs_6	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_pusch_ProcessingType2_8[] = {
	{ ATF_POINTER, 3, offsetof(struct NR_FeatureSetUplink_v1540__pusch_ProcessingType2, scs_15kHz),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ProcessingParameters,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs-15kHz"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_FeatureSetUplink_v1540__pusch_ProcessingType2, scs_30kHz),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ProcessingParameters,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs-30kHz"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_FeatureSetUplink_v1540__pusch_ProcessingType2, scs_60kHz),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ProcessingParameters,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs-60kHz"
		},
};
static const int asn_MAP_NR_pusch_ProcessingType2_oms_8[] = { 0, 1, 2 };
static const ber_tlv_tag_t asn_DEF_NR_pusch_ProcessingType2_tags_8[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_pusch_ProcessingType2_tag2el_8[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* scs-15kHz */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* scs-30kHz */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* scs-60kHz */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_pusch_ProcessingType2_specs_8 = {
	sizeof(struct NR_FeatureSetUplink_v1540__pusch_ProcessingType2),
	offsetof(struct NR_FeatureSetUplink_v1540__pusch_ProcessingType2, _asn_ctx),
	asn_MAP_NR_pusch_ProcessingType2_tag2el_8,
	3,	/* Count of tags in the map */
	asn_MAP_NR_pusch_ProcessingType2_oms_8,	/* Optional members */
	3, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pusch_ProcessingType2_8 = {
	"pusch-ProcessingType2",
	"pusch-ProcessingType2",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_pusch_ProcessingType2_tags_8,
	sizeof(asn_DEF_NR_pusch_ProcessingType2_tags_8)
		/sizeof(asn_DEF_NR_pusch_ProcessingType2_tags_8[0]) - 1, /* 1 */
	asn_DEF_NR_pusch_ProcessingType2_tags_8,	/* Same as above */
	sizeof(asn_DEF_NR_pusch_ProcessingType2_tags_8)
		/sizeof(asn_DEF_NR_pusch_ProcessingType2_tags_8[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_pusch_ProcessingType2_8,
	3,	/* Elements count */
	&asn_SPC_NR_pusch_ProcessingType2_specs_8	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_ul_MCS_TableAlt_DynamicIndication_value2enum_12[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_ul_MCS_TableAlt_DynamicIndication_enum2value_12[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ul_MCS_TableAlt_DynamicIndication_specs_12 = {
	asn_MAP_NR_ul_MCS_TableAlt_DynamicIndication_value2enum_12,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ul_MCS_TableAlt_DynamicIndication_enum2value_12,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ul_MCS_TableAlt_DynamicIndication_tags_12[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ul_MCS_TableAlt_DynamicIndication_12 = {
	"ul-MCS-TableAlt-DynamicIndication",
	"ul-MCS-TableAlt-DynamicIndication",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ul_MCS_TableAlt_DynamicIndication_tags_12,
	sizeof(asn_DEF_NR_ul_MCS_TableAlt_DynamicIndication_tags_12)
		/sizeof(asn_DEF_NR_ul_MCS_TableAlt_DynamicIndication_tags_12[0]) - 1, /* 1 */
	asn_DEF_NR_ul_MCS_TableAlt_DynamicIndication_tags_12,	/* Same as above */
	sizeof(asn_DEF_NR_ul_MCS_TableAlt_DynamicIndication_tags_12)
		/sizeof(asn_DEF_NR_ul_MCS_TableAlt_DynamicIndication_tags_12[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ul_MCS_TableAlt_DynamicIndication_constr_12,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ul_MCS_TableAlt_DynamicIndication_specs_12	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_FeatureSetUplink_v1540_1[] = {
	{ ATF_POINTER, 5, offsetof(struct NR_FeatureSetUplink_v1540, zeroSlotOffsetAperiodicSRS),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_zeroSlotOffsetAperiodicSRS_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"zeroSlotOffsetAperiodicSRS"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_FeatureSetUplink_v1540, pa_PhaseDiscontinuityImpacts),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pa_PhaseDiscontinuityImpacts_4,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pa-PhaseDiscontinuityImpacts"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_FeatureSetUplink_v1540, pusch_SeparationWithGap),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pusch_SeparationWithGap_6,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pusch-SeparationWithGap"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_FeatureSetUplink_v1540, pusch_ProcessingType2),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		0,
		&asn_DEF_NR_pusch_ProcessingType2_8,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pusch-ProcessingType2"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_FeatureSetUplink_v1540, ul_MCS_TableAlt_DynamicIndication),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ul_MCS_TableAlt_DynamicIndication_12,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ul-MCS-TableAlt-DynamicIndication"
		},
};
static const int asn_MAP_NR_FeatureSetUplink_v1540_oms_1[] = { 0, 1, 2, 3, 4 };
static const ber_tlv_tag_t asn_DEF_NR_FeatureSetUplink_v1540_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_FeatureSetUplink_v1540_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* zeroSlotOffsetAperiodicSRS */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* pa-PhaseDiscontinuityImpacts */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* pusch-SeparationWithGap */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* pusch-ProcessingType2 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 } /* ul-MCS-TableAlt-DynamicIndication */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_FeatureSetUplink_v1540_specs_1 = {
	sizeof(struct NR_FeatureSetUplink_v1540),
	offsetof(struct NR_FeatureSetUplink_v1540, _asn_ctx),
	asn_MAP_NR_FeatureSetUplink_v1540_tag2el_1,
	5,	/* Count of tags in the map */
	asn_MAP_NR_FeatureSetUplink_v1540_oms_1,	/* Optional members */
	5, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_FeatureSetUplink_v1540 = {
	"FeatureSetUplink-v1540",
	"FeatureSetUplink-v1540",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_FeatureSetUplink_v1540_tags_1,
	sizeof(asn_DEF_NR_FeatureSetUplink_v1540_tags_1)
		/sizeof(asn_DEF_NR_FeatureSetUplink_v1540_tags_1[0]), /* 1 */
	asn_DEF_NR_FeatureSetUplink_v1540_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_FeatureSetUplink_v1540_tags_1)
		/sizeof(asn_DEF_NR_FeatureSetUplink_v1540_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_FeatureSetUplink_v1540_1,
	5,	/* Elements count */
	&asn_SPC_NR_FeatureSetUplink_v1540_specs_1	/* Additional specs */
};

