/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_MsgA_PUSCH_Resource_r16_H_
#define	_NR_MsgA_PUSCH_Resource_r16_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include <NativeEnumerated.h>
#include <BIT_STRING.h>
#include "NR_MsgA-DMRS-Config-r16.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_MsgA_PUSCH_Resource_r16__nrofMsgA_PO_PerSlot_r16 {
	NR_MsgA_PUSCH_Resource_r16__nrofMsgA_PO_PerSlot_r16_one	= 0,
	NR_MsgA_PUSCH_Resource_r16__nrofMsgA_PO_PerSlot_r16_two	= 1,
	NR_MsgA_PUSCH_Resource_r16__nrofMsgA_PO_PerSlot_r16_three	= 2,
	NR_MsgA_PUSCH_Resource_r16__nrofMsgA_PO_PerSlot_r16_six	= 3
} e_NR_MsgA_PUSCH_Resource_r16__nrofMsgA_PO_PerSlot_r16;
typedef enum NR_MsgA_PUSCH_Resource_r16__mappingTypeMsgA_PUSCH_r16 {
	NR_MsgA_PUSCH_Resource_r16__mappingTypeMsgA_PUSCH_r16_typeA	= 0,
	NR_MsgA_PUSCH_Resource_r16__mappingTypeMsgA_PUSCH_r16_typeB	= 1
} e_NR_MsgA_PUSCH_Resource_r16__mappingTypeMsgA_PUSCH_r16;
typedef enum NR_MsgA_PUSCH_Resource_r16__nrofMsgA_PO_FDM_r16 {
	NR_MsgA_PUSCH_Resource_r16__nrofMsgA_PO_FDM_r16_one	= 0,
	NR_MsgA_PUSCH_Resource_r16__nrofMsgA_PO_FDM_r16_two	= 1,
	NR_MsgA_PUSCH_Resource_r16__nrofMsgA_PO_FDM_r16_four	= 2,
	NR_MsgA_PUSCH_Resource_r16__nrofMsgA_PO_FDM_r16_eight	= 3
} e_NR_MsgA_PUSCH_Resource_r16__nrofMsgA_PO_FDM_r16;
typedef enum NR_MsgA_PUSCH_Resource_r16__msgA_IntraSlotFrequencyHopping_r16 {
	NR_MsgA_PUSCH_Resource_r16__msgA_IntraSlotFrequencyHopping_r16_enabled	= 0
} e_NR_MsgA_PUSCH_Resource_r16__msgA_IntraSlotFrequencyHopping_r16;
typedef enum NR_MsgA_PUSCH_Resource_r16__msgA_Alpha_r16 {
	NR_MsgA_PUSCH_Resource_r16__msgA_Alpha_r16_alpha0	= 0,
	NR_MsgA_PUSCH_Resource_r16__msgA_Alpha_r16_alpha04	= 1,
	NR_MsgA_PUSCH_Resource_r16__msgA_Alpha_r16_alpha05	= 2,
	NR_MsgA_PUSCH_Resource_r16__msgA_Alpha_r16_alpha06	= 3,
	NR_MsgA_PUSCH_Resource_r16__msgA_Alpha_r16_alpha07	= 4,
	NR_MsgA_PUSCH_Resource_r16__msgA_Alpha_r16_alpha08	= 5,
	NR_MsgA_PUSCH_Resource_r16__msgA_Alpha_r16_alpha09	= 6,
	NR_MsgA_PUSCH_Resource_r16__msgA_Alpha_r16_alpha1	= 7
} e_NR_MsgA_PUSCH_Resource_r16__msgA_Alpha_r16;

/* NR_MsgA-PUSCH-Resource-r16 */
typedef struct NR_MsgA_PUSCH_Resource_r16 {
	long	 msgA_MCS_r16;
	long	 nrofSlotsMsgA_PUSCH_r16;
	long	 nrofMsgA_PO_PerSlot_r16;
	long	 msgA_PUSCH_TimeDomainOffset_r16;
	long	*msgA_PUSCH_TimeDomainAllocation_r16;	/* OPTIONAL */
	long	*startSymbolAndLengthMsgA_PO_r16;	/* OPTIONAL */
	long	*mappingTypeMsgA_PUSCH_r16;	/* OPTIONAL */
	long	*guardPeriodMsgA_PUSCH_r16;	/* OPTIONAL */
	long	 guardBandMsgA_PUSCH_r16;
	long	 frequencyStartMsgA_PUSCH_r16;
	long	 nrofPRBs_PerMsgA_PO_r16;
	long	 nrofMsgA_PO_FDM_r16;
	long	*msgA_IntraSlotFrequencyHopping_r16;	/* OPTIONAL */
	BIT_STRING_t	*msgA_HoppingBits_r16;	/* OPTIONAL */
	NR_MsgA_DMRS_Config_r16_t	 msgA_DMRS_Config_r16;
	long	 nrofDMRS_Sequences_r16;
	long	*msgA_Alpha_r16;	/* OPTIONAL */
	long	*interlaceIndexFirstPO_MsgA_PUSCH_r16;	/* OPTIONAL */
	long	*nrofInterlacesPerMsgA_PO_r16;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_MsgA_PUSCH_Resource_r16_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_nrofMsgA_PO_PerSlot_r16_4;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mappingTypeMsgA_PUSCH_r16_12;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_nrofMsgA_PO_FDM_r16_19;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_msgA_IntraSlotFrequencyHopping_r16_24;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_msgA_Alpha_r16_29;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_MsgA_PUSCH_Resource_r16;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_MsgA_PUSCH_Resource_r16_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_MsgA_PUSCH_Resource_r16_1[19];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_MsgA_PUSCH_Resource_r16_H_ */
#include <asn_internal.h>
