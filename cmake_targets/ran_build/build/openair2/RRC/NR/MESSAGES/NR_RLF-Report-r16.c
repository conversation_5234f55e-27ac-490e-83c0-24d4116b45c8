/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_RLF-Report-r16.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_csi_rsRLMConfigBitmap_v1650_constraint_42(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	const BIT_STRING_t *st = (const BIT_STRING_t *)sptr;
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	if(st->size > 0) {
		/* Size in bits */
		size = 8 * st->size - (st->bits_unused & 0x07);
	} else {
		size = 0;
	}
	
	if((size == 96UL)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_timeConnFailure_r16_constraint_2(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 1023L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_previousPCellId_r16_constr_8 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_nrFailedPCellId_r16_constr_12 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_eutraFailedPCellId_r16_constr_15 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_failedPCellId_r16_constr_11 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_reconnectCellId_r16_constr_18 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_connectionFailureType_r16_constr_25 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_rlf_Cause_r16_constr_28 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  7 }	/* (0..7) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_noSuitableCellFound_r16_constr_38 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_csi_rsRLMConfigBitmap_v1650_constr_43 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 0,  0,  96,  96 }	/* (SIZE(96..96)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_lastHO_Type_r17_constr_45 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_choCellId_r17_constr_52 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_timeConnFailure_r16_constr_23 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 10,  10,  0,  1023 }	/* (0..1023) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_NR_RLF_Report_r16_constr_1 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static asn_TYPE_member_t asn_MBR_NR_measResultNeighCells_r16_4[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__measResultNeighCells_r16, measResultListNR_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_MeasResultList2NR_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"measResultListNR-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__measResultNeighCells_r16, measResultListEUTRA_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_MeasResultList2EUTRA_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"measResultListEUTRA-r16"
		},
};
static const int asn_MAP_NR_measResultNeighCells_r16_oms_4[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_measResultNeighCells_r16_tags_4[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_measResultNeighCells_r16_tag2el_4[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* measResultListNR-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* measResultListEUTRA-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_measResultNeighCells_r16_specs_4 = {
	sizeof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__measResultNeighCells_r16),
	offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__measResultNeighCells_r16, _asn_ctx),
	asn_MAP_NR_measResultNeighCells_r16_tag2el_4,
	2,	/* Count of tags in the map */
	asn_MAP_NR_measResultNeighCells_r16_oms_4,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_measResultNeighCells_r16_4 = {
	"measResultNeighCells-r16",
	"measResultNeighCells-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_measResultNeighCells_r16_tags_4,
	sizeof(asn_DEF_NR_measResultNeighCells_r16_tags_4)
		/sizeof(asn_DEF_NR_measResultNeighCells_r16_tags_4[0]) - 1, /* 1 */
	asn_DEF_NR_measResultNeighCells_r16_tags_4,	/* Same as above */
	sizeof(asn_DEF_NR_measResultNeighCells_r16_tags_4)
		/sizeof(asn_DEF_NR_measResultNeighCells_r16_tags_4[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_measResultNeighCells_r16_4,
	2,	/* Elements count */
	&asn_SPC_NR_measResultNeighCells_r16_specs_4	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_previousPCellId_r16_8[] = {
	{ ATF_POINTER, 0, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__previousPCellId_r16, choice.nrPreviousCell_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_CGI_Info_Logging_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"nrPreviousCell-r16"
		},
	{ ATF_POINTER, 0, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__previousPCellId_r16, choice.eutraPreviousCell_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_CGI_InfoEUTRALogging,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"eutraPreviousCell-r16"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_NR_previousPCellId_r16_tag2el_8[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* nrPreviousCell-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* eutraPreviousCell-r16 */
};
static asn_CHOICE_specifics_t asn_SPC_NR_previousPCellId_r16_specs_8 = {
	sizeof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__previousPCellId_r16),
	offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__previousPCellId_r16, _asn_ctx),
	offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__previousPCellId_r16, present),
	sizeof(((struct NR_RLF_Report_r16__nr_RLF_Report_r16__previousPCellId_r16 *)0)->present),
	asn_MAP_NR_previousPCellId_r16_tag2el_8,
	2,	/* Count of tags in the map */
	0, 0,
	-1	/* Extensions start */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_previousPCellId_r16_8 = {
	"previousPCellId-r16",
	"previousPCellId-r16",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_previousPCellId_r16_constr_8,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		CHOICE_constraint
	},
	asn_MBR_NR_previousPCellId_r16_8,
	2,	/* Elements count */
	&asn_SPC_NR_previousPCellId_r16_specs_8	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_nrFailedPCellId_r16_12[] = {
	{ ATF_POINTER, 0, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__failedPCellId_r16__nrFailedPCellId_r16, choice.cellGlobalId_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_CGI_Info_Logging_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"cellGlobalId-r16"
		},
	{ ATF_POINTER, 0, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__failedPCellId_r16__nrFailedPCellId_r16, choice.pci_arfcn_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_PCI_ARFCN_NR_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pci-arfcn-r16"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_NR_nrFailedPCellId_r16_tag2el_12[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* cellGlobalId-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* pci-arfcn-r16 */
};
static asn_CHOICE_specifics_t asn_SPC_NR_nrFailedPCellId_r16_specs_12 = {
	sizeof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__failedPCellId_r16__nrFailedPCellId_r16),
	offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__failedPCellId_r16__nrFailedPCellId_r16, _asn_ctx),
	offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__failedPCellId_r16__nrFailedPCellId_r16, present),
	sizeof(((struct NR_RLF_Report_r16__nr_RLF_Report_r16__failedPCellId_r16__nrFailedPCellId_r16 *)0)->present),
	asn_MAP_NR_nrFailedPCellId_r16_tag2el_12,
	2,	/* Count of tags in the map */
	0, 0,
	-1	/* Extensions start */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_nrFailedPCellId_r16_12 = {
	"nrFailedPCellId-r16",
	"nrFailedPCellId-r16",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_nrFailedPCellId_r16_constr_12,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		CHOICE_constraint
	},
	asn_MBR_NR_nrFailedPCellId_r16_12,
	2,	/* Elements count */
	&asn_SPC_NR_nrFailedPCellId_r16_specs_12	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_eutraFailedPCellId_r16_15[] = {
	{ ATF_POINTER, 0, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__failedPCellId_r16__eutraFailedPCellId_r16, choice.cellGlobalId_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_CGI_InfoEUTRALogging,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"cellGlobalId-r16"
		},
	{ ATF_POINTER, 0, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__failedPCellId_r16__eutraFailedPCellId_r16, choice.pci_arfcn_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_PCI_ARFCN_EUTRA_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pci-arfcn-r16"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_NR_eutraFailedPCellId_r16_tag2el_15[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* cellGlobalId-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* pci-arfcn-r16 */
};
static asn_CHOICE_specifics_t asn_SPC_NR_eutraFailedPCellId_r16_specs_15 = {
	sizeof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__failedPCellId_r16__eutraFailedPCellId_r16),
	offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__failedPCellId_r16__eutraFailedPCellId_r16, _asn_ctx),
	offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__failedPCellId_r16__eutraFailedPCellId_r16, present),
	sizeof(((struct NR_RLF_Report_r16__nr_RLF_Report_r16__failedPCellId_r16__eutraFailedPCellId_r16 *)0)->present),
	asn_MAP_NR_eutraFailedPCellId_r16_tag2el_15,
	2,	/* Count of tags in the map */
	0, 0,
	-1	/* Extensions start */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_eutraFailedPCellId_r16_15 = {
	"eutraFailedPCellId-r16",
	"eutraFailedPCellId-r16",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_eutraFailedPCellId_r16_constr_15,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		CHOICE_constraint
	},
	asn_MBR_NR_eutraFailedPCellId_r16_15,
	2,	/* Elements count */
	&asn_SPC_NR_eutraFailedPCellId_r16_specs_15	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_failedPCellId_r16_11[] = {
	{ ATF_POINTER, 0, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__failedPCellId_r16, choice.nrFailedPCellId_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_nrFailedPCellId_r16_12,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"nrFailedPCellId-r16"
		},
	{ ATF_POINTER, 0, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__failedPCellId_r16, choice.eutraFailedPCellId_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_eutraFailedPCellId_r16_15,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"eutraFailedPCellId-r16"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_NR_failedPCellId_r16_tag2el_11[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* nrFailedPCellId-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* eutraFailedPCellId-r16 */
};
static asn_CHOICE_specifics_t asn_SPC_NR_failedPCellId_r16_specs_11 = {
	sizeof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__failedPCellId_r16),
	offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__failedPCellId_r16, _asn_ctx),
	offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__failedPCellId_r16, present),
	sizeof(((struct NR_RLF_Report_r16__nr_RLF_Report_r16__failedPCellId_r16 *)0)->present),
	asn_MAP_NR_failedPCellId_r16_tag2el_11,
	2,	/* Count of tags in the map */
	0, 0,
	-1	/* Extensions start */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_failedPCellId_r16_11 = {
	"failedPCellId-r16",
	"failedPCellId-r16",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_failedPCellId_r16_constr_11,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		CHOICE_constraint
	},
	asn_MBR_NR_failedPCellId_r16_11,
	2,	/* Elements count */
	&asn_SPC_NR_failedPCellId_r16_specs_11	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_reconnectCellId_r16_18[] = {
	{ ATF_POINTER, 0, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__reconnectCellId_r16, choice.nrReconnectCellId_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_CGI_Info_Logging_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"nrReconnectCellId-r16"
		},
	{ ATF_POINTER, 0, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__reconnectCellId_r16, choice.eutraReconnectCellId_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_CGI_InfoEUTRALogging,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"eutraReconnectCellId-r16"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_NR_reconnectCellId_r16_tag2el_18[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* nrReconnectCellId-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* eutraReconnectCellId-r16 */
};
static asn_CHOICE_specifics_t asn_SPC_NR_reconnectCellId_r16_specs_18 = {
	sizeof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__reconnectCellId_r16),
	offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__reconnectCellId_r16, _asn_ctx),
	offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__reconnectCellId_r16, present),
	sizeof(((struct NR_RLF_Report_r16__nr_RLF_Report_r16__reconnectCellId_r16 *)0)->present),
	asn_MAP_NR_reconnectCellId_r16_tag2el_18,
	2,	/* Count of tags in the map */
	0, 0,
	-1	/* Extensions start */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_reconnectCellId_r16_18 = {
	"reconnectCellId-r16",
	"reconnectCellId-r16",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_reconnectCellId_r16_constr_18,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		CHOICE_constraint
	},
	asn_MBR_NR_reconnectCellId_r16_18,
	2,	/* Elements count */
	&asn_SPC_NR_reconnectCellId_r16_specs_18	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_connectionFailureType_r16_value2enum_25[] = {
	{ 0,	3,	"rlf" },
	{ 1,	3,	"hof" }
};
static const unsigned int asn_MAP_NR_connectionFailureType_r16_enum2value_25[] = {
	1,	/* hof(1) */
	0	/* rlf(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_connectionFailureType_r16_specs_25 = {
	asn_MAP_NR_connectionFailureType_r16_value2enum_25,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_connectionFailureType_r16_enum2value_25,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_connectionFailureType_r16_tags_25[] = {
	(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_connectionFailureType_r16_25 = {
	"connectionFailureType-r16",
	"connectionFailureType-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_connectionFailureType_r16_tags_25,
	sizeof(asn_DEF_NR_connectionFailureType_r16_tags_25)
		/sizeof(asn_DEF_NR_connectionFailureType_r16_tags_25[0]) - 1, /* 1 */
	asn_DEF_NR_connectionFailureType_r16_tags_25,	/* Same as above */
	sizeof(asn_DEF_NR_connectionFailureType_r16_tags_25)
		/sizeof(asn_DEF_NR_connectionFailureType_r16_tags_25[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_connectionFailureType_r16_constr_25,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_connectionFailureType_r16_specs_25	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_rlf_Cause_r16_value2enum_28[] = {
	{ 0,	11,	"t310-Expiry" },
	{ 1,	19,	"randomAccessProblem" },
	{ 2,	14,	"rlc-MaxNumRetx" },
	{ 3,	26,	"beamFailureRecoveryFailure" },
	{ 4,	14,	"lbtFailure-r16" },
	{ 5,	21,	"bh-rlfRecoveryFailure" },
	{ 6,	15,	"t312-expiry-r17" },
	{ 7,	6,	"spare1" }
};
static const unsigned int asn_MAP_NR_rlf_Cause_r16_enum2value_28[] = {
	3,	/* beamFailureRecoveryFailure(3) */
	5,	/* bh-rlfRecoveryFailure(5) */
	4,	/* lbtFailure-r16(4) */
	1,	/* randomAccessProblem(1) */
	2,	/* rlc-MaxNumRetx(2) */
	7,	/* spare1(7) */
	0,	/* t310-Expiry(0) */
	6	/* t312-expiry-r17(6) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_rlf_Cause_r16_specs_28 = {
	asn_MAP_NR_rlf_Cause_r16_value2enum_28,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_rlf_Cause_r16_enum2value_28,	/* N => "tag"; sorted by N */
	8,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_rlf_Cause_r16_tags_28[] = {
	(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_rlf_Cause_r16_28 = {
	"rlf-Cause-r16",
	"rlf-Cause-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_rlf_Cause_r16_tags_28,
	sizeof(asn_DEF_NR_rlf_Cause_r16_tags_28)
		/sizeof(asn_DEF_NR_rlf_Cause_r16_tags_28[0]) - 1, /* 1 */
	asn_DEF_NR_rlf_Cause_r16_tags_28,	/* Same as above */
	sizeof(asn_DEF_NR_rlf_Cause_r16_tags_28)
		/sizeof(asn_DEF_NR_rlf_Cause_r16_tags_28[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_rlf_Cause_r16_constr_28,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_rlf_Cause_r16_specs_28	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_noSuitableCellFound_r16_value2enum_38[] = {
	{ 0,	4,	"true" }
};
static const unsigned int asn_MAP_NR_noSuitableCellFound_r16_enum2value_38[] = {
	0	/* true(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_noSuitableCellFound_r16_specs_38 = {
	asn_MAP_NR_noSuitableCellFound_r16_value2enum_38,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_noSuitableCellFound_r16_enum2value_38,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_noSuitableCellFound_r16_tags_38[] = {
	(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_noSuitableCellFound_r16_38 = {
	"noSuitableCellFound-r16",
	"noSuitableCellFound-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_noSuitableCellFound_r16_tags_38,
	sizeof(asn_DEF_NR_noSuitableCellFound_r16_tags_38)
		/sizeof(asn_DEF_NR_noSuitableCellFound_r16_tags_38[0]) - 1, /* 1 */
	asn_DEF_NR_noSuitableCellFound_r16_tags_38,	/* Same as above */
	sizeof(asn_DEF_NR_noSuitableCellFound_r16_tags_38)
		/sizeof(asn_DEF_NR_noSuitableCellFound_r16_tags_38[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_noSuitableCellFound_r16_constr_38,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_noSuitableCellFound_r16_specs_38	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext1_42[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__ext1, csi_rsRLMConfigBitmap_v1650),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_BIT_STRING,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_csi_rsRLMConfigBitmap_v1650_constr_43,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_csi_rsRLMConfigBitmap_v1650_constraint_42
		},
		0, 0, /* No default value */
		"csi-rsRLMConfigBitmap-v1650"
		},
};
static const int asn_MAP_NR_ext1_oms_42[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_42[] = {
	(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_42[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* csi-rsRLMConfigBitmap-v1650 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_42 = {
	sizeof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__ext1),
	offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_42,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_42,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_42 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_42,
	sizeof(asn_DEF_NR_ext1_tags_42)
		/sizeof(asn_DEF_NR_ext1_tags_42[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_42,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_42)
		/sizeof(asn_DEF_NR_ext1_tags_42[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_42,
	1,	/* Elements count */
	&asn_SPC_NR_ext1_specs_42	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_lastHO_Type_r17_value2enum_45[] = {
	{ 0,	3,	"cho" },
	{ 1,	4,	"daps" },
	{ 2,	6,	"spare2" },
	{ 3,	6,	"spare1" }
};
static const unsigned int asn_MAP_NR_lastHO_Type_r17_enum2value_45[] = {
	0,	/* cho(0) */
	1,	/* daps(1) */
	3,	/* spare1(3) */
	2	/* spare2(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_lastHO_Type_r17_specs_45 = {
	asn_MAP_NR_lastHO_Type_r17_value2enum_45,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_lastHO_Type_r17_enum2value_45,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_lastHO_Type_r17_tags_45[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_lastHO_Type_r17_45 = {
	"lastHO-Type-r17",
	"lastHO-Type-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_lastHO_Type_r17_tags_45,
	sizeof(asn_DEF_NR_lastHO_Type_r17_tags_45)
		/sizeof(asn_DEF_NR_lastHO_Type_r17_tags_45[0]) - 1, /* 1 */
	asn_DEF_NR_lastHO_Type_r17_tags_45,	/* Same as above */
	sizeof(asn_DEF_NR_lastHO_Type_r17_tags_45)
		/sizeof(asn_DEF_NR_lastHO_Type_r17_tags_45[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_lastHO_Type_r17_constr_45,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_lastHO_Type_r17_specs_45	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_choCellId_r17_52[] = {
	{ ATF_POINTER, 0, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__ext2__choCellId_r17, choice.cellGlobalId_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_CGI_Info_Logging_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"cellGlobalId-r17"
		},
	{ ATF_POINTER, 0, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__ext2__choCellId_r17, choice.pci_arfcn_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_PCI_ARFCN_NR_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pci-arfcn-r17"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_NR_choCellId_r17_tag2el_52[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* cellGlobalId-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* pci-arfcn-r17 */
};
static asn_CHOICE_specifics_t asn_SPC_NR_choCellId_r17_specs_52 = {
	sizeof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__ext2__choCellId_r17),
	offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__ext2__choCellId_r17, _asn_ctx),
	offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__ext2__choCellId_r17, present),
	sizeof(((struct NR_RLF_Report_r16__nr_RLF_Report_r16__ext2__choCellId_r17 *)0)->present),
	asn_MAP_NR_choCellId_r17_tag2el_52,
	2,	/* Count of tags in the map */
	0, 0,
	-1	/* Extensions start */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_choCellId_r17_52 = {
	"choCellId-r17",
	"choCellId-r17",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_choCellId_r17_constr_52,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		CHOICE_constraint
	},
	asn_MBR_NR_choCellId_r17_52,
	2,	/* Elements count */
	&asn_SPC_NR_choCellId_r17_specs_52	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext2_44[] = {
	{ ATF_POINTER, 5, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__ext2, lastHO_Type_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_lastHO_Type_r17_45,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"lastHO-Type-r17"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__ext2, timeConnSourceDAPS_Failure_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_TimeConnSourceDAPS_Failure_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"timeConnSourceDAPS-Failure-r17"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__ext2, timeSinceCHO_Reconfig_r17),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_TimeSinceCHO_Reconfig_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"timeSinceCHO-Reconfig-r17"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__ext2, choCellId_r17),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_choCellId_r17_52,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"choCellId-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__ext2, choCandidateCellList_r17),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ChoCandidateCellList_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"choCandidateCellList-r17"
		},
};
static const int asn_MAP_NR_ext2_oms_44[] = { 0, 1, 2, 3, 4 };
static const ber_tlv_tag_t asn_DEF_NR_ext2_tags_44[] = {
	(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext2_tag2el_44[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* lastHO-Type-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* timeConnSourceDAPS-Failure-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* timeSinceCHO-Reconfig-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* choCellId-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 } /* choCandidateCellList-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext2_specs_44 = {
	sizeof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__ext2),
	offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16__ext2, _asn_ctx),
	asn_MAP_NR_ext2_tag2el_44,
	5,	/* Count of tags in the map */
	asn_MAP_NR_ext2_oms_44,	/* Optional members */
	5, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext2_44 = {
	"ext2",
	"ext2",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext2_tags_44,
	sizeof(asn_DEF_NR_ext2_tags_44)
		/sizeof(asn_DEF_NR_ext2_tags_44[0]) - 1, /* 1 */
	asn_DEF_NR_ext2_tags_44,	/* Same as above */
	sizeof(asn_DEF_NR_ext2_tags_44)
		/sizeof(asn_DEF_NR_ext2_tags_44[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext2_44,
	5,	/* Elements count */
	&asn_SPC_NR_ext2_specs_44	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_nr_RLF_Report_r16_2[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16, measResultLastServCell_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_MeasResultRLFNR_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"measResultLastServCell-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16, measResultNeighCells_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_measResultNeighCells_r16_4,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"measResultNeighCells-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16, c_RNTI_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_RNTI_Value,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"c-RNTI-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16, previousPCellId_r16),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_previousPCellId_r16_8,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"previousPCellId-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16, failedPCellId_r16),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_failedPCellId_r16_11,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"failedPCellId-r16"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16, reconnectCellId_r16),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_reconnectCellId_r16_18,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"reconnectCellId-r16"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16, timeUntilReconnection_r16),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_TimeUntilReconnection_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"timeUntilReconnection-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16, reestablishmentCellId_r16),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_CGI_Info_Logging_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"reestablishmentCellId-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16, timeConnFailure_r16),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_timeConnFailure_r16_constr_23,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_timeConnFailure_r16_constraint_2
		},
		0, 0, /* No default value */
		"timeConnFailure-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16, timeSinceFailure_r16),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_TimeSinceFailure_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"timeSinceFailure-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16, connectionFailureType_r16),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_connectionFailureType_r16_25,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"connectionFailureType-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16, rlf_Cause_r16),
		(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_rlf_Cause_r16_28,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"rlf-Cause-r16"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16, locationInfo_r16),
		(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_LocationInfo_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"locationInfo-r16"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16, noSuitableCellFound_r16),
		(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_noSuitableCellFound_r16_38,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"noSuitableCellFound-r16"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16, ra_InformationCommon_r16),
		(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_RA_InformationCommon_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ra-InformationCommon-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16, ext1),
		(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
		0,
		&asn_DEF_NR_ext1_42,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16, ext2),
		(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
		0,
		&asn_DEF_NR_ext2_44,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext2"
		},
};
static const int asn_MAP_NR_nr_RLF_Report_r16_oms_2[] = { 1, 3, 5, 6, 7, 8, 12, 13, 14, 15, 16 };
static const ber_tlv_tag_t asn_DEF_NR_nr_RLF_Report_r16_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_nr_RLF_Report_r16_tag2el_2[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* measResultLastServCell-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* measResultNeighCells-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* c-RNTI-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* previousPCellId-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* failedPCellId-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* reconnectCellId-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* timeUntilReconnection-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* reestablishmentCellId-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* timeConnFailure-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* timeSinceFailure-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 }, /* connectionFailureType-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (11 << 2)), 11, 0, 0 }, /* rlf-Cause-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (12 << 2)), 12, 0, 0 }, /* locationInfo-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (13 << 2)), 13, 0, 0 }, /* noSuitableCellFound-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (14 << 2)), 14, 0, 0 }, /* ra-InformationCommon-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (15 << 2)), 15, 0, 0 }, /* ext1 */
    { (ASN_TAG_CLASS_CONTEXT | (16 << 2)), 16, 0, 0 } /* ext2 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_nr_RLF_Report_r16_specs_2 = {
	sizeof(struct NR_RLF_Report_r16__nr_RLF_Report_r16),
	offsetof(struct NR_RLF_Report_r16__nr_RLF_Report_r16, _asn_ctx),
	asn_MAP_NR_nr_RLF_Report_r16_tag2el_2,
	17,	/* Count of tags in the map */
	asn_MAP_NR_nr_RLF_Report_r16_oms_2,	/* Optional members */
	9, 2,	/* Root/Additions */
	15,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_nr_RLF_Report_r16_2 = {
	"nr-RLF-Report-r16",
	"nr-RLF-Report-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_nr_RLF_Report_r16_tags_2,
	sizeof(asn_DEF_NR_nr_RLF_Report_r16_tags_2)
		/sizeof(asn_DEF_NR_nr_RLF_Report_r16_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_nr_RLF_Report_r16_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_nr_RLF_Report_r16_tags_2)
		/sizeof(asn_DEF_NR_nr_RLF_Report_r16_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_nr_RLF_Report_r16_2,
	17,	/* Elements count */
	&asn_SPC_NR_nr_RLF_Report_r16_specs_2	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext1_60[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_RLF_Report_r16__eutra_RLF_Report_r16__ext1, measResult_RLF_Report_EUTRA_v1690),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_OCTET_STRING,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"measResult-RLF-Report-EUTRA-v1690"
		},
};
static const int asn_MAP_NR_ext1_oms_60[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_60[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_60[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* measResult-RLF-Report-EUTRA-v1690 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_60 = {
	sizeof(struct NR_RLF_Report_r16__eutra_RLF_Report_r16__ext1),
	offsetof(struct NR_RLF_Report_r16__eutra_RLF_Report_r16__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_60,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_60,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_60 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_60,
	sizeof(asn_DEF_NR_ext1_tags_60)
		/sizeof(asn_DEF_NR_ext1_tags_60[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_60,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_60)
		/sizeof(asn_DEF_NR_ext1_tags_60[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_60,
	1,	/* Elements count */
	&asn_SPC_NR_ext1_specs_60	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_eutra_RLF_Report_r16_56[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RLF_Report_r16__eutra_RLF_Report_r16, failedPCellId_EUTRA),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_CGI_InfoEUTRALogging,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"failedPCellId-EUTRA"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RLF_Report_r16__eutra_RLF_Report_r16, measResult_RLF_Report_EUTRA_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_OCTET_STRING,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"measResult-RLF-Report-EUTRA-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RLF_Report_r16__eutra_RLF_Report_r16, ext1),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		0,
		&asn_DEF_NR_ext1_60,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
};
static const int asn_MAP_NR_eutra_RLF_Report_r16_oms_56[] = { 2 };
static const ber_tlv_tag_t asn_DEF_NR_eutra_RLF_Report_r16_tags_56[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_eutra_RLF_Report_r16_tag2el_56[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* failedPCellId-EUTRA */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* measResult-RLF-Report-EUTRA-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* ext1 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_eutra_RLF_Report_r16_specs_56 = {
	sizeof(struct NR_RLF_Report_r16__eutra_RLF_Report_r16),
	offsetof(struct NR_RLF_Report_r16__eutra_RLF_Report_r16, _asn_ctx),
	asn_MAP_NR_eutra_RLF_Report_r16_tag2el_56,
	3,	/* Count of tags in the map */
	asn_MAP_NR_eutra_RLF_Report_r16_oms_56,	/* Optional members */
	0, 1,	/* Root/Additions */
	2,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_eutra_RLF_Report_r16_56 = {
	"eutra-RLF-Report-r16",
	"eutra-RLF-Report-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_eutra_RLF_Report_r16_tags_56,
	sizeof(asn_DEF_NR_eutra_RLF_Report_r16_tags_56)
		/sizeof(asn_DEF_NR_eutra_RLF_Report_r16_tags_56[0]) - 1, /* 1 */
	asn_DEF_NR_eutra_RLF_Report_r16_tags_56,	/* Same as above */
	sizeof(asn_DEF_NR_eutra_RLF_Report_r16_tags_56)
		/sizeof(asn_DEF_NR_eutra_RLF_Report_r16_tags_56[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_eutra_RLF_Report_r16_56,
	3,	/* Elements count */
	&asn_SPC_NR_eutra_RLF_Report_r16_specs_56	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_RLF_Report_r16_1[] = {
	{ ATF_POINTER, 0, offsetof(struct NR_RLF_Report_r16, choice.nr_RLF_Report_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_nr_RLF_Report_r16_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"nr-RLF-Report-r16"
		},
	{ ATF_POINTER, 0, offsetof(struct NR_RLF_Report_r16, choice.eutra_RLF_Report_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_eutra_RLF_Report_r16_56,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"eutra-RLF-Report-r16"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_NR_RLF_Report_r16_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* nr-RLF-Report-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* eutra-RLF-Report-r16 */
};
asn_CHOICE_specifics_t asn_SPC_NR_RLF_Report_r16_specs_1 = {
	sizeof(struct NR_RLF_Report_r16),
	offsetof(struct NR_RLF_Report_r16, _asn_ctx),
	offsetof(struct NR_RLF_Report_r16, present),
	sizeof(((struct NR_RLF_Report_r16 *)0)->present),
	asn_MAP_NR_RLF_Report_r16_tag2el_1,
	2,	/* Count of tags in the map */
	0, 0,
	-1	/* Extensions start */
};
asn_TYPE_descriptor_t asn_DEF_NR_RLF_Report_r16 = {
	"RLF-Report-r16",
	"RLF-Report-r16",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_RLF_Report_r16_constr_1,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		CHOICE_constraint
	},
	asn_MBR_NR_RLF_Report_r16_1,
	2,	/* Elements count */
	&asn_SPC_NR_RLF_Report_r16_specs_1	/* Additional specs */
};

