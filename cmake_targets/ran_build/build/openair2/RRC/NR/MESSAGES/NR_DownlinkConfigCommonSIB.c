/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_DownlinkConfigCommonSIB.h"

static asn_TYPE_member_t asn_MBR_NR_ext1_7[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_DownlinkConfigCommonSIB__ext1, pei_Config_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_PEI_Config_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pei-Config-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_DownlinkConfigCommonSIB__ext1, initialDownlinkBWP_RedCap_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BWP_DownlinkCommon,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"initialDownlinkBWP-RedCap-r17"
		},
};
static const int asn_MAP_NR_ext1_oms_7[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_7[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_7[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* pei-Config-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* initialDownlinkBWP-RedCap-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_7 = {
	sizeof(struct NR_DownlinkConfigCommonSIB__ext1),
	offsetof(struct NR_DownlinkConfigCommonSIB__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_7,
	2,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_7,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_7 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_7,
	sizeof(asn_DEF_NR_ext1_tags_7)
		/sizeof(asn_DEF_NR_ext1_tags_7[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_7,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_7)
		/sizeof(asn_DEF_NR_ext1_tags_7[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_7,
	2,	/* Elements count */
	&asn_SPC_NR_ext1_specs_7	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_DownlinkConfigCommonSIB_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_DownlinkConfigCommonSIB, frequencyInfoDL),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_FrequencyInfoDL_SIB,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"frequencyInfoDL"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_DownlinkConfigCommonSIB, initialDownlinkBWP),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BWP_DownlinkCommon,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"initialDownlinkBWP"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_DownlinkConfigCommonSIB, bcch_Config),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_BCCH_Config,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"bcch-Config"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_DownlinkConfigCommonSIB, pcch_Config),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_PCCH_Config,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pcch-Config"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_DownlinkConfigCommonSIB, ext1),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		0,
		&asn_DEF_NR_ext1_7,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
};
static const int asn_MAP_NR_DownlinkConfigCommonSIB_oms_1[] = { 4 };
static const ber_tlv_tag_t asn_DEF_NR_DownlinkConfigCommonSIB_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_DownlinkConfigCommonSIB_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* frequencyInfoDL */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* initialDownlinkBWP */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* bcch-Config */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* pcch-Config */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 } /* ext1 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_DownlinkConfigCommonSIB_specs_1 = {
	sizeof(struct NR_DownlinkConfigCommonSIB),
	offsetof(struct NR_DownlinkConfigCommonSIB, _asn_ctx),
	asn_MAP_NR_DownlinkConfigCommonSIB_tag2el_1,
	5,	/* Count of tags in the map */
	asn_MAP_NR_DownlinkConfigCommonSIB_oms_1,	/* Optional members */
	0, 1,	/* Root/Additions */
	4,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_DownlinkConfigCommonSIB = {
	"DownlinkConfigCommonSIB",
	"DownlinkConfigCommonSIB",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_DownlinkConfigCommonSIB_tags_1,
	sizeof(asn_DEF_NR_DownlinkConfigCommonSIB_tags_1)
		/sizeof(asn_DEF_NR_DownlinkConfigCommonSIB_tags_1[0]), /* 1 */
	asn_DEF_NR_DownlinkConfigCommonSIB_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_DownlinkConfigCommonSIB_tags_1)
		/sizeof(asn_DEF_NR_DownlinkConfigCommonSIB_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_DownlinkConfigCommonSIB_1,
	5,	/* Elements count */
	&asn_SPC_NR_DownlinkConfigCommonSIB_specs_1	/* Additional specs */
};

