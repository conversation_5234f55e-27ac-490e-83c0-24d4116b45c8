/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_Phy-ParametersXDD-Diff.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dynamicSFI_constr_2 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_twoPUCCH_F0_2_ConsecSymbols_constr_4 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_twoDifferentTPC_Loop_PUSCH_constr_6 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_twoDifferentTPC_Loop_PUCCH_constr_8 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dl_SchedulingOffset_PDSCH_TypeA_constr_12 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dl_SchedulingOffset_PDSCH_TypeB_constr_14 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ul_SchedulingOffset_constr_16 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_dynamicSFI_value2enum_2[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_dynamicSFI_enum2value_2[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dynamicSFI_specs_2 = {
	asn_MAP_NR_dynamicSFI_value2enum_2,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dynamicSFI_enum2value_2,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dynamicSFI_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dynamicSFI_2 = {
	"dynamicSFI",
	"dynamicSFI",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dynamicSFI_tags_2,
	sizeof(asn_DEF_NR_dynamicSFI_tags_2)
		/sizeof(asn_DEF_NR_dynamicSFI_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_dynamicSFI_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_dynamicSFI_tags_2)
		/sizeof(asn_DEF_NR_dynamicSFI_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dynamicSFI_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dynamicSFI_specs_2	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_twoPUCCH_F0_2_ConsecSymbols_value2enum_4[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_twoPUCCH_F0_2_ConsecSymbols_enum2value_4[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_twoPUCCH_F0_2_ConsecSymbols_specs_4 = {
	asn_MAP_NR_twoPUCCH_F0_2_ConsecSymbols_value2enum_4,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_twoPUCCH_F0_2_ConsecSymbols_enum2value_4,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_twoPUCCH_F0_2_ConsecSymbols_tags_4[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_twoPUCCH_F0_2_ConsecSymbols_4 = {
	"twoPUCCH-F0-2-ConsecSymbols",
	"twoPUCCH-F0-2-ConsecSymbols",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_twoPUCCH_F0_2_ConsecSymbols_tags_4,
	sizeof(asn_DEF_NR_twoPUCCH_F0_2_ConsecSymbols_tags_4)
		/sizeof(asn_DEF_NR_twoPUCCH_F0_2_ConsecSymbols_tags_4[0]) - 1, /* 1 */
	asn_DEF_NR_twoPUCCH_F0_2_ConsecSymbols_tags_4,	/* Same as above */
	sizeof(asn_DEF_NR_twoPUCCH_F0_2_ConsecSymbols_tags_4)
		/sizeof(asn_DEF_NR_twoPUCCH_F0_2_ConsecSymbols_tags_4[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_twoPUCCH_F0_2_ConsecSymbols_constr_4,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_twoPUCCH_F0_2_ConsecSymbols_specs_4	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_twoDifferentTPC_Loop_PUSCH_value2enum_6[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_twoDifferentTPC_Loop_PUSCH_enum2value_6[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_twoDifferentTPC_Loop_PUSCH_specs_6 = {
	asn_MAP_NR_twoDifferentTPC_Loop_PUSCH_value2enum_6,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_twoDifferentTPC_Loop_PUSCH_enum2value_6,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_twoDifferentTPC_Loop_PUSCH_tags_6[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_twoDifferentTPC_Loop_PUSCH_6 = {
	"twoDifferentTPC-Loop-PUSCH",
	"twoDifferentTPC-Loop-PUSCH",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_twoDifferentTPC_Loop_PUSCH_tags_6,
	sizeof(asn_DEF_NR_twoDifferentTPC_Loop_PUSCH_tags_6)
		/sizeof(asn_DEF_NR_twoDifferentTPC_Loop_PUSCH_tags_6[0]) - 1, /* 1 */
	asn_DEF_NR_twoDifferentTPC_Loop_PUSCH_tags_6,	/* Same as above */
	sizeof(asn_DEF_NR_twoDifferentTPC_Loop_PUSCH_tags_6)
		/sizeof(asn_DEF_NR_twoDifferentTPC_Loop_PUSCH_tags_6[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_twoDifferentTPC_Loop_PUSCH_constr_6,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_twoDifferentTPC_Loop_PUSCH_specs_6	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_twoDifferentTPC_Loop_PUCCH_value2enum_8[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_twoDifferentTPC_Loop_PUCCH_enum2value_8[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_twoDifferentTPC_Loop_PUCCH_specs_8 = {
	asn_MAP_NR_twoDifferentTPC_Loop_PUCCH_value2enum_8,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_twoDifferentTPC_Loop_PUCCH_enum2value_8,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_twoDifferentTPC_Loop_PUCCH_tags_8[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_twoDifferentTPC_Loop_PUCCH_8 = {
	"twoDifferentTPC-Loop-PUCCH",
	"twoDifferentTPC-Loop-PUCCH",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_twoDifferentTPC_Loop_PUCCH_tags_8,
	sizeof(asn_DEF_NR_twoDifferentTPC_Loop_PUCCH_tags_8)
		/sizeof(asn_DEF_NR_twoDifferentTPC_Loop_PUCCH_tags_8[0]) - 1, /* 1 */
	asn_DEF_NR_twoDifferentTPC_Loop_PUCCH_tags_8,	/* Same as above */
	sizeof(asn_DEF_NR_twoDifferentTPC_Loop_PUCCH_tags_8)
		/sizeof(asn_DEF_NR_twoDifferentTPC_Loop_PUCCH_tags_8[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_twoDifferentTPC_Loop_PUCCH_constr_8,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_twoDifferentTPC_Loop_PUCCH_specs_8	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dl_SchedulingOffset_PDSCH_TypeA_value2enum_12[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_dl_SchedulingOffset_PDSCH_TypeA_enum2value_12[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dl_SchedulingOffset_PDSCH_TypeA_specs_12 = {
	asn_MAP_NR_dl_SchedulingOffset_PDSCH_TypeA_value2enum_12,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dl_SchedulingOffset_PDSCH_TypeA_enum2value_12,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeA_tags_12[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeA_12 = {
	"dl-SchedulingOffset-PDSCH-TypeA",
	"dl-SchedulingOffset-PDSCH-TypeA",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeA_tags_12,
	sizeof(asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeA_tags_12)
		/sizeof(asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeA_tags_12[0]) - 1, /* 1 */
	asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeA_tags_12,	/* Same as above */
	sizeof(asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeA_tags_12)
		/sizeof(asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeA_tags_12[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dl_SchedulingOffset_PDSCH_TypeA_constr_12,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dl_SchedulingOffset_PDSCH_TypeA_specs_12	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dl_SchedulingOffset_PDSCH_TypeB_value2enum_14[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_dl_SchedulingOffset_PDSCH_TypeB_enum2value_14[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dl_SchedulingOffset_PDSCH_TypeB_specs_14 = {
	asn_MAP_NR_dl_SchedulingOffset_PDSCH_TypeB_value2enum_14,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dl_SchedulingOffset_PDSCH_TypeB_enum2value_14,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeB_tags_14[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeB_14 = {
	"dl-SchedulingOffset-PDSCH-TypeB",
	"dl-SchedulingOffset-PDSCH-TypeB",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeB_tags_14,
	sizeof(asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeB_tags_14)
		/sizeof(asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeB_tags_14[0]) - 1, /* 1 */
	asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeB_tags_14,	/* Same as above */
	sizeof(asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeB_tags_14)
		/sizeof(asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeB_tags_14[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dl_SchedulingOffset_PDSCH_TypeB_constr_14,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dl_SchedulingOffset_PDSCH_TypeB_specs_14	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_ul_SchedulingOffset_value2enum_16[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_ul_SchedulingOffset_enum2value_16[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ul_SchedulingOffset_specs_16 = {
	asn_MAP_NR_ul_SchedulingOffset_value2enum_16,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ul_SchedulingOffset_enum2value_16,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ul_SchedulingOffset_tags_16[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ul_SchedulingOffset_16 = {
	"ul-SchedulingOffset",
	"ul-SchedulingOffset",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ul_SchedulingOffset_tags_16,
	sizeof(asn_DEF_NR_ul_SchedulingOffset_tags_16)
		/sizeof(asn_DEF_NR_ul_SchedulingOffset_tags_16[0]) - 1, /* 1 */
	asn_DEF_NR_ul_SchedulingOffset_tags_16,	/* Same as above */
	sizeof(asn_DEF_NR_ul_SchedulingOffset_tags_16)
		/sizeof(asn_DEF_NR_ul_SchedulingOffset_tags_16[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ul_SchedulingOffset_constr_16,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ul_SchedulingOffset_specs_16	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext1_11[] = {
	{ ATF_POINTER, 3, offsetof(struct NR_Phy_ParametersXDD_Diff__ext1, dl_SchedulingOffset_PDSCH_TypeA),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeA_12,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dl-SchedulingOffset-PDSCH-TypeA"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_Phy_ParametersXDD_Diff__ext1, dl_SchedulingOffset_PDSCH_TypeB),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dl_SchedulingOffset_PDSCH_TypeB_14,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dl-SchedulingOffset-PDSCH-TypeB"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersXDD_Diff__ext1, ul_SchedulingOffset),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ul_SchedulingOffset_16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ul-SchedulingOffset"
		},
};
static const int asn_MAP_NR_ext1_oms_11[] = { 0, 1, 2 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_11[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_11[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* dl-SchedulingOffset-PDSCH-TypeA */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* dl-SchedulingOffset-PDSCH-TypeB */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* ul-SchedulingOffset */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_11 = {
	sizeof(struct NR_Phy_ParametersXDD_Diff__ext1),
	offsetof(struct NR_Phy_ParametersXDD_Diff__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_11,
	3,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_11,	/* Optional members */
	3, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_11 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_11,
	sizeof(asn_DEF_NR_ext1_tags_11)
		/sizeof(asn_DEF_NR_ext1_tags_11[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_11,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_11)
		/sizeof(asn_DEF_NR_ext1_tags_11[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_11,
	3,	/* Elements count */
	&asn_SPC_NR_ext1_specs_11	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_Phy_ParametersXDD_Diff_1[] = {
	{ ATF_POINTER, 5, offsetof(struct NR_Phy_ParametersXDD_Diff, dynamicSFI),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dynamicSFI_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dynamicSFI"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_Phy_ParametersXDD_Diff, twoPUCCH_F0_2_ConsecSymbols),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_twoPUCCH_F0_2_ConsecSymbols_4,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"twoPUCCH-F0-2-ConsecSymbols"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_Phy_ParametersXDD_Diff, twoDifferentTPC_Loop_PUSCH),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_twoDifferentTPC_Loop_PUSCH_6,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"twoDifferentTPC-Loop-PUSCH"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_Phy_ParametersXDD_Diff, twoDifferentTPC_Loop_PUCCH),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_twoDifferentTPC_Loop_PUCCH_8,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"twoDifferentTPC-Loop-PUCCH"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_Phy_ParametersXDD_Diff, ext1),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		0,
		&asn_DEF_NR_ext1_11,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
};
static const int asn_MAP_NR_Phy_ParametersXDD_Diff_oms_1[] = { 0, 1, 2, 3, 4 };
static const ber_tlv_tag_t asn_DEF_NR_Phy_ParametersXDD_Diff_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_Phy_ParametersXDD_Diff_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* dynamicSFI */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* twoPUCCH-F0-2-ConsecSymbols */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* twoDifferentTPC-Loop-PUSCH */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* twoDifferentTPC-Loop-PUCCH */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 } /* ext1 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_Phy_ParametersXDD_Diff_specs_1 = {
	sizeof(struct NR_Phy_ParametersXDD_Diff),
	offsetof(struct NR_Phy_ParametersXDD_Diff, _asn_ctx),
	asn_MAP_NR_Phy_ParametersXDD_Diff_tag2el_1,
	5,	/* Count of tags in the map */
	asn_MAP_NR_Phy_ParametersXDD_Diff_oms_1,	/* Optional members */
	4, 1,	/* Root/Additions */
	4,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_Phy_ParametersXDD_Diff = {
	"Phy-ParametersXDD-Diff",
	"Phy-ParametersXDD-Diff",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_Phy_ParametersXDD_Diff_tags_1,
	sizeof(asn_DEF_NR_Phy_ParametersXDD_Diff_tags_1)
		/sizeof(asn_DEF_NR_Phy_ParametersXDD_Diff_tags_1[0]), /* 1 */
	asn_DEF_NR_Phy_ParametersXDD_Diff_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_Phy_ParametersXDD_Diff_tags_1)
		/sizeof(asn_DEF_NR_Phy_ParametersXDD_Diff_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_Phy_ParametersXDD_Diff_1,
	5,	/* Elements count */
	&asn_SPC_NR_Phy_ParametersXDD_Diff_specs_1	/* Additional specs */
};

