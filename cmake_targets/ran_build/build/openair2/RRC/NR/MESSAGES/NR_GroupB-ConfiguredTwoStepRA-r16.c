/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_GroupB-ConfiguredTwoStepRA-r16.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_numberOfRA_PreamblesGroupA_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 64L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ra_MsgA_SizeGroupA_constr_2 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  15 }	/* (0..15) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_messagePowerOffsetGroupB_constr_19 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  7 }	/* (0..7) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_numberOfRA_PreamblesGroupA_constr_28 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 6,  6,  1,  64 }	/* (1..64) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_ra_MsgA_SizeGroupA_value2enum_2[] = {
	{ 0,	3,	"b56" },
	{ 1,	4,	"b144" },
	{ 2,	4,	"b208" },
	{ 3,	4,	"b256" },
	{ 4,	4,	"b282" },
	{ 5,	4,	"b480" },
	{ 6,	4,	"b640" },
	{ 7,	4,	"b800" },
	{ 8,	5,	"b1000" },
	{ 9,	3,	"b72" },
	{ 10,	6,	"spare6" },
	{ 11,	6,	"spare5" },
	{ 12,	6,	"spare4" },
	{ 13,	6,	"spare3" },
	{ 14,	6,	"spare2" },
	{ 15,	6,	"spare1" }
};
static const unsigned int asn_MAP_NR_ra_MsgA_SizeGroupA_enum2value_2[] = {
	8,	/* b1000(8) */
	1,	/* b144(1) */
	2,	/* b208(2) */
	3,	/* b256(3) */
	4,	/* b282(4) */
	5,	/* b480(5) */
	0,	/* b56(0) */
	6,	/* b640(6) */
	9,	/* b72(9) */
	7,	/* b800(7) */
	15,	/* spare1(15) */
	14,	/* spare2(14) */
	13,	/* spare3(13) */
	12,	/* spare4(12) */
	11,	/* spare5(11) */
	10	/* spare6(10) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ra_MsgA_SizeGroupA_specs_2 = {
	asn_MAP_NR_ra_MsgA_SizeGroupA_value2enum_2,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ra_MsgA_SizeGroupA_enum2value_2,	/* N => "tag"; sorted by N */
	16,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ra_MsgA_SizeGroupA_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ra_MsgA_SizeGroupA_2 = {
	"ra-MsgA-SizeGroupA",
	"ra-MsgA-SizeGroupA",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ra_MsgA_SizeGroupA_tags_2,
	sizeof(asn_DEF_NR_ra_MsgA_SizeGroupA_tags_2)
		/sizeof(asn_DEF_NR_ra_MsgA_SizeGroupA_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_ra_MsgA_SizeGroupA_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_ra_MsgA_SizeGroupA_tags_2)
		/sizeof(asn_DEF_NR_ra_MsgA_SizeGroupA_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ra_MsgA_SizeGroupA_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ra_MsgA_SizeGroupA_specs_2	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_messagePowerOffsetGroupB_value2enum_19[] = {
	{ 0,	13,	"minusinfinity" },
	{ 1,	3,	"dB0" },
	{ 2,	3,	"dB5" },
	{ 3,	3,	"dB8" },
	{ 4,	4,	"dB10" },
	{ 5,	4,	"dB12" },
	{ 6,	4,	"dB15" },
	{ 7,	4,	"dB18" }
};
static const unsigned int asn_MAP_NR_messagePowerOffsetGroupB_enum2value_19[] = {
	1,	/* dB0(1) */
	4,	/* dB10(4) */
	5,	/* dB12(5) */
	6,	/* dB15(6) */
	7,	/* dB18(7) */
	2,	/* dB5(2) */
	3,	/* dB8(3) */
	0	/* minusinfinity(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_messagePowerOffsetGroupB_specs_19 = {
	asn_MAP_NR_messagePowerOffsetGroupB_value2enum_19,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_messagePowerOffsetGroupB_enum2value_19,	/* N => "tag"; sorted by N */
	8,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_messagePowerOffsetGroupB_tags_19[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_messagePowerOffsetGroupB_19 = {
	"messagePowerOffsetGroupB",
	"messagePowerOffsetGroupB",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_messagePowerOffsetGroupB_tags_19,
	sizeof(asn_DEF_NR_messagePowerOffsetGroupB_tags_19)
		/sizeof(asn_DEF_NR_messagePowerOffsetGroupB_tags_19[0]) - 1, /* 1 */
	asn_DEF_NR_messagePowerOffsetGroupB_tags_19,	/* Same as above */
	sizeof(asn_DEF_NR_messagePowerOffsetGroupB_tags_19)
		/sizeof(asn_DEF_NR_messagePowerOffsetGroupB_tags_19[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_messagePowerOffsetGroupB_constr_19,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_messagePowerOffsetGroupB_specs_19	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_GroupB_ConfiguredTwoStepRA_r16_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_GroupB_ConfiguredTwoStepRA_r16, ra_MsgA_SizeGroupA),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ra_MsgA_SizeGroupA_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ra-MsgA-SizeGroupA"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_GroupB_ConfiguredTwoStepRA_r16, messagePowerOffsetGroupB),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_messagePowerOffsetGroupB_19,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"messagePowerOffsetGroupB"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_GroupB_ConfiguredTwoStepRA_r16, numberOfRA_PreamblesGroupA),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_numberOfRA_PreamblesGroupA_constr_28,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_numberOfRA_PreamblesGroupA_constraint_1
		},
		0, 0, /* No default value */
		"numberOfRA-PreamblesGroupA"
		},
};
static const ber_tlv_tag_t asn_DEF_NR_GroupB_ConfiguredTwoStepRA_r16_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_GroupB_ConfiguredTwoStepRA_r16_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* ra-MsgA-SizeGroupA */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* messagePowerOffsetGroupB */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* numberOfRA-PreamblesGroupA */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_GroupB_ConfiguredTwoStepRA_r16_specs_1 = {
	sizeof(struct NR_GroupB_ConfiguredTwoStepRA_r16),
	offsetof(struct NR_GroupB_ConfiguredTwoStepRA_r16, _asn_ctx),
	asn_MAP_NR_GroupB_ConfiguredTwoStepRA_r16_tag2el_1,
	3,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_GroupB_ConfiguredTwoStepRA_r16 = {
	"GroupB-ConfiguredTwoStepRA-r16",
	"GroupB-ConfiguredTwoStepRA-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_GroupB_ConfiguredTwoStepRA_r16_tags_1,
	sizeof(asn_DEF_NR_GroupB_ConfiguredTwoStepRA_r16_tags_1)
		/sizeof(asn_DEF_NR_GroupB_ConfiguredTwoStepRA_r16_tags_1[0]), /* 1 */
	asn_DEF_NR_GroupB_ConfiguredTwoStepRA_r16_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_GroupB_ConfiguredTwoStepRA_r16_tags_1)
		/sizeof(asn_DEF_NR_GroupB_ConfiguredTwoStepRA_r16_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_GroupB_ConfiguredTwoStepRA_r16_1,
	3,	/* Elements count */
	&asn_SPC_NR_GroupB_ConfiguredTwoStepRA_r16_specs_1	/* Additional specs */
};

