/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_MultiDCI_MultiTRP_r16_H_
#define	_NR_MultiDCI_MultiTRP_r16_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <NativeInteger.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_MultiDCI_MultiTRP_r16__maxNumberCORESET_r16 {
	NR_MultiDCI_MultiTRP_r16__maxNumberCORESET_r16_n2	= 0,
	NR_MultiDCI_MultiTRP_r16__maxNumberCORESET_r16_n3	= 1,
	NR_MultiDCI_MultiTRP_r16__maxNumberCORESET_r16_n4	= 2,
	NR_MultiDCI_MultiTRP_r16__maxNumberCORESET_r16_n5	= 3
} e_NR_MultiDCI_MultiTRP_r16__maxNumberCORESET_r16;
typedef enum NR_MultiDCI_MultiTRP_r16__maxNumberUnicastPDSCH_PerPool_r16 {
	NR_MultiDCI_MultiTRP_r16__maxNumberUnicastPDSCH_PerPool_r16_n1	= 0,
	NR_MultiDCI_MultiTRP_r16__maxNumberUnicastPDSCH_PerPool_r16_n2	= 1,
	NR_MultiDCI_MultiTRP_r16__maxNumberUnicastPDSCH_PerPool_r16_n3	= 2,
	NR_MultiDCI_MultiTRP_r16__maxNumberUnicastPDSCH_PerPool_r16_n4	= 3,
	NR_MultiDCI_MultiTRP_r16__maxNumberUnicastPDSCH_PerPool_r16_n7	= 4
} e_NR_MultiDCI_MultiTRP_r16__maxNumberUnicastPDSCH_PerPool_r16;

/* NR_MultiDCI-MultiTRP-r16 */
typedef struct NR_MultiDCI_MultiTRP_r16 {
	long	 maxNumberCORESET_r16;
	long	 maxNumberCORESETPerPoolIndex_r16;
	long	 maxNumberUnicastPDSCH_PerPool_r16;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_MultiDCI_MultiTRP_r16_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumberCORESET_r16_2;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumberUnicastPDSCH_PerPool_r16_8;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_MultiDCI_MultiTRP_r16;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_MultiDCI_MultiTRP_r16_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_MultiDCI_MultiTRP_r16_1[3];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_MultiDCI_MultiTRP_r16_H_ */
#include <asn_internal.h>
