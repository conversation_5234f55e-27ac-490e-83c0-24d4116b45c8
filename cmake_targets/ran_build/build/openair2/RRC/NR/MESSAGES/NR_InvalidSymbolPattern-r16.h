/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_InvalidSymbolPattern_r16_H_
#define	_NR_InvalidSymbolPattern_r16_H_


#include <asn_application.h>

/* Including external dependencies */
#include <BIT_STRING.h>
#include <constr_CHOICE.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_InvalidSymbolPattern_r16__symbols_r16_PR {
	NR_InvalidSymbolPattern_r16__symbols_r16_PR_NOTHING,	/* No components present */
	NR_InvalidSymbolPattern_r16__symbols_r16_PR_oneSlot,
	NR_InvalidSymbolPattern_r16__symbols_r16_PR_twoSlots
} NR_InvalidSymbolPattern_r16__symbols_r16_PR;
typedef enum NR_InvalidSymbolPattern_r16__periodicityAndPattern_r16_PR {
	NR_InvalidSymbolPattern_r16__periodicityAndPattern_r16_PR_NOTHING,	/* No components present */
	NR_InvalidSymbolPattern_r16__periodicityAndPattern_r16_PR_n2,
	NR_InvalidSymbolPattern_r16__periodicityAndPattern_r16_PR_n4,
	NR_InvalidSymbolPattern_r16__periodicityAndPattern_r16_PR_n5,
	NR_InvalidSymbolPattern_r16__periodicityAndPattern_r16_PR_n8,
	NR_InvalidSymbolPattern_r16__periodicityAndPattern_r16_PR_n10,
	NR_InvalidSymbolPattern_r16__periodicityAndPattern_r16_PR_n20,
	NR_InvalidSymbolPattern_r16__periodicityAndPattern_r16_PR_n40
} NR_InvalidSymbolPattern_r16__periodicityAndPattern_r16_PR;

/* NR_InvalidSymbolPattern-r16 */
typedef struct NR_InvalidSymbolPattern_r16 {
	struct NR_InvalidSymbolPattern_r16__symbols_r16 {
		NR_InvalidSymbolPattern_r16__symbols_r16_PR present;
		union NR_InvalidSymbolPattern_r16__NR_symbols_r16_u {
			BIT_STRING_t	 oneSlot;
			BIT_STRING_t	 twoSlots;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} symbols_r16;
	struct NR_InvalidSymbolPattern_r16__periodicityAndPattern_r16 {
		NR_InvalidSymbolPattern_r16__periodicityAndPattern_r16_PR present;
		union NR_InvalidSymbolPattern_r16__NR_periodicityAndPattern_r16_u {
			BIT_STRING_t	 n2;
			BIT_STRING_t	 n4;
			BIT_STRING_t	 n5;
			BIT_STRING_t	 n8;
			BIT_STRING_t	 n10;
			BIT_STRING_t	 n20;
			BIT_STRING_t	 n40;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *periodicityAndPattern_r16;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_InvalidSymbolPattern_r16_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_NR_InvalidSymbolPattern_r16;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_InvalidSymbolPattern_r16_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_InvalidSymbolPattern_r16_1[2];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_InvalidSymbolPattern_r16_H_ */
#include <asn_internal.h>
