/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_FilterCoefficient_H_
#define	_NR_FilterCoefficient_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_FilterCoefficient {
	NR_FilterCoefficient_fc0	= 0,
	NR_FilterCoefficient_fc1	= 1,
	NR_FilterCoefficient_fc2	= 2,
	NR_FilterCoefficient_fc3	= 3,
	NR_FilterCoefficient_fc4	= 4,
	NR_FilterCoefficient_fc5	= 5,
	NR_FilterCoefficient_fc6	= 6,
	NR_FilterCoefficient_fc7	= 7,
	NR_FilterCoefficient_fc8	= 8,
	NR_FilterCoefficient_fc9	= 9,
	NR_FilterCoefficient_fc11	= 10,
	NR_FilterCoefficient_fc13	= 11,
	NR_FilterCoefficient_fc15	= 12,
	NR_FilterCoefficient_fc17	= 13,
	NR_FilterCoefficient_fc19	= 14,
	NR_FilterCoefficient_spare1	= 15
	/*
	 * Enumeration is extensible
	 */
} e_NR_FilterCoefficient;

/* NR_FilterCoefficient */
typedef long	 NR_FilterCoefficient_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_NR_FilterCoefficient_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_NR_FilterCoefficient;
extern const asn_INTEGER_specifics_t asn_SPC_NR_FilterCoefficient_specs_1;
asn_struct_free_f NR_FilterCoefficient_free;
asn_struct_print_f NR_FilterCoefficient_print;
asn_constr_check_f NR_FilterCoefficient_constraint;
xer_type_decoder_f NR_FilterCoefficient_decode_xer;
xer_type_encoder_f NR_FilterCoefficient_encode_xer;
per_type_decoder_f NR_FilterCoefficient_decode_uper;
per_type_encoder_f NR_FilterCoefficient_encode_uper;
per_type_decoder_f NR_FilterCoefficient_decode_aper;
per_type_encoder_f NR_FilterCoefficient_encode_aper;

#ifdef __cplusplus
}
#endif

#endif	/* _NR_FilterCoefficient_H_ */
#include <asn_internal.h>
