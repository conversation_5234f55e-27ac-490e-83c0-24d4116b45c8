/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_RLC_Config_H_
#define	_NR_RLC_Config_H_


#include <asn_application.h>

/* Including external dependencies */
#include "NR_UL-AM-RLC.h"
#include "NR_DL-AM-RLC.h"
#include <constr_SEQUENCE.h>
#include "NR_UL-UM-RLC.h"
#include "NR_DL-UM-RLC.h"
#include <constr_CHOICE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_RLC_Config_PR {
	NR_RLC_Config_PR_NOTHING,	/* No components present */
	NR_RLC_Config_PR_am,
	NR_RLC_Config_PR_um_Bi_Directional,
	NR_RLC_Config_PR_um_Uni_Directional_UL,
	NR_RLC_Config_PR_um_Uni_Directional_DL
	/* Extensions may appear below */
	
} NR_RLC_Config_PR;

/* NR_RLC-Config */
typedef struct NR_RLC_Config {
	NR_RLC_Config_PR present;
	union NR_RLC_Config_u {
		struct NR_RLC_Config__am {
			NR_UL_AM_RLC_t	 ul_AM_RLC;
			NR_DL_AM_RLC_t	 dl_AM_RLC;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *am;
		struct NR_RLC_Config__um_Bi_Directional {
			NR_UL_UM_RLC_t	 ul_UM_RLC;
			NR_DL_UM_RLC_t	 dl_UM_RLC;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *um_Bi_Directional;
		struct NR_RLC_Config__um_Uni_Directional_UL {
			NR_UL_UM_RLC_t	 ul_UM_RLC;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *um_Uni_Directional_UL;
		struct NR_RLC_Config__um_Uni_Directional_DL {
			NR_DL_UM_RLC_t	 dl_UM_RLC;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *um_Uni_Directional_DL;
		/*
		 * This type is extensible,
		 * possible extensions are below.
		 */
	} choice;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_RLC_Config_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_NR_RLC_Config;
extern asn_CHOICE_specifics_t asn_SPC_NR_RLC_Config_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_RLC_Config_1[4];
extern asn_per_constraints_t asn_PER_type_NR_RLC_Config_constr_1;

#ifdef __cplusplus
}
#endif

#endif	/* _NR_RLC_Config_H_ */
#include <asn_internal.h>
