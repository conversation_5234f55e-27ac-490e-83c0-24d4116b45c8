/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_MAC-ParametersCommon.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_lcp_Restriction_constr_2 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dummy_constr_4 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_lch_ToSCellRestriction_constr_6 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_recommendedBitRate_constr_10 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_recommendedBitRateQuery_constr_12 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_recommendedBitRateMultiplier_r16_constr_15 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_preEmptiveBSR_r16_constr_17 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_autonomousTransmission_r16_constr_19 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_lch_PriorityBasedPrioritization_r16_constr_21 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_lch_ToConfiguredGrantMapping_r16_constr_23 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_lch_ToGrantPriorityRestriction_r16_constr_25 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_singlePHR_P_r16_constr_27 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ul_LBT_FailureDetectionRecovery_r16_constr_29 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_tdd_MPE_P_MPR_Reporting_r16_constr_31 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_lcid_ExtensionIAB_r16_constr_33 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_spCell_BFR_CBRA_r16_constr_36 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_srs_ResourceId_Ext_r16_constr_39 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_enhancedUuDRX_forSidelink_r17_constr_42 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_mg_ActivationRequestPRS_Meas_r17_constr_44 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_mg_ActivationCommPRS_Meas_r17_constr_46 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_intraCG_Prioritization_r17_constr_48 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_jointPrioritizationCG_Retx_Timer_r17_constr_50 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_survivalTime_r17_constr_52 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_lcg_ExtensionIAB_r17_constr_54 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_harq_FeedbackDisabled_r17_constr_56 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_uplink_Harq_ModeB_r17_constr_58 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_sr_TriggeredBy_TA_Report_r17_constr_60 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_extendedDRX_CycleInactive_r17_constr_62 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_simultaneousSR_PUSCH_DiffPUCCH_groups_r17_constr_64 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_lastTransmissionUL_r17_constr_66 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_lcp_Restriction_value2enum_2[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_lcp_Restriction_enum2value_2[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_lcp_Restriction_specs_2 = {
	asn_MAP_NR_lcp_Restriction_value2enum_2,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_lcp_Restriction_enum2value_2,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_lcp_Restriction_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_lcp_Restriction_2 = {
	"lcp-Restriction",
	"lcp-Restriction",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_lcp_Restriction_tags_2,
	sizeof(asn_DEF_NR_lcp_Restriction_tags_2)
		/sizeof(asn_DEF_NR_lcp_Restriction_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_lcp_Restriction_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_lcp_Restriction_tags_2)
		/sizeof(asn_DEF_NR_lcp_Restriction_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_lcp_Restriction_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_lcp_Restriction_specs_2	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dummy_value2enum_4[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_dummy_enum2value_4[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dummy_specs_4 = {
	asn_MAP_NR_dummy_value2enum_4,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dummy_enum2value_4,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dummy_tags_4[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dummy_4 = {
	"dummy",
	"dummy",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dummy_tags_4,
	sizeof(asn_DEF_NR_dummy_tags_4)
		/sizeof(asn_DEF_NR_dummy_tags_4[0]) - 1, /* 1 */
	asn_DEF_NR_dummy_tags_4,	/* Same as above */
	sizeof(asn_DEF_NR_dummy_tags_4)
		/sizeof(asn_DEF_NR_dummy_tags_4[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dummy_constr_4,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dummy_specs_4	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_lch_ToSCellRestriction_value2enum_6[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_lch_ToSCellRestriction_enum2value_6[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_lch_ToSCellRestriction_specs_6 = {
	asn_MAP_NR_lch_ToSCellRestriction_value2enum_6,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_lch_ToSCellRestriction_enum2value_6,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_lch_ToSCellRestriction_tags_6[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_lch_ToSCellRestriction_6 = {
	"lch-ToSCellRestriction",
	"lch-ToSCellRestriction",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_lch_ToSCellRestriction_tags_6,
	sizeof(asn_DEF_NR_lch_ToSCellRestriction_tags_6)
		/sizeof(asn_DEF_NR_lch_ToSCellRestriction_tags_6[0]) - 1, /* 1 */
	asn_DEF_NR_lch_ToSCellRestriction_tags_6,	/* Same as above */
	sizeof(asn_DEF_NR_lch_ToSCellRestriction_tags_6)
		/sizeof(asn_DEF_NR_lch_ToSCellRestriction_tags_6[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_lch_ToSCellRestriction_constr_6,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_lch_ToSCellRestriction_specs_6	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_recommendedBitRate_value2enum_10[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_recommendedBitRate_enum2value_10[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_recommendedBitRate_specs_10 = {
	asn_MAP_NR_recommendedBitRate_value2enum_10,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_recommendedBitRate_enum2value_10,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_recommendedBitRate_tags_10[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_recommendedBitRate_10 = {
	"recommendedBitRate",
	"recommendedBitRate",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_recommendedBitRate_tags_10,
	sizeof(asn_DEF_NR_recommendedBitRate_tags_10)
		/sizeof(asn_DEF_NR_recommendedBitRate_tags_10[0]) - 1, /* 1 */
	asn_DEF_NR_recommendedBitRate_tags_10,	/* Same as above */
	sizeof(asn_DEF_NR_recommendedBitRate_tags_10)
		/sizeof(asn_DEF_NR_recommendedBitRate_tags_10[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_recommendedBitRate_constr_10,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_recommendedBitRate_specs_10	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_recommendedBitRateQuery_value2enum_12[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_recommendedBitRateQuery_enum2value_12[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_recommendedBitRateQuery_specs_12 = {
	asn_MAP_NR_recommendedBitRateQuery_value2enum_12,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_recommendedBitRateQuery_enum2value_12,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_recommendedBitRateQuery_tags_12[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_recommendedBitRateQuery_12 = {
	"recommendedBitRateQuery",
	"recommendedBitRateQuery",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_recommendedBitRateQuery_tags_12,
	sizeof(asn_DEF_NR_recommendedBitRateQuery_tags_12)
		/sizeof(asn_DEF_NR_recommendedBitRateQuery_tags_12[0]) - 1, /* 1 */
	asn_DEF_NR_recommendedBitRateQuery_tags_12,	/* Same as above */
	sizeof(asn_DEF_NR_recommendedBitRateQuery_tags_12)
		/sizeof(asn_DEF_NR_recommendedBitRateQuery_tags_12[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_recommendedBitRateQuery_constr_12,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_recommendedBitRateQuery_specs_12	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext1_9[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_MAC_ParametersCommon__ext1, recommendedBitRate),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_recommendedBitRate_10,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"recommendedBitRate"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MAC_ParametersCommon__ext1, recommendedBitRateQuery),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_recommendedBitRateQuery_12,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"recommendedBitRateQuery"
		},
};
static const int asn_MAP_NR_ext1_oms_9[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_9[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_9[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* recommendedBitRate */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* recommendedBitRateQuery */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_9 = {
	sizeof(struct NR_MAC_ParametersCommon__ext1),
	offsetof(struct NR_MAC_ParametersCommon__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_9,
	2,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_9,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_9 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_9,
	sizeof(asn_DEF_NR_ext1_tags_9)
		/sizeof(asn_DEF_NR_ext1_tags_9[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_9,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_9)
		/sizeof(asn_DEF_NR_ext1_tags_9[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_9,
	2,	/* Elements count */
	&asn_SPC_NR_ext1_specs_9	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_recommendedBitRateMultiplier_r16_value2enum_15[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_recommendedBitRateMultiplier_r16_enum2value_15[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_recommendedBitRateMultiplier_r16_specs_15 = {
	asn_MAP_NR_recommendedBitRateMultiplier_r16_value2enum_15,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_recommendedBitRateMultiplier_r16_enum2value_15,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_recommendedBitRateMultiplier_r16_tags_15[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_recommendedBitRateMultiplier_r16_15 = {
	"recommendedBitRateMultiplier-r16",
	"recommendedBitRateMultiplier-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_recommendedBitRateMultiplier_r16_tags_15,
	sizeof(asn_DEF_NR_recommendedBitRateMultiplier_r16_tags_15)
		/sizeof(asn_DEF_NR_recommendedBitRateMultiplier_r16_tags_15[0]) - 1, /* 1 */
	asn_DEF_NR_recommendedBitRateMultiplier_r16_tags_15,	/* Same as above */
	sizeof(asn_DEF_NR_recommendedBitRateMultiplier_r16_tags_15)
		/sizeof(asn_DEF_NR_recommendedBitRateMultiplier_r16_tags_15[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_recommendedBitRateMultiplier_r16_constr_15,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_recommendedBitRateMultiplier_r16_specs_15	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_preEmptiveBSR_r16_value2enum_17[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_preEmptiveBSR_r16_enum2value_17[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_preEmptiveBSR_r16_specs_17 = {
	asn_MAP_NR_preEmptiveBSR_r16_value2enum_17,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_preEmptiveBSR_r16_enum2value_17,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_preEmptiveBSR_r16_tags_17[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_preEmptiveBSR_r16_17 = {
	"preEmptiveBSR-r16",
	"preEmptiveBSR-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_preEmptiveBSR_r16_tags_17,
	sizeof(asn_DEF_NR_preEmptiveBSR_r16_tags_17)
		/sizeof(asn_DEF_NR_preEmptiveBSR_r16_tags_17[0]) - 1, /* 1 */
	asn_DEF_NR_preEmptiveBSR_r16_tags_17,	/* Same as above */
	sizeof(asn_DEF_NR_preEmptiveBSR_r16_tags_17)
		/sizeof(asn_DEF_NR_preEmptiveBSR_r16_tags_17[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_preEmptiveBSR_r16_constr_17,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_preEmptiveBSR_r16_specs_17	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_autonomousTransmission_r16_value2enum_19[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_autonomousTransmission_r16_enum2value_19[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_autonomousTransmission_r16_specs_19 = {
	asn_MAP_NR_autonomousTransmission_r16_value2enum_19,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_autonomousTransmission_r16_enum2value_19,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_autonomousTransmission_r16_tags_19[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_autonomousTransmission_r16_19 = {
	"autonomousTransmission-r16",
	"autonomousTransmission-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_autonomousTransmission_r16_tags_19,
	sizeof(asn_DEF_NR_autonomousTransmission_r16_tags_19)
		/sizeof(asn_DEF_NR_autonomousTransmission_r16_tags_19[0]) - 1, /* 1 */
	asn_DEF_NR_autonomousTransmission_r16_tags_19,	/* Same as above */
	sizeof(asn_DEF_NR_autonomousTransmission_r16_tags_19)
		/sizeof(asn_DEF_NR_autonomousTransmission_r16_tags_19[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_autonomousTransmission_r16_constr_19,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_autonomousTransmission_r16_specs_19	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_lch_PriorityBasedPrioritization_r16_value2enum_21[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_lch_PriorityBasedPrioritization_r16_enum2value_21[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_lch_PriorityBasedPrioritization_r16_specs_21 = {
	asn_MAP_NR_lch_PriorityBasedPrioritization_r16_value2enum_21,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_lch_PriorityBasedPrioritization_r16_enum2value_21,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_lch_PriorityBasedPrioritization_r16_tags_21[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_lch_PriorityBasedPrioritization_r16_21 = {
	"lch-PriorityBasedPrioritization-r16",
	"lch-PriorityBasedPrioritization-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_lch_PriorityBasedPrioritization_r16_tags_21,
	sizeof(asn_DEF_NR_lch_PriorityBasedPrioritization_r16_tags_21)
		/sizeof(asn_DEF_NR_lch_PriorityBasedPrioritization_r16_tags_21[0]) - 1, /* 1 */
	asn_DEF_NR_lch_PriorityBasedPrioritization_r16_tags_21,	/* Same as above */
	sizeof(asn_DEF_NR_lch_PriorityBasedPrioritization_r16_tags_21)
		/sizeof(asn_DEF_NR_lch_PriorityBasedPrioritization_r16_tags_21[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_lch_PriorityBasedPrioritization_r16_constr_21,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_lch_PriorityBasedPrioritization_r16_specs_21	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_lch_ToConfiguredGrantMapping_r16_value2enum_23[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_lch_ToConfiguredGrantMapping_r16_enum2value_23[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_lch_ToConfiguredGrantMapping_r16_specs_23 = {
	asn_MAP_NR_lch_ToConfiguredGrantMapping_r16_value2enum_23,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_lch_ToConfiguredGrantMapping_r16_enum2value_23,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_lch_ToConfiguredGrantMapping_r16_tags_23[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_lch_ToConfiguredGrantMapping_r16_23 = {
	"lch-ToConfiguredGrantMapping-r16",
	"lch-ToConfiguredGrantMapping-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_lch_ToConfiguredGrantMapping_r16_tags_23,
	sizeof(asn_DEF_NR_lch_ToConfiguredGrantMapping_r16_tags_23)
		/sizeof(asn_DEF_NR_lch_ToConfiguredGrantMapping_r16_tags_23[0]) - 1, /* 1 */
	asn_DEF_NR_lch_ToConfiguredGrantMapping_r16_tags_23,	/* Same as above */
	sizeof(asn_DEF_NR_lch_ToConfiguredGrantMapping_r16_tags_23)
		/sizeof(asn_DEF_NR_lch_ToConfiguredGrantMapping_r16_tags_23[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_lch_ToConfiguredGrantMapping_r16_constr_23,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_lch_ToConfiguredGrantMapping_r16_specs_23	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_lch_ToGrantPriorityRestriction_r16_value2enum_25[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_lch_ToGrantPriorityRestriction_r16_enum2value_25[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_lch_ToGrantPriorityRestriction_r16_specs_25 = {
	asn_MAP_NR_lch_ToGrantPriorityRestriction_r16_value2enum_25,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_lch_ToGrantPriorityRestriction_r16_enum2value_25,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_lch_ToGrantPriorityRestriction_r16_tags_25[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_lch_ToGrantPriorityRestriction_r16_25 = {
	"lch-ToGrantPriorityRestriction-r16",
	"lch-ToGrantPriorityRestriction-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_lch_ToGrantPriorityRestriction_r16_tags_25,
	sizeof(asn_DEF_NR_lch_ToGrantPriorityRestriction_r16_tags_25)
		/sizeof(asn_DEF_NR_lch_ToGrantPriorityRestriction_r16_tags_25[0]) - 1, /* 1 */
	asn_DEF_NR_lch_ToGrantPriorityRestriction_r16_tags_25,	/* Same as above */
	sizeof(asn_DEF_NR_lch_ToGrantPriorityRestriction_r16_tags_25)
		/sizeof(asn_DEF_NR_lch_ToGrantPriorityRestriction_r16_tags_25[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_lch_ToGrantPriorityRestriction_r16_constr_25,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_lch_ToGrantPriorityRestriction_r16_specs_25	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_singlePHR_P_r16_value2enum_27[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_singlePHR_P_r16_enum2value_27[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_singlePHR_P_r16_specs_27 = {
	asn_MAP_NR_singlePHR_P_r16_value2enum_27,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_singlePHR_P_r16_enum2value_27,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_singlePHR_P_r16_tags_27[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_singlePHR_P_r16_27 = {
	"singlePHR-P-r16",
	"singlePHR-P-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_singlePHR_P_r16_tags_27,
	sizeof(asn_DEF_NR_singlePHR_P_r16_tags_27)
		/sizeof(asn_DEF_NR_singlePHR_P_r16_tags_27[0]) - 1, /* 1 */
	asn_DEF_NR_singlePHR_P_r16_tags_27,	/* Same as above */
	sizeof(asn_DEF_NR_singlePHR_P_r16_tags_27)
		/sizeof(asn_DEF_NR_singlePHR_P_r16_tags_27[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_singlePHR_P_r16_constr_27,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_singlePHR_P_r16_specs_27	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_ul_LBT_FailureDetectionRecovery_r16_value2enum_29[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_ul_LBT_FailureDetectionRecovery_r16_enum2value_29[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ul_LBT_FailureDetectionRecovery_r16_specs_29 = {
	asn_MAP_NR_ul_LBT_FailureDetectionRecovery_r16_value2enum_29,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ul_LBT_FailureDetectionRecovery_r16_enum2value_29,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ul_LBT_FailureDetectionRecovery_r16_tags_29[] = {
	(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ul_LBT_FailureDetectionRecovery_r16_29 = {
	"ul-LBT-FailureDetectionRecovery-r16",
	"ul-LBT-FailureDetectionRecovery-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ul_LBT_FailureDetectionRecovery_r16_tags_29,
	sizeof(asn_DEF_NR_ul_LBT_FailureDetectionRecovery_r16_tags_29)
		/sizeof(asn_DEF_NR_ul_LBT_FailureDetectionRecovery_r16_tags_29[0]) - 1, /* 1 */
	asn_DEF_NR_ul_LBT_FailureDetectionRecovery_r16_tags_29,	/* Same as above */
	sizeof(asn_DEF_NR_ul_LBT_FailureDetectionRecovery_r16_tags_29)
		/sizeof(asn_DEF_NR_ul_LBT_FailureDetectionRecovery_r16_tags_29[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ul_LBT_FailureDetectionRecovery_r16_constr_29,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ul_LBT_FailureDetectionRecovery_r16_specs_29	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_tdd_MPE_P_MPR_Reporting_r16_value2enum_31[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_tdd_MPE_P_MPR_Reporting_r16_enum2value_31[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_tdd_MPE_P_MPR_Reporting_r16_specs_31 = {
	asn_MAP_NR_tdd_MPE_P_MPR_Reporting_r16_value2enum_31,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_tdd_MPE_P_MPR_Reporting_r16_enum2value_31,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_tdd_MPE_P_MPR_Reporting_r16_tags_31[] = {
	(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_tdd_MPE_P_MPR_Reporting_r16_31 = {
	"tdd-MPE-P-MPR-Reporting-r16",
	"tdd-MPE-P-MPR-Reporting-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_tdd_MPE_P_MPR_Reporting_r16_tags_31,
	sizeof(asn_DEF_NR_tdd_MPE_P_MPR_Reporting_r16_tags_31)
		/sizeof(asn_DEF_NR_tdd_MPE_P_MPR_Reporting_r16_tags_31[0]) - 1, /* 1 */
	asn_DEF_NR_tdd_MPE_P_MPR_Reporting_r16_tags_31,	/* Same as above */
	sizeof(asn_DEF_NR_tdd_MPE_P_MPR_Reporting_r16_tags_31)
		/sizeof(asn_DEF_NR_tdd_MPE_P_MPR_Reporting_r16_tags_31[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_tdd_MPE_P_MPR_Reporting_r16_constr_31,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_tdd_MPE_P_MPR_Reporting_r16_specs_31	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_lcid_ExtensionIAB_r16_value2enum_33[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_lcid_ExtensionIAB_r16_enum2value_33[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_lcid_ExtensionIAB_r16_specs_33 = {
	asn_MAP_NR_lcid_ExtensionIAB_r16_value2enum_33,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_lcid_ExtensionIAB_r16_enum2value_33,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_lcid_ExtensionIAB_r16_tags_33[] = {
	(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_lcid_ExtensionIAB_r16_33 = {
	"lcid-ExtensionIAB-r16",
	"lcid-ExtensionIAB-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_lcid_ExtensionIAB_r16_tags_33,
	sizeof(asn_DEF_NR_lcid_ExtensionIAB_r16_tags_33)
		/sizeof(asn_DEF_NR_lcid_ExtensionIAB_r16_tags_33[0]) - 1, /* 1 */
	asn_DEF_NR_lcid_ExtensionIAB_r16_tags_33,	/* Same as above */
	sizeof(asn_DEF_NR_lcid_ExtensionIAB_r16_tags_33)
		/sizeof(asn_DEF_NR_lcid_ExtensionIAB_r16_tags_33[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_lcid_ExtensionIAB_r16_constr_33,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_lcid_ExtensionIAB_r16_specs_33	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext2_14[] = {
	{ ATF_POINTER, 10, offsetof(struct NR_MAC_ParametersCommon__ext2, recommendedBitRateMultiplier_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_recommendedBitRateMultiplier_r16_15,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"recommendedBitRateMultiplier-r16"
		},
	{ ATF_POINTER, 9, offsetof(struct NR_MAC_ParametersCommon__ext2, preEmptiveBSR_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_preEmptiveBSR_r16_17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"preEmptiveBSR-r16"
		},
	{ ATF_POINTER, 8, offsetof(struct NR_MAC_ParametersCommon__ext2, autonomousTransmission_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_autonomousTransmission_r16_19,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"autonomousTransmission-r16"
		},
	{ ATF_POINTER, 7, offsetof(struct NR_MAC_ParametersCommon__ext2, lch_PriorityBasedPrioritization_r16),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_lch_PriorityBasedPrioritization_r16_21,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"lch-PriorityBasedPrioritization-r16"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_MAC_ParametersCommon__ext2, lch_ToConfiguredGrantMapping_r16),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_lch_ToConfiguredGrantMapping_r16_23,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"lch-ToConfiguredGrantMapping-r16"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_MAC_ParametersCommon__ext2, lch_ToGrantPriorityRestriction_r16),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_lch_ToGrantPriorityRestriction_r16_25,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"lch-ToGrantPriorityRestriction-r16"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_MAC_ParametersCommon__ext2, singlePHR_P_r16),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_singlePHR_P_r16_27,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"singlePHR-P-r16"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_MAC_ParametersCommon__ext2, ul_LBT_FailureDetectionRecovery_r16),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ul_LBT_FailureDetectionRecovery_r16_29,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ul-LBT-FailureDetectionRecovery-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_MAC_ParametersCommon__ext2, tdd_MPE_P_MPR_Reporting_r16),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_tdd_MPE_P_MPR_Reporting_r16_31,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"tdd-MPE-P-MPR-Reporting-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MAC_ParametersCommon__ext2, lcid_ExtensionIAB_r16),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_lcid_ExtensionIAB_r16_33,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"lcid-ExtensionIAB-r16"
		},
};
static const int asn_MAP_NR_ext2_oms_14[] = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9 };
static const ber_tlv_tag_t asn_DEF_NR_ext2_tags_14[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext2_tag2el_14[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* recommendedBitRateMultiplier-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* preEmptiveBSR-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* autonomousTransmission-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* lch-PriorityBasedPrioritization-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* lch-ToConfiguredGrantMapping-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* lch-ToGrantPriorityRestriction-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* singlePHR-P-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* ul-LBT-FailureDetectionRecovery-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* tdd-MPE-P-MPR-Reporting-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 } /* lcid-ExtensionIAB-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext2_specs_14 = {
	sizeof(struct NR_MAC_ParametersCommon__ext2),
	offsetof(struct NR_MAC_ParametersCommon__ext2, _asn_ctx),
	asn_MAP_NR_ext2_tag2el_14,
	10,	/* Count of tags in the map */
	asn_MAP_NR_ext2_oms_14,	/* Optional members */
	10, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext2_14 = {
	"ext2",
	"ext2",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext2_tags_14,
	sizeof(asn_DEF_NR_ext2_tags_14)
		/sizeof(asn_DEF_NR_ext2_tags_14[0]) - 1, /* 1 */
	asn_DEF_NR_ext2_tags_14,	/* Same as above */
	sizeof(asn_DEF_NR_ext2_tags_14)
		/sizeof(asn_DEF_NR_ext2_tags_14[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext2_14,
	10,	/* Elements count */
	&asn_SPC_NR_ext2_specs_14	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_spCell_BFR_CBRA_r16_value2enum_36[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_spCell_BFR_CBRA_r16_enum2value_36[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_spCell_BFR_CBRA_r16_specs_36 = {
	asn_MAP_NR_spCell_BFR_CBRA_r16_value2enum_36,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_spCell_BFR_CBRA_r16_enum2value_36,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_spCell_BFR_CBRA_r16_tags_36[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_spCell_BFR_CBRA_r16_36 = {
	"spCell-BFR-CBRA-r16",
	"spCell-BFR-CBRA-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_spCell_BFR_CBRA_r16_tags_36,
	sizeof(asn_DEF_NR_spCell_BFR_CBRA_r16_tags_36)
		/sizeof(asn_DEF_NR_spCell_BFR_CBRA_r16_tags_36[0]) - 1, /* 1 */
	asn_DEF_NR_spCell_BFR_CBRA_r16_tags_36,	/* Same as above */
	sizeof(asn_DEF_NR_spCell_BFR_CBRA_r16_tags_36)
		/sizeof(asn_DEF_NR_spCell_BFR_CBRA_r16_tags_36[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_spCell_BFR_CBRA_r16_constr_36,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_spCell_BFR_CBRA_r16_specs_36	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext3_35[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_MAC_ParametersCommon__ext3, spCell_BFR_CBRA_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_spCell_BFR_CBRA_r16_36,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"spCell-BFR-CBRA-r16"
		},
};
static const int asn_MAP_NR_ext3_oms_35[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext3_tags_35[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext3_tag2el_35[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* spCell-BFR-CBRA-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext3_specs_35 = {
	sizeof(struct NR_MAC_ParametersCommon__ext3),
	offsetof(struct NR_MAC_ParametersCommon__ext3, _asn_ctx),
	asn_MAP_NR_ext3_tag2el_35,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext3_oms_35,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext3_35 = {
	"ext3",
	"ext3",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext3_tags_35,
	sizeof(asn_DEF_NR_ext3_tags_35)
		/sizeof(asn_DEF_NR_ext3_tags_35[0]) - 1, /* 1 */
	asn_DEF_NR_ext3_tags_35,	/* Same as above */
	sizeof(asn_DEF_NR_ext3_tags_35)
		/sizeof(asn_DEF_NR_ext3_tags_35[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext3_35,
	1,	/* Elements count */
	&asn_SPC_NR_ext3_specs_35	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_srs_ResourceId_Ext_r16_value2enum_39[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_srs_ResourceId_Ext_r16_enum2value_39[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_srs_ResourceId_Ext_r16_specs_39 = {
	asn_MAP_NR_srs_ResourceId_Ext_r16_value2enum_39,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_srs_ResourceId_Ext_r16_enum2value_39,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_srs_ResourceId_Ext_r16_tags_39[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_srs_ResourceId_Ext_r16_39 = {
	"srs-ResourceId-Ext-r16",
	"srs-ResourceId-Ext-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_srs_ResourceId_Ext_r16_tags_39,
	sizeof(asn_DEF_NR_srs_ResourceId_Ext_r16_tags_39)
		/sizeof(asn_DEF_NR_srs_ResourceId_Ext_r16_tags_39[0]) - 1, /* 1 */
	asn_DEF_NR_srs_ResourceId_Ext_r16_tags_39,	/* Same as above */
	sizeof(asn_DEF_NR_srs_ResourceId_Ext_r16_tags_39)
		/sizeof(asn_DEF_NR_srs_ResourceId_Ext_r16_tags_39[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_srs_ResourceId_Ext_r16_constr_39,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_srs_ResourceId_Ext_r16_specs_39	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext4_38[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_MAC_ParametersCommon__ext4, srs_ResourceId_Ext_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_srs_ResourceId_Ext_r16_39,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"srs-ResourceId-Ext-r16"
		},
};
static const int asn_MAP_NR_ext4_oms_38[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext4_tags_38[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext4_tag2el_38[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* srs-ResourceId-Ext-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext4_specs_38 = {
	sizeof(struct NR_MAC_ParametersCommon__ext4),
	offsetof(struct NR_MAC_ParametersCommon__ext4, _asn_ctx),
	asn_MAP_NR_ext4_tag2el_38,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext4_oms_38,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext4_38 = {
	"ext4",
	"ext4",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext4_tags_38,
	sizeof(asn_DEF_NR_ext4_tags_38)
		/sizeof(asn_DEF_NR_ext4_tags_38[0]) - 1, /* 1 */
	asn_DEF_NR_ext4_tags_38,	/* Same as above */
	sizeof(asn_DEF_NR_ext4_tags_38)
		/sizeof(asn_DEF_NR_ext4_tags_38[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext4_38,
	1,	/* Elements count */
	&asn_SPC_NR_ext4_specs_38	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_enhancedUuDRX_forSidelink_r17_value2enum_42[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_enhancedUuDRX_forSidelink_r17_enum2value_42[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_enhancedUuDRX_forSidelink_r17_specs_42 = {
	asn_MAP_NR_enhancedUuDRX_forSidelink_r17_value2enum_42,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_enhancedUuDRX_forSidelink_r17_enum2value_42,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_enhancedUuDRX_forSidelink_r17_tags_42[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_enhancedUuDRX_forSidelink_r17_42 = {
	"enhancedUuDRX-forSidelink-r17",
	"enhancedUuDRX-forSidelink-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_enhancedUuDRX_forSidelink_r17_tags_42,
	sizeof(asn_DEF_NR_enhancedUuDRX_forSidelink_r17_tags_42)
		/sizeof(asn_DEF_NR_enhancedUuDRX_forSidelink_r17_tags_42[0]) - 1, /* 1 */
	asn_DEF_NR_enhancedUuDRX_forSidelink_r17_tags_42,	/* Same as above */
	sizeof(asn_DEF_NR_enhancedUuDRX_forSidelink_r17_tags_42)
		/sizeof(asn_DEF_NR_enhancedUuDRX_forSidelink_r17_tags_42[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_enhancedUuDRX_forSidelink_r17_constr_42,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_enhancedUuDRX_forSidelink_r17_specs_42	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_mg_ActivationRequestPRS_Meas_r17_value2enum_44[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_mg_ActivationRequestPRS_Meas_r17_enum2value_44[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_mg_ActivationRequestPRS_Meas_r17_specs_44 = {
	asn_MAP_NR_mg_ActivationRequestPRS_Meas_r17_value2enum_44,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_mg_ActivationRequestPRS_Meas_r17_enum2value_44,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_mg_ActivationRequestPRS_Meas_r17_tags_44[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mg_ActivationRequestPRS_Meas_r17_44 = {
	"mg-ActivationRequestPRS-Meas-r17",
	"mg-ActivationRequestPRS-Meas-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_mg_ActivationRequestPRS_Meas_r17_tags_44,
	sizeof(asn_DEF_NR_mg_ActivationRequestPRS_Meas_r17_tags_44)
		/sizeof(asn_DEF_NR_mg_ActivationRequestPRS_Meas_r17_tags_44[0]) - 1, /* 1 */
	asn_DEF_NR_mg_ActivationRequestPRS_Meas_r17_tags_44,	/* Same as above */
	sizeof(asn_DEF_NR_mg_ActivationRequestPRS_Meas_r17_tags_44)
		/sizeof(asn_DEF_NR_mg_ActivationRequestPRS_Meas_r17_tags_44[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_mg_ActivationRequestPRS_Meas_r17_constr_44,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_mg_ActivationRequestPRS_Meas_r17_specs_44	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_mg_ActivationCommPRS_Meas_r17_value2enum_46[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_mg_ActivationCommPRS_Meas_r17_enum2value_46[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_mg_ActivationCommPRS_Meas_r17_specs_46 = {
	asn_MAP_NR_mg_ActivationCommPRS_Meas_r17_value2enum_46,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_mg_ActivationCommPRS_Meas_r17_enum2value_46,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_mg_ActivationCommPRS_Meas_r17_tags_46[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mg_ActivationCommPRS_Meas_r17_46 = {
	"mg-ActivationCommPRS-Meas-r17",
	"mg-ActivationCommPRS-Meas-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_mg_ActivationCommPRS_Meas_r17_tags_46,
	sizeof(asn_DEF_NR_mg_ActivationCommPRS_Meas_r17_tags_46)
		/sizeof(asn_DEF_NR_mg_ActivationCommPRS_Meas_r17_tags_46[0]) - 1, /* 1 */
	asn_DEF_NR_mg_ActivationCommPRS_Meas_r17_tags_46,	/* Same as above */
	sizeof(asn_DEF_NR_mg_ActivationCommPRS_Meas_r17_tags_46)
		/sizeof(asn_DEF_NR_mg_ActivationCommPRS_Meas_r17_tags_46[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_mg_ActivationCommPRS_Meas_r17_constr_46,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_mg_ActivationCommPRS_Meas_r17_specs_46	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_intraCG_Prioritization_r17_value2enum_48[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_intraCG_Prioritization_r17_enum2value_48[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_intraCG_Prioritization_r17_specs_48 = {
	asn_MAP_NR_intraCG_Prioritization_r17_value2enum_48,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_intraCG_Prioritization_r17_enum2value_48,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_intraCG_Prioritization_r17_tags_48[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_intraCG_Prioritization_r17_48 = {
	"intraCG-Prioritization-r17",
	"intraCG-Prioritization-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_intraCG_Prioritization_r17_tags_48,
	sizeof(asn_DEF_NR_intraCG_Prioritization_r17_tags_48)
		/sizeof(asn_DEF_NR_intraCG_Prioritization_r17_tags_48[0]) - 1, /* 1 */
	asn_DEF_NR_intraCG_Prioritization_r17_tags_48,	/* Same as above */
	sizeof(asn_DEF_NR_intraCG_Prioritization_r17_tags_48)
		/sizeof(asn_DEF_NR_intraCG_Prioritization_r17_tags_48[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_intraCG_Prioritization_r17_constr_48,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_intraCG_Prioritization_r17_specs_48	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_jointPrioritizationCG_Retx_Timer_r17_value2enum_50[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_jointPrioritizationCG_Retx_Timer_r17_enum2value_50[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_jointPrioritizationCG_Retx_Timer_r17_specs_50 = {
	asn_MAP_NR_jointPrioritizationCG_Retx_Timer_r17_value2enum_50,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_jointPrioritizationCG_Retx_Timer_r17_enum2value_50,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_jointPrioritizationCG_Retx_Timer_r17_tags_50[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_jointPrioritizationCG_Retx_Timer_r17_50 = {
	"jointPrioritizationCG-Retx-Timer-r17",
	"jointPrioritizationCG-Retx-Timer-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_jointPrioritizationCG_Retx_Timer_r17_tags_50,
	sizeof(asn_DEF_NR_jointPrioritizationCG_Retx_Timer_r17_tags_50)
		/sizeof(asn_DEF_NR_jointPrioritizationCG_Retx_Timer_r17_tags_50[0]) - 1, /* 1 */
	asn_DEF_NR_jointPrioritizationCG_Retx_Timer_r17_tags_50,	/* Same as above */
	sizeof(asn_DEF_NR_jointPrioritizationCG_Retx_Timer_r17_tags_50)
		/sizeof(asn_DEF_NR_jointPrioritizationCG_Retx_Timer_r17_tags_50[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_jointPrioritizationCG_Retx_Timer_r17_constr_50,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_jointPrioritizationCG_Retx_Timer_r17_specs_50	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_survivalTime_r17_value2enum_52[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_survivalTime_r17_enum2value_52[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_survivalTime_r17_specs_52 = {
	asn_MAP_NR_survivalTime_r17_value2enum_52,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_survivalTime_r17_enum2value_52,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_survivalTime_r17_tags_52[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_survivalTime_r17_52 = {
	"survivalTime-r17",
	"survivalTime-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_survivalTime_r17_tags_52,
	sizeof(asn_DEF_NR_survivalTime_r17_tags_52)
		/sizeof(asn_DEF_NR_survivalTime_r17_tags_52[0]) - 1, /* 1 */
	asn_DEF_NR_survivalTime_r17_tags_52,	/* Same as above */
	sizeof(asn_DEF_NR_survivalTime_r17_tags_52)
		/sizeof(asn_DEF_NR_survivalTime_r17_tags_52[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_survivalTime_r17_constr_52,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_survivalTime_r17_specs_52	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_lcg_ExtensionIAB_r17_value2enum_54[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_lcg_ExtensionIAB_r17_enum2value_54[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_lcg_ExtensionIAB_r17_specs_54 = {
	asn_MAP_NR_lcg_ExtensionIAB_r17_value2enum_54,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_lcg_ExtensionIAB_r17_enum2value_54,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_lcg_ExtensionIAB_r17_tags_54[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_lcg_ExtensionIAB_r17_54 = {
	"lcg-ExtensionIAB-r17",
	"lcg-ExtensionIAB-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_lcg_ExtensionIAB_r17_tags_54,
	sizeof(asn_DEF_NR_lcg_ExtensionIAB_r17_tags_54)
		/sizeof(asn_DEF_NR_lcg_ExtensionIAB_r17_tags_54[0]) - 1, /* 1 */
	asn_DEF_NR_lcg_ExtensionIAB_r17_tags_54,	/* Same as above */
	sizeof(asn_DEF_NR_lcg_ExtensionIAB_r17_tags_54)
		/sizeof(asn_DEF_NR_lcg_ExtensionIAB_r17_tags_54[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_lcg_ExtensionIAB_r17_constr_54,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_lcg_ExtensionIAB_r17_specs_54	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_harq_FeedbackDisabled_r17_value2enum_56[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_harq_FeedbackDisabled_r17_enum2value_56[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_harq_FeedbackDisabled_r17_specs_56 = {
	asn_MAP_NR_harq_FeedbackDisabled_r17_value2enum_56,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_harq_FeedbackDisabled_r17_enum2value_56,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_harq_FeedbackDisabled_r17_tags_56[] = {
	(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_harq_FeedbackDisabled_r17_56 = {
	"harq-FeedbackDisabled-r17",
	"harq-FeedbackDisabled-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_harq_FeedbackDisabled_r17_tags_56,
	sizeof(asn_DEF_NR_harq_FeedbackDisabled_r17_tags_56)
		/sizeof(asn_DEF_NR_harq_FeedbackDisabled_r17_tags_56[0]) - 1, /* 1 */
	asn_DEF_NR_harq_FeedbackDisabled_r17_tags_56,	/* Same as above */
	sizeof(asn_DEF_NR_harq_FeedbackDisabled_r17_tags_56)
		/sizeof(asn_DEF_NR_harq_FeedbackDisabled_r17_tags_56[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_harq_FeedbackDisabled_r17_constr_56,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_harq_FeedbackDisabled_r17_specs_56	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_uplink_Harq_ModeB_r17_value2enum_58[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_uplink_Harq_ModeB_r17_enum2value_58[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_uplink_Harq_ModeB_r17_specs_58 = {
	asn_MAP_NR_uplink_Harq_ModeB_r17_value2enum_58,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_uplink_Harq_ModeB_r17_enum2value_58,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_uplink_Harq_ModeB_r17_tags_58[] = {
	(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_uplink_Harq_ModeB_r17_58 = {
	"uplink-Harq-ModeB-r17",
	"uplink-Harq-ModeB-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_uplink_Harq_ModeB_r17_tags_58,
	sizeof(asn_DEF_NR_uplink_Harq_ModeB_r17_tags_58)
		/sizeof(asn_DEF_NR_uplink_Harq_ModeB_r17_tags_58[0]) - 1, /* 1 */
	asn_DEF_NR_uplink_Harq_ModeB_r17_tags_58,	/* Same as above */
	sizeof(asn_DEF_NR_uplink_Harq_ModeB_r17_tags_58)
		/sizeof(asn_DEF_NR_uplink_Harq_ModeB_r17_tags_58[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_uplink_Harq_ModeB_r17_constr_58,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_uplink_Harq_ModeB_r17_specs_58	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_sr_TriggeredBy_TA_Report_r17_value2enum_60[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_sr_TriggeredBy_TA_Report_r17_enum2value_60[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_sr_TriggeredBy_TA_Report_r17_specs_60 = {
	asn_MAP_NR_sr_TriggeredBy_TA_Report_r17_value2enum_60,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_sr_TriggeredBy_TA_Report_r17_enum2value_60,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_sr_TriggeredBy_TA_Report_r17_tags_60[] = {
	(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_sr_TriggeredBy_TA_Report_r17_60 = {
	"sr-TriggeredBy-TA-Report-r17",
	"sr-TriggeredBy-TA-Report-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_sr_TriggeredBy_TA_Report_r17_tags_60,
	sizeof(asn_DEF_NR_sr_TriggeredBy_TA_Report_r17_tags_60)
		/sizeof(asn_DEF_NR_sr_TriggeredBy_TA_Report_r17_tags_60[0]) - 1, /* 1 */
	asn_DEF_NR_sr_TriggeredBy_TA_Report_r17_tags_60,	/* Same as above */
	sizeof(asn_DEF_NR_sr_TriggeredBy_TA_Report_r17_tags_60)
		/sizeof(asn_DEF_NR_sr_TriggeredBy_TA_Report_r17_tags_60[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_sr_TriggeredBy_TA_Report_r17_constr_60,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_sr_TriggeredBy_TA_Report_r17_specs_60	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_extendedDRX_CycleInactive_r17_value2enum_62[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_extendedDRX_CycleInactive_r17_enum2value_62[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_extendedDRX_CycleInactive_r17_specs_62 = {
	asn_MAP_NR_extendedDRX_CycleInactive_r17_value2enum_62,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_extendedDRX_CycleInactive_r17_enum2value_62,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_extendedDRX_CycleInactive_r17_tags_62[] = {
	(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_extendedDRX_CycleInactive_r17_62 = {
	"extendedDRX-CycleInactive-r17",
	"extendedDRX-CycleInactive-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_extendedDRX_CycleInactive_r17_tags_62,
	sizeof(asn_DEF_NR_extendedDRX_CycleInactive_r17_tags_62)
		/sizeof(asn_DEF_NR_extendedDRX_CycleInactive_r17_tags_62[0]) - 1, /* 1 */
	asn_DEF_NR_extendedDRX_CycleInactive_r17_tags_62,	/* Same as above */
	sizeof(asn_DEF_NR_extendedDRX_CycleInactive_r17_tags_62)
		/sizeof(asn_DEF_NR_extendedDRX_CycleInactive_r17_tags_62[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_extendedDRX_CycleInactive_r17_constr_62,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_extendedDRX_CycleInactive_r17_specs_62	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_simultaneousSR_PUSCH_DiffPUCCH_groups_r17_value2enum_64[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_simultaneousSR_PUSCH_DiffPUCCH_groups_r17_enum2value_64[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_simultaneousSR_PUSCH_DiffPUCCH_groups_r17_specs_64 = {
	asn_MAP_NR_simultaneousSR_PUSCH_DiffPUCCH_groups_r17_value2enum_64,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_simultaneousSR_PUSCH_DiffPUCCH_groups_r17_enum2value_64,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_simultaneousSR_PUSCH_DiffPUCCH_groups_r17_tags_64[] = {
	(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_simultaneousSR_PUSCH_DiffPUCCH_groups_r17_64 = {
	"simultaneousSR-PUSCH-DiffPUCCH-groups-r17",
	"simultaneousSR-PUSCH-DiffPUCCH-groups-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_simultaneousSR_PUSCH_DiffPUCCH_groups_r17_tags_64,
	sizeof(asn_DEF_NR_simultaneousSR_PUSCH_DiffPUCCH_groups_r17_tags_64)
		/sizeof(asn_DEF_NR_simultaneousSR_PUSCH_DiffPUCCH_groups_r17_tags_64[0]) - 1, /* 1 */
	asn_DEF_NR_simultaneousSR_PUSCH_DiffPUCCH_groups_r17_tags_64,	/* Same as above */
	sizeof(asn_DEF_NR_simultaneousSR_PUSCH_DiffPUCCH_groups_r17_tags_64)
		/sizeof(asn_DEF_NR_simultaneousSR_PUSCH_DiffPUCCH_groups_r17_tags_64[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_simultaneousSR_PUSCH_DiffPUCCH_groups_r17_constr_64,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_simultaneousSR_PUSCH_DiffPUCCH_groups_r17_specs_64	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_lastTransmissionUL_r17_value2enum_66[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_lastTransmissionUL_r17_enum2value_66[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_lastTransmissionUL_r17_specs_66 = {
	asn_MAP_NR_lastTransmissionUL_r17_value2enum_66,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_lastTransmissionUL_r17_enum2value_66,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_lastTransmissionUL_r17_tags_66[] = {
	(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_lastTransmissionUL_r17_66 = {
	"lastTransmissionUL-r17",
	"lastTransmissionUL-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_lastTransmissionUL_r17_tags_66,
	sizeof(asn_DEF_NR_lastTransmissionUL_r17_tags_66)
		/sizeof(asn_DEF_NR_lastTransmissionUL_r17_tags_66[0]) - 1, /* 1 */
	asn_DEF_NR_lastTransmissionUL_r17_tags_66,	/* Same as above */
	sizeof(asn_DEF_NR_lastTransmissionUL_r17_tags_66)
		/sizeof(asn_DEF_NR_lastTransmissionUL_r17_tags_66[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_lastTransmissionUL_r17_constr_66,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_lastTransmissionUL_r17_specs_66	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext5_41[] = {
	{ ATF_POINTER, 13, offsetof(struct NR_MAC_ParametersCommon__ext5, enhancedUuDRX_forSidelink_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_enhancedUuDRX_forSidelink_r17_42,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"enhancedUuDRX-forSidelink-r17"
		},
	{ ATF_POINTER, 12, offsetof(struct NR_MAC_ParametersCommon__ext5, mg_ActivationRequestPRS_Meas_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_mg_ActivationRequestPRS_Meas_r17_44,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mg-ActivationRequestPRS-Meas-r17"
		},
	{ ATF_POINTER, 11, offsetof(struct NR_MAC_ParametersCommon__ext5, mg_ActivationCommPRS_Meas_r17),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_mg_ActivationCommPRS_Meas_r17_46,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mg-ActivationCommPRS-Meas-r17"
		},
	{ ATF_POINTER, 10, offsetof(struct NR_MAC_ParametersCommon__ext5, intraCG_Prioritization_r17),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_intraCG_Prioritization_r17_48,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"intraCG-Prioritization-r17"
		},
	{ ATF_POINTER, 9, offsetof(struct NR_MAC_ParametersCommon__ext5, jointPrioritizationCG_Retx_Timer_r17),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_jointPrioritizationCG_Retx_Timer_r17_50,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"jointPrioritizationCG-Retx-Timer-r17"
		},
	{ ATF_POINTER, 8, offsetof(struct NR_MAC_ParametersCommon__ext5, survivalTime_r17),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_survivalTime_r17_52,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"survivalTime-r17"
		},
	{ ATF_POINTER, 7, offsetof(struct NR_MAC_ParametersCommon__ext5, lcg_ExtensionIAB_r17),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_lcg_ExtensionIAB_r17_54,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"lcg-ExtensionIAB-r17"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_MAC_ParametersCommon__ext5, harq_FeedbackDisabled_r17),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_harq_FeedbackDisabled_r17_56,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"harq-FeedbackDisabled-r17"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_MAC_ParametersCommon__ext5, uplink_Harq_ModeB_r17),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_uplink_Harq_ModeB_r17_58,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"uplink-Harq-ModeB-r17"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_MAC_ParametersCommon__ext5, sr_TriggeredBy_TA_Report_r17),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_sr_TriggeredBy_TA_Report_r17_60,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"sr-TriggeredBy-TA-Report-r17"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_MAC_ParametersCommon__ext5, extendedDRX_CycleInactive_r17),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_extendedDRX_CycleInactive_r17_62,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"extendedDRX-CycleInactive-r17"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_MAC_ParametersCommon__ext5, simultaneousSR_PUSCH_DiffPUCCH_groups_r17),
		(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_simultaneousSR_PUSCH_DiffPUCCH_groups_r17_64,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"simultaneousSR-PUSCH-DiffPUCCH-groups-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MAC_ParametersCommon__ext5, lastTransmissionUL_r17),
		(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_lastTransmissionUL_r17_66,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"lastTransmissionUL-r17"
		},
};
static const int asn_MAP_NR_ext5_oms_41[] = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12 };
static const ber_tlv_tag_t asn_DEF_NR_ext5_tags_41[] = {
	(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext5_tag2el_41[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* enhancedUuDRX-forSidelink-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* mg-ActivationRequestPRS-Meas-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* mg-ActivationCommPRS-Meas-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* intraCG-Prioritization-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* jointPrioritizationCG-Retx-Timer-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* survivalTime-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* lcg-ExtensionIAB-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* harq-FeedbackDisabled-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* uplink-Harq-ModeB-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* sr-TriggeredBy-TA-Report-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 }, /* extendedDRX-CycleInactive-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (11 << 2)), 11, 0, 0 }, /* simultaneousSR-PUSCH-DiffPUCCH-groups-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (12 << 2)), 12, 0, 0 } /* lastTransmissionUL-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext5_specs_41 = {
	sizeof(struct NR_MAC_ParametersCommon__ext5),
	offsetof(struct NR_MAC_ParametersCommon__ext5, _asn_ctx),
	asn_MAP_NR_ext5_tag2el_41,
	13,	/* Count of tags in the map */
	asn_MAP_NR_ext5_oms_41,	/* Optional members */
	13, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext5_41 = {
	"ext5",
	"ext5",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext5_tags_41,
	sizeof(asn_DEF_NR_ext5_tags_41)
		/sizeof(asn_DEF_NR_ext5_tags_41[0]) - 1, /* 1 */
	asn_DEF_NR_ext5_tags_41,	/* Same as above */
	sizeof(asn_DEF_NR_ext5_tags_41)
		/sizeof(asn_DEF_NR_ext5_tags_41[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext5_41,
	13,	/* Elements count */
	&asn_SPC_NR_ext5_specs_41	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_MAC_ParametersCommon_1[] = {
	{ ATF_POINTER, 8, offsetof(struct NR_MAC_ParametersCommon, lcp_Restriction),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_lcp_Restriction_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"lcp-Restriction"
		},
	{ ATF_POINTER, 7, offsetof(struct NR_MAC_ParametersCommon, dummy),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dummy_4,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dummy"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_MAC_ParametersCommon, lch_ToSCellRestriction),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_lch_ToSCellRestriction_6,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"lch-ToSCellRestriction"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_MAC_ParametersCommon, ext1),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		0,
		&asn_DEF_NR_ext1_9,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_MAC_ParametersCommon, ext2),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		0,
		&asn_DEF_NR_ext2_14,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext2"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_MAC_ParametersCommon, ext3),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		0,
		&asn_DEF_NR_ext3_35,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext3"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_MAC_ParametersCommon, ext4),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		0,
		&asn_DEF_NR_ext4_38,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext4"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_MAC_ParametersCommon, ext5),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		0,
		&asn_DEF_NR_ext5_41,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext5"
		},
};
static const int asn_MAP_NR_MAC_ParametersCommon_oms_1[] = { 0, 1, 2, 3, 4, 5, 6, 7 };
static const ber_tlv_tag_t asn_DEF_NR_MAC_ParametersCommon_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_MAC_ParametersCommon_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* lcp-Restriction */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* dummy */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* lch-ToSCellRestriction */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* ext1 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* ext2 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* ext3 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* ext4 */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 } /* ext5 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_MAC_ParametersCommon_specs_1 = {
	sizeof(struct NR_MAC_ParametersCommon),
	offsetof(struct NR_MAC_ParametersCommon, _asn_ctx),
	asn_MAP_NR_MAC_ParametersCommon_tag2el_1,
	8,	/* Count of tags in the map */
	asn_MAP_NR_MAC_ParametersCommon_oms_1,	/* Optional members */
	3, 5,	/* Root/Additions */
	3,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_MAC_ParametersCommon = {
	"MAC-ParametersCommon",
	"MAC-ParametersCommon",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_MAC_ParametersCommon_tags_1,
	sizeof(asn_DEF_NR_MAC_ParametersCommon_tags_1)
		/sizeof(asn_DEF_NR_MAC_ParametersCommon_tags_1[0]), /* 1 */
	asn_DEF_NR_MAC_ParametersCommon_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_MAC_ParametersCommon_tags_1)
		/sizeof(asn_DEF_NR_MAC_ParametersCommon_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_MAC_ParametersCommon_1,
	8,	/* Elements count */
	&asn_SPC_NR_MAC_ParametersCommon_specs_1	/* Additional specs */
};

