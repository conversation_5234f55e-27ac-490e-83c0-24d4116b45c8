/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_HighSpeedParameters_v1650_H_
#define	_NR_HighSpeedParameters_v1650_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <constr_CHOICE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_HighSpeedParameters_v1650_PR {
	NR_HighSpeedParameters_v1650_PR_NOTHING,	/* No components present */
	NR_HighSpeedParameters_v1650_PR_intraNR_MeasurementEnhancement_r16,
	NR_HighSpeedParameters_v1650_PR_interRAT_MeasurementEnhancement_r16
} NR_HighSpeedParameters_v1650_PR;
typedef enum NR_HighSpeedParameters_v1650__intraNR_MeasurementEnhancement_r16 {
	NR_HighSpeedParameters_v1650__intraNR_MeasurementEnhancement_r16_supported	= 0
} e_NR_HighSpeedParameters_v1650__intraNR_MeasurementEnhancement_r16;
typedef enum NR_HighSpeedParameters_v1650__interRAT_MeasurementEnhancement_r16 {
	NR_HighSpeedParameters_v1650__interRAT_MeasurementEnhancement_r16_supported	= 0
} e_NR_HighSpeedParameters_v1650__interRAT_MeasurementEnhancement_r16;

/* NR_HighSpeedParameters-v1650 */
typedef struct NR_HighSpeedParameters_v1650 {
	NR_HighSpeedParameters_v1650_PR present;
	union NR_HighSpeedParameters_v1650_u {
		long	 intraNR_MeasurementEnhancement_r16;
		long	 interRAT_MeasurementEnhancement_r16;
	} choice;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_HighSpeedParameters_v1650_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_intraNR_MeasurementEnhancement_r16_2;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_interRAT_MeasurementEnhancement_r16_4;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_HighSpeedParameters_v1650;
extern asn_CHOICE_specifics_t asn_SPC_NR_HighSpeedParameters_v1650_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_HighSpeedParameters_v1650_1[2];
extern asn_per_constraints_t asn_PER_type_NR_HighSpeedParameters_v1650_constr_1;

#ifdef __cplusplus
}
#endif

#endif	/* _NR_HighSpeedParameters_v1650_H_ */
#include <asn_internal.h>
