/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_LogicalChannelConfig_H_
#define	_NR_LogicalChannelConfig_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include <NativeEnumerated.h>
#include "NR_SchedulingRequestId.h"
#include <BOOLEAN.h>
#include "NR_ServCellIndex.h"
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include "NR_SubcarrierSpacing.h"
#include "NR_ConfiguredGrantConfigIndexMAC-r16.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_LogicalChannelConfig__ul_SpecificParameters__prioritisedBitRate {
	NR_LogicalChannelConfig__ul_SpecificParameters__prioritisedBitRate_kBps0	= 0,
	NR_LogicalChannelConfig__ul_SpecificParameters__prioritisedBitRate_kBps8	= 1,
	NR_LogicalChannelConfig__ul_SpecificParameters__prioritisedBitRate_kBps16	= 2,
	NR_LogicalChannelConfig__ul_SpecificParameters__prioritisedBitRate_kBps32	= 3,
	NR_LogicalChannelConfig__ul_SpecificParameters__prioritisedBitRate_kBps64	= 4,
	NR_LogicalChannelConfig__ul_SpecificParameters__prioritisedBitRate_kBps128	= 5,
	NR_LogicalChannelConfig__ul_SpecificParameters__prioritisedBitRate_kBps256	= 6,
	NR_LogicalChannelConfig__ul_SpecificParameters__prioritisedBitRate_kBps512	= 7,
	NR_LogicalChannelConfig__ul_SpecificParameters__prioritisedBitRate_kBps1024	= 8,
	NR_LogicalChannelConfig__ul_SpecificParameters__prioritisedBitRate_kBps2048	= 9,
	NR_LogicalChannelConfig__ul_SpecificParameters__prioritisedBitRate_kBps4096	= 10,
	NR_LogicalChannelConfig__ul_SpecificParameters__prioritisedBitRate_kBps8192	= 11,
	NR_LogicalChannelConfig__ul_SpecificParameters__prioritisedBitRate_kBps16384	= 12,
	NR_LogicalChannelConfig__ul_SpecificParameters__prioritisedBitRate_kBps32768	= 13,
	NR_LogicalChannelConfig__ul_SpecificParameters__prioritisedBitRate_kBps65536	= 14,
	NR_LogicalChannelConfig__ul_SpecificParameters__prioritisedBitRate_infinity	= 15
} e_NR_LogicalChannelConfig__ul_SpecificParameters__prioritisedBitRate;
typedef enum NR_LogicalChannelConfig__ul_SpecificParameters__bucketSizeDuration {
	NR_LogicalChannelConfig__ul_SpecificParameters__bucketSizeDuration_ms5	= 0,
	NR_LogicalChannelConfig__ul_SpecificParameters__bucketSizeDuration_ms10	= 1,
	NR_LogicalChannelConfig__ul_SpecificParameters__bucketSizeDuration_ms20	= 2,
	NR_LogicalChannelConfig__ul_SpecificParameters__bucketSizeDuration_ms50	= 3,
	NR_LogicalChannelConfig__ul_SpecificParameters__bucketSizeDuration_ms100	= 4,
	NR_LogicalChannelConfig__ul_SpecificParameters__bucketSizeDuration_ms150	= 5,
	NR_LogicalChannelConfig__ul_SpecificParameters__bucketSizeDuration_ms300	= 6,
	NR_LogicalChannelConfig__ul_SpecificParameters__bucketSizeDuration_ms500	= 7,
	NR_LogicalChannelConfig__ul_SpecificParameters__bucketSizeDuration_ms1000	= 8,
	NR_LogicalChannelConfig__ul_SpecificParameters__bucketSizeDuration_spare7	= 9,
	NR_LogicalChannelConfig__ul_SpecificParameters__bucketSizeDuration_spare6	= 10,
	NR_LogicalChannelConfig__ul_SpecificParameters__bucketSizeDuration_spare5	= 11,
	NR_LogicalChannelConfig__ul_SpecificParameters__bucketSizeDuration_spare4	= 12,
	NR_LogicalChannelConfig__ul_SpecificParameters__bucketSizeDuration_spare3	= 13,
	NR_LogicalChannelConfig__ul_SpecificParameters__bucketSizeDuration_spare2	= 14,
	NR_LogicalChannelConfig__ul_SpecificParameters__bucketSizeDuration_spare1	= 15
} e_NR_LogicalChannelConfig__ul_SpecificParameters__bucketSizeDuration;
typedef enum NR_LogicalChannelConfig__ul_SpecificParameters__maxPUSCH_Duration {
	NR_LogicalChannelConfig__ul_SpecificParameters__maxPUSCH_Duration_ms0p02	= 0,
	NR_LogicalChannelConfig__ul_SpecificParameters__maxPUSCH_Duration_ms0p04	= 1,
	NR_LogicalChannelConfig__ul_SpecificParameters__maxPUSCH_Duration_ms0p0625	= 2,
	NR_LogicalChannelConfig__ul_SpecificParameters__maxPUSCH_Duration_ms0p125	= 3,
	NR_LogicalChannelConfig__ul_SpecificParameters__maxPUSCH_Duration_ms0p25	= 4,
	NR_LogicalChannelConfig__ul_SpecificParameters__maxPUSCH_Duration_ms0p5	= 5,
	NR_LogicalChannelConfig__ul_SpecificParameters__maxPUSCH_Duration_ms0p01_v1700	= 6,
	NR_LogicalChannelConfig__ul_SpecificParameters__maxPUSCH_Duration_spare1	= 7
} e_NR_LogicalChannelConfig__ul_SpecificParameters__maxPUSCH_Duration;
typedef enum NR_LogicalChannelConfig__ul_SpecificParameters__configuredGrantType1Allowed {
	NR_LogicalChannelConfig__ul_SpecificParameters__configuredGrantType1Allowed_true	= 0
} e_NR_LogicalChannelConfig__ul_SpecificParameters__configuredGrantType1Allowed;
typedef enum NR_LogicalChannelConfig__ul_SpecificParameters__bitRateQueryProhibitTimer {
	NR_LogicalChannelConfig__ul_SpecificParameters__bitRateQueryProhibitTimer_s0	= 0,
	NR_LogicalChannelConfig__ul_SpecificParameters__bitRateQueryProhibitTimer_s0dot4	= 1,
	NR_LogicalChannelConfig__ul_SpecificParameters__bitRateQueryProhibitTimer_s0dot8	= 2,
	NR_LogicalChannelConfig__ul_SpecificParameters__bitRateQueryProhibitTimer_s1dot6	= 3,
	NR_LogicalChannelConfig__ul_SpecificParameters__bitRateQueryProhibitTimer_s3	= 4,
	NR_LogicalChannelConfig__ul_SpecificParameters__bitRateQueryProhibitTimer_s6	= 5,
	NR_LogicalChannelConfig__ul_SpecificParameters__bitRateQueryProhibitTimer_s12	= 6,
	NR_LogicalChannelConfig__ul_SpecificParameters__bitRateQueryProhibitTimer_s30	= 7
} e_NR_LogicalChannelConfig__ul_SpecificParameters__bitRateQueryProhibitTimer;
typedef enum NR_LogicalChannelConfig__ul_SpecificParameters__ext1__allowedPHY_PriorityIndex_r16 {
	NR_LogicalChannelConfig__ul_SpecificParameters__ext1__allowedPHY_PriorityIndex_r16_p0	= 0,
	NR_LogicalChannelConfig__ul_SpecificParameters__ext1__allowedPHY_PriorityIndex_r16_p1	= 1
} e_NR_LogicalChannelConfig__ul_SpecificParameters__ext1__allowedPHY_PriorityIndex_r16;
typedef enum NR_LogicalChannelConfig__ul_SpecificParameters__ext2__allowedHARQ_mode_r17 {
	NR_LogicalChannelConfig__ul_SpecificParameters__ext2__allowedHARQ_mode_r17_harqModeA	= 0,
	NR_LogicalChannelConfig__ul_SpecificParameters__ext2__allowedHARQ_mode_r17_harqModeB	= 1
} e_NR_LogicalChannelConfig__ul_SpecificParameters__ext2__allowedHARQ_mode_r17;
typedef enum NR_LogicalChannelConfig__ext1__bitRateMultiplier_r16 {
	NR_LogicalChannelConfig__ext1__bitRateMultiplier_r16_x40	= 0,
	NR_LogicalChannelConfig__ext1__bitRateMultiplier_r16_x70	= 1,
	NR_LogicalChannelConfig__ext1__bitRateMultiplier_r16_x100	= 2,
	NR_LogicalChannelConfig__ext1__bitRateMultiplier_r16_x200	= 3
} e_NR_LogicalChannelConfig__ext1__bitRateMultiplier_r16;

/* NR_LogicalChannelConfig */
typedef struct NR_LogicalChannelConfig {
	struct NR_LogicalChannelConfig__ul_SpecificParameters {
		long	 priority;
		long	 prioritisedBitRate;
		long	 bucketSizeDuration;
		struct NR_LogicalChannelConfig__ul_SpecificParameters__allowedServingCells {
			A_SEQUENCE_OF(NR_ServCellIndex_t) list;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *allowedServingCells;
		struct NR_LogicalChannelConfig__ul_SpecificParameters__allowedSCS_List {
			A_SEQUENCE_OF(NR_SubcarrierSpacing_t) list;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *allowedSCS_List;
		long	*maxPUSCH_Duration;	/* OPTIONAL */
		long	*configuredGrantType1Allowed;	/* OPTIONAL */
		long	*logicalChannelGroup;	/* OPTIONAL */
		NR_SchedulingRequestId_t	*schedulingRequestID;	/* OPTIONAL */
		BOOLEAN_t	 logicalChannelSR_Mask;
		BOOLEAN_t	 logicalChannelSR_DelayTimerApplied;
		/*
		 * This type is extensible,
		 * possible extensions are below.
		 */
		long	*bitRateQueryProhibitTimer;	/* OPTIONAL */
		struct NR_LogicalChannelConfig__ul_SpecificParameters__ext1 {
			struct NR_LogicalChannelConfig__ul_SpecificParameters__ext1__allowedCG_List_r16 {
				A_SEQUENCE_OF(NR_ConfiguredGrantConfigIndexMAC_r16_t) list;
				
				/* Context for parsing across buffer boundaries */
				asn_struct_ctx_t _asn_ctx;
			} *allowedCG_List_r16;
			long	*allowedPHY_PriorityIndex_r16;	/* OPTIONAL */
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *ext1;
		struct NR_LogicalChannelConfig__ul_SpecificParameters__ext2 {
			long	*logicalChannelGroupIAB_Ext_r17;	/* OPTIONAL */
			long	*allowedHARQ_mode_r17;	/* OPTIONAL */
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *ext2;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ul_SpecificParameters;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	struct NR_LogicalChannelConfig__ext1 {
		long	*channelAccessPriority_r16;	/* OPTIONAL */
		long	*bitRateMultiplier_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext1;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_LogicalChannelConfig_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_prioritisedBitRate_4;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_bucketSizeDuration_21;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxPUSCH_Duration_42;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_configuredGrantType1Allowed_51;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_bitRateQueryProhibitTimer_58;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_allowedPHY_PriorityIndex_r16_70;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_allowedHARQ_mode_r17_75;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_bitRateMultiplier_r16_81;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_LogicalChannelConfig;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_LogicalChannelConfig_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_LogicalChannelConfig_1[2];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_LogicalChannelConfig_H_ */
#include <asn_internal.h>
