/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_DedicatedSIBRequest-r16-IEs.h"

static int
memb_NR_requestedSIB_List_r16_constraint_2(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 8UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_requestedPosSIB_List_r16_constraint_2(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 32UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_requestedSIB_List_r16_constr_3 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (SIZE(1..8)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_requestedPosSIB_List_r16_constr_5 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 5,  5,  1,  32 }	/* (SIZE(1..32)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_requestedSIB_List_r16_constr_3 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (SIZE(1..8)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_requestedPosSIB_List_r16_constr_5 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 5,  5,  1,  32 }	/* (SIZE(1..32)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static asn_TYPE_member_t asn_MBR_NR_requestedSIB_List_r16_3[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (10 << 2)),
		0,
		&asn_DEF_NR_SIB_ReqInfo_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_requestedSIB_List_r16_tags_3[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_requestedSIB_List_r16_specs_3 = {
	sizeof(struct NR_DedicatedSIBRequest_r16_IEs__onDemandSIB_RequestList_r16__requestedSIB_List_r16),
	offsetof(struct NR_DedicatedSIBRequest_r16_IEs__onDemandSIB_RequestList_r16__requestedSIB_List_r16, _asn_ctx),
	1,	/* XER encoding is XMLValueList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_requestedSIB_List_r16_3 = {
	"requestedSIB-List-r16",
	"requestedSIB-List-r16",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_requestedSIB_List_r16_tags_3,
	sizeof(asn_DEF_NR_requestedSIB_List_r16_tags_3)
		/sizeof(asn_DEF_NR_requestedSIB_List_r16_tags_3[0]) - 1, /* 1 */
	asn_DEF_NR_requestedSIB_List_r16_tags_3,	/* Same as above */
	sizeof(asn_DEF_NR_requestedSIB_List_r16_tags_3)
		/sizeof(asn_DEF_NR_requestedSIB_List_r16_tags_3[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_requestedSIB_List_r16_constr_3,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_requestedSIB_List_r16_3,
	1,	/* Single element */
	&asn_SPC_NR_requestedSIB_List_r16_specs_3	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_requestedPosSIB_List_r16_5[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_PosSIB_ReqInfo_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_requestedPosSIB_List_r16_tags_5[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_requestedPosSIB_List_r16_specs_5 = {
	sizeof(struct NR_DedicatedSIBRequest_r16_IEs__onDemandSIB_RequestList_r16__requestedPosSIB_List_r16),
	offsetof(struct NR_DedicatedSIBRequest_r16_IEs__onDemandSIB_RequestList_r16__requestedPosSIB_List_r16, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_requestedPosSIB_List_r16_5 = {
	"requestedPosSIB-List-r16",
	"requestedPosSIB-List-r16",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_requestedPosSIB_List_r16_tags_5,
	sizeof(asn_DEF_NR_requestedPosSIB_List_r16_tags_5)
		/sizeof(asn_DEF_NR_requestedPosSIB_List_r16_tags_5[0]) - 1, /* 1 */
	asn_DEF_NR_requestedPosSIB_List_r16_tags_5,	/* Same as above */
	sizeof(asn_DEF_NR_requestedPosSIB_List_r16_tags_5)
		/sizeof(asn_DEF_NR_requestedPosSIB_List_r16_tags_5[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_requestedPosSIB_List_r16_constr_5,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_requestedPosSIB_List_r16_5,
	1,	/* Single element */
	&asn_SPC_NR_requestedPosSIB_List_r16_specs_5	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_onDemandSIB_RequestList_r16_2[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_DedicatedSIBRequest_r16_IEs__onDemandSIB_RequestList_r16, requestedSIB_List_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_requestedSIB_List_r16_3,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_requestedSIB_List_r16_constr_3,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_requestedSIB_List_r16_constraint_2
		},
		0, 0, /* No default value */
		"requestedSIB-List-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_DedicatedSIBRequest_r16_IEs__onDemandSIB_RequestList_r16, requestedPosSIB_List_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_requestedPosSIB_List_r16_5,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_requestedPosSIB_List_r16_constr_5,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_requestedPosSIB_List_r16_constraint_2
		},
		0, 0, /* No default value */
		"requestedPosSIB-List-r16"
		},
};
static const int asn_MAP_NR_onDemandSIB_RequestList_r16_oms_2[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_onDemandSIB_RequestList_r16_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_onDemandSIB_RequestList_r16_tag2el_2[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* requestedSIB-List-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* requestedPosSIB-List-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_onDemandSIB_RequestList_r16_specs_2 = {
	sizeof(struct NR_DedicatedSIBRequest_r16_IEs__onDemandSIB_RequestList_r16),
	offsetof(struct NR_DedicatedSIBRequest_r16_IEs__onDemandSIB_RequestList_r16, _asn_ctx),
	asn_MAP_NR_onDemandSIB_RequestList_r16_tag2el_2,
	2,	/* Count of tags in the map */
	asn_MAP_NR_onDemandSIB_RequestList_r16_oms_2,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_onDemandSIB_RequestList_r16_2 = {
	"onDemandSIB-RequestList-r16",
	"onDemandSIB-RequestList-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_onDemandSIB_RequestList_r16_tags_2,
	sizeof(asn_DEF_NR_onDemandSIB_RequestList_r16_tags_2)
		/sizeof(asn_DEF_NR_onDemandSIB_RequestList_r16_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_onDemandSIB_RequestList_r16_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_onDemandSIB_RequestList_r16_tags_2)
		/sizeof(asn_DEF_NR_onDemandSIB_RequestList_r16_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_onDemandSIB_RequestList_r16_2,
	2,	/* Elements count */
	&asn_SPC_NR_onDemandSIB_RequestList_r16_specs_2	/* Additional specs */
};

static const ber_tlv_tag_t asn_DEF_NR_nonCriticalExtension_tags_8[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_nonCriticalExtension_specs_8 = {
	sizeof(struct NR_DedicatedSIBRequest_r16_IEs__nonCriticalExtension),
	offsetof(struct NR_DedicatedSIBRequest_r16_IEs__nonCriticalExtension, _asn_ctx),
	0,	/* No top level tags */
	0,	/* No tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_nonCriticalExtension_8 = {
	"nonCriticalExtension",
	"nonCriticalExtension",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_nonCriticalExtension_tags_8,
	sizeof(asn_DEF_NR_nonCriticalExtension_tags_8)
		/sizeof(asn_DEF_NR_nonCriticalExtension_tags_8[0]) - 1, /* 1 */
	asn_DEF_NR_nonCriticalExtension_tags_8,	/* Same as above */
	sizeof(asn_DEF_NR_nonCriticalExtension_tags_8)
		/sizeof(asn_DEF_NR_nonCriticalExtension_tags_8[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	0, 0,	/* No members */
	&asn_SPC_NR_nonCriticalExtension_specs_8	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_DedicatedSIBRequest_r16_IEs_1[] = {
	{ ATF_POINTER, 3, offsetof(struct NR_DedicatedSIBRequest_r16_IEs, onDemandSIB_RequestList_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_onDemandSIB_RequestList_r16_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"onDemandSIB-RequestList-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_DedicatedSIBRequest_r16_IEs, lateNonCriticalExtension),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_OCTET_STRING,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"lateNonCriticalExtension"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_DedicatedSIBRequest_r16_IEs, nonCriticalExtension),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		0,
		&asn_DEF_NR_nonCriticalExtension_8,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"nonCriticalExtension"
		},
};
static const int asn_MAP_NR_DedicatedSIBRequest_r16_IEs_oms_1[] = { 0, 1, 2 };
static const ber_tlv_tag_t asn_DEF_NR_DedicatedSIBRequest_r16_IEs_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_DedicatedSIBRequest_r16_IEs_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* onDemandSIB-RequestList-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* lateNonCriticalExtension */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* nonCriticalExtension */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_DedicatedSIBRequest_r16_IEs_specs_1 = {
	sizeof(struct NR_DedicatedSIBRequest_r16_IEs),
	offsetof(struct NR_DedicatedSIBRequest_r16_IEs, _asn_ctx),
	asn_MAP_NR_DedicatedSIBRequest_r16_IEs_tag2el_1,
	3,	/* Count of tags in the map */
	asn_MAP_NR_DedicatedSIBRequest_r16_IEs_oms_1,	/* Optional members */
	3, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_DedicatedSIBRequest_r16_IEs = {
	"DedicatedSIBRequest-r16-IEs",
	"DedicatedSIBRequest-r16-IEs",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_DedicatedSIBRequest_r16_IEs_tags_1,
	sizeof(asn_DEF_NR_DedicatedSIBRequest_r16_IEs_tags_1)
		/sizeof(asn_DEF_NR_DedicatedSIBRequest_r16_IEs_tags_1[0]), /* 1 */
	asn_DEF_NR_DedicatedSIBRequest_r16_IEs_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_DedicatedSIBRequest_r16_IEs_tags_1)
		/sizeof(asn_DEF_NR_DedicatedSIBRequest_r16_IEs_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_DedicatedSIBRequest_r16_IEs_1,
	3,	/* Elements count */
	&asn_SPC_NR_DedicatedSIBRequest_r16_IEs_specs_1	/* Additional specs */
};

