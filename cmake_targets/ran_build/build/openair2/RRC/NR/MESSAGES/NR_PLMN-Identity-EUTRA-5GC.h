/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_PLMN_Identity_EUTRA_5GC_H_
#define	_NR_PLMN_Identity_EUTRA_5GC_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include <constr_CHOICE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_PLMN_Identity_EUTRA_5GC_PR {
	NR_PLMN_Identity_EUTRA_5GC_PR_NOTHING,	/* No components present */
	NR_PLMN_Identity_EUTRA_5GC_PR_plmn_Identity_EUTRA_5GC,
	NR_PLMN_Identity_EUTRA_5GC_PR_plmn_index
} NR_PLMN_Identity_EUTRA_5GC_PR;

/* Forward declarations */
struct NR_PLMN_Identity;

/* NR_PLMN-Identity-EUTRA-5GC */
typedef struct NR_PLMN_Identity_EUTRA_5GC {
	NR_PLMN_Identity_EUTRA_5GC_PR present;
	union NR_PLMN_Identity_EUTRA_5GC_u {
		struct NR_PLMN_Identity	*plmn_Identity_EUTRA_5GC;
		long	 plmn_index;
	} choice;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_PLMN_Identity_EUTRA_5GC_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_NR_PLMN_Identity_EUTRA_5GC;
extern asn_CHOICE_specifics_t asn_SPC_NR_PLMN_Identity_EUTRA_5GC_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_PLMN_Identity_EUTRA_5GC_1[2];
extern asn_per_constraints_t asn_PER_type_NR_PLMN_Identity_EUTRA_5GC_constr_1;

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_PLMN-Identity.h"

#endif	/* _NR_PLMN_Identity_EUTRA_5GC_H_ */
#include <asn_internal.h>
