/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_PUCCH_format4_H_
#define	_NR_PUCCH_format4_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_PUCCH_format4__occ_Length {
	NR_PUCCH_format4__occ_Length_n2	= 0,
	NR_PUCCH_format4__occ_Length_n4	= 1
} e_NR_PUCCH_format4__occ_Length;
typedef enum NR_PUCCH_format4__occ_Index {
	NR_PUCCH_format4__occ_Index_n0	= 0,
	NR_PUCCH_format4__occ_Index_n1	= 1,
	NR_PUCCH_format4__occ_Index_n2	= 2,
	NR_PUCCH_format4__occ_Index_n3	= 3
} e_NR_PUCCH_format4__occ_Index;

/* NR_PUCCH-format4 */
typedef struct NR_PUCCH_format4 {
	long	 nrofSymbols;
	long	 occ_Length;
	long	 occ_Index;
	long	 startingSymbolIndex;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_PUCCH_format4_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_occ_Length_3;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_occ_Index_6;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_PUCCH_format4;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_PUCCH_format4_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_PUCCH_format4_1[4];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_PUCCH_format4_H_ */
#include <asn_internal.h>
