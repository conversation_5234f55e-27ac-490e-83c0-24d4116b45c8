/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_DRX-Preference-r16.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_preferredDRX_ShortCycleTimer_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 16L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_preferredDRX_InactivityTimer_r16_constr_2 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 5,  5,  0,  31 }	/* (0..31) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_preferredDRX_LongCycle_r16_constr_35 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 5,  5,  0,  31 }	/* (0..31) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_preferredDRX_ShortCycle_r16_constr_68 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 5,  5,  0,  31 }	/* (0..31) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_preferredDRX_ShortCycleTimer_r16_constr_101 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (1..16) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_preferredDRX_InactivityTimer_r16_value2enum_2[] = {
	{ 0,	3,	"ms0" },
	{ 1,	3,	"ms1" },
	{ 2,	3,	"ms2" },
	{ 3,	3,	"ms3" },
	{ 4,	3,	"ms4" },
	{ 5,	3,	"ms5" },
	{ 6,	3,	"ms6" },
	{ 7,	3,	"ms8" },
	{ 8,	4,	"ms10" },
	{ 9,	4,	"ms20" },
	{ 10,	4,	"ms30" },
	{ 11,	4,	"ms40" },
	{ 12,	4,	"ms50" },
	{ 13,	4,	"ms60" },
	{ 14,	4,	"ms80" },
	{ 15,	5,	"ms100" },
	{ 16,	5,	"ms200" },
	{ 17,	5,	"ms300" },
	{ 18,	5,	"ms500" },
	{ 19,	5,	"ms750" },
	{ 20,	6,	"ms1280" },
	{ 21,	6,	"ms1920" },
	{ 22,	6,	"ms2560" },
	{ 23,	6,	"spare9" },
	{ 24,	6,	"spare8" },
	{ 25,	6,	"spare7" },
	{ 26,	6,	"spare6" },
	{ 27,	6,	"spare5" },
	{ 28,	6,	"spare4" },
	{ 29,	6,	"spare3" },
	{ 30,	6,	"spare2" },
	{ 31,	6,	"spare1" }
};
static const unsigned int asn_MAP_NR_preferredDRX_InactivityTimer_r16_enum2value_2[] = {
	0,	/* ms0(0) */
	1,	/* ms1(1) */
	8,	/* ms10(8) */
	15,	/* ms100(15) */
	20,	/* ms1280(20) */
	21,	/* ms1920(21) */
	2,	/* ms2(2) */
	9,	/* ms20(9) */
	16,	/* ms200(16) */
	22,	/* ms2560(22) */
	3,	/* ms3(3) */
	10,	/* ms30(10) */
	17,	/* ms300(17) */
	4,	/* ms4(4) */
	11,	/* ms40(11) */
	5,	/* ms5(5) */
	12,	/* ms50(12) */
	18,	/* ms500(18) */
	6,	/* ms6(6) */
	13,	/* ms60(13) */
	19,	/* ms750(19) */
	7,	/* ms8(7) */
	14,	/* ms80(14) */
	31,	/* spare1(31) */
	30,	/* spare2(30) */
	29,	/* spare3(29) */
	28,	/* spare4(28) */
	27,	/* spare5(27) */
	26,	/* spare6(26) */
	25,	/* spare7(25) */
	24,	/* spare8(24) */
	23	/* spare9(23) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_preferredDRX_InactivityTimer_r16_specs_2 = {
	asn_MAP_NR_preferredDRX_InactivityTimer_r16_value2enum_2,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_preferredDRX_InactivityTimer_r16_enum2value_2,	/* N => "tag"; sorted by N */
	32,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_preferredDRX_InactivityTimer_r16_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_preferredDRX_InactivityTimer_r16_2 = {
	"preferredDRX-InactivityTimer-r16",
	"preferredDRX-InactivityTimer-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_preferredDRX_InactivityTimer_r16_tags_2,
	sizeof(asn_DEF_NR_preferredDRX_InactivityTimer_r16_tags_2)
		/sizeof(asn_DEF_NR_preferredDRX_InactivityTimer_r16_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_preferredDRX_InactivityTimer_r16_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_preferredDRX_InactivityTimer_r16_tags_2)
		/sizeof(asn_DEF_NR_preferredDRX_InactivityTimer_r16_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_preferredDRX_InactivityTimer_r16_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_preferredDRX_InactivityTimer_r16_specs_2	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_preferredDRX_LongCycle_r16_value2enum_35[] = {
	{ 0,	4,	"ms10" },
	{ 1,	4,	"ms20" },
	{ 2,	4,	"ms32" },
	{ 3,	4,	"ms40" },
	{ 4,	4,	"ms60" },
	{ 5,	4,	"ms64" },
	{ 6,	4,	"ms70" },
	{ 7,	4,	"ms80" },
	{ 8,	5,	"ms128" },
	{ 9,	5,	"ms160" },
	{ 10,	5,	"ms256" },
	{ 11,	5,	"ms320" },
	{ 12,	5,	"ms512" },
	{ 13,	5,	"ms640" },
	{ 14,	6,	"ms1024" },
	{ 15,	6,	"ms1280" },
	{ 16,	6,	"ms2048" },
	{ 17,	6,	"ms2560" },
	{ 18,	6,	"ms5120" },
	{ 19,	7,	"ms10240" },
	{ 20,	7,	"spare12" },
	{ 21,	7,	"spare11" },
	{ 22,	7,	"spare10" },
	{ 23,	6,	"spare9" },
	{ 24,	6,	"spare8" },
	{ 25,	6,	"spare7" },
	{ 26,	6,	"spare6" },
	{ 27,	6,	"spare5" },
	{ 28,	6,	"spare4" },
	{ 29,	6,	"spare3" },
	{ 30,	6,	"spare2" },
	{ 31,	6,	"spare1" }
};
static const unsigned int asn_MAP_NR_preferredDRX_LongCycle_r16_enum2value_35[] = {
	0,	/* ms10(0) */
	14,	/* ms1024(14) */
	19,	/* ms10240(19) */
	8,	/* ms128(8) */
	15,	/* ms1280(15) */
	9,	/* ms160(9) */
	1,	/* ms20(1) */
	16,	/* ms2048(16) */
	10,	/* ms256(10) */
	17,	/* ms2560(17) */
	2,	/* ms32(2) */
	11,	/* ms320(11) */
	3,	/* ms40(3) */
	12,	/* ms512(12) */
	18,	/* ms5120(18) */
	4,	/* ms60(4) */
	5,	/* ms64(5) */
	13,	/* ms640(13) */
	6,	/* ms70(6) */
	7,	/* ms80(7) */
	31,	/* spare1(31) */
	22,	/* spare10(22) */
	21,	/* spare11(21) */
	20,	/* spare12(20) */
	30,	/* spare2(30) */
	29,	/* spare3(29) */
	28,	/* spare4(28) */
	27,	/* spare5(27) */
	26,	/* spare6(26) */
	25,	/* spare7(25) */
	24,	/* spare8(24) */
	23	/* spare9(23) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_preferredDRX_LongCycle_r16_specs_35 = {
	asn_MAP_NR_preferredDRX_LongCycle_r16_value2enum_35,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_preferredDRX_LongCycle_r16_enum2value_35,	/* N => "tag"; sorted by N */
	32,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_preferredDRX_LongCycle_r16_tags_35[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_preferredDRX_LongCycle_r16_35 = {
	"preferredDRX-LongCycle-r16",
	"preferredDRX-LongCycle-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_preferredDRX_LongCycle_r16_tags_35,
	sizeof(asn_DEF_NR_preferredDRX_LongCycle_r16_tags_35)
		/sizeof(asn_DEF_NR_preferredDRX_LongCycle_r16_tags_35[0]) - 1, /* 1 */
	asn_DEF_NR_preferredDRX_LongCycle_r16_tags_35,	/* Same as above */
	sizeof(asn_DEF_NR_preferredDRX_LongCycle_r16_tags_35)
		/sizeof(asn_DEF_NR_preferredDRX_LongCycle_r16_tags_35[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_preferredDRX_LongCycle_r16_constr_35,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_preferredDRX_LongCycle_r16_specs_35	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_preferredDRX_ShortCycle_r16_value2enum_68[] = {
	{ 0,	3,	"ms2" },
	{ 1,	3,	"ms3" },
	{ 2,	3,	"ms4" },
	{ 3,	3,	"ms5" },
	{ 4,	3,	"ms6" },
	{ 5,	3,	"ms7" },
	{ 6,	3,	"ms8" },
	{ 7,	4,	"ms10" },
	{ 8,	4,	"ms14" },
	{ 9,	4,	"ms16" },
	{ 10,	4,	"ms20" },
	{ 11,	4,	"ms30" },
	{ 12,	4,	"ms32" },
	{ 13,	4,	"ms35" },
	{ 14,	4,	"ms40" },
	{ 15,	4,	"ms64" },
	{ 16,	4,	"ms80" },
	{ 17,	5,	"ms128" },
	{ 18,	5,	"ms160" },
	{ 19,	5,	"ms256" },
	{ 20,	5,	"ms320" },
	{ 21,	5,	"ms512" },
	{ 22,	5,	"ms640" },
	{ 23,	6,	"spare9" },
	{ 24,	6,	"spare8" },
	{ 25,	6,	"spare7" },
	{ 26,	6,	"spare6" },
	{ 27,	6,	"spare5" },
	{ 28,	6,	"spare4" },
	{ 29,	6,	"spare3" },
	{ 30,	6,	"spare2" },
	{ 31,	6,	"spare1" }
};
static const unsigned int asn_MAP_NR_preferredDRX_ShortCycle_r16_enum2value_68[] = {
	7,	/* ms10(7) */
	17,	/* ms128(17) */
	8,	/* ms14(8) */
	9,	/* ms16(9) */
	18,	/* ms160(18) */
	0,	/* ms2(0) */
	10,	/* ms20(10) */
	19,	/* ms256(19) */
	1,	/* ms3(1) */
	11,	/* ms30(11) */
	12,	/* ms32(12) */
	20,	/* ms320(20) */
	13,	/* ms35(13) */
	2,	/* ms4(2) */
	14,	/* ms40(14) */
	3,	/* ms5(3) */
	21,	/* ms512(21) */
	4,	/* ms6(4) */
	15,	/* ms64(15) */
	22,	/* ms640(22) */
	5,	/* ms7(5) */
	6,	/* ms8(6) */
	16,	/* ms80(16) */
	31,	/* spare1(31) */
	30,	/* spare2(30) */
	29,	/* spare3(29) */
	28,	/* spare4(28) */
	27,	/* spare5(27) */
	26,	/* spare6(26) */
	25,	/* spare7(25) */
	24,	/* spare8(24) */
	23	/* spare9(23) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_preferredDRX_ShortCycle_r16_specs_68 = {
	asn_MAP_NR_preferredDRX_ShortCycle_r16_value2enum_68,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_preferredDRX_ShortCycle_r16_enum2value_68,	/* N => "tag"; sorted by N */
	32,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_preferredDRX_ShortCycle_r16_tags_68[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_preferredDRX_ShortCycle_r16_68 = {
	"preferredDRX-ShortCycle-r16",
	"preferredDRX-ShortCycle-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_preferredDRX_ShortCycle_r16_tags_68,
	sizeof(asn_DEF_NR_preferredDRX_ShortCycle_r16_tags_68)
		/sizeof(asn_DEF_NR_preferredDRX_ShortCycle_r16_tags_68[0]) - 1, /* 1 */
	asn_DEF_NR_preferredDRX_ShortCycle_r16_tags_68,	/* Same as above */
	sizeof(asn_DEF_NR_preferredDRX_ShortCycle_r16_tags_68)
		/sizeof(asn_DEF_NR_preferredDRX_ShortCycle_r16_tags_68[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_preferredDRX_ShortCycle_r16_constr_68,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_preferredDRX_ShortCycle_r16_specs_68	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_DRX_Preference_r16_1[] = {
	{ ATF_POINTER, 4, offsetof(struct NR_DRX_Preference_r16, preferredDRX_InactivityTimer_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_preferredDRX_InactivityTimer_r16_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"preferredDRX-InactivityTimer-r16"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_DRX_Preference_r16, preferredDRX_LongCycle_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_preferredDRX_LongCycle_r16_35,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"preferredDRX-LongCycle-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_DRX_Preference_r16, preferredDRX_ShortCycle_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_preferredDRX_ShortCycle_r16_68,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"preferredDRX-ShortCycle-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_DRX_Preference_r16, preferredDRX_ShortCycleTimer_r16),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_preferredDRX_ShortCycleTimer_r16_constr_101,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_preferredDRX_ShortCycleTimer_r16_constraint_1
		},
		0, 0, /* No default value */
		"preferredDRX-ShortCycleTimer-r16"
		},
};
static const int asn_MAP_NR_DRX_Preference_r16_oms_1[] = { 0, 1, 2, 3 };
static const ber_tlv_tag_t asn_DEF_NR_DRX_Preference_r16_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_DRX_Preference_r16_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* preferredDRX-InactivityTimer-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* preferredDRX-LongCycle-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* preferredDRX-ShortCycle-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 } /* preferredDRX-ShortCycleTimer-r16 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_DRX_Preference_r16_specs_1 = {
	sizeof(struct NR_DRX_Preference_r16),
	offsetof(struct NR_DRX_Preference_r16, _asn_ctx),
	asn_MAP_NR_DRX_Preference_r16_tag2el_1,
	4,	/* Count of tags in the map */
	asn_MAP_NR_DRX_Preference_r16_oms_1,	/* Optional members */
	4, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_DRX_Preference_r16 = {
	"DRX-Preference-r16",
	"DRX-Preference-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_DRX_Preference_r16_tags_1,
	sizeof(asn_DEF_NR_DRX_Preference_r16_tags_1)
		/sizeof(asn_DEF_NR_DRX_Preference_r16_tags_1[0]), /* 1 */
	asn_DEF_NR_DRX_Preference_r16_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_DRX_Preference_r16_tags_1)
		/sizeof(asn_DEF_NR_DRX_Preference_r16_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_DRX_Preference_r16_1,
	4,	/* Elements count */
	&asn_SPC_NR_DRX_Preference_r16_specs_1	/* Additional specs */
};

