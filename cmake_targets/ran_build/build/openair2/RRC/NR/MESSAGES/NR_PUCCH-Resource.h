/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_PUCCH_Resource_H_
#define	_NR_PUCCH_Resource_H_


#include <asn_application.h>

/* Including external dependencies */
#include "NR_PUCCH-ResourceId.h"
#include "NR_PRB-Id.h"
#include <NativeEnumerated.h>
#include <constr_CHOICE.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_PUCCH_Resource__intraSlotFrequencyHopping {
	NR_PUCCH_Resource__intraSlotFrequencyHopping_enabled	= 0
} e_NR_PUCCH_Resource__intraSlotFrequencyHopping;
typedef enum NR_PUCCH_Resource__format_PR {
	NR_PUCCH_Resource__format_PR_NOTHING,	/* No components present */
	NR_PUCCH_Resource__format_PR_format0,
	NR_PUCCH_Resource__format_PR_format1,
	NR_PUCCH_Resource__format_PR_format2,
	NR_PUCCH_Resource__format_PR_format3,
	NR_PUCCH_Resource__format_PR_format4
} NR_PUCCH_Resource__format_PR;

/* Forward declarations */
struct NR_PUCCH_format0;
struct NR_PUCCH_format1;
struct NR_PUCCH_format2;
struct NR_PUCCH_format3;
struct NR_PUCCH_format4;

/* NR_PUCCH-Resource */
typedef struct NR_PUCCH_Resource {
	NR_PUCCH_ResourceId_t	 pucch_ResourceId;
	NR_PRB_Id_t	 startingPRB;
	long	*intraSlotFrequencyHopping;	/* OPTIONAL */
	NR_PRB_Id_t	*secondHopPRB;	/* OPTIONAL */
	struct NR_PUCCH_Resource__format {
		NR_PUCCH_Resource__format_PR present;
		union NR_PUCCH_Resource__NR_format_u {
			struct NR_PUCCH_format0	*format0;
			struct NR_PUCCH_format1	*format1;
			struct NR_PUCCH_format2	*format2;
			struct NR_PUCCH_format3	*format3;
			struct NR_PUCCH_format4	*format4;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} format;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_PUCCH_Resource_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_intraSlotFrequencyHopping_4;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_PUCCH_Resource;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_PUCCH_Resource_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_PUCCH_Resource_1[5];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_PUCCH-format0.h"
#include "NR_PUCCH-format1.h"
#include "NR_PUCCH-format2.h"
#include "NR_PUCCH-format3.h"
#include "NR_PUCCH-format4.h"

#endif	/* _NR_PUCCH_Resource_H_ */
#include <asn_internal.h>
