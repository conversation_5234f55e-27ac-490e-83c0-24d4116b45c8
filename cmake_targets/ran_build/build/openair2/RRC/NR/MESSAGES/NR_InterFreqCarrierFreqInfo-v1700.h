/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_InterFreqCarrierFreqInfo_v1700_H_
#define	_NR_InterFreqCarrierFreqInfo_v1700_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include "NR_SSB-PositionQCL-Relation-r17.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_InterFreqCarrierFreqInfo_v1700__highSpeedMeasInterFreq_r17 {
	NR_InterFreqCarrierFreqInfo_v1700__highSpeedMeasInterFreq_r17_true	= 0
} e_NR_InterFreqCarrierFreqInfo_v1700__highSpeedMeasInterFreq_r17;
typedef enum NR_InterFreqCarrierFreqInfo_v1700__redCapAccessAllowed_r17 {
	NR_InterFreqCarrierFreqInfo_v1700__redCapAccessAllowed_r17_true	= 0
} e_NR_InterFreqCarrierFreqInfo_v1700__redCapAccessAllowed_r17;

/* Forward declarations */
struct NR_InterFreqNeighHSDN_CellList_r17;
struct NR_InterFreqNeighCellList_v1710;

/* NR_InterFreqCarrierFreqInfo-v1700 */
typedef struct NR_InterFreqCarrierFreqInfo_v1700 {
	struct NR_InterFreqNeighHSDN_CellList_r17	*interFreqNeighHSDN_CellList_r17;	/* OPTIONAL */
	long	*highSpeedMeasInterFreq_r17;	/* OPTIONAL */
	long	*redCapAccessAllowed_r17;	/* OPTIONAL */
	NR_SSB_PositionQCL_Relation_r17_t	*ssb_PositionQCL_Common_r17;	/* OPTIONAL */
	struct NR_InterFreqNeighCellList_v1710	*interFreqNeighCellList_v1710;	/* OPTIONAL */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_InterFreqCarrierFreqInfo_v1700_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_highSpeedMeasInterFreq_r17_3;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_redCapAccessAllowed_r17_5;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_InterFreqCarrierFreqInfo_v1700;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_InterFreqCarrierFreqInfo_v1700_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_InterFreqCarrierFreqInfo_v1700_1[5];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_InterFreqNeighHSDN-CellList-r17.h"
#include "NR_InterFreqNeighCellList-v1710.h"

#endif	/* _NR_InterFreqCarrierFreqInfo_v1700_H_ */
#include <asn_internal.h>
