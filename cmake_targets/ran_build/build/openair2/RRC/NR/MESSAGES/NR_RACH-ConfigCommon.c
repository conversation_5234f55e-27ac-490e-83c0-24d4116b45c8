/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_RACH-ConfigCommon.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_four_constraint_4(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 16L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_eight_constraint_4(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 8L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_sixteen_constraint_4(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 4L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_numberOfRA_PreamblesGroupA_constraint_85(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 64L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_l839_constraint_124(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 837L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_l139_constraint_124(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 137L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_ra_PrioritizationForAI_r16_constraint_136(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	const BIT_STRING_t *st = (const BIT_STRING_t *)sptr;
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	if(st->size > 0) {
		/* Size in bits */
		size = 8 * st->size - (st->bits_unused & 0x07);
	} else {
		size = 0;
	}
	
	if((size == 2UL)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_l571_constraint_139(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 569L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_l1151_constraint_139(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 1149L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_featureCombinationPreamblesList_r17_constraint_142(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 256UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_totalNumberOfRA_Preambles_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 63L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_oneEighth_constr_5 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  15 }	/* (0..15) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_oneFourth_constr_22 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  15 }	/* (0..15) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_oneHalf_constr_39 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  15 }	/* (0..15) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_one_constr_56 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  15 }	/* (0..15) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_two_constr_73 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  7 }	/* (0..7) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_four_constr_82 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (1..16) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_eight_constr_83 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (1..8) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_sixteen_constr_84 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  1,  4 }	/* (1..4) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ssb_perRACH_OccasionAndCB_PreamblesPerSSB_constr_4 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  7 }	/* (0..7) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ra_Msg3SizeGroupA_constr_86 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  15 }	/* (0..15) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_messagePowerOffsetGroupB_constr_103 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  7 }	/* (0..7) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_numberOfRA_PreamblesGroupA_constr_112 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 6,  6,  1,  64 }	/* (1..64) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ra_ContentionResolutionTimer_constr_113 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  7 }	/* (0..7) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_l839_constr_125 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 10,  10,  0,  837 }	/* (0..837) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_l139_constr_126 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 8,  8,  0,  137 }	/* (0..137) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_prach_RootSequenceIndex_constr_124 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_restrictedSetConfig_constr_128 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_msg3_transformPrecoder_constr_132 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_ra_PrioritizationForAI_r16_constr_138 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 0,  0,  2,  2 }	/* (SIZE(2..2)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_l571_constr_140 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 10,  10,  0,  569 }	/* (0..569) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_l1151_constr_141 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 11,  11,  0,  1149 }	/* (0..1149) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_prach_RootSequenceIndex_r16_constr_139 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_featureCombinationPreamblesList_r17_constr_144 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 8,  8,  1,  256 }	/* (SIZE(1..256)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_featureCombinationPreamblesList_r17_constr_144 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 8,  8,  1,  256 }	/* (SIZE(1..256)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_totalNumberOfRA_Preambles_constr_3 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 6,  6,  1,  63 }	/* (1..63) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_oneEighth_value2enum_5[] = {
	{ 0,	2,	"n4" },
	{ 1,	2,	"n8" },
	{ 2,	3,	"n12" },
	{ 3,	3,	"n16" },
	{ 4,	3,	"n20" },
	{ 5,	3,	"n24" },
	{ 6,	3,	"n28" },
	{ 7,	3,	"n32" },
	{ 8,	3,	"n36" },
	{ 9,	3,	"n40" },
	{ 10,	3,	"n44" },
	{ 11,	3,	"n48" },
	{ 12,	3,	"n52" },
	{ 13,	3,	"n56" },
	{ 14,	3,	"n60" },
	{ 15,	3,	"n64" }
};
static const unsigned int asn_MAP_NR_oneEighth_enum2value_5[] = {
	2,	/* n12(2) */
	3,	/* n16(3) */
	4,	/* n20(4) */
	5,	/* n24(5) */
	6,	/* n28(6) */
	7,	/* n32(7) */
	8,	/* n36(8) */
	0,	/* n4(0) */
	9,	/* n40(9) */
	10,	/* n44(10) */
	11,	/* n48(11) */
	12,	/* n52(12) */
	13,	/* n56(13) */
	14,	/* n60(14) */
	15,	/* n64(15) */
	1	/* n8(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_oneEighth_specs_5 = {
	asn_MAP_NR_oneEighth_value2enum_5,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_oneEighth_enum2value_5,	/* N => "tag"; sorted by N */
	16,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_oneEighth_tags_5[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_oneEighth_5 = {
	"oneEighth",
	"oneEighth",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_oneEighth_tags_5,
	sizeof(asn_DEF_NR_oneEighth_tags_5)
		/sizeof(asn_DEF_NR_oneEighth_tags_5[0]) - 1, /* 1 */
	asn_DEF_NR_oneEighth_tags_5,	/* Same as above */
	sizeof(asn_DEF_NR_oneEighth_tags_5)
		/sizeof(asn_DEF_NR_oneEighth_tags_5[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_oneEighth_constr_5,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_oneEighth_specs_5	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_oneFourth_value2enum_22[] = {
	{ 0,	2,	"n4" },
	{ 1,	2,	"n8" },
	{ 2,	3,	"n12" },
	{ 3,	3,	"n16" },
	{ 4,	3,	"n20" },
	{ 5,	3,	"n24" },
	{ 6,	3,	"n28" },
	{ 7,	3,	"n32" },
	{ 8,	3,	"n36" },
	{ 9,	3,	"n40" },
	{ 10,	3,	"n44" },
	{ 11,	3,	"n48" },
	{ 12,	3,	"n52" },
	{ 13,	3,	"n56" },
	{ 14,	3,	"n60" },
	{ 15,	3,	"n64" }
};
static const unsigned int asn_MAP_NR_oneFourth_enum2value_22[] = {
	2,	/* n12(2) */
	3,	/* n16(3) */
	4,	/* n20(4) */
	5,	/* n24(5) */
	6,	/* n28(6) */
	7,	/* n32(7) */
	8,	/* n36(8) */
	0,	/* n4(0) */
	9,	/* n40(9) */
	10,	/* n44(10) */
	11,	/* n48(11) */
	12,	/* n52(12) */
	13,	/* n56(13) */
	14,	/* n60(14) */
	15,	/* n64(15) */
	1	/* n8(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_oneFourth_specs_22 = {
	asn_MAP_NR_oneFourth_value2enum_22,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_oneFourth_enum2value_22,	/* N => "tag"; sorted by N */
	16,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_oneFourth_tags_22[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_oneFourth_22 = {
	"oneFourth",
	"oneFourth",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_oneFourth_tags_22,
	sizeof(asn_DEF_NR_oneFourth_tags_22)
		/sizeof(asn_DEF_NR_oneFourth_tags_22[0]) - 1, /* 1 */
	asn_DEF_NR_oneFourth_tags_22,	/* Same as above */
	sizeof(asn_DEF_NR_oneFourth_tags_22)
		/sizeof(asn_DEF_NR_oneFourth_tags_22[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_oneFourth_constr_22,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_oneFourth_specs_22	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_oneHalf_value2enum_39[] = {
	{ 0,	2,	"n4" },
	{ 1,	2,	"n8" },
	{ 2,	3,	"n12" },
	{ 3,	3,	"n16" },
	{ 4,	3,	"n20" },
	{ 5,	3,	"n24" },
	{ 6,	3,	"n28" },
	{ 7,	3,	"n32" },
	{ 8,	3,	"n36" },
	{ 9,	3,	"n40" },
	{ 10,	3,	"n44" },
	{ 11,	3,	"n48" },
	{ 12,	3,	"n52" },
	{ 13,	3,	"n56" },
	{ 14,	3,	"n60" },
	{ 15,	3,	"n64" }
};
static const unsigned int asn_MAP_NR_oneHalf_enum2value_39[] = {
	2,	/* n12(2) */
	3,	/* n16(3) */
	4,	/* n20(4) */
	5,	/* n24(5) */
	6,	/* n28(6) */
	7,	/* n32(7) */
	8,	/* n36(8) */
	0,	/* n4(0) */
	9,	/* n40(9) */
	10,	/* n44(10) */
	11,	/* n48(11) */
	12,	/* n52(12) */
	13,	/* n56(13) */
	14,	/* n60(14) */
	15,	/* n64(15) */
	1	/* n8(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_oneHalf_specs_39 = {
	asn_MAP_NR_oneHalf_value2enum_39,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_oneHalf_enum2value_39,	/* N => "tag"; sorted by N */
	16,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_oneHalf_tags_39[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_oneHalf_39 = {
	"oneHalf",
	"oneHalf",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_oneHalf_tags_39,
	sizeof(asn_DEF_NR_oneHalf_tags_39)
		/sizeof(asn_DEF_NR_oneHalf_tags_39[0]) - 1, /* 1 */
	asn_DEF_NR_oneHalf_tags_39,	/* Same as above */
	sizeof(asn_DEF_NR_oneHalf_tags_39)
		/sizeof(asn_DEF_NR_oneHalf_tags_39[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_oneHalf_constr_39,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_oneHalf_specs_39	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_one_value2enum_56[] = {
	{ 0,	2,	"n4" },
	{ 1,	2,	"n8" },
	{ 2,	3,	"n12" },
	{ 3,	3,	"n16" },
	{ 4,	3,	"n20" },
	{ 5,	3,	"n24" },
	{ 6,	3,	"n28" },
	{ 7,	3,	"n32" },
	{ 8,	3,	"n36" },
	{ 9,	3,	"n40" },
	{ 10,	3,	"n44" },
	{ 11,	3,	"n48" },
	{ 12,	3,	"n52" },
	{ 13,	3,	"n56" },
	{ 14,	3,	"n60" },
	{ 15,	3,	"n64" }
};
static const unsigned int asn_MAP_NR_one_enum2value_56[] = {
	2,	/* n12(2) */
	3,	/* n16(3) */
	4,	/* n20(4) */
	5,	/* n24(5) */
	6,	/* n28(6) */
	7,	/* n32(7) */
	8,	/* n36(8) */
	0,	/* n4(0) */
	9,	/* n40(9) */
	10,	/* n44(10) */
	11,	/* n48(11) */
	12,	/* n52(12) */
	13,	/* n56(13) */
	14,	/* n60(14) */
	15,	/* n64(15) */
	1	/* n8(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_one_specs_56 = {
	asn_MAP_NR_one_value2enum_56,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_one_enum2value_56,	/* N => "tag"; sorted by N */
	16,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_one_tags_56[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_one_56 = {
	"one",
	"one",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_one_tags_56,
	sizeof(asn_DEF_NR_one_tags_56)
		/sizeof(asn_DEF_NR_one_tags_56[0]) - 1, /* 1 */
	asn_DEF_NR_one_tags_56,	/* Same as above */
	sizeof(asn_DEF_NR_one_tags_56)
		/sizeof(asn_DEF_NR_one_tags_56[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_one_constr_56,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_one_specs_56	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_two_value2enum_73[] = {
	{ 0,	2,	"n4" },
	{ 1,	2,	"n8" },
	{ 2,	3,	"n12" },
	{ 3,	3,	"n16" },
	{ 4,	3,	"n20" },
	{ 5,	3,	"n24" },
	{ 6,	3,	"n28" },
	{ 7,	3,	"n32" }
};
static const unsigned int asn_MAP_NR_two_enum2value_73[] = {
	2,	/* n12(2) */
	3,	/* n16(3) */
	4,	/* n20(4) */
	5,	/* n24(5) */
	6,	/* n28(6) */
	7,	/* n32(7) */
	0,	/* n4(0) */
	1	/* n8(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_two_specs_73 = {
	asn_MAP_NR_two_value2enum_73,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_two_enum2value_73,	/* N => "tag"; sorted by N */
	8,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_two_tags_73[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_two_73 = {
	"two",
	"two",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_two_tags_73,
	sizeof(asn_DEF_NR_two_tags_73)
		/sizeof(asn_DEF_NR_two_tags_73[0]) - 1, /* 1 */
	asn_DEF_NR_two_tags_73,	/* Same as above */
	sizeof(asn_DEF_NR_two_tags_73)
		/sizeof(asn_DEF_NR_two_tags_73[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_two_constr_73,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_two_specs_73	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ssb_perRACH_OccasionAndCB_PreamblesPerSSB_4[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommon__ssb_perRACH_OccasionAndCB_PreamblesPerSSB, choice.oneEighth),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_oneEighth_5,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"oneEighth"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommon__ssb_perRACH_OccasionAndCB_PreamblesPerSSB, choice.oneFourth),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_oneFourth_22,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"oneFourth"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommon__ssb_perRACH_OccasionAndCB_PreamblesPerSSB, choice.oneHalf),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_oneHalf_39,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"oneHalf"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommon__ssb_perRACH_OccasionAndCB_PreamblesPerSSB, choice.one),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_one_56,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"one"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommon__ssb_perRACH_OccasionAndCB_PreamblesPerSSB, choice.two),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_two_73,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"two"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommon__ssb_perRACH_OccasionAndCB_PreamblesPerSSB, choice.four),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_four_constr_82,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_four_constraint_4
		},
		0, 0, /* No default value */
		"four"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommon__ssb_perRACH_OccasionAndCB_PreamblesPerSSB, choice.eight),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_eight_constr_83,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_eight_constraint_4
		},
		0, 0, /* No default value */
		"eight"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommon__ssb_perRACH_OccasionAndCB_PreamblesPerSSB, choice.sixteen),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_sixteen_constr_84,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_sixteen_constraint_4
		},
		0, 0, /* No default value */
		"sixteen"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ssb_perRACH_OccasionAndCB_PreamblesPerSSB_tag2el_4[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* oneEighth */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* oneFourth */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* oneHalf */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* one */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* two */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* four */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* eight */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 } /* sixteen */
};
static asn_CHOICE_specifics_t asn_SPC_NR_ssb_perRACH_OccasionAndCB_PreamblesPerSSB_specs_4 = {
	sizeof(struct NR_RACH_ConfigCommon__ssb_perRACH_OccasionAndCB_PreamblesPerSSB),
	offsetof(struct NR_RACH_ConfigCommon__ssb_perRACH_OccasionAndCB_PreamblesPerSSB, _asn_ctx),
	offsetof(struct NR_RACH_ConfigCommon__ssb_perRACH_OccasionAndCB_PreamblesPerSSB, present),
	sizeof(((struct NR_RACH_ConfigCommon__ssb_perRACH_OccasionAndCB_PreamblesPerSSB *)0)->present),
	asn_MAP_NR_ssb_perRACH_OccasionAndCB_PreamblesPerSSB_tag2el_4,
	8,	/* Count of tags in the map */
	0, 0,
	-1	/* Extensions start */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ssb_perRACH_OccasionAndCB_PreamblesPerSSB_4 = {
	"ssb-perRACH-OccasionAndCB-PreamblesPerSSB",
	"ssb-perRACH-OccasionAndCB-PreamblesPerSSB",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ssb_perRACH_OccasionAndCB_PreamblesPerSSB_constr_4,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		CHOICE_constraint
	},
	asn_MBR_NR_ssb_perRACH_OccasionAndCB_PreamblesPerSSB_4,
	8,	/* Elements count */
	&asn_SPC_NR_ssb_perRACH_OccasionAndCB_PreamblesPerSSB_specs_4	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_ra_Msg3SizeGroupA_value2enum_86[] = {
	{ 0,	3,	"b56" },
	{ 1,	4,	"b144" },
	{ 2,	4,	"b208" },
	{ 3,	4,	"b256" },
	{ 4,	4,	"b282" },
	{ 5,	4,	"b480" },
	{ 6,	4,	"b640" },
	{ 7,	4,	"b800" },
	{ 8,	5,	"b1000" },
	{ 9,	3,	"b72" },
	{ 10,	6,	"spare6" },
	{ 11,	6,	"spare5" },
	{ 12,	6,	"spare4" },
	{ 13,	6,	"spare3" },
	{ 14,	6,	"spare2" },
	{ 15,	6,	"spare1" }
};
static const unsigned int asn_MAP_NR_ra_Msg3SizeGroupA_enum2value_86[] = {
	8,	/* b1000(8) */
	1,	/* b144(1) */
	2,	/* b208(2) */
	3,	/* b256(3) */
	4,	/* b282(4) */
	5,	/* b480(5) */
	0,	/* b56(0) */
	6,	/* b640(6) */
	9,	/* b72(9) */
	7,	/* b800(7) */
	15,	/* spare1(15) */
	14,	/* spare2(14) */
	13,	/* spare3(13) */
	12,	/* spare4(12) */
	11,	/* spare5(11) */
	10	/* spare6(10) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ra_Msg3SizeGroupA_specs_86 = {
	asn_MAP_NR_ra_Msg3SizeGroupA_value2enum_86,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ra_Msg3SizeGroupA_enum2value_86,	/* N => "tag"; sorted by N */
	16,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ra_Msg3SizeGroupA_tags_86[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ra_Msg3SizeGroupA_86 = {
	"ra-Msg3SizeGroupA",
	"ra-Msg3SizeGroupA",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ra_Msg3SizeGroupA_tags_86,
	sizeof(asn_DEF_NR_ra_Msg3SizeGroupA_tags_86)
		/sizeof(asn_DEF_NR_ra_Msg3SizeGroupA_tags_86[0]) - 1, /* 1 */
	asn_DEF_NR_ra_Msg3SizeGroupA_tags_86,	/* Same as above */
	sizeof(asn_DEF_NR_ra_Msg3SizeGroupA_tags_86)
		/sizeof(asn_DEF_NR_ra_Msg3SizeGroupA_tags_86[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ra_Msg3SizeGroupA_constr_86,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ra_Msg3SizeGroupA_specs_86	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_messagePowerOffsetGroupB_value2enum_103[] = {
	{ 0,	13,	"minusinfinity" },
	{ 1,	3,	"dB0" },
	{ 2,	3,	"dB5" },
	{ 3,	3,	"dB8" },
	{ 4,	4,	"dB10" },
	{ 5,	4,	"dB12" },
	{ 6,	4,	"dB15" },
	{ 7,	4,	"dB18" }
};
static const unsigned int asn_MAP_NR_messagePowerOffsetGroupB_enum2value_103[] = {
	1,	/* dB0(1) */
	4,	/* dB10(4) */
	5,	/* dB12(5) */
	6,	/* dB15(6) */
	7,	/* dB18(7) */
	2,	/* dB5(2) */
	3,	/* dB8(3) */
	0	/* minusinfinity(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_messagePowerOffsetGroupB_specs_103 = {
	asn_MAP_NR_messagePowerOffsetGroupB_value2enum_103,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_messagePowerOffsetGroupB_enum2value_103,	/* N => "tag"; sorted by N */
	8,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_messagePowerOffsetGroupB_tags_103[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_messagePowerOffsetGroupB_103 = {
	"messagePowerOffsetGroupB",
	"messagePowerOffsetGroupB",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_messagePowerOffsetGroupB_tags_103,
	sizeof(asn_DEF_NR_messagePowerOffsetGroupB_tags_103)
		/sizeof(asn_DEF_NR_messagePowerOffsetGroupB_tags_103[0]) - 1, /* 1 */
	asn_DEF_NR_messagePowerOffsetGroupB_tags_103,	/* Same as above */
	sizeof(asn_DEF_NR_messagePowerOffsetGroupB_tags_103)
		/sizeof(asn_DEF_NR_messagePowerOffsetGroupB_tags_103[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_messagePowerOffsetGroupB_constr_103,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_messagePowerOffsetGroupB_specs_103	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_groupBconfigured_85[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommon__groupBconfigured, ra_Msg3SizeGroupA),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ra_Msg3SizeGroupA_86,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ra-Msg3SizeGroupA"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommon__groupBconfigured, messagePowerOffsetGroupB),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_messagePowerOffsetGroupB_103,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"messagePowerOffsetGroupB"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommon__groupBconfigured, numberOfRA_PreamblesGroupA),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_numberOfRA_PreamblesGroupA_constr_112,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_numberOfRA_PreamblesGroupA_constraint_85
		},
		0, 0, /* No default value */
		"numberOfRA-PreamblesGroupA"
		},
};
static const ber_tlv_tag_t asn_DEF_NR_groupBconfigured_tags_85[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_groupBconfigured_tag2el_85[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* ra-Msg3SizeGroupA */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* messagePowerOffsetGroupB */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* numberOfRA-PreamblesGroupA */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_groupBconfigured_specs_85 = {
	sizeof(struct NR_RACH_ConfigCommon__groupBconfigured),
	offsetof(struct NR_RACH_ConfigCommon__groupBconfigured, _asn_ctx),
	asn_MAP_NR_groupBconfigured_tag2el_85,
	3,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_groupBconfigured_85 = {
	"groupBconfigured",
	"groupBconfigured",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_groupBconfigured_tags_85,
	sizeof(asn_DEF_NR_groupBconfigured_tags_85)
		/sizeof(asn_DEF_NR_groupBconfigured_tags_85[0]) - 1, /* 1 */
	asn_DEF_NR_groupBconfigured_tags_85,	/* Same as above */
	sizeof(asn_DEF_NR_groupBconfigured_tags_85)
		/sizeof(asn_DEF_NR_groupBconfigured_tags_85[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_groupBconfigured_85,
	3,	/* Elements count */
	&asn_SPC_NR_groupBconfigured_specs_85	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_ra_ContentionResolutionTimer_value2enum_113[] = {
	{ 0,	3,	"sf8" },
	{ 1,	4,	"sf16" },
	{ 2,	4,	"sf24" },
	{ 3,	4,	"sf32" },
	{ 4,	4,	"sf40" },
	{ 5,	4,	"sf48" },
	{ 6,	4,	"sf56" },
	{ 7,	4,	"sf64" }
};
static const unsigned int asn_MAP_NR_ra_ContentionResolutionTimer_enum2value_113[] = {
	1,	/* sf16(1) */
	2,	/* sf24(2) */
	3,	/* sf32(3) */
	4,	/* sf40(4) */
	5,	/* sf48(5) */
	6,	/* sf56(6) */
	7,	/* sf64(7) */
	0	/* sf8(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ra_ContentionResolutionTimer_specs_113 = {
	asn_MAP_NR_ra_ContentionResolutionTimer_value2enum_113,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ra_ContentionResolutionTimer_enum2value_113,	/* N => "tag"; sorted by N */
	8,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ra_ContentionResolutionTimer_tags_113[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ra_ContentionResolutionTimer_113 = {
	"ra-ContentionResolutionTimer",
	"ra-ContentionResolutionTimer",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ra_ContentionResolutionTimer_tags_113,
	sizeof(asn_DEF_NR_ra_ContentionResolutionTimer_tags_113)
		/sizeof(asn_DEF_NR_ra_ContentionResolutionTimer_tags_113[0]) - 1, /* 1 */
	asn_DEF_NR_ra_ContentionResolutionTimer_tags_113,	/* Same as above */
	sizeof(asn_DEF_NR_ra_ContentionResolutionTimer_tags_113)
		/sizeof(asn_DEF_NR_ra_ContentionResolutionTimer_tags_113[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ra_ContentionResolutionTimer_constr_113,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ra_ContentionResolutionTimer_specs_113	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_prach_RootSequenceIndex_124[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommon__prach_RootSequenceIndex, choice.l839),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_l839_constr_125,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_l839_constraint_124
		},
		0, 0, /* No default value */
		"l839"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommon__prach_RootSequenceIndex, choice.l139),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_l139_constr_126,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_l139_constraint_124
		},
		0, 0, /* No default value */
		"l139"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_NR_prach_RootSequenceIndex_tag2el_124[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* l839 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* l139 */
};
static asn_CHOICE_specifics_t asn_SPC_NR_prach_RootSequenceIndex_specs_124 = {
	sizeof(struct NR_RACH_ConfigCommon__prach_RootSequenceIndex),
	offsetof(struct NR_RACH_ConfigCommon__prach_RootSequenceIndex, _asn_ctx),
	offsetof(struct NR_RACH_ConfigCommon__prach_RootSequenceIndex, present),
	sizeof(((struct NR_RACH_ConfigCommon__prach_RootSequenceIndex *)0)->present),
	asn_MAP_NR_prach_RootSequenceIndex_tag2el_124,
	2,	/* Count of tags in the map */
	0, 0,
	-1	/* Extensions start */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_prach_RootSequenceIndex_124 = {
	"prach-RootSequenceIndex",
	"prach-RootSequenceIndex",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_prach_RootSequenceIndex_constr_124,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		CHOICE_constraint
	},
	asn_MBR_NR_prach_RootSequenceIndex_124,
	2,	/* Elements count */
	&asn_SPC_NR_prach_RootSequenceIndex_specs_124	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_restrictedSetConfig_value2enum_128[] = {
	{ 0,	15,	"unrestrictedSet" },
	{ 1,	18,	"restrictedSetTypeA" },
	{ 2,	18,	"restrictedSetTypeB" }
};
static const unsigned int asn_MAP_NR_restrictedSetConfig_enum2value_128[] = {
	1,	/* restrictedSetTypeA(1) */
	2,	/* restrictedSetTypeB(2) */
	0	/* unrestrictedSet(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_restrictedSetConfig_specs_128 = {
	asn_MAP_NR_restrictedSetConfig_value2enum_128,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_restrictedSetConfig_enum2value_128,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_restrictedSetConfig_tags_128[] = {
	(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_restrictedSetConfig_128 = {
	"restrictedSetConfig",
	"restrictedSetConfig",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_restrictedSetConfig_tags_128,
	sizeof(asn_DEF_NR_restrictedSetConfig_tags_128)
		/sizeof(asn_DEF_NR_restrictedSetConfig_tags_128[0]) - 1, /* 1 */
	asn_DEF_NR_restrictedSetConfig_tags_128,	/* Same as above */
	sizeof(asn_DEF_NR_restrictedSetConfig_tags_128)
		/sizeof(asn_DEF_NR_restrictedSetConfig_tags_128[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_restrictedSetConfig_constr_128,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_restrictedSetConfig_specs_128	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_msg3_transformPrecoder_value2enum_132[] = {
	{ 0,	7,	"enabled" }
};
static const unsigned int asn_MAP_NR_msg3_transformPrecoder_enum2value_132[] = {
	0	/* enabled(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_msg3_transformPrecoder_specs_132 = {
	asn_MAP_NR_msg3_transformPrecoder_value2enum_132,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_msg3_transformPrecoder_enum2value_132,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_msg3_transformPrecoder_tags_132[] = {
	(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_msg3_transformPrecoder_132 = {
	"msg3-transformPrecoder",
	"msg3-transformPrecoder",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_msg3_transformPrecoder_tags_132,
	sizeof(asn_DEF_NR_msg3_transformPrecoder_tags_132)
		/sizeof(asn_DEF_NR_msg3_transformPrecoder_tags_132[0]) - 1, /* 1 */
	asn_DEF_NR_msg3_transformPrecoder_tags_132,	/* Same as above */
	sizeof(asn_DEF_NR_msg3_transformPrecoder_tags_132)
		/sizeof(asn_DEF_NR_msg3_transformPrecoder_tags_132[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_msg3_transformPrecoder_constr_132,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_msg3_transformPrecoder_specs_132	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ra_PrioritizationForAccessIdentity_r16_136[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommon__ext1__ra_PrioritizationForAccessIdentity_r16, ra_Prioritization_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_RA_Prioritization,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ra-Prioritization-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommon__ext1__ra_PrioritizationForAccessIdentity_r16, ra_PrioritizationForAI_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_BIT_STRING,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_ra_PrioritizationForAI_r16_constr_138,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_ra_PrioritizationForAI_r16_constraint_136
		},
		0, 0, /* No default value */
		"ra-PrioritizationForAI-r16"
		},
};
static const ber_tlv_tag_t asn_DEF_NR_ra_PrioritizationForAccessIdentity_r16_tags_136[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ra_PrioritizationForAccessIdentity_r16_tag2el_136[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* ra-Prioritization-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* ra-PrioritizationForAI-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ra_PrioritizationForAccessIdentity_r16_specs_136 = {
	sizeof(struct NR_RACH_ConfigCommon__ext1__ra_PrioritizationForAccessIdentity_r16),
	offsetof(struct NR_RACH_ConfigCommon__ext1__ra_PrioritizationForAccessIdentity_r16, _asn_ctx),
	asn_MAP_NR_ra_PrioritizationForAccessIdentity_r16_tag2el_136,
	2,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ra_PrioritizationForAccessIdentity_r16_136 = {
	"ra-PrioritizationForAccessIdentity-r16",
	"ra-PrioritizationForAccessIdentity-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ra_PrioritizationForAccessIdentity_r16_tags_136,
	sizeof(asn_DEF_NR_ra_PrioritizationForAccessIdentity_r16_tags_136)
		/sizeof(asn_DEF_NR_ra_PrioritizationForAccessIdentity_r16_tags_136[0]) - 1, /* 1 */
	asn_DEF_NR_ra_PrioritizationForAccessIdentity_r16_tags_136,	/* Same as above */
	sizeof(asn_DEF_NR_ra_PrioritizationForAccessIdentity_r16_tags_136)
		/sizeof(asn_DEF_NR_ra_PrioritizationForAccessIdentity_r16_tags_136[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ra_PrioritizationForAccessIdentity_r16_136,
	2,	/* Elements count */
	&asn_SPC_NR_ra_PrioritizationForAccessIdentity_r16_specs_136	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_prach_RootSequenceIndex_r16_139[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommon__ext1__prach_RootSequenceIndex_r16, choice.l571),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_l571_constr_140,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_l571_constraint_139
		},
		0, 0, /* No default value */
		"l571"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommon__ext1__prach_RootSequenceIndex_r16, choice.l1151),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_l1151_constr_141,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_l1151_constraint_139
		},
		0, 0, /* No default value */
		"l1151"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_NR_prach_RootSequenceIndex_r16_tag2el_139[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* l571 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* l1151 */
};
static asn_CHOICE_specifics_t asn_SPC_NR_prach_RootSequenceIndex_r16_specs_139 = {
	sizeof(struct NR_RACH_ConfigCommon__ext1__prach_RootSequenceIndex_r16),
	offsetof(struct NR_RACH_ConfigCommon__ext1__prach_RootSequenceIndex_r16, _asn_ctx),
	offsetof(struct NR_RACH_ConfigCommon__ext1__prach_RootSequenceIndex_r16, present),
	sizeof(((struct NR_RACH_ConfigCommon__ext1__prach_RootSequenceIndex_r16 *)0)->present),
	asn_MAP_NR_prach_RootSequenceIndex_r16_tag2el_139,
	2,	/* Count of tags in the map */
	0, 0,
	-1	/* Extensions start */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_prach_RootSequenceIndex_r16_139 = {
	"prach-RootSequenceIndex-r16",
	"prach-RootSequenceIndex-r16",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_prach_RootSequenceIndex_r16_constr_139,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		CHOICE_constraint
	},
	asn_MBR_NR_prach_RootSequenceIndex_r16_139,
	2,	/* Elements count */
	&asn_SPC_NR_prach_RootSequenceIndex_r16_specs_139	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext1_135[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_RACH_ConfigCommon__ext1, ra_PrioritizationForAccessIdentity_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_ra_PrioritizationForAccessIdentity_r16_136,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ra-PrioritizationForAccessIdentity-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RACH_ConfigCommon__ext1, prach_RootSequenceIndex_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_prach_RootSequenceIndex_r16_139,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"prach-RootSequenceIndex-r16"
		},
};
static const int asn_MAP_NR_ext1_oms_135[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_135[] = {
	(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_135[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* ra-PrioritizationForAccessIdentity-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* prach-RootSequenceIndex-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_135 = {
	sizeof(struct NR_RACH_ConfigCommon__ext1),
	offsetof(struct NR_RACH_ConfigCommon__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_135,
	2,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_135,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_135 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_135,
	sizeof(asn_DEF_NR_ext1_tags_135)
		/sizeof(asn_DEF_NR_ext1_tags_135[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_135,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_135)
		/sizeof(asn_DEF_NR_ext1_tags_135[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_135,
	2,	/* Elements count */
	&asn_SPC_NR_ext1_specs_135	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_featureCombinationPreamblesList_r17_144[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_FeatureCombinationPreambles_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_featureCombinationPreamblesList_r17_tags_144[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_featureCombinationPreamblesList_r17_specs_144 = {
	sizeof(struct NR_RACH_ConfigCommon__ext2__featureCombinationPreamblesList_r17),
	offsetof(struct NR_RACH_ConfigCommon__ext2__featureCombinationPreamblesList_r17, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_featureCombinationPreamblesList_r17_144 = {
	"featureCombinationPreamblesList-r17",
	"featureCombinationPreamblesList-r17",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_featureCombinationPreamblesList_r17_tags_144,
	sizeof(asn_DEF_NR_featureCombinationPreamblesList_r17_tags_144)
		/sizeof(asn_DEF_NR_featureCombinationPreamblesList_r17_tags_144[0]) - 1, /* 1 */
	asn_DEF_NR_featureCombinationPreamblesList_r17_tags_144,	/* Same as above */
	sizeof(asn_DEF_NR_featureCombinationPreamblesList_r17_tags_144)
		/sizeof(asn_DEF_NR_featureCombinationPreamblesList_r17_tags_144[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_featureCombinationPreamblesList_r17_constr_144,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_featureCombinationPreamblesList_r17_144,
	1,	/* Single element */
	&asn_SPC_NR_featureCombinationPreamblesList_r17_specs_144	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext2_142[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_RACH_ConfigCommon__ext2, ra_PrioritizationForSlicing_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_RA_PrioritizationForSlicing_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ra-PrioritizationForSlicing-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RACH_ConfigCommon__ext2, featureCombinationPreamblesList_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_featureCombinationPreamblesList_r17_144,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_featureCombinationPreamblesList_r17_constr_144,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_featureCombinationPreamblesList_r17_constraint_142
		},
		0, 0, /* No default value */
		"featureCombinationPreamblesList-r17"
		},
};
static const int asn_MAP_NR_ext2_oms_142[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_ext2_tags_142[] = {
	(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext2_tag2el_142[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* ra-PrioritizationForSlicing-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* featureCombinationPreamblesList-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext2_specs_142 = {
	sizeof(struct NR_RACH_ConfigCommon__ext2),
	offsetof(struct NR_RACH_ConfigCommon__ext2, _asn_ctx),
	asn_MAP_NR_ext2_tag2el_142,
	2,	/* Count of tags in the map */
	asn_MAP_NR_ext2_oms_142,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext2_142 = {
	"ext2",
	"ext2",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext2_tags_142,
	sizeof(asn_DEF_NR_ext2_tags_142)
		/sizeof(asn_DEF_NR_ext2_tags_142[0]) - 1, /* 1 */
	asn_DEF_NR_ext2_tags_142,	/* Same as above */
	sizeof(asn_DEF_NR_ext2_tags_142)
		/sizeof(asn_DEF_NR_ext2_tags_142[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext2_142,
	2,	/* Elements count */
	&asn_SPC_NR_ext2_specs_142	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_RACH_ConfigCommon_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommon, rach_ConfigGeneric),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_RACH_ConfigGeneric,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"rach-ConfigGeneric"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_RACH_ConfigCommon, totalNumberOfRA_Preambles),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_totalNumberOfRA_Preambles_constr_3,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_totalNumberOfRA_Preambles_constraint_1
		},
		0, 0, /* No default value */
		"totalNumberOfRA-Preambles"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_RACH_ConfigCommon, ssb_perRACH_OccasionAndCB_PreamblesPerSSB),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_ssb_perRACH_OccasionAndCB_PreamblesPerSSB_4,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ssb-perRACH-OccasionAndCB-PreamblesPerSSB"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RACH_ConfigCommon, groupBconfigured),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		0,
		&asn_DEF_NR_groupBconfigured_85,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"groupBconfigured"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommon, ra_ContentionResolutionTimer),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ra_ContentionResolutionTimer_113,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ra-ContentionResolutionTimer"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_RACH_ConfigCommon, rsrp_ThresholdSSB),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_RSRP_Range,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"rsrp-ThresholdSSB"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RACH_ConfigCommon, rsrp_ThresholdSSB_SUL),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_RSRP_Range,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"rsrp-ThresholdSSB-SUL"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommon, prach_RootSequenceIndex),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_prach_RootSequenceIndex_124,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"prach-RootSequenceIndex"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RACH_ConfigCommon, msg1_SubcarrierSpacing),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_SubcarrierSpacing,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"msg1-SubcarrierSpacing"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommon, restrictedSetConfig),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_restrictedSetConfig_128,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"restrictedSetConfig"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_RACH_ConfigCommon, msg3_transformPrecoder),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_msg3_transformPrecoder_132,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"msg3-transformPrecoder"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_RACH_ConfigCommon, ext1),
		(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
		0,
		&asn_DEF_NR_ext1_135,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RACH_ConfigCommon, ext2),
		(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
		0,
		&asn_DEF_NR_ext2_142,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext2"
		},
};
static const int asn_MAP_NR_RACH_ConfigCommon_oms_1[] = { 1, 2, 3, 5, 6, 8, 10, 11, 12 };
static const ber_tlv_tag_t asn_DEF_NR_RACH_ConfigCommon_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_RACH_ConfigCommon_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* rach-ConfigGeneric */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* totalNumberOfRA-Preambles */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* ssb-perRACH-OccasionAndCB-PreamblesPerSSB */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* groupBconfigured */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* ra-ContentionResolutionTimer */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* rsrp-ThresholdSSB */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* rsrp-ThresholdSSB-SUL */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* prach-RootSequenceIndex */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* msg1-SubcarrierSpacing */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* restrictedSetConfig */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 }, /* msg3-transformPrecoder */
    { (ASN_TAG_CLASS_CONTEXT | (11 << 2)), 11, 0, 0 }, /* ext1 */
    { (ASN_TAG_CLASS_CONTEXT | (12 << 2)), 12, 0, 0 } /* ext2 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_RACH_ConfigCommon_specs_1 = {
	sizeof(struct NR_RACH_ConfigCommon),
	offsetof(struct NR_RACH_ConfigCommon, _asn_ctx),
	asn_MAP_NR_RACH_ConfigCommon_tag2el_1,
	13,	/* Count of tags in the map */
	asn_MAP_NR_RACH_ConfigCommon_oms_1,	/* Optional members */
	7, 2,	/* Root/Additions */
	11,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_RACH_ConfigCommon = {
	"RACH-ConfigCommon",
	"RACH-ConfigCommon",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_RACH_ConfigCommon_tags_1,
	sizeof(asn_DEF_NR_RACH_ConfigCommon_tags_1)
		/sizeof(asn_DEF_NR_RACH_ConfigCommon_tags_1[0]), /* 1 */
	asn_DEF_NR_RACH_ConfigCommon_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_RACH_ConfigCommon_tags_1)
		/sizeof(asn_DEF_NR_RACH_ConfigCommon_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_RACH_ConfigCommon_1,
	13,	/* Elements count */
	&asn_SPC_NR_RACH_ConfigCommon_specs_1	/* Additional specs */
};

