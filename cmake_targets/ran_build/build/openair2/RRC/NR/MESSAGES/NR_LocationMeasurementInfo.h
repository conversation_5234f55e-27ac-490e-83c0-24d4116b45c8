/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_LocationMeasurementInfo_H_
#define	_NR_LocationMeasurementInfo_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NULL.h>
#include <constr_CHOICE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_LocationMeasurementInfo_PR {
	NR_LocationMeasurementInfo_PR_NOTHING,	/* No components present */
	NR_LocationMeasurementInfo_PR_eutra_RSTD,
	/* Extensions may appear below */
	NR_LocationMeasurementInfo_PR_eutra_FineTimingDetection,
	NR_LocationMeasurementInfo_PR_nr_PRS_Measurement_r16
} NR_LocationMeasurementInfo_PR;

/* Forward declarations */
struct NR_EUTRA_RSTD_InfoList;
struct NR_NR_PRS_MeasurementInfoList_r16;

/* NR_LocationMeasurementInfo */
typedef struct NR_LocationMeasurementInfo {
	NR_LocationMeasurementInfo_PR present;
	union NR_LocationMeasurementInfo_u {
		struct NR_EUTRA_RSTD_InfoList	*eutra_RSTD;
		/*
		 * This type is extensible,
		 * possible extensions are below.
		 */
		NULL_t	 eutra_FineTimingDetection;
		struct NR_NR_PRS_MeasurementInfoList_r16	*nr_PRS_Measurement_r16;
	} choice;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_LocationMeasurementInfo_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_NR_LocationMeasurementInfo;
extern asn_CHOICE_specifics_t asn_SPC_NR_LocationMeasurementInfo_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_LocationMeasurementInfo_1[3];
extern asn_per_constraints_t asn_PER_type_NR_LocationMeasurementInfo_constr_1;

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_EUTRA-RSTD-InfoList.h"
#include "NR_NR-PRS-MeasurementInfoList-r16.h"

#endif	/* _NR_LocationMeasurementInfo_H_ */
#include <asn_internal.h>
