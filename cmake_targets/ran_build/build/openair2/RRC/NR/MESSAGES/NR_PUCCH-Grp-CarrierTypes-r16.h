/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_PUCCH_Grp_CarrierTypes_r16_H_
#define	_NR_PUCCH_Grp_CarrierTypes_r16_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_PUCCH_Grp_CarrierTypes_r16__fr1_NonSharedTDD_r16 {
	NR_PUCCH_Grp_CarrierTypes_r16__fr1_NonSharedTDD_r16_supported	= 0
} e_NR_PUCCH_Grp_CarrierTypes_r16__fr1_NonSharedTDD_r16;
typedef enum NR_PUCCH_Grp_CarrierTypes_r16__fr1_SharedTDD_r16 {
	NR_PUCCH_Grp_CarrierTypes_r16__fr1_SharedTDD_r16_supported	= 0
} e_NR_PUCCH_Grp_CarrierTypes_r16__fr1_SharedTDD_r16;
typedef enum NR_PUCCH_Grp_CarrierTypes_r16__fr1_NonSharedFDD_r16 {
	NR_PUCCH_Grp_CarrierTypes_r16__fr1_NonSharedFDD_r16_supported	= 0
} e_NR_PUCCH_Grp_CarrierTypes_r16__fr1_NonSharedFDD_r16;
typedef enum NR_PUCCH_Grp_CarrierTypes_r16__fr2_r16 {
	NR_PUCCH_Grp_CarrierTypes_r16__fr2_r16_supported	= 0
} e_NR_PUCCH_Grp_CarrierTypes_r16__fr2_r16;

/* NR_PUCCH-Grp-CarrierTypes-r16 */
typedef struct NR_PUCCH_Grp_CarrierTypes_r16 {
	long	*fr1_NonSharedTDD_r16;	/* OPTIONAL */
	long	*fr1_SharedTDD_r16;	/* OPTIONAL */
	long	*fr1_NonSharedFDD_r16;	/* OPTIONAL */
	long	*fr2_r16;	/* OPTIONAL */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_PUCCH_Grp_CarrierTypes_r16_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_fr1_NonSharedTDD_r16_2;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_fr1_SharedTDD_r16_4;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_fr1_NonSharedFDD_r16_6;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_fr2_r16_8;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_PUCCH_Grp_CarrierTypes_r16;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_PUCCH_Grp_CarrierTypes_r16_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_PUCCH_Grp_CarrierTypes_r16_1[4];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_PUCCH_Grp_CarrierTypes_r16_H_ */
#include <asn_internal.h>
