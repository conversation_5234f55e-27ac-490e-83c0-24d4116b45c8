/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_InterFreqCarrierFreqInfo_H_
#define	_NR_InterFreqCarrierFreqInfo_H_


#include <asn_application.h>

/* Including external dependencies */
#include "NR_ARFCN-ValueNR.h"
#include <NativeInteger.h>
#include "NR_SubcarrierSpacing.h"
#include <BOOLEAN.h>
#include "NR_Q-RxLevMin.h"
#include "NR_Q-QualMin.h"
#include "NR_P-Max.h"
#include "NR_T-Reselection.h"
#include "NR_ReselectionThreshold.h"
#include "NR_CellReselectionPriority.h"
#include "NR_CellReselectionSubPriority.h"
#include "NR_Q-OffsetRange.h"
#include "NR_ReselectionThresholdQ.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct NR_MultiFrequencyBandListNR_SIB;
struct NR_ThresholdNR;
struct NR_SSB_MTC;
struct NR_SSB_ToMeasure;
struct NR_SS_RSSI_Measurement;
struct NR_SpeedStateScaleFactors;
struct NR_InterFreqNeighCellList;
struct NR_InterFreqExcludedCellList;

/* NR_InterFreqCarrierFreqInfo */
typedef struct NR_InterFreqCarrierFreqInfo {
	NR_ARFCN_ValueNR_t	 dl_CarrierFreq;
	struct NR_MultiFrequencyBandListNR_SIB	*frequencyBandList;	/* OPTIONAL */
	struct NR_MultiFrequencyBandListNR_SIB	*frequencyBandListSUL;	/* OPTIONAL */
	long	*nrofSS_BlocksToAverage;	/* OPTIONAL */
	struct NR_ThresholdNR	*absThreshSS_BlocksConsolidation;	/* OPTIONAL */
	struct NR_SSB_MTC	*smtc;	/* OPTIONAL */
	NR_SubcarrierSpacing_t	 ssbSubcarrierSpacing;
	struct NR_SSB_ToMeasure	*ssb_ToMeasure;	/* OPTIONAL */
	BOOLEAN_t	 deriveSSB_IndexFromCell;
	struct NR_SS_RSSI_Measurement	*ss_RSSI_Measurement;	/* OPTIONAL */
	NR_Q_RxLevMin_t	 q_RxLevMin;
	NR_Q_RxLevMin_t	*q_RxLevMinSUL;	/* OPTIONAL */
	NR_Q_QualMin_t	*q_QualMin;	/* OPTIONAL */
	NR_P_Max_t	*p_Max;	/* OPTIONAL */
	NR_T_Reselection_t	 t_ReselectionNR;
	struct NR_SpeedStateScaleFactors	*t_ReselectionNR_SF;	/* OPTIONAL */
	NR_ReselectionThreshold_t	 threshX_HighP;
	NR_ReselectionThreshold_t	 threshX_LowP;
	struct NR_InterFreqCarrierFreqInfo__threshX_Q {
		NR_ReselectionThresholdQ_t	 threshX_HighQ;
		NR_ReselectionThresholdQ_t	 threshX_LowQ;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *threshX_Q;
	NR_CellReselectionPriority_t	*cellReselectionPriority;	/* OPTIONAL */
	NR_CellReselectionSubPriority_t	*cellReselectionSubPriority;	/* OPTIONAL */
	NR_Q_OffsetRange_t	*q_OffsetFreq;	/* DEFAULT 15 */
	struct NR_InterFreqNeighCellList	*interFreqNeighCellList;	/* OPTIONAL */
	struct NR_InterFreqExcludedCellList	*interFreqExcludedCellList;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_InterFreqCarrierFreqInfo_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_NR_InterFreqCarrierFreqInfo;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_InterFreqCarrierFreqInfo_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_InterFreqCarrierFreqInfo_1[24];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_MultiFrequencyBandListNR-SIB.h"
#include "NR_ThresholdNR.h"
#include "NR_SSB-MTC.h"
#include "NR_SSB-ToMeasure.h"
#include "NR_SS-RSSI-Measurement.h"
#include "NR_SpeedStateScaleFactors.h"
#include "NR_InterFreqNeighCellList.h"
#include "NR_InterFreqExcludedCellList.h"

#endif	/* _NR_InterFreqCarrierFreqInfo_H_ */
#include <asn_internal.h>
