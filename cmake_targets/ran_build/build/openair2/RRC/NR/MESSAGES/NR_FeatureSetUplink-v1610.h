/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_FeatureSetUplink_v1610_H_
#define	_NR_FeatureSetUplink_v1610_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include "NR_FreqSeparationClassUL-v1620.h"
#include <constr_SEQUENCE.h>
#include <BIT_STRING.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_FeatureSetUplink_v1610__pusch_RepetitionTypeB_r16__maxNumberPUSCH_Tx_r16 {
	NR_FeatureSetUplink_v1610__pusch_RepetitionTypeB_r16__maxNumberPUSCH_Tx_r16_n2	= 0,
	NR_FeatureSetUplink_v1610__pusch_RepetitionTypeB_r16__maxNumberPUSCH_Tx_r16_n3	= 1,
	NR_FeatureSetUplink_v1610__pusch_RepetitionTypeB_r16__maxNumberPUSCH_Tx_r16_n4	= 2,
	NR_FeatureSetUplink_v1610__pusch_RepetitionTypeB_r16__maxNumberPUSCH_Tx_r16_n7	= 3,
	NR_FeatureSetUplink_v1610__pusch_RepetitionTypeB_r16__maxNumberPUSCH_Tx_r16_n8	= 4,
	NR_FeatureSetUplink_v1610__pusch_RepetitionTypeB_r16__maxNumberPUSCH_Tx_r16_n12	= 5
} e_NR_FeatureSetUplink_v1610__pusch_RepetitionTypeB_r16__maxNumberPUSCH_Tx_r16;
typedef enum NR_FeatureSetUplink_v1610__pusch_RepetitionTypeB_r16__hoppingScheme_r16 {
	NR_FeatureSetUplink_v1610__pusch_RepetitionTypeB_r16__hoppingScheme_r16_interSlotHopping	= 0,
	NR_FeatureSetUplink_v1610__pusch_RepetitionTypeB_r16__hoppingScheme_r16_interRepetitionHopping	= 1,
	NR_FeatureSetUplink_v1610__pusch_RepetitionTypeB_r16__hoppingScheme_r16_both	= 2
} e_NR_FeatureSetUplink_v1610__pusch_RepetitionTypeB_r16__hoppingScheme_r16;
typedef enum NR_FeatureSetUplink_v1610__ul_CancellationSelfCarrier_r16 {
	NR_FeatureSetUplink_v1610__ul_CancellationSelfCarrier_r16_supported	= 0
} e_NR_FeatureSetUplink_v1610__ul_CancellationSelfCarrier_r16;
typedef enum NR_FeatureSetUplink_v1610__ul_CancellationCrossCarrier_r16 {
	NR_FeatureSetUplink_v1610__ul_CancellationCrossCarrier_r16_supported	= 0
} e_NR_FeatureSetUplink_v1610__ul_CancellationCrossCarrier_r16;
typedef enum NR_FeatureSetUplink_v1610__ul_FullPwrMode2_MaxSRS_ResInSet_r16 {
	NR_FeatureSetUplink_v1610__ul_FullPwrMode2_MaxSRS_ResInSet_r16_n1	= 0,
	NR_FeatureSetUplink_v1610__ul_FullPwrMode2_MaxSRS_ResInSet_r16_n2	= 1,
	NR_FeatureSetUplink_v1610__ul_FullPwrMode2_MaxSRS_ResInSet_r16_n4	= 2
} e_NR_FeatureSetUplink_v1610__ul_FullPwrMode2_MaxSRS_ResInSet_r16;
typedef enum NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16__scs_15kHz_r16 {
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16__scs_15kHz_r16_one_pusch	= 0,
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16__scs_15kHz_r16_upto2	= 1,
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16__scs_15kHz_r16_upto4	= 2,
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16__scs_15kHz_r16_upto7	= 3
} e_NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16__scs_15kHz_r16;
typedef enum NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16__scs_30kHz_r16 {
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16__scs_30kHz_r16_one_pusch	= 0,
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16__scs_30kHz_r16_upto2	= 1,
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16__scs_30kHz_r16_upto4	= 2,
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16__scs_30kHz_r16_upto7	= 3
} e_NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16__scs_30kHz_r16;
typedef enum NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16__scs_60kHz_r16 {
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16__scs_60kHz_r16_one_pusch	= 0,
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16__scs_60kHz_r16_upto2	= 1,
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16__scs_60kHz_r16_upto4	= 2,
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16__scs_60kHz_r16_upto7	= 3
} e_NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16__scs_60kHz_r16;
typedef enum NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16__scs_120kHz_r16 {
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16__scs_120kHz_r16_one_pusch	= 0,
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16__scs_120kHz_r16_upto2	= 1,
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16__scs_120kHz_r16_upto4	= 2,
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16__scs_120kHz_r16_upto7	= 3
} e_NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16__scs_120kHz_r16;
typedef enum NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16__scs_15kHz_r16 {
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16__scs_15kHz_r16_one_pusch	= 0,
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16__scs_15kHz_r16_upto2	= 1,
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16__scs_15kHz_r16_upto4	= 2,
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16__scs_15kHz_r16_upto7	= 3
} e_NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16__scs_15kHz_r16;
typedef enum NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16__scs_30kHz_r16 {
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16__scs_30kHz_r16_one_pusch	= 0,
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16__scs_30kHz_r16_upto2	= 1,
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16__scs_30kHz_r16_upto4	= 2,
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16__scs_30kHz_r16_upto7	= 3
} e_NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16__scs_30kHz_r16;
typedef enum NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16__scs_60kHz_r16 {
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16__scs_60kHz_r16_one_pusch	= 0,
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16__scs_60kHz_r16_upto2	= 1,
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16__scs_60kHz_r16_upto4	= 2,
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16__scs_60kHz_r16_upto7	= 3
} e_NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16__scs_60kHz_r16;
typedef enum NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16__scs_120kHz_r16 {
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16__scs_120kHz_r16_one_pusch	= 0,
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16__scs_120kHz_r16_upto2	= 1,
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16__scs_120kHz_r16_upto4	= 2,
	NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16__scs_120kHz_r16_upto7	= 3
} e_NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16__scs_120kHz_r16;
typedef enum NR_FeatureSetUplink_v1610__intraFreqDAPS_UL_r16__dummy {
	NR_FeatureSetUplink_v1610__intraFreqDAPS_UL_r16__dummy_supported	= 0
} e_NR_FeatureSetUplink_v1610__intraFreqDAPS_UL_r16__dummy;
typedef enum NR_FeatureSetUplink_v1610__intraFreqDAPS_UL_r16__intraFreqTwoTAGs_DAPS_r16 {
	NR_FeatureSetUplink_v1610__intraFreqDAPS_UL_r16__intraFreqTwoTAGs_DAPS_r16_supported	= 0
} e_NR_FeatureSetUplink_v1610__intraFreqDAPS_UL_r16__intraFreqTwoTAGs_DAPS_r16;
typedef enum NR_FeatureSetUplink_v1610__intraFreqDAPS_UL_r16__dummy1 {
	NR_FeatureSetUplink_v1610__intraFreqDAPS_UL_r16__dummy1_supported	= 0
} e_NR_FeatureSetUplink_v1610__intraFreqDAPS_UL_r16__dummy1;
typedef enum NR_FeatureSetUplink_v1610__intraFreqDAPS_UL_r16__dummy2 {
	NR_FeatureSetUplink_v1610__intraFreqDAPS_UL_r16__dummy2_supported	= 0
} e_NR_FeatureSetUplink_v1610__intraFreqDAPS_UL_r16__dummy2;
typedef enum NR_FeatureSetUplink_v1610__intraFreqDAPS_UL_r16__dummy3 {
	NR_FeatureSetUplink_v1610__intraFreqDAPS_UL_r16__dummy3_short	= 0,
	NR_FeatureSetUplink_v1610__intraFreqDAPS_UL_r16__dummy3_long	= 1
} e_NR_FeatureSetUplink_v1610__intraFreqDAPS_UL_r16__dummy3;
typedef enum NR_FeatureSetUplink_v1610__multiPUCCH_r16__sub_SlotConfig_NCP_r16 {
	NR_FeatureSetUplink_v1610__multiPUCCH_r16__sub_SlotConfig_NCP_r16_set1	= 0,
	NR_FeatureSetUplink_v1610__multiPUCCH_r16__sub_SlotConfig_NCP_r16_set2	= 1
} e_NR_FeatureSetUplink_v1610__multiPUCCH_r16__sub_SlotConfig_NCP_r16;
typedef enum NR_FeatureSetUplink_v1610__multiPUCCH_r16__sub_SlotConfig_ECP_r16 {
	NR_FeatureSetUplink_v1610__multiPUCCH_r16__sub_SlotConfig_ECP_r16_set1	= 0,
	NR_FeatureSetUplink_v1610__multiPUCCH_r16__sub_SlotConfig_ECP_r16_set2	= 1
} e_NR_FeatureSetUplink_v1610__multiPUCCH_r16__sub_SlotConfig_ECP_r16;
typedef enum NR_FeatureSetUplink_v1610__twoPUCCH_Type1_r16 {
	NR_FeatureSetUplink_v1610__twoPUCCH_Type1_r16_supported	= 0
} e_NR_FeatureSetUplink_v1610__twoPUCCH_Type1_r16;
typedef enum NR_FeatureSetUplink_v1610__twoPUCCH_Type2_r16 {
	NR_FeatureSetUplink_v1610__twoPUCCH_Type2_r16_supported	= 0
} e_NR_FeatureSetUplink_v1610__twoPUCCH_Type2_r16;
typedef enum NR_FeatureSetUplink_v1610__twoPUCCH_Type3_r16 {
	NR_FeatureSetUplink_v1610__twoPUCCH_Type3_r16_supported	= 0
} e_NR_FeatureSetUplink_v1610__twoPUCCH_Type3_r16;
typedef enum NR_FeatureSetUplink_v1610__twoPUCCH_Type4_r16 {
	NR_FeatureSetUplink_v1610__twoPUCCH_Type4_r16_supported	= 0
} e_NR_FeatureSetUplink_v1610__twoPUCCH_Type4_r16;
typedef enum NR_FeatureSetUplink_v1610__mux_SR_HARQ_ACK_r16 {
	NR_FeatureSetUplink_v1610__mux_SR_HARQ_ACK_r16_supported	= 0
} e_NR_FeatureSetUplink_v1610__mux_SR_HARQ_ACK_r16;
typedef enum NR_FeatureSetUplink_v1610__dummy1 {
	NR_FeatureSetUplink_v1610__dummy1_supported	= 0
} e_NR_FeatureSetUplink_v1610__dummy1;
typedef enum NR_FeatureSetUplink_v1610__dummy2 {
	NR_FeatureSetUplink_v1610__dummy2_supported	= 0
} e_NR_FeatureSetUplink_v1610__dummy2;
typedef enum NR_FeatureSetUplink_v1610__twoPUCCH_Type5_r16 {
	NR_FeatureSetUplink_v1610__twoPUCCH_Type5_r16_supported	= 0
} e_NR_FeatureSetUplink_v1610__twoPUCCH_Type5_r16;
typedef enum NR_FeatureSetUplink_v1610__twoPUCCH_Type6_r16 {
	NR_FeatureSetUplink_v1610__twoPUCCH_Type6_r16_supported	= 0
} e_NR_FeatureSetUplink_v1610__twoPUCCH_Type6_r16;
typedef enum NR_FeatureSetUplink_v1610__twoPUCCH_Type7_r16 {
	NR_FeatureSetUplink_v1610__twoPUCCH_Type7_r16_supported	= 0
} e_NR_FeatureSetUplink_v1610__twoPUCCH_Type7_r16;
typedef enum NR_FeatureSetUplink_v1610__twoPUCCH_Type8_r16 {
	NR_FeatureSetUplink_v1610__twoPUCCH_Type8_r16_supported	= 0
} e_NR_FeatureSetUplink_v1610__twoPUCCH_Type8_r16;
typedef enum NR_FeatureSetUplink_v1610__twoPUCCH_Type9_r16 {
	NR_FeatureSetUplink_v1610__twoPUCCH_Type9_r16_supported	= 0
} e_NR_FeatureSetUplink_v1610__twoPUCCH_Type9_r16;
typedef enum NR_FeatureSetUplink_v1610__twoPUCCH_Type10_r16 {
	NR_FeatureSetUplink_v1610__twoPUCCH_Type10_r16_supported	= 0
} e_NR_FeatureSetUplink_v1610__twoPUCCH_Type10_r16;
typedef enum NR_FeatureSetUplink_v1610__twoPUCCH_Type11_r16 {
	NR_FeatureSetUplink_v1610__twoPUCCH_Type11_r16_supported	= 0
} e_NR_FeatureSetUplink_v1610__twoPUCCH_Type11_r16;
typedef enum NR_FeatureSetUplink_v1610__ul_IntraUE_Mux_r16__pusch_PreparationLowPriority_r16 {
	NR_FeatureSetUplink_v1610__ul_IntraUE_Mux_r16__pusch_PreparationLowPriority_r16_sym0	= 0,
	NR_FeatureSetUplink_v1610__ul_IntraUE_Mux_r16__pusch_PreparationLowPriority_r16_sym1	= 1,
	NR_FeatureSetUplink_v1610__ul_IntraUE_Mux_r16__pusch_PreparationLowPriority_r16_sym2	= 2
} e_NR_FeatureSetUplink_v1610__ul_IntraUE_Mux_r16__pusch_PreparationLowPriority_r16;
typedef enum NR_FeatureSetUplink_v1610__ul_IntraUE_Mux_r16__pusch_PreparationHighPriority_r16 {
	NR_FeatureSetUplink_v1610__ul_IntraUE_Mux_r16__pusch_PreparationHighPriority_r16_sym0	= 0,
	NR_FeatureSetUplink_v1610__ul_IntraUE_Mux_r16__pusch_PreparationHighPriority_r16_sym1	= 1,
	NR_FeatureSetUplink_v1610__ul_IntraUE_Mux_r16__pusch_PreparationHighPriority_r16_sym2	= 2
} e_NR_FeatureSetUplink_v1610__ul_IntraUE_Mux_r16__pusch_PreparationHighPriority_r16;
typedef enum NR_FeatureSetUplink_v1610__ul_FullPwrMode_r16 {
	NR_FeatureSetUplink_v1610__ul_FullPwrMode_r16_supported	= 0
} e_NR_FeatureSetUplink_v1610__ul_FullPwrMode_r16;
typedef enum NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16__scs_15kHz_120kHz_r16 {
	NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16__scs_15kHz_120kHz_r16_n1	= 0,
	NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16__scs_15kHz_120kHz_r16_n2	= 1,
	NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16__scs_15kHz_120kHz_r16_n4	= 2
} e_NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16__scs_15kHz_120kHz_r16;
typedef enum NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16__scs_15kHz_60kHz_r16 {
	NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16__scs_15kHz_60kHz_r16_n1	= 0,
	NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16__scs_15kHz_60kHz_r16_n2	= 1,
	NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16__scs_15kHz_60kHz_r16_n4	= 2
} e_NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16__scs_15kHz_60kHz_r16;
typedef enum NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16__scs_30kHz_120kHz_r16 {
	NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16__scs_30kHz_120kHz_r16_n1	= 0,
	NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16__scs_30kHz_120kHz_r16_n2	= 1,
	NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16__scs_30kHz_120kHz_r16_n4	= 2
} e_NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16__scs_30kHz_120kHz_r16;
typedef enum NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16__scs_15kHz_30kHz_r16 {
	NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16__scs_15kHz_30kHz_r16_n2	= 0
} e_NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16__scs_15kHz_30kHz_r16;
typedef enum NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16__scs_30kHz_60kHz_r16 {
	NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16__scs_30kHz_60kHz_r16_n2	= 0
} e_NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16__scs_30kHz_60kHz_r16;
typedef enum NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16__scs_60kHz_120kHz_r16 {
	NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16__scs_60kHz_120kHz_r16_n2	= 0
} e_NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16__scs_60kHz_120kHz_r16;
typedef enum NR_FeatureSetUplink_v1610__ul_FullPwrMode1_r16 {
	NR_FeatureSetUplink_v1610__ul_FullPwrMode1_r16_supported	= 0
} e_NR_FeatureSetUplink_v1610__ul_FullPwrMode1_r16;
typedef enum NR_FeatureSetUplink_v1610__ul_FullPwrMode2_SRSConfig_diffNumSRSPorts_r16 {
	NR_FeatureSetUplink_v1610__ul_FullPwrMode2_SRSConfig_diffNumSRSPorts_r16_p1_2	= 0,
	NR_FeatureSetUplink_v1610__ul_FullPwrMode2_SRSConfig_diffNumSRSPorts_r16_p1_4	= 1,
	NR_FeatureSetUplink_v1610__ul_FullPwrMode2_SRSConfig_diffNumSRSPorts_r16_p1_2_4	= 2
} e_NR_FeatureSetUplink_v1610__ul_FullPwrMode2_SRSConfig_diffNumSRSPorts_r16;
typedef enum NR_FeatureSetUplink_v1610__ul_FullPwrMode2_TPMIGroup_r16__fourPortsNonCoherent_r16 {
	NR_FeatureSetUplink_v1610__ul_FullPwrMode2_TPMIGroup_r16__fourPortsNonCoherent_r16_g0	= 0,
	NR_FeatureSetUplink_v1610__ul_FullPwrMode2_TPMIGroup_r16__fourPortsNonCoherent_r16_g1	= 1,
	NR_FeatureSetUplink_v1610__ul_FullPwrMode2_TPMIGroup_r16__fourPortsNonCoherent_r16_g2	= 2,
	NR_FeatureSetUplink_v1610__ul_FullPwrMode2_TPMIGroup_r16__fourPortsNonCoherent_r16_g3	= 3
} e_NR_FeatureSetUplink_v1610__ul_FullPwrMode2_TPMIGroup_r16__fourPortsNonCoherent_r16;
typedef enum NR_FeatureSetUplink_v1610__ul_FullPwrMode2_TPMIGroup_r16__fourPortsPartialCoherent_r16 {
	NR_FeatureSetUplink_v1610__ul_FullPwrMode2_TPMIGroup_r16__fourPortsPartialCoherent_r16_g0	= 0,
	NR_FeatureSetUplink_v1610__ul_FullPwrMode2_TPMIGroup_r16__fourPortsPartialCoherent_r16_g1	= 1,
	NR_FeatureSetUplink_v1610__ul_FullPwrMode2_TPMIGroup_r16__fourPortsPartialCoherent_r16_g2	= 2,
	NR_FeatureSetUplink_v1610__ul_FullPwrMode2_TPMIGroup_r16__fourPortsPartialCoherent_r16_g3	= 3,
	NR_FeatureSetUplink_v1610__ul_FullPwrMode2_TPMIGroup_r16__fourPortsPartialCoherent_r16_g4	= 4,
	NR_FeatureSetUplink_v1610__ul_FullPwrMode2_TPMIGroup_r16__fourPortsPartialCoherent_r16_g5	= 5,
	NR_FeatureSetUplink_v1610__ul_FullPwrMode2_TPMIGroup_r16__fourPortsPartialCoherent_r16_g6	= 6
} e_NR_FeatureSetUplink_v1610__ul_FullPwrMode2_TPMIGroup_r16__fourPortsPartialCoherent_r16;

/* Forward declarations */
struct NR_SRS_AllPosResources_r16;

/* NR_FeatureSetUplink-v1610 */
typedef struct NR_FeatureSetUplink_v1610 {
	struct NR_FeatureSetUplink_v1610__pusch_RepetitionTypeB_r16 {
		long	 maxNumberPUSCH_Tx_r16;
		long	 hoppingScheme_r16;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *pusch_RepetitionTypeB_r16;
	long	*ul_CancellationSelfCarrier_r16;	/* OPTIONAL */
	long	*ul_CancellationCrossCarrier_r16;	/* OPTIONAL */
	long	*ul_FullPwrMode2_MaxSRS_ResInSet_r16;	/* OPTIONAL */
	struct NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16 {
		long	*scs_15kHz_r16;	/* OPTIONAL */
		long	*scs_30kHz_r16;	/* OPTIONAL */
		long	*scs_60kHz_r16;	/* OPTIONAL */
		long	*scs_120kHz_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16;
	struct NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16 {
		long	*scs_15kHz_r16;	/* OPTIONAL */
		long	*scs_30kHz_r16;	/* OPTIONAL */
		long	*scs_60kHz_r16;	/* OPTIONAL */
		long	*scs_120kHz_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16;
	struct NR_SRS_AllPosResources_r16	*supportedSRS_PosResources_r16;	/* OPTIONAL */
	struct NR_FeatureSetUplink_v1610__intraFreqDAPS_UL_r16 {
		long	*dummy;	/* OPTIONAL */
		long	*intraFreqTwoTAGs_DAPS_r16;	/* OPTIONAL */
		long	*dummy1;	/* OPTIONAL */
		long	*dummy2;	/* OPTIONAL */
		long	*dummy3;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *intraFreqDAPS_UL_r16;
	NR_FreqSeparationClassUL_v1620_t	*intraBandFreqSeparationUL_v1620;	/* OPTIONAL */
	struct NR_FeatureSetUplink_v1610__multiPUCCH_r16 {
		long	*sub_SlotConfig_NCP_r16;	/* OPTIONAL */
		long	*sub_SlotConfig_ECP_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *multiPUCCH_r16;
	long	*twoPUCCH_Type1_r16;	/* OPTIONAL */
	long	*twoPUCCH_Type2_r16;	/* OPTIONAL */
	long	*twoPUCCH_Type3_r16;	/* OPTIONAL */
	long	*twoPUCCH_Type4_r16;	/* OPTIONAL */
	long	*mux_SR_HARQ_ACK_r16;	/* OPTIONAL */
	long	*dummy1;	/* OPTIONAL */
	long	*dummy2;	/* OPTIONAL */
	long	*twoPUCCH_Type5_r16;	/* OPTIONAL */
	long	*twoPUCCH_Type6_r16;	/* OPTIONAL */
	long	*twoPUCCH_Type7_r16;	/* OPTIONAL */
	long	*twoPUCCH_Type8_r16;	/* OPTIONAL */
	long	*twoPUCCH_Type9_r16;	/* OPTIONAL */
	long	*twoPUCCH_Type10_r16;	/* OPTIONAL */
	long	*twoPUCCH_Type11_r16;	/* OPTIONAL */
	struct NR_FeatureSetUplink_v1610__ul_IntraUE_Mux_r16 {
		long	 pusch_PreparationLowPriority_r16;
		long	 pusch_PreparationHighPriority_r16;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ul_IntraUE_Mux_r16;
	long	*ul_FullPwrMode_r16;	/* OPTIONAL */
	struct NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16 {
		long	*scs_15kHz_120kHz_r16;	/* OPTIONAL */
		long	*scs_15kHz_60kHz_r16;	/* OPTIONAL */
		long	*scs_30kHz_120kHz_r16;	/* OPTIONAL */
		long	*scs_15kHz_30kHz_r16;	/* OPTIONAL */
		long	*scs_30kHz_60kHz_r16;	/* OPTIONAL */
		long	*scs_60kHz_120kHz_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *crossCarrierSchedulingProcessing_DiffSCS_r16;
	long	*ul_FullPwrMode1_r16;	/* OPTIONAL */
	long	*ul_FullPwrMode2_SRSConfig_diffNumSRSPorts_r16;	/* OPTIONAL */
	struct NR_FeatureSetUplink_v1610__ul_FullPwrMode2_TPMIGroup_r16 {
		BIT_STRING_t	*twoPorts_r16;	/* OPTIONAL */
		long	*fourPortsNonCoherent_r16;	/* OPTIONAL */
		long	*fourPortsPartialCoherent_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ul_FullPwrMode2_TPMIGroup_r16;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_FeatureSetUplink_v1610_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumberPUSCH_Tx_r16_3;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_hoppingScheme_r16_10;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_ul_CancellationSelfCarrier_r16_14;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_ul_CancellationCrossCarrier_r16_16;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_ul_FullPwrMode2_MaxSRS_ResInSet_r16_18;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_15kHz_r16_23;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_30kHz_r16_28;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_60kHz_r16_33;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_120kHz_r16_38;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_15kHz_r16_44;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_30kHz_r16_49;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_60kHz_r16_54;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_120kHz_r16_59;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dummy_66;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_intraFreqTwoTAGs_DAPS_r16_68;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dummy1_70;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dummy2_72;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dummy3_74;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_sub_SlotConfig_NCP_r16_79;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_sub_SlotConfig_ECP_r16_82;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_twoPUCCH_Type1_r16_85;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_twoPUCCH_Type2_r16_87;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_twoPUCCH_Type3_r16_89;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_twoPUCCH_Type4_r16_91;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mux_SR_HARQ_ACK_r16_93;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dummy1_95;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dummy2_97;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_twoPUCCH_Type5_r16_99;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_twoPUCCH_Type6_r16_101;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_twoPUCCH_Type7_r16_103;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_twoPUCCH_Type8_r16_105;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_twoPUCCH_Type9_r16_107;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_twoPUCCH_Type10_r16_109;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_twoPUCCH_Type11_r16_111;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pusch_PreparationLowPriority_r16_114;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pusch_PreparationHighPriority_r16_118;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_ul_FullPwrMode_r16_122;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_15kHz_120kHz_r16_125;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_15kHz_60kHz_r16_129;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_30kHz_120kHz_r16_133;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_15kHz_30kHz_r16_137;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_30kHz_60kHz_r16_139;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_60kHz_120kHz_r16_141;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_ul_FullPwrMode1_r16_143;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_ul_FullPwrMode2_SRSConfig_diffNumSRSPorts_r16_145;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_fourPortsNonCoherent_r16_151;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_fourPortsPartialCoherent_r16_156;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_FeatureSetUplink_v1610;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_FeatureSetUplink_v1610_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_FeatureSetUplink_v1610_1[30];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_SRS-AllPosResources-r16.h"

#endif	/* _NR_FeatureSetUplink_v1610_H_ */
#include <asn_internal.h>
