/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_RACH_ConfigGeneric_H_
#define	_NR_RACH_ConfigGeneric_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_RACH_ConfigGeneric__msg1_FDM {
	NR_RACH_ConfigGeneric__msg1_FDM_one	= 0,
	NR_RACH_ConfigGeneric__msg1_FDM_two	= 1,
	NR_RACH_ConfigGeneric__msg1_FDM_four	= 2,
	NR_RACH_ConfigGeneric__msg1_FDM_eight	= 3
} e_NR_RACH_ConfigGeneric__msg1_FDM;
typedef enum NR_RACH_ConfigGeneric__preambleTransMax {
	NR_RACH_ConfigGeneric__preambleTransMax_n3	= 0,
	NR_RACH_ConfigGeneric__preambleTransMax_n4	= 1,
	NR_RACH_ConfigGeneric__preambleTransMax_n5	= 2,
	NR_RACH_ConfigGeneric__preambleTransMax_n6	= 3,
	NR_RACH_ConfigGeneric__preambleTransMax_n7	= 4,
	NR_RACH_ConfigGeneric__preambleTransMax_n8	= 5,
	NR_RACH_ConfigGeneric__preambleTransMax_n10	= 6,
	NR_RACH_ConfigGeneric__preambleTransMax_n20	= 7,
	NR_RACH_ConfigGeneric__preambleTransMax_n50	= 8,
	NR_RACH_ConfigGeneric__preambleTransMax_n100	= 9,
	NR_RACH_ConfigGeneric__preambleTransMax_n200	= 10
} e_NR_RACH_ConfigGeneric__preambleTransMax;
typedef enum NR_RACH_ConfigGeneric__powerRampingStep {
	NR_RACH_ConfigGeneric__powerRampingStep_dB0	= 0,
	NR_RACH_ConfigGeneric__powerRampingStep_dB2	= 1,
	NR_RACH_ConfigGeneric__powerRampingStep_dB4	= 2,
	NR_RACH_ConfigGeneric__powerRampingStep_dB6	= 3
} e_NR_RACH_ConfigGeneric__powerRampingStep;
typedef enum NR_RACH_ConfigGeneric__ra_ResponseWindow {
	NR_RACH_ConfigGeneric__ra_ResponseWindow_sl1	= 0,
	NR_RACH_ConfigGeneric__ra_ResponseWindow_sl2	= 1,
	NR_RACH_ConfigGeneric__ra_ResponseWindow_sl4	= 2,
	NR_RACH_ConfigGeneric__ra_ResponseWindow_sl8	= 3,
	NR_RACH_ConfigGeneric__ra_ResponseWindow_sl10	= 4,
	NR_RACH_ConfigGeneric__ra_ResponseWindow_sl20	= 5,
	NR_RACH_ConfigGeneric__ra_ResponseWindow_sl40	= 6,
	NR_RACH_ConfigGeneric__ra_ResponseWindow_sl80	= 7
} e_NR_RACH_ConfigGeneric__ra_ResponseWindow;
typedef enum NR_RACH_ConfigGeneric__ext1__prach_ConfigurationPeriodScaling_IAB_r16 {
	NR_RACH_ConfigGeneric__ext1__prach_ConfigurationPeriodScaling_IAB_r16_scf1	= 0,
	NR_RACH_ConfigGeneric__ext1__prach_ConfigurationPeriodScaling_IAB_r16_scf2	= 1,
	NR_RACH_ConfigGeneric__ext1__prach_ConfigurationPeriodScaling_IAB_r16_scf4	= 2,
	NR_RACH_ConfigGeneric__ext1__prach_ConfigurationPeriodScaling_IAB_r16_scf8	= 3,
	NR_RACH_ConfigGeneric__ext1__prach_ConfigurationPeriodScaling_IAB_r16_scf16	= 4,
	NR_RACH_ConfigGeneric__ext1__prach_ConfigurationPeriodScaling_IAB_r16_scf32	= 5,
	NR_RACH_ConfigGeneric__ext1__prach_ConfigurationPeriodScaling_IAB_r16_scf64	= 6
} e_NR_RACH_ConfigGeneric__ext1__prach_ConfigurationPeriodScaling_IAB_r16;
typedef enum NR_RACH_ConfigGeneric__ext1__ra_ResponseWindow_v1610 {
	NR_RACH_ConfigGeneric__ext1__ra_ResponseWindow_v1610_sl60	= 0,
	NR_RACH_ConfigGeneric__ext1__ra_ResponseWindow_v1610_sl160	= 1
} e_NR_RACH_ConfigGeneric__ext1__ra_ResponseWindow_v1610;
typedef enum NR_RACH_ConfigGeneric__ext2__ra_ResponseWindow_v1700 {
	NR_RACH_ConfigGeneric__ext2__ra_ResponseWindow_v1700_sl240	= 0,
	NR_RACH_ConfigGeneric__ext2__ra_ResponseWindow_v1700_sl320	= 1,
	NR_RACH_ConfigGeneric__ext2__ra_ResponseWindow_v1700_sl640	= 2,
	NR_RACH_ConfigGeneric__ext2__ra_ResponseWindow_v1700_sl960	= 3,
	NR_RACH_ConfigGeneric__ext2__ra_ResponseWindow_v1700_sl1280	= 4,
	NR_RACH_ConfigGeneric__ext2__ra_ResponseWindow_v1700_sl1920	= 5,
	NR_RACH_ConfigGeneric__ext2__ra_ResponseWindow_v1700_sl2560	= 6
} e_NR_RACH_ConfigGeneric__ext2__ra_ResponseWindow_v1700;

/* NR_RACH-ConfigGeneric */
typedef struct NR_RACH_ConfigGeneric {
	long	 prach_ConfigurationIndex;
	long	 msg1_FDM;
	long	 msg1_FrequencyStart;
	long	 zeroCorrelationZoneConfig;
	long	 preambleReceivedTargetPower;
	long	 preambleTransMax;
	long	 powerRampingStep;
	long	 ra_ResponseWindow;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	struct NR_RACH_ConfigGeneric__ext1 {
		long	*prach_ConfigurationPeriodScaling_IAB_r16;	/* OPTIONAL */
		long	*prach_ConfigurationFrameOffset_IAB_r16;	/* OPTIONAL */
		long	*prach_ConfigurationSOffset_IAB_r16;	/* OPTIONAL */
		long	*ra_ResponseWindow_v1610;	/* OPTIONAL */
		long	*prach_ConfigurationIndex_v1610;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext1;
	struct NR_RACH_ConfigGeneric__ext2 {
		long	*ra_ResponseWindow_v1700;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext2;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_RACH_ConfigGeneric_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_msg1_FDM_3;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_preambleTransMax_11;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_powerRampingStep_23;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_ra_ResponseWindow_28;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_prach_ConfigurationPeriodScaling_IAB_r16_39;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_ra_ResponseWindow_v1610_49;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_ra_ResponseWindow_v1700_54;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_RACH_ConfigGeneric;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_RACH_ConfigGeneric_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_RACH_ConfigGeneric_1[10];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_RACH_ConfigGeneric_H_ */
#include <asn_internal.h>
