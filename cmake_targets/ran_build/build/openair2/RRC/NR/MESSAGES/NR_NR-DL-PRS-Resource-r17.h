/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_NR_DL_PRS_Resource_r17_H_
#define	_NR_NR_DL_PRS_Resource_r17_H_


#include <asn_application.h>

/* Including external dependencies */
#include "NR_NR-DL-PRS-ResourceID-r17.h"
#include <NativeInteger.h>
#include <constr_CHOICE.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_NR_DL_PRS_Resource_r17__dl_PRS_CombSizeN_AndReOffset_r17_PR {
	NR_NR_DL_PRS_Resource_r17__dl_PRS_CombSizeN_AndReOffset_r17_PR_NOTHING,	/* No components present */
	NR_NR_DL_PRS_Resource_r17__dl_PRS_CombSizeN_AndReOffset_r17_PR_n2_r17,
	NR_NR_DL_PRS_Resource_r17__dl_PRS_CombSizeN_AndReOffset_r17_PR_n4_r17,
	NR_NR_DL_PRS_Resource_r17__dl_PRS_CombSizeN_AndReOffset_r17_PR_n6_r17,
	NR_NR_DL_PRS_Resource_r17__dl_PRS_CombSizeN_AndReOffset_r17_PR_n12_r17
	/* Extensions may appear below */
	
} NR_NR_DL_PRS_Resource_r17__dl_PRS_CombSizeN_AndReOffset_r17_PR;

/* Forward declarations */
struct NR_DL_PRS_QCL_Info_r17;

/* NR_NR-DL-PRS-Resource-r17 */
typedef struct NR_NR_DL_PRS_Resource_r17 {
	NR_NR_DL_PRS_ResourceID_r17_t	 nr_DL_PRS_ResourceID_r17;
	long	 dl_PRS_SequenceID_r17;
	struct NR_NR_DL_PRS_Resource_r17__dl_PRS_CombSizeN_AndReOffset_r17 {
		NR_NR_DL_PRS_Resource_r17__dl_PRS_CombSizeN_AndReOffset_r17_PR present;
		union NR_NR_DL_PRS_Resource_r17__NR_dl_PRS_CombSizeN_AndReOffset_r17_u {
			long	 n2_r17;
			long	 n4_r17;
			long	 n6_r17;
			long	 n12_r17;
			/*
			 * This type is extensible,
			 * possible extensions are below.
			 */
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} dl_PRS_CombSizeN_AndReOffset_r17;
	long	 dl_PRS_ResourceSlotOffset_r17;
	long	 dl_PRS_ResourceSymbolOffset_r17;
	struct NR_DL_PRS_QCL_Info_r17	*dl_PRS_QCL_Info_r17;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_NR_DL_PRS_Resource_r17_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_NR_NR_DL_PRS_Resource_r17;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_NR_DL_PRS_Resource_r17_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_NR_DL_PRS_Resource_r17_1[6];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_DL-PRS-QCL-Info-r17.h"

#endif	/* _NR_NR_DL_PRS_Resource_r17_H_ */
#include <asn_internal.h>
