/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_RF_ParametersMRDC_H_
#define	_NR_RF_ParametersMRDC_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_RF_ParametersMRDC__ext1__srs_SwitchingTimeRequested {
	NR_RF_ParametersMRDC__ext1__srs_SwitchingTimeRequested_true	= 0
} e_NR_RF_ParametersMRDC__ext1__srs_SwitchingTimeRequested;

/* Forward declarations */
struct NR_BandCombinationList;
struct NR_FreqBandList;
struct NR_BandCombinationList_v1540;
struct NR_BandCombinationList_v1550;
struct NR_BandCombinationList_v1560;
struct NR_BandCombinationList_v1570;
struct NR_BandCombinationList_v1580;
struct NR_BandCombinationList_v1590;
struct NR_BandCombinationList_v1610;
struct NR_BandCombinationList_UplinkTxSwitch_r16;
struct NR_BandCombinationList_v1630;
struct NR_BandCombinationList_UplinkTxSwitch_v1630;
struct NR_BandCombinationList_v1640;
struct NR_BandCombinationList_UplinkTxSwitch_v1640;
struct NR_BandCombinationList_UplinkTxSwitch_v1670;
struct NR_BandCombinationList_v1700;
struct NR_BandCombinationList_UplinkTxSwitch_v1700;
struct NR_BandCombinationList_v1720;
struct NR_BandCombinationList_UplinkTxSwitch_v1720;
struct NR_BandCombinationList_v1730;
struct NR_BandCombinationList_UplinkTxSwitch_v1730;

/* NR_RF-ParametersMRDC */
typedef struct NR_RF_ParametersMRDC {
	struct NR_BandCombinationList	*supportedBandCombinationList;	/* OPTIONAL */
	struct NR_FreqBandList	*appliedFreqBandListFilter;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	struct NR_RF_ParametersMRDC__ext1 {
		long	*srs_SwitchingTimeRequested;	/* OPTIONAL */
		struct NR_BandCombinationList_v1540	*supportedBandCombinationList_v1540;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext1;
	struct NR_RF_ParametersMRDC__ext2 {
		struct NR_BandCombinationList_v1550	*supportedBandCombinationList_v1550;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext2;
	struct NR_RF_ParametersMRDC__ext3 {
		struct NR_BandCombinationList_v1560	*supportedBandCombinationList_v1560;	/* OPTIONAL */
		struct NR_BandCombinationList	*supportedBandCombinationListNEDC_Only;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext3;
	struct NR_RF_ParametersMRDC__ext4 {
		struct NR_BandCombinationList_v1570	*supportedBandCombinationList_v1570;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext4;
	struct NR_RF_ParametersMRDC__ext5 {
		struct NR_BandCombinationList_v1580	*supportedBandCombinationList_v1580;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext5;
	struct NR_RF_ParametersMRDC__ext6 {
		struct NR_BandCombinationList_v1590	*supportedBandCombinationList_v1590;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext6;
	struct NR_RF_ParametersMRDC__ext7 {
		struct NR_RF_ParametersMRDC__ext7__supportedBandCombinationListNEDC_Only_v15a0 {
			struct NR_BandCombinationList_v1540	*supportedBandCombinationList_v1540;	/* OPTIONAL */
			struct NR_BandCombinationList_v1560	*supportedBandCombinationList_v1560;	/* OPTIONAL */
			struct NR_BandCombinationList_v1570	*supportedBandCombinationList_v1570;	/* OPTIONAL */
			struct NR_BandCombinationList_v1580	*supportedBandCombinationList_v1580;	/* OPTIONAL */
			struct NR_BandCombinationList_v1590	*supportedBandCombinationList_v1590;	/* OPTIONAL */
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *supportedBandCombinationListNEDC_Only_v15a0;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext7;
	struct NR_RF_ParametersMRDC__ext8 {
		struct NR_BandCombinationList_v1610	*supportedBandCombinationList_v1610;	/* OPTIONAL */
		struct NR_BandCombinationList_v1610	*supportedBandCombinationListNEDC_Only_v1610;	/* OPTIONAL */
		struct NR_BandCombinationList_UplinkTxSwitch_r16	*supportedBandCombinationList_UplinkTxSwitch_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext8;
	struct NR_RF_ParametersMRDC__ext9 {
		struct NR_BandCombinationList_v1630	*supportedBandCombinationList_v1630;	/* OPTIONAL */
		struct NR_BandCombinationList_v1630	*supportedBandCombinationListNEDC_Only_v1630;	/* OPTIONAL */
		struct NR_BandCombinationList_UplinkTxSwitch_v1630	*supportedBandCombinationList_UplinkTxSwitch_v1630;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext9;
	struct NR_RF_ParametersMRDC__ext10 {
		struct NR_BandCombinationList_v1640	*supportedBandCombinationList_v1640;	/* OPTIONAL */
		struct NR_BandCombinationList_v1640	*supportedBandCombinationListNEDC_Only_v1640;	/* OPTIONAL */
		struct NR_BandCombinationList_UplinkTxSwitch_v1640	*supportedBandCombinationList_UplinkTxSwitch_v1640;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext10;
	struct NR_RF_ParametersMRDC__ext11 {
		struct NR_BandCombinationList_UplinkTxSwitch_v1670	*supportedBandCombinationList_UplinkTxSwitch_v1670;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext11;
	struct NR_RF_ParametersMRDC__ext12 {
		struct NR_BandCombinationList_v1700	*supportedBandCombinationList_v1700;	/* OPTIONAL */
		struct NR_BandCombinationList_UplinkTxSwitch_v1700	*supportedBandCombinationList_UplinkTxSwitch_v1700;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext12;
	struct NR_RF_ParametersMRDC__ext13 {
		struct NR_BandCombinationList_v1720	*supportedBandCombinationList_v1720;	/* OPTIONAL */
		struct NR_RF_ParametersMRDC__ext13__supportedBandCombinationListNEDC_Only_v1720 {
			struct NR_BandCombinationList_v1700	*supportedBandCombinationList_v1700;	/* OPTIONAL */
			struct NR_BandCombinationList_v1720	*supportedBandCombinationList_v1720;	/* OPTIONAL */
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *supportedBandCombinationListNEDC_Only_v1720;
		struct NR_BandCombinationList_UplinkTxSwitch_v1720	*supportedBandCombinationList_UplinkTxSwitch_v1720;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext13;
	struct NR_RF_ParametersMRDC__ext14 {
		struct NR_BandCombinationList_v1730	*supportedBandCombinationList_v1730;	/* OPTIONAL */
		struct NR_BandCombinationList_v1730	*supportedBandCombinationListNEDC_Only_v1730;	/* OPTIONAL */
		struct NR_BandCombinationList_UplinkTxSwitch_v1730	*supportedBandCombinationList_UplinkTxSwitch_v1730;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext14;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_RF_ParametersMRDC_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_srs_SwitchingTimeRequested_6;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_RF_ParametersMRDC;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_RF_ParametersMRDC_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_RF_ParametersMRDC_1[16];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_BandCombinationList.h"
#include "NR_FreqBandList.h"
#include "NR_BandCombinationList-v1540.h"
#include "NR_BandCombinationList-v1550.h"
#include "NR_BandCombinationList-v1560.h"
#include "NR_BandCombinationList-v1570.h"
#include "NR_BandCombinationList-v1580.h"
#include "NR_BandCombinationList-v1590.h"
#include "NR_BandCombinationList-v1610.h"
#include "NR_BandCombinationList-UplinkTxSwitch-r16.h"
#include "NR_BandCombinationList-v1630.h"
#include "NR_BandCombinationList-UplinkTxSwitch-v1630.h"
#include "NR_BandCombinationList-v1640.h"
#include "NR_BandCombinationList-UplinkTxSwitch-v1640.h"
#include "NR_BandCombinationList-UplinkTxSwitch-v1670.h"
#include "NR_BandCombinationList-v1700.h"
#include "NR_BandCombinationList-UplinkTxSwitch-v1700.h"
#include "NR_BandCombinationList-v1720.h"
#include "NR_BandCombinationList-UplinkTxSwitch-v1720.h"
#include "NR_BandCombinationList-v1730.h"
#include "NR_BandCombinationList-UplinkTxSwitch-v1730.h"

#endif	/* _NR_RF_ParametersMRDC_H_ */
#include <asn_internal.h>
