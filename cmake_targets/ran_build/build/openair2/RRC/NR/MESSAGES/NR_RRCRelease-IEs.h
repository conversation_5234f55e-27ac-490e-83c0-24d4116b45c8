/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_RRCRelease_IEs_H_
#define	_NR_RRCRelease_IEs_H_


#include <asn_application.h>

/* Including external dependencies */
#include <OCTET_STRING.h>
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_RRCRelease_IEs__deprioritisationReq__deprioritisationType {
	NR_RRCRelease_IEs__deprioritisationReq__deprioritisationType_frequency	= 0,
	NR_RRCRelease_IEs__deprioritisationReq__deprioritisationType_nr	= 1
} e_NR_RRCRelease_IEs__deprioritisationReq__deprioritisationType;
typedef enum NR_RRCRelease_IEs__deprioritisationReq__deprioritisationTimer {
	NR_RRCRelease_IEs__deprioritisationReq__deprioritisationTimer_min5	= 0,
	NR_RRCRelease_IEs__deprioritisationReq__deprioritisationTimer_min10	= 1,
	NR_RRCRelease_IEs__deprioritisationReq__deprioritisationTimer_min15	= 2,
	NR_RRCRelease_IEs__deprioritisationReq__deprioritisationTimer_min30	= 3
} e_NR_RRCRelease_IEs__deprioritisationReq__deprioritisationTimer;

/* Forward declarations */
struct NR_RedirectedCarrierInfo;
struct NR_CellReselectionPriorities;
struct NR_SuspendConfig;
struct NR_RRCRelease_v1540_IEs;

/* NR_RRCRelease-IEs */
typedef struct NR_RRCRelease_IEs {
	struct NR_RedirectedCarrierInfo	*redirectedCarrierInfo;	/* OPTIONAL */
	struct NR_CellReselectionPriorities	*cellReselectionPriorities;	/* OPTIONAL */
	struct NR_SuspendConfig	*suspendConfig;	/* OPTIONAL */
	struct NR_RRCRelease_IEs__deprioritisationReq {
		long	 deprioritisationType;
		long	 deprioritisationTimer;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *deprioritisationReq;
	OCTET_STRING_t	*lateNonCriticalExtension;	/* OPTIONAL */
	struct NR_RRCRelease_v1540_IEs	*nonCriticalExtension;	/* OPTIONAL */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_RRCRelease_IEs_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_deprioritisationType_6;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_deprioritisationTimer_9;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_RRCRelease_IEs;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_RRCRelease_IEs_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_RRCRelease_IEs_1[6];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_RedirectedCarrierInfo.h"
#include "NR_CellReselectionPriorities.h"
#include "NR_SuspendConfig.h"
#include "NR_RRCRelease-v1540-IEs.h"

#endif	/* _NR_RRCRelease_IEs_H_ */
#include <asn_internal.h>
