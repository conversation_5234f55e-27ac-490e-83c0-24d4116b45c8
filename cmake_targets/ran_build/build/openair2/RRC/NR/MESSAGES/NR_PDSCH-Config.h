/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_PDSCH_Config_H_
#define	_NR_PDSCH_Config_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include <NativeEnumerated.h>
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include "NR_TCI-StateId.h"
#include "NR_RateMatchPatternId.h"
#include <constr_SEQUENCE.h>
#include <constr_CHOICE.h>
#include "NR_ZP-CSI-RS-ResourceId.h"
#include "NR_ZP-CSI-RS-ResourceSetId.h"

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_PDSCH_Config__vrb_ToPRB_Interleaver {
	NR_PDSCH_Config__vrb_ToPRB_Interleaver_n2	= 0,
	NR_PDSCH_Config__vrb_ToPRB_Interleaver_n4	= 1
} e_NR_PDSCH_Config__vrb_ToPRB_Interleaver;
typedef enum NR_PDSCH_Config__resourceAllocation {
	NR_PDSCH_Config__resourceAllocation_resourceAllocationType0	= 0,
	NR_PDSCH_Config__resourceAllocation_resourceAllocationType1	= 1,
	NR_PDSCH_Config__resourceAllocation_dynamicSwitch	= 2
} e_NR_PDSCH_Config__resourceAllocation;
typedef enum NR_PDSCH_Config__pdsch_AggregationFactor {
	NR_PDSCH_Config__pdsch_AggregationFactor_n2	= 0,
	NR_PDSCH_Config__pdsch_AggregationFactor_n4	= 1,
	NR_PDSCH_Config__pdsch_AggregationFactor_n8	= 2
} e_NR_PDSCH_Config__pdsch_AggregationFactor;
typedef enum NR_PDSCH_Config__rbg_Size {
	NR_PDSCH_Config__rbg_Size_config1	= 0,
	NR_PDSCH_Config__rbg_Size_config2	= 1
} e_NR_PDSCH_Config__rbg_Size;
typedef enum NR_PDSCH_Config__mcs_Table {
	NR_PDSCH_Config__mcs_Table_qam256	= 0,
	NR_PDSCH_Config__mcs_Table_qam64LowSE	= 1
} e_NR_PDSCH_Config__mcs_Table;
typedef enum NR_PDSCH_Config__maxNrofCodeWordsScheduledByDCI {
	NR_PDSCH_Config__maxNrofCodeWordsScheduledByDCI_n1	= 0,
	NR_PDSCH_Config__maxNrofCodeWordsScheduledByDCI_n2	= 1
} e_NR_PDSCH_Config__maxNrofCodeWordsScheduledByDCI;
typedef enum NR_PDSCH_Config__prb_BundlingType_PR {
	NR_PDSCH_Config__prb_BundlingType_PR_NOTHING,	/* No components present */
	NR_PDSCH_Config__prb_BundlingType_PR_staticBundling,
	NR_PDSCH_Config__prb_BundlingType_PR_dynamicBundling
} NR_PDSCH_Config__prb_BundlingType_PR;
typedef enum NR_PDSCH_Config__prb_BundlingType__staticBundling__bundleSize {
	NR_PDSCH_Config__prb_BundlingType__staticBundling__bundleSize_n4	= 0,
	NR_PDSCH_Config__prb_BundlingType__staticBundling__bundleSize_wideband	= 1
} e_NR_PDSCH_Config__prb_BundlingType__staticBundling__bundleSize;
typedef enum NR_PDSCH_Config__prb_BundlingType__dynamicBundling__bundleSizeSet1 {
	NR_PDSCH_Config__prb_BundlingType__dynamicBundling__bundleSizeSet1_n4	= 0,
	NR_PDSCH_Config__prb_BundlingType__dynamicBundling__bundleSizeSet1_wideband	= 1,
	NR_PDSCH_Config__prb_BundlingType__dynamicBundling__bundleSizeSet1_n2_wideband	= 2,
	NR_PDSCH_Config__prb_BundlingType__dynamicBundling__bundleSizeSet1_n4_wideband	= 3
} e_NR_PDSCH_Config__prb_BundlingType__dynamicBundling__bundleSizeSet1;
typedef enum NR_PDSCH_Config__prb_BundlingType__dynamicBundling__bundleSizeSet2 {
	NR_PDSCH_Config__prb_BundlingType__dynamicBundling__bundleSizeSet2_n4	= 0,
	NR_PDSCH_Config__prb_BundlingType__dynamicBundling__bundleSizeSet2_wideband	= 1
} e_NR_PDSCH_Config__prb_BundlingType__dynamicBundling__bundleSizeSet2;
typedef enum NR_PDSCH_Config__ext1__antennaPortsFieldPresenceDCI_1_2_r16 {
	NR_PDSCH_Config__ext1__antennaPortsFieldPresenceDCI_1_2_r16_enabled	= 0
} e_NR_PDSCH_Config__ext1__antennaPortsFieldPresenceDCI_1_2_r16;
typedef enum NR_PDSCH_Config__ext1__dmrs_SequenceInitializationDCI_1_2_r16 {
	NR_PDSCH_Config__ext1__dmrs_SequenceInitializationDCI_1_2_r16_enabled	= 0
} e_NR_PDSCH_Config__ext1__dmrs_SequenceInitializationDCI_1_2_r16;
typedef enum NR_PDSCH_Config__ext1__mcs_TableDCI_1_2_r16 {
	NR_PDSCH_Config__ext1__mcs_TableDCI_1_2_r16_qam256	= 0,
	NR_PDSCH_Config__ext1__mcs_TableDCI_1_2_r16_qam64LowSE	= 1
} e_NR_PDSCH_Config__ext1__mcs_TableDCI_1_2_r16;
typedef enum NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16_PR {
	NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16_PR_NOTHING,	/* No components present */
	NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16_PR_staticBundling_r16,
	NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16_PR_dynamicBundling_r16
} NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16_PR;
typedef enum NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16__staticBundling_r16__bundleSize_r16 {
	NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16__staticBundling_r16__bundleSize_r16_n4	= 0,
	NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16__staticBundling_r16__bundleSize_r16_wideband	= 1
} e_NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16__staticBundling_r16__bundleSize_r16;
typedef enum NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16__dynamicBundling_r16__bundleSizeSet1_r16 {
	NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16__dynamicBundling_r16__bundleSizeSet1_r16_n4	= 0,
	NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16__dynamicBundling_r16__bundleSizeSet1_r16_wideband	= 1,
	NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16__dynamicBundling_r16__bundleSizeSet1_r16_n2_wideband	= 2,
	NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16__dynamicBundling_r16__bundleSizeSet1_r16_n4_wideband	= 3
} e_NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16__dynamicBundling_r16__bundleSizeSet1_r16;
typedef enum NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16__dynamicBundling_r16__bundleSizeSet2_r16 {
	NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16__dynamicBundling_r16__bundleSizeSet2_r16_n4	= 0,
	NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16__dynamicBundling_r16__bundleSizeSet2_r16_wideband	= 1
} e_NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16__dynamicBundling_r16__bundleSizeSet2_r16;
typedef enum NR_PDSCH_Config__ext1__priorityIndicatorDCI_1_2_r16 {
	NR_PDSCH_Config__ext1__priorityIndicatorDCI_1_2_r16_enabled	= 0
} e_NR_PDSCH_Config__ext1__priorityIndicatorDCI_1_2_r16;
typedef enum NR_PDSCH_Config__ext1__resourceAllocationType1GranularityDCI_1_2_r16 {
	NR_PDSCH_Config__ext1__resourceAllocationType1GranularityDCI_1_2_r16_n2	= 0,
	NR_PDSCH_Config__ext1__resourceAllocationType1GranularityDCI_1_2_r16_n4	= 1,
	NR_PDSCH_Config__ext1__resourceAllocationType1GranularityDCI_1_2_r16_n8	= 2,
	NR_PDSCH_Config__ext1__resourceAllocationType1GranularityDCI_1_2_r16_n16	= 3
} e_NR_PDSCH_Config__ext1__resourceAllocationType1GranularityDCI_1_2_r16;
typedef enum NR_PDSCH_Config__ext1__vrb_ToPRB_InterleaverDCI_1_2_r16 {
	NR_PDSCH_Config__ext1__vrb_ToPRB_InterleaverDCI_1_2_r16_n2	= 0,
	NR_PDSCH_Config__ext1__vrb_ToPRB_InterleaverDCI_1_2_r16_n4	= 1
} e_NR_PDSCH_Config__ext1__vrb_ToPRB_InterleaverDCI_1_2_r16;
typedef enum NR_PDSCH_Config__ext1__referenceOfSLIVDCI_1_2_r16 {
	NR_PDSCH_Config__ext1__referenceOfSLIVDCI_1_2_r16_enabled	= 0
} e_NR_PDSCH_Config__ext1__referenceOfSLIVDCI_1_2_r16;
typedef enum NR_PDSCH_Config__ext1__resourceAllocationDCI_1_2_r16 {
	NR_PDSCH_Config__ext1__resourceAllocationDCI_1_2_r16_resourceAllocationType0	= 0,
	NR_PDSCH_Config__ext1__resourceAllocationDCI_1_2_r16_resourceAllocationType1	= 1,
	NR_PDSCH_Config__ext1__resourceAllocationDCI_1_2_r16_dynamicSwitch	= 2
} e_NR_PDSCH_Config__ext1__resourceAllocationDCI_1_2_r16;
typedef enum NR_PDSCH_Config__ext1__priorityIndicatorDCI_1_1_r16 {
	NR_PDSCH_Config__ext1__priorityIndicatorDCI_1_1_r16_enabled	= 0
} e_NR_PDSCH_Config__ext1__priorityIndicatorDCI_1_1_r16;
typedef enum NR_PDSCH_Config__ext3__pdsch_HARQ_ACK_OneShotFeedbackDCI_1_2_r17 {
	NR_PDSCH_Config__ext3__pdsch_HARQ_ACK_OneShotFeedbackDCI_1_2_r17_enabled	= 0
} e_NR_PDSCH_Config__ext3__pdsch_HARQ_ACK_OneShotFeedbackDCI_1_2_r17;
typedef enum NR_PDSCH_Config__ext3__pdsch_HARQ_ACK_EnhType3DCI_1_2_r17 {
	NR_PDSCH_Config__ext3__pdsch_HARQ_ACK_EnhType3DCI_1_2_r17_enabled	= 0
} e_NR_PDSCH_Config__ext3__pdsch_HARQ_ACK_EnhType3DCI_1_2_r17;
typedef enum NR_PDSCH_Config__ext3__pdsch_HARQ_ACK_EnhType3DCI_Field_1_2_r17 {
	NR_PDSCH_Config__ext3__pdsch_HARQ_ACK_EnhType3DCI_Field_1_2_r17_enabled	= 0
} e_NR_PDSCH_Config__ext3__pdsch_HARQ_ACK_EnhType3DCI_Field_1_2_r17;
typedef enum NR_PDSCH_Config__ext3__pdsch_HARQ_ACK_RetxDCI_1_2_r17 {
	NR_PDSCH_Config__ext3__pdsch_HARQ_ACK_RetxDCI_1_2_r17_enabled	= 0
} e_NR_PDSCH_Config__ext3__pdsch_HARQ_ACK_RetxDCI_1_2_r17;
typedef enum NR_PDSCH_Config__ext3__pucch_sSCellDynDCI_1_2_r17 {
	NR_PDSCH_Config__ext3__pucch_sSCellDynDCI_1_2_r17_enabled	= 0
} e_NR_PDSCH_Config__ext3__pucch_sSCellDynDCI_1_2_r17;
typedef enum NR_PDSCH_Config__ext3__dl_OrJointTCI_StateList_r17_PR {
	NR_PDSCH_Config__ext3__dl_OrJointTCI_StateList_r17_PR_NOTHING,	/* No components present */
	NR_PDSCH_Config__ext3__dl_OrJointTCI_StateList_r17_PR_explicitlist,
	NR_PDSCH_Config__ext3__dl_OrJointTCI_StateList_r17_PR_unifiedTCI_StateRef_r17
} NR_PDSCH_Config__ext3__dl_OrJointTCI_StateList_r17_PR;
typedef enum NR_PDSCH_Config__ext3__beamAppTime_r17 {
	NR_PDSCH_Config__ext3__beamAppTime_r17_n1	= 0,
	NR_PDSCH_Config__ext3__beamAppTime_r17_n2	= 1,
	NR_PDSCH_Config__ext3__beamAppTime_r17_n4	= 2,
	NR_PDSCH_Config__ext3__beamAppTime_r17_n7	= 3,
	NR_PDSCH_Config__ext3__beamAppTime_r17_n14	= 4,
	NR_PDSCH_Config__ext3__beamAppTime_r17_n28	= 5,
	NR_PDSCH_Config__ext3__beamAppTime_r17_n42	= 6,
	NR_PDSCH_Config__ext3__beamAppTime_r17_n56	= 7,
	NR_PDSCH_Config__ext3__beamAppTime_r17_n70	= 8,
	NR_PDSCH_Config__ext3__beamAppTime_r17_n84	= 9,
	NR_PDSCH_Config__ext3__beamAppTime_r17_n98	= 10,
	NR_PDSCH_Config__ext3__beamAppTime_r17_n112	= 11,
	NR_PDSCH_Config__ext3__beamAppTime_r17_n224	= 12,
	NR_PDSCH_Config__ext3__beamAppTime_r17_n336	= 13,
	NR_PDSCH_Config__ext3__beamAppTime_r17_spare2	= 14,
	NR_PDSCH_Config__ext3__beamAppTime_r17_spare1	= 15
} e_NR_PDSCH_Config__ext3__beamAppTime_r17;
typedef enum NR_PDSCH_Config__ext3__dmrs_FD_OCC_DisabledForRank1_PDSCH_r17 {
	NR_PDSCH_Config__ext3__dmrs_FD_OCC_DisabledForRank1_PDSCH_r17_true	= 0
} e_NR_PDSCH_Config__ext3__dmrs_FD_OCC_DisabledForRank1_PDSCH_r17;
typedef enum NR_PDSCH_Config__ext3__mcs_Table_r17 {
	NR_PDSCH_Config__ext3__mcs_Table_r17_qam1024	= 0
} e_NR_PDSCH_Config__ext3__mcs_Table_r17;
typedef enum NR_PDSCH_Config__ext3__mcs_TableDCI_1_2_r17 {
	NR_PDSCH_Config__ext3__mcs_TableDCI_1_2_r17_qam1024	= 0
} e_NR_PDSCH_Config__ext3__mcs_TableDCI_1_2_r17;
typedef enum NR_PDSCH_Config__ext3__xOverheadMulticast_r17 {
	NR_PDSCH_Config__ext3__xOverheadMulticast_r17_xOh6	= 0,
	NR_PDSCH_Config__ext3__xOverheadMulticast_r17_xOh12	= 1,
	NR_PDSCH_Config__ext3__xOverheadMulticast_r17_xOh18	= 2
} e_NR_PDSCH_Config__ext3__xOverheadMulticast_r17;
typedef enum NR_PDSCH_Config__ext3__priorityIndicatorDCI_4_2_r17 {
	NR_PDSCH_Config__ext3__priorityIndicatorDCI_4_2_r17_enabled	= 0
} e_NR_PDSCH_Config__ext3__priorityIndicatorDCI_4_2_r17;

/* Forward declarations */
struct NR_SetupRelease_DMRS_DownlinkConfig;
struct NR_SetupRelease_PDSCH_TimeDomainResourceAllocationList;
struct NR_RateMatchPatternGroup;
struct NR_SetupRelease_ZP_CSI_RS_ResourceSet;
struct NR_TCI_State;
struct NR_RateMatchPattern;
struct NR_ZP_CSI_RS_Resource;
struct NR_ZP_CSI_RS_ResourceSet;
struct NR_SetupRelease_MaxMIMO_LayersDL_r16;
struct NR_SetupRelease_MinSchedulingOffsetK0_Values_r16;
struct NR_SetupRelease_PDSCH_TimeDomainResourceAllocationList_r16;
struct NR_SetupRelease_RepetitionSchemeConfig_r16;
struct NR_SetupRelease_RepetitionSchemeConfig_v1630;
struct NR_SetupRelease_Dummy_TDRA_List;
struct NR_SetupRelease_MinSchedulingOffsetK0_Values_r17;
struct NR_ServingCellAndBWP_Id_r17;
struct NR_SetupRelease_MultiPDSCH_TDRA_List_r17;

/* NR_PDSCH-Config */
typedef struct NR_PDSCH_Config {
	long	*dataScramblingIdentityPDSCH;	/* OPTIONAL */
	struct NR_SetupRelease_DMRS_DownlinkConfig	*dmrs_DownlinkForPDSCH_MappingTypeA;	/* OPTIONAL */
	struct NR_SetupRelease_DMRS_DownlinkConfig	*dmrs_DownlinkForPDSCH_MappingTypeB;	/* OPTIONAL */
	struct NR_PDSCH_Config__tci_StatesToAddModList {
		A_SEQUENCE_OF(struct NR_TCI_State) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *tci_StatesToAddModList;
	struct NR_PDSCH_Config__tci_StatesToReleaseList {
		A_SEQUENCE_OF(NR_TCI_StateId_t) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *tci_StatesToReleaseList;
	long	*vrb_ToPRB_Interleaver;	/* OPTIONAL */
	long	 resourceAllocation;
	struct NR_SetupRelease_PDSCH_TimeDomainResourceAllocationList	*pdsch_TimeDomainAllocationList;	/* OPTIONAL */
	long	*pdsch_AggregationFactor;	/* OPTIONAL */
	struct NR_PDSCH_Config__rateMatchPatternToAddModList {
		A_SEQUENCE_OF(struct NR_RateMatchPattern) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *rateMatchPatternToAddModList;
	struct NR_PDSCH_Config__rateMatchPatternToReleaseList {
		A_SEQUENCE_OF(NR_RateMatchPatternId_t) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *rateMatchPatternToReleaseList;
	struct NR_RateMatchPatternGroup	*rateMatchPatternGroup1;	/* OPTIONAL */
	struct NR_RateMatchPatternGroup	*rateMatchPatternGroup2;	/* OPTIONAL */
	long	 rbg_Size;
	long	*mcs_Table;	/* OPTIONAL */
	long	*maxNrofCodeWordsScheduledByDCI;	/* OPTIONAL */
	struct NR_PDSCH_Config__prb_BundlingType {
		NR_PDSCH_Config__prb_BundlingType_PR present;
		union NR_PDSCH_Config__NR_prb_BundlingType_u {
			struct NR_PDSCH_Config__prb_BundlingType__staticBundling {
				long	*bundleSize;	/* OPTIONAL */
				
				/* Context for parsing across buffer boundaries */
				asn_struct_ctx_t _asn_ctx;
			} *staticBundling;
			struct NR_PDSCH_Config__prb_BundlingType__dynamicBundling {
				long	*bundleSizeSet1;	/* OPTIONAL */
				long	*bundleSizeSet2;	/* OPTIONAL */
				
				/* Context for parsing across buffer boundaries */
				asn_struct_ctx_t _asn_ctx;
			} *dynamicBundling;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} prb_BundlingType;
	struct NR_PDSCH_Config__zp_CSI_RS_ResourceToAddModList {
		A_SEQUENCE_OF(struct NR_ZP_CSI_RS_Resource) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *zp_CSI_RS_ResourceToAddModList;
	struct NR_PDSCH_Config__zp_CSI_RS_ResourceToReleaseList {
		A_SEQUENCE_OF(NR_ZP_CSI_RS_ResourceId_t) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *zp_CSI_RS_ResourceToReleaseList;
	struct NR_PDSCH_Config__aperiodic_ZP_CSI_RS_ResourceSetsToAddModList {
		A_SEQUENCE_OF(struct NR_ZP_CSI_RS_ResourceSet) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *aperiodic_ZP_CSI_RS_ResourceSetsToAddModList;
	struct NR_PDSCH_Config__aperiodic_ZP_CSI_RS_ResourceSetsToReleaseList {
		A_SEQUENCE_OF(NR_ZP_CSI_RS_ResourceSetId_t) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *aperiodic_ZP_CSI_RS_ResourceSetsToReleaseList;
	struct NR_PDSCH_Config__sp_ZP_CSI_RS_ResourceSetsToAddModList {
		A_SEQUENCE_OF(struct NR_ZP_CSI_RS_ResourceSet) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *sp_ZP_CSI_RS_ResourceSetsToAddModList;
	struct NR_PDSCH_Config__sp_ZP_CSI_RS_ResourceSetsToReleaseList {
		A_SEQUENCE_OF(NR_ZP_CSI_RS_ResourceSetId_t) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *sp_ZP_CSI_RS_ResourceSetsToReleaseList;
	struct NR_SetupRelease_ZP_CSI_RS_ResourceSet	*p_ZP_CSI_RS_ResourceSet;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	struct NR_PDSCH_Config__ext1 {
		struct NR_SetupRelease_MaxMIMO_LayersDL_r16	*maxMIMO_Layers_r16;	/* OPTIONAL */
		struct NR_SetupRelease_MinSchedulingOffsetK0_Values_r16	*minimumSchedulingOffsetK0_r16;	/* OPTIONAL */
		long	*antennaPortsFieldPresenceDCI_1_2_r16;	/* OPTIONAL */
		struct NR_PDSCH_Config__ext1__aperiodicZP_CSI_RS_ResourceSetsToAddModListDCI_1_2_r16 {
			A_SEQUENCE_OF(struct NR_ZP_CSI_RS_ResourceSet) list;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *aperiodicZP_CSI_RS_ResourceSetsToAddModListDCI_1_2_r16;
		struct NR_PDSCH_Config__ext1__aperiodicZP_CSI_RS_ResourceSetsToReleaseListDCI_1_2_r16 {
			A_SEQUENCE_OF(NR_ZP_CSI_RS_ResourceSetId_t) list;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *aperiodicZP_CSI_RS_ResourceSetsToReleaseListDCI_1_2_r16;
		struct NR_SetupRelease_DMRS_DownlinkConfig	*dmrs_DownlinkForPDSCH_MappingTypeA_DCI_1_2_r16;	/* OPTIONAL */
		struct NR_SetupRelease_DMRS_DownlinkConfig	*dmrs_DownlinkForPDSCH_MappingTypeB_DCI_1_2_r16;	/* OPTIONAL */
		long	*dmrs_SequenceInitializationDCI_1_2_r16;	/* OPTIONAL */
		long	*harq_ProcessNumberSizeDCI_1_2_r16;	/* OPTIONAL */
		long	*mcs_TableDCI_1_2_r16;	/* OPTIONAL */
		long	*numberOfBitsForRV_DCI_1_2_r16;	/* OPTIONAL */
		struct NR_SetupRelease_PDSCH_TimeDomainResourceAllocationList_r16	*pdsch_TimeDomainAllocationListDCI_1_2_r16;	/* OPTIONAL */
		struct NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16 {
			NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16_PR present;
			union NR_PDSCH_Config__NR_ext1__NR_prb_BundlingTypeDCI_1_2_r16_u {
				struct NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16__staticBundling_r16 {
					long	*bundleSize_r16;	/* OPTIONAL */
					
					/* Context for parsing across buffer boundaries */
					asn_struct_ctx_t _asn_ctx;
				} *staticBundling_r16;
				struct NR_PDSCH_Config__ext1__prb_BundlingTypeDCI_1_2_r16__dynamicBundling_r16 {
					long	*bundleSizeSet1_r16;	/* OPTIONAL */
					long	*bundleSizeSet2_r16;	/* OPTIONAL */
					
					/* Context for parsing across buffer boundaries */
					asn_struct_ctx_t _asn_ctx;
				} *dynamicBundling_r16;
			} choice;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *prb_BundlingTypeDCI_1_2_r16;
		long	*priorityIndicatorDCI_1_2_r16;	/* OPTIONAL */
		struct NR_RateMatchPatternGroup	*rateMatchPatternGroup1DCI_1_2_r16;	/* OPTIONAL */
		struct NR_RateMatchPatternGroup	*rateMatchPatternGroup2DCI_1_2_r16;	/* OPTIONAL */
		long	*resourceAllocationType1GranularityDCI_1_2_r16;	/* OPTIONAL */
		long	*vrb_ToPRB_InterleaverDCI_1_2_r16;	/* OPTIONAL */
		long	*referenceOfSLIVDCI_1_2_r16;	/* OPTIONAL */
		long	*resourceAllocationDCI_1_2_r16;	/* OPTIONAL */
		long	*priorityIndicatorDCI_1_1_r16;	/* OPTIONAL */
		long	*dataScramblingIdentityPDSCH2_r16;	/* OPTIONAL */
		struct NR_SetupRelease_PDSCH_TimeDomainResourceAllocationList_r16	*pdsch_TimeDomainAllocationList_r16;	/* OPTIONAL */
		struct NR_SetupRelease_RepetitionSchemeConfig_r16	*repetitionSchemeConfig_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext1;
	struct NR_PDSCH_Config__ext2 {
		struct NR_SetupRelease_RepetitionSchemeConfig_v1630	*repetitionSchemeConfig_v1630;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext2;
	struct NR_PDSCH_Config__ext3 {
		long	*pdsch_HARQ_ACK_OneShotFeedbackDCI_1_2_r17;	/* OPTIONAL */
		long	*pdsch_HARQ_ACK_EnhType3DCI_1_2_r17;	/* OPTIONAL */
		long	*pdsch_HARQ_ACK_EnhType3DCI_Field_1_2_r17;	/* OPTIONAL */
		long	*pdsch_HARQ_ACK_RetxDCI_1_2_r17;	/* OPTIONAL */
		long	*pucch_sSCellDynDCI_1_2_r17;	/* OPTIONAL */
		struct NR_PDSCH_Config__ext3__dl_OrJointTCI_StateList_r17 {
			NR_PDSCH_Config__ext3__dl_OrJointTCI_StateList_r17_PR present;
			union NR_PDSCH_Config__NR_ext3__NR_dl_OrJointTCI_StateList_r17_u {
				struct NR_PDSCH_Config__ext3__dl_OrJointTCI_StateList_r17__explicitlist {
					struct NR_PDSCH_Config__ext3__dl_OrJointTCI_StateList_r17__explicitlist__dl_OrJointTCI_StateToAddModList_r17 {
						A_SEQUENCE_OF(struct NR_TCI_State) list;
						
						/* Context for parsing across buffer boundaries */
						asn_struct_ctx_t _asn_ctx;
					} *dl_OrJointTCI_StateToAddModList_r17;
					struct NR_PDSCH_Config__ext3__dl_OrJointTCI_StateList_r17__explicitlist__dl_OrJointTCI_StateToReleaseList_r17 {
						A_SEQUENCE_OF(NR_TCI_StateId_t) list;
						
						/* Context for parsing across buffer boundaries */
						asn_struct_ctx_t _asn_ctx;
					} *dl_OrJointTCI_StateToReleaseList_r17;
					
					/* Context for parsing across buffer boundaries */
					asn_struct_ctx_t _asn_ctx;
				} *explicitlist;
				struct NR_ServingCellAndBWP_Id_r17	*unifiedTCI_StateRef_r17;
			} choice;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *dl_OrJointTCI_StateList_r17;
		long	*beamAppTime_r17;	/* OPTIONAL */
		struct NR_SetupRelease_Dummy_TDRA_List	*dummy;	/* OPTIONAL */
		long	*dmrs_FD_OCC_DisabledForRank1_PDSCH_r17;	/* OPTIONAL */
		struct NR_SetupRelease_MinSchedulingOffsetK0_Values_r17	*minimumSchedulingOffsetK0_r17;	/* OPTIONAL */
		long	*harq_ProcessNumberSizeDCI_1_2_v1700;	/* OPTIONAL */
		long	*harq_ProcessNumberSizeDCI_1_1_r17;	/* OPTIONAL */
		long	*mcs_Table_r17;	/* OPTIONAL */
		long	*mcs_TableDCI_1_2_r17;	/* OPTIONAL */
		long	*xOverheadMulticast_r17;	/* OPTIONAL */
		long	*priorityIndicatorDCI_4_2_r17;	/* OPTIONAL */
		long	*sizeDCI_4_2_r17;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext3;
	struct NR_PDSCH_Config__ext4 {
		struct NR_SetupRelease_MultiPDSCH_TDRA_List_r17	*pdsch_TimeDomainAllocationListForMultiPDSCH_r17;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext4;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_PDSCH_Config_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_vrb_ToPRB_Interleaver_9;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_resourceAllocation_12;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pdsch_AggregationFactor_17;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_rbg_Size_27;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mcs_Table_30;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNrofCodeWordsScheduledByDCI_33;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_bundleSize_38;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_bundleSizeSet1_42;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_bundleSizeSet2_47;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_antennaPortsFieldPresenceDCI_1_2_r16_67;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dmrs_SequenceInitializationDCI_1_2_r16_75;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mcs_TableDCI_1_2_r16_78;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_bundleSize_r16_85;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_bundleSizeSet1_r16_89;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_bundleSizeSet2_r16_94;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_priorityIndicatorDCI_1_2_r16_97;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_resourceAllocationType1GranularityDCI_1_2_r16_101;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_vrb_ToPRB_InterleaverDCI_1_2_r16_106;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_referenceOfSLIVDCI_1_2_r16_109;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_resourceAllocationDCI_1_2_r16_111;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_priorityIndicatorDCI_1_1_r16_115;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pdsch_HARQ_ACK_OneShotFeedbackDCI_1_2_r17_123;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pdsch_HARQ_ACK_EnhType3DCI_1_2_r17_125;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pdsch_HARQ_ACK_EnhType3DCI_Field_1_2_r17_127;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pdsch_HARQ_ACK_RetxDCI_1_2_r17_129;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pucch_sSCellDynDCI_1_2_r17_131;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_beamAppTime_r17_140;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dmrs_FD_OCC_DisabledForRank1_PDSCH_r17_158;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mcs_Table_r17_163;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mcs_TableDCI_1_2_r17_165;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_xOverheadMulticast_r17_167;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_priorityIndicatorDCI_4_2_r17_171;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_PDSCH_Config;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_PDSCH_Config_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_PDSCH_Config_1[28];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_SetupRelease.h"
#include "NR_RateMatchPatternGroup.h"
#include "NR_TCI-State.h"
#include "NR_RateMatchPattern.h"
#include "NR_ZP-CSI-RS-Resource.h"
#include "NR_ZP-CSI-RS-ResourceSet.h"
#include "NR_ServingCellAndBWP-Id-r17.h"

#endif	/* _NR_PDSCH_Config_H_ */
#include <asn_internal.h>
