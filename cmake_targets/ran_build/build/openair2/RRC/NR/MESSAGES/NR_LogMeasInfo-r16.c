/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_LogMeasInfo-r16.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_relativeTimeStamp_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 7200L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_anyCellSelectionDetected_r16_constr_9 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_inDeviceCoexDetected_r17_constr_13 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_relativeTimeStamp_r16_constr_3 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 13,  13,  0,  7200 }	/* (0..7200) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static asn_TYPE_member_t asn_MBR_NR_measResultNeighCells_r16_6[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_LogMeasInfo_r16__measResultNeighCells_r16, measResultNeighCellListNR),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_MeasResultListLogging2NR_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"measResultNeighCellListNR"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_LogMeasInfo_r16__measResultNeighCells_r16, measResultNeighCellListEUTRA),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_MeasResultList2EUTRA_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"measResultNeighCellListEUTRA"
		},
};
static const int asn_MAP_NR_measResultNeighCells_r16_oms_6[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_measResultNeighCells_r16_tags_6[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_measResultNeighCells_r16_tag2el_6[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* measResultNeighCellListNR */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* measResultNeighCellListEUTRA */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_measResultNeighCells_r16_specs_6 = {
	sizeof(struct NR_LogMeasInfo_r16__measResultNeighCells_r16),
	offsetof(struct NR_LogMeasInfo_r16__measResultNeighCells_r16, _asn_ctx),
	asn_MAP_NR_measResultNeighCells_r16_tag2el_6,
	2,	/* Count of tags in the map */
	asn_MAP_NR_measResultNeighCells_r16_oms_6,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_measResultNeighCells_r16_6 = {
	"measResultNeighCells-r16",
	"measResultNeighCells-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_measResultNeighCells_r16_tags_6,
	sizeof(asn_DEF_NR_measResultNeighCells_r16_tags_6)
		/sizeof(asn_DEF_NR_measResultNeighCells_r16_tags_6[0]) - 1, /* 1 */
	asn_DEF_NR_measResultNeighCells_r16_tags_6,	/* Same as above */
	sizeof(asn_DEF_NR_measResultNeighCells_r16_tags_6)
		/sizeof(asn_DEF_NR_measResultNeighCells_r16_tags_6[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_measResultNeighCells_r16_6,
	2,	/* Elements count */
	&asn_SPC_NR_measResultNeighCells_r16_specs_6	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_anyCellSelectionDetected_r16_value2enum_9[] = {
	{ 0,	4,	"true" }
};
static const unsigned int asn_MAP_NR_anyCellSelectionDetected_r16_enum2value_9[] = {
	0	/* true(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_anyCellSelectionDetected_r16_specs_9 = {
	asn_MAP_NR_anyCellSelectionDetected_r16_value2enum_9,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_anyCellSelectionDetected_r16_enum2value_9,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_anyCellSelectionDetected_r16_tags_9[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_anyCellSelectionDetected_r16_9 = {
	"anyCellSelectionDetected-r16",
	"anyCellSelectionDetected-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_anyCellSelectionDetected_r16_tags_9,
	sizeof(asn_DEF_NR_anyCellSelectionDetected_r16_tags_9)
		/sizeof(asn_DEF_NR_anyCellSelectionDetected_r16_tags_9[0]) - 1, /* 1 */
	asn_DEF_NR_anyCellSelectionDetected_r16_tags_9,	/* Same as above */
	sizeof(asn_DEF_NR_anyCellSelectionDetected_r16_tags_9)
		/sizeof(asn_DEF_NR_anyCellSelectionDetected_r16_tags_9[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_anyCellSelectionDetected_r16_constr_9,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_anyCellSelectionDetected_r16_specs_9	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_inDeviceCoexDetected_r17_value2enum_13[] = {
	{ 0,	4,	"true" }
};
static const unsigned int asn_MAP_NR_inDeviceCoexDetected_r17_enum2value_13[] = {
	0	/* true(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_inDeviceCoexDetected_r17_specs_13 = {
	asn_MAP_NR_inDeviceCoexDetected_r17_value2enum_13,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_inDeviceCoexDetected_r17_enum2value_13,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_inDeviceCoexDetected_r17_tags_13[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_inDeviceCoexDetected_r17_13 = {
	"inDeviceCoexDetected-r17",
	"inDeviceCoexDetected-r17",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_inDeviceCoexDetected_r17_tags_13,
	sizeof(asn_DEF_NR_inDeviceCoexDetected_r17_tags_13)
		/sizeof(asn_DEF_NR_inDeviceCoexDetected_r17_tags_13[0]) - 1, /* 1 */
	asn_DEF_NR_inDeviceCoexDetected_r17_tags_13,	/* Same as above */
	sizeof(asn_DEF_NR_inDeviceCoexDetected_r17_tags_13)
		/sizeof(asn_DEF_NR_inDeviceCoexDetected_r17_tags_13[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_inDeviceCoexDetected_r17_constr_13,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_inDeviceCoexDetected_r17_specs_13	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext1_12[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_LogMeasInfo_r16__ext1, inDeviceCoexDetected_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_inDeviceCoexDetected_r17_13,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"inDeviceCoexDetected-r17"
		},
};
static const int asn_MAP_NR_ext1_oms_12[] = { 0 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_12[] = {
	(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_12[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 } /* inDeviceCoexDetected-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_12 = {
	sizeof(struct NR_LogMeasInfo_r16__ext1),
	offsetof(struct NR_LogMeasInfo_r16__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_12,
	1,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_12,	/* Optional members */
	1, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_12 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_12,
	sizeof(asn_DEF_NR_ext1_tags_12)
		/sizeof(asn_DEF_NR_ext1_tags_12[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_12,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_12)
		/sizeof(asn_DEF_NR_ext1_tags_12[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_12,
	1,	/* Elements count */
	&asn_SPC_NR_ext1_specs_12	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_LogMeasInfo_r16_1[] = {
	{ ATF_POINTER, 1, offsetof(struct NR_LogMeasInfo_r16, locationInfo_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_LocationInfo_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"locationInfo-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_LogMeasInfo_r16, relativeTimeStamp_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_relativeTimeStamp_r16_constr_3,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_relativeTimeStamp_r16_constraint_1
		},
		0, 0, /* No default value */
		"relativeTimeStamp-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_LogMeasInfo_r16, servCellIdentity_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_CGI_Info_Logging_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"servCellIdentity-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_LogMeasInfo_r16, measResultServingCell_r16),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_MeasResultServingCell_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"measResultServingCell-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_LogMeasInfo_r16, measResultNeighCells_r16),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		0,
		&asn_DEF_NR_measResultNeighCells_r16_6,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"measResultNeighCells-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_LogMeasInfo_r16, anyCellSelectionDetected_r16),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_anyCellSelectionDetected_r16_9,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"anyCellSelectionDetected-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_LogMeasInfo_r16, ext1),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		0,
		&asn_DEF_NR_ext1_12,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
};
static const int asn_MAP_NR_LogMeasInfo_r16_oms_1[] = { 0, 2, 3, 5, 6 };
static const ber_tlv_tag_t asn_DEF_NR_LogMeasInfo_r16_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_LogMeasInfo_r16_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* locationInfo-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* relativeTimeStamp-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* servCellIdentity-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* measResultServingCell-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* measResultNeighCells-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* anyCellSelectionDetected-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 } /* ext1 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_LogMeasInfo_r16_specs_1 = {
	sizeof(struct NR_LogMeasInfo_r16),
	offsetof(struct NR_LogMeasInfo_r16, _asn_ctx),
	asn_MAP_NR_LogMeasInfo_r16_tag2el_1,
	7,	/* Count of tags in the map */
	asn_MAP_NR_LogMeasInfo_r16_oms_1,	/* Optional members */
	4, 1,	/* Root/Additions */
	6,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_LogMeasInfo_r16 = {
	"LogMeasInfo-r16",
	"LogMeasInfo-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_LogMeasInfo_r16_tags_1,
	sizeof(asn_DEF_NR_LogMeasInfo_r16_tags_1)
		/sizeof(asn_DEF_NR_LogMeasInfo_r16_tags_1[0]), /* 1 */
	asn_DEF_NR_LogMeasInfo_r16_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_LogMeasInfo_r16_tags_1)
		/sizeof(asn_DEF_NR_LogMeasInfo_r16_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_LogMeasInfo_r16_1,
	7,	/* Elements count */
	&asn_SPC_NR_LogMeasInfo_r16_specs_1	/* Additional specs */
};

