/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_MIMO_ParametersPerBand_H_
#define	_NR_MIMO_ParametersPerBand_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <NativeInteger.h>
#include <constr_SEQUENCE.h>
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include <BIT_STRING.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_MIMO_ParametersPerBand__tci_StatePDSCH__maxNumberConfiguredTCI_StatesPerCC {
	NR_MIMO_ParametersPerBand__tci_StatePDSCH__maxNumberConfiguredTCI_StatesPerCC_n4	= 0,
	NR_MIMO_ParametersPerBand__tci_StatePDSCH__maxNumberConfiguredTCI_StatesPerCC_n8	= 1,
	NR_MIMO_ParametersPerBand__tci_StatePDSCH__maxNumberConfiguredTCI_StatesPerCC_n16	= 2,
	NR_MIMO_ParametersPerBand__tci_StatePDSCH__maxNumberConfiguredTCI_StatesPerCC_n32	= 3,
	NR_MIMO_ParametersPerBand__tci_StatePDSCH__maxNumberConfiguredTCI_StatesPerCC_n64	= 4,
	NR_MIMO_ParametersPerBand__tci_StatePDSCH__maxNumberConfiguredTCI_StatesPerCC_n128	= 5
} e_NR_MIMO_ParametersPerBand__tci_StatePDSCH__maxNumberConfiguredTCI_StatesPerCC;
typedef enum NR_MIMO_ParametersPerBand__tci_StatePDSCH__maxNumberActiveTCI_PerBWP {
	NR_MIMO_ParametersPerBand__tci_StatePDSCH__maxNumberActiveTCI_PerBWP_n1	= 0,
	NR_MIMO_ParametersPerBand__tci_StatePDSCH__maxNumberActiveTCI_PerBWP_n2	= 1,
	NR_MIMO_ParametersPerBand__tci_StatePDSCH__maxNumberActiveTCI_PerBWP_n4	= 2,
	NR_MIMO_ParametersPerBand__tci_StatePDSCH__maxNumberActiveTCI_PerBWP_n8	= 3
} e_NR_MIMO_ParametersPerBand__tci_StatePDSCH__maxNumberActiveTCI_PerBWP;
typedef enum NR_MIMO_ParametersPerBand__additionalActiveTCI_StatePDCCH {
	NR_MIMO_ParametersPerBand__additionalActiveTCI_StatePDCCH_supported	= 0
} e_NR_MIMO_ParametersPerBand__additionalActiveTCI_StatePDCCH;
typedef enum NR_MIMO_ParametersPerBand__pusch_TransCoherence {
	NR_MIMO_ParametersPerBand__pusch_TransCoherence_nonCoherent	= 0,
	NR_MIMO_ParametersPerBand__pusch_TransCoherence_partialCoherent	= 1,
	NR_MIMO_ParametersPerBand__pusch_TransCoherence_fullCoherent	= 2
} e_NR_MIMO_ParametersPerBand__pusch_TransCoherence;
typedef enum NR_MIMO_ParametersPerBand__beamCorrespondenceWithoutUL_BeamSweeping {
	NR_MIMO_ParametersPerBand__beamCorrespondenceWithoutUL_BeamSweeping_supported	= 0
} e_NR_MIMO_ParametersPerBand__beamCorrespondenceWithoutUL_BeamSweeping;
typedef enum NR_MIMO_ParametersPerBand__periodicBeamReport {
	NR_MIMO_ParametersPerBand__periodicBeamReport_supported	= 0
} e_NR_MIMO_ParametersPerBand__periodicBeamReport;
typedef enum NR_MIMO_ParametersPerBand__aperiodicBeamReport {
	NR_MIMO_ParametersPerBand__aperiodicBeamReport_supported	= 0
} e_NR_MIMO_ParametersPerBand__aperiodicBeamReport;
typedef enum NR_MIMO_ParametersPerBand__sp_BeamReportPUCCH {
	NR_MIMO_ParametersPerBand__sp_BeamReportPUCCH_supported	= 0
} e_NR_MIMO_ParametersPerBand__sp_BeamReportPUCCH;
typedef enum NR_MIMO_ParametersPerBand__sp_BeamReportPUSCH {
	NR_MIMO_ParametersPerBand__sp_BeamReportPUSCH_supported	= 0
} e_NR_MIMO_ParametersPerBand__sp_BeamReportPUSCH;
typedef enum NR_MIMO_ParametersPerBand__maxNumberRxTxBeamSwitchDL__scs_15kHz {
	NR_MIMO_ParametersPerBand__maxNumberRxTxBeamSwitchDL__scs_15kHz_n4	= 0,
	NR_MIMO_ParametersPerBand__maxNumberRxTxBeamSwitchDL__scs_15kHz_n7	= 1,
	NR_MIMO_ParametersPerBand__maxNumberRxTxBeamSwitchDL__scs_15kHz_n14	= 2
} e_NR_MIMO_ParametersPerBand__maxNumberRxTxBeamSwitchDL__scs_15kHz;
typedef enum NR_MIMO_ParametersPerBand__maxNumberRxTxBeamSwitchDL__scs_30kHz {
	NR_MIMO_ParametersPerBand__maxNumberRxTxBeamSwitchDL__scs_30kHz_n4	= 0,
	NR_MIMO_ParametersPerBand__maxNumberRxTxBeamSwitchDL__scs_30kHz_n7	= 1,
	NR_MIMO_ParametersPerBand__maxNumberRxTxBeamSwitchDL__scs_30kHz_n14	= 2
} e_NR_MIMO_ParametersPerBand__maxNumberRxTxBeamSwitchDL__scs_30kHz;
typedef enum NR_MIMO_ParametersPerBand__maxNumberRxTxBeamSwitchDL__scs_60kHz {
	NR_MIMO_ParametersPerBand__maxNumberRxTxBeamSwitchDL__scs_60kHz_n4	= 0,
	NR_MIMO_ParametersPerBand__maxNumberRxTxBeamSwitchDL__scs_60kHz_n7	= 1,
	NR_MIMO_ParametersPerBand__maxNumberRxTxBeamSwitchDL__scs_60kHz_n14	= 2
} e_NR_MIMO_ParametersPerBand__maxNumberRxTxBeamSwitchDL__scs_60kHz;
typedef enum NR_MIMO_ParametersPerBand__maxNumberRxTxBeamSwitchDL__scs_120kHz {
	NR_MIMO_ParametersPerBand__maxNumberRxTxBeamSwitchDL__scs_120kHz_n4	= 0,
	NR_MIMO_ParametersPerBand__maxNumberRxTxBeamSwitchDL__scs_120kHz_n7	= 1,
	NR_MIMO_ParametersPerBand__maxNumberRxTxBeamSwitchDL__scs_120kHz_n14	= 2
} e_NR_MIMO_ParametersPerBand__maxNumberRxTxBeamSwitchDL__scs_120kHz;
typedef enum NR_MIMO_ParametersPerBand__maxNumberRxTxBeamSwitchDL__scs_240kHz {
	NR_MIMO_ParametersPerBand__maxNumberRxTxBeamSwitchDL__scs_240kHz_n4	= 0,
	NR_MIMO_ParametersPerBand__maxNumberRxTxBeamSwitchDL__scs_240kHz_n7	= 1,
	NR_MIMO_ParametersPerBand__maxNumberRxTxBeamSwitchDL__scs_240kHz_n14	= 2
} e_NR_MIMO_ParametersPerBand__maxNumberRxTxBeamSwitchDL__scs_240kHz;
typedef enum NR_MIMO_ParametersPerBand__maxNumberNonGroupBeamReporting {
	NR_MIMO_ParametersPerBand__maxNumberNonGroupBeamReporting_n1	= 0,
	NR_MIMO_ParametersPerBand__maxNumberNonGroupBeamReporting_n2	= 1,
	NR_MIMO_ParametersPerBand__maxNumberNonGroupBeamReporting_n4	= 2
} e_NR_MIMO_ParametersPerBand__maxNumberNonGroupBeamReporting;
typedef enum NR_MIMO_ParametersPerBand__groupBeamReporting {
	NR_MIMO_ParametersPerBand__groupBeamReporting_supported	= 0
} e_NR_MIMO_ParametersPerBand__groupBeamReporting;
typedef enum NR_MIMO_ParametersPerBand__uplinkBeamManagement__maxNumberSRS_ResourcePerSet_BM {
	NR_MIMO_ParametersPerBand__uplinkBeamManagement__maxNumberSRS_ResourcePerSet_BM_n2	= 0,
	NR_MIMO_ParametersPerBand__uplinkBeamManagement__maxNumberSRS_ResourcePerSet_BM_n4	= 1,
	NR_MIMO_ParametersPerBand__uplinkBeamManagement__maxNumberSRS_ResourcePerSet_BM_n8	= 2,
	NR_MIMO_ParametersPerBand__uplinkBeamManagement__maxNumberSRS_ResourcePerSet_BM_n16	= 3
} e_NR_MIMO_ParametersPerBand__uplinkBeamManagement__maxNumberSRS_ResourcePerSet_BM;
typedef enum NR_MIMO_ParametersPerBand__dummy2 {
	NR_MIMO_ParametersPerBand__dummy2_supported	= 0
} e_NR_MIMO_ParametersPerBand__dummy2;
typedef enum NR_MIMO_ParametersPerBand__twoPortsPTRS_UL {
	NR_MIMO_ParametersPerBand__twoPortsPTRS_UL_supported	= 0
} e_NR_MIMO_ParametersPerBand__twoPortsPTRS_UL;
typedef enum NR_MIMO_ParametersPerBand__beamReportTiming__scs_15kHz {
	NR_MIMO_ParametersPerBand__beamReportTiming__scs_15kHz_sym2	= 0,
	NR_MIMO_ParametersPerBand__beamReportTiming__scs_15kHz_sym4	= 1,
	NR_MIMO_ParametersPerBand__beamReportTiming__scs_15kHz_sym8	= 2
} e_NR_MIMO_ParametersPerBand__beamReportTiming__scs_15kHz;
typedef enum NR_MIMO_ParametersPerBand__beamReportTiming__scs_30kHz {
	NR_MIMO_ParametersPerBand__beamReportTiming__scs_30kHz_sym4	= 0,
	NR_MIMO_ParametersPerBand__beamReportTiming__scs_30kHz_sym8	= 1,
	NR_MIMO_ParametersPerBand__beamReportTiming__scs_30kHz_sym14	= 2,
	NR_MIMO_ParametersPerBand__beamReportTiming__scs_30kHz_sym28	= 3
} e_NR_MIMO_ParametersPerBand__beamReportTiming__scs_30kHz;
typedef enum NR_MIMO_ParametersPerBand__beamReportTiming__scs_60kHz {
	NR_MIMO_ParametersPerBand__beamReportTiming__scs_60kHz_sym8	= 0,
	NR_MIMO_ParametersPerBand__beamReportTiming__scs_60kHz_sym14	= 1,
	NR_MIMO_ParametersPerBand__beamReportTiming__scs_60kHz_sym28	= 2
} e_NR_MIMO_ParametersPerBand__beamReportTiming__scs_60kHz;
typedef enum NR_MIMO_ParametersPerBand__beamReportTiming__scs_120kHz {
	NR_MIMO_ParametersPerBand__beamReportTiming__scs_120kHz_sym14	= 0,
	NR_MIMO_ParametersPerBand__beamReportTiming__scs_120kHz_sym28	= 1,
	NR_MIMO_ParametersPerBand__beamReportTiming__scs_120kHz_sym56	= 2
} e_NR_MIMO_ParametersPerBand__beamReportTiming__scs_120kHz;
typedef enum NR_MIMO_ParametersPerBand__aperiodicTRS {
	NR_MIMO_ParametersPerBand__aperiodicTRS_supported	= 0
} e_NR_MIMO_ParametersPerBand__aperiodicTRS;
typedef enum NR_MIMO_ParametersPerBand__ext1__dummy6 {
	NR_MIMO_ParametersPerBand__ext1__dummy6_true	= 0
} e_NR_MIMO_ParametersPerBand__ext1__dummy6;
typedef enum NR_MIMO_ParametersPerBand__ext1__beamSwitchTiming__scs_60kHz {
	NR_MIMO_ParametersPerBand__ext1__beamSwitchTiming__scs_60kHz_sym14	= 0,
	NR_MIMO_ParametersPerBand__ext1__beamSwitchTiming__scs_60kHz_sym28	= 1,
	NR_MIMO_ParametersPerBand__ext1__beamSwitchTiming__scs_60kHz_sym48	= 2,
	NR_MIMO_ParametersPerBand__ext1__beamSwitchTiming__scs_60kHz_sym224	= 3,
	NR_MIMO_ParametersPerBand__ext1__beamSwitchTiming__scs_60kHz_sym336	= 4
} e_NR_MIMO_ParametersPerBand__ext1__beamSwitchTiming__scs_60kHz;
typedef enum NR_MIMO_ParametersPerBand__ext1__beamSwitchTiming__scs_120kHz {
	NR_MIMO_ParametersPerBand__ext1__beamSwitchTiming__scs_120kHz_sym14	= 0,
	NR_MIMO_ParametersPerBand__ext1__beamSwitchTiming__scs_120kHz_sym28	= 1,
	NR_MIMO_ParametersPerBand__ext1__beamSwitchTiming__scs_120kHz_sym48	= 2,
	NR_MIMO_ParametersPerBand__ext1__beamSwitchTiming__scs_120kHz_sym224	= 3,
	NR_MIMO_ParametersPerBand__ext1__beamSwitchTiming__scs_120kHz_sym336	= 4
} e_NR_MIMO_ParametersPerBand__ext1__beamSwitchTiming__scs_120kHz;
typedef enum NR_MIMO_ParametersPerBand__ext2__defaultQCL_TwoTCI_r16 {
	NR_MIMO_ParametersPerBand__ext2__defaultQCL_TwoTCI_r16_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext2__defaultQCL_TwoTCI_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__simul_SpatialRelationUpdatePUCCHResGroup_r16 {
	NR_MIMO_ParametersPerBand__ext2__simul_SpatialRelationUpdatePUCCHResGroup_r16_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext2__simul_SpatialRelationUpdatePUCCHResGroup_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__maxNumberSCellBFR_r16 {
	NR_MIMO_ParametersPerBand__ext2__maxNumberSCellBFR_r16_n1	= 0,
	NR_MIMO_ParametersPerBand__ext2__maxNumberSCellBFR_r16_n2	= 1,
	NR_MIMO_ParametersPerBand__ext2__maxNumberSCellBFR_r16_n4	= 2,
	NR_MIMO_ParametersPerBand__ext2__maxNumberSCellBFR_r16_n8	= 3
} e_NR_MIMO_ParametersPerBand__ext2__maxNumberSCellBFR_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__simultaneousReceptionDiffTypeD_r16 {
	NR_MIMO_ParametersPerBand__ext2__simultaneousReceptionDiffTypeD_r16_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext2__simultaneousReceptionDiffTypeD_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberSSB_CSIRS_OneTx_CMR_r16 {
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberSSB_CSIRS_OneTx_CMR_r16_n8	= 0,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberSSB_CSIRS_OneTx_CMR_r16_n16	= 1,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberSSB_CSIRS_OneTx_CMR_r16_n32	= 2,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberSSB_CSIRS_OneTx_CMR_r16_n64	= 3
} e_NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberSSB_CSIRS_OneTx_CMR_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberCSI_IM_NZP_IMR_res_r16 {
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberCSI_IM_NZP_IMR_res_r16_n8	= 0,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberCSI_IM_NZP_IMR_res_r16_n16	= 1,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberCSI_IM_NZP_IMR_res_r16_n32	= 2,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberCSI_IM_NZP_IMR_res_r16_n64	= 3
} e_NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberCSI_IM_NZP_IMR_res_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberCSIRS_2Tx_res_r16 {
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberCSIRS_2Tx_res_r16_n0	= 0,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberCSIRS_2Tx_res_r16_n4	= 1,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberCSIRS_2Tx_res_r16_n8	= 2,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberCSIRS_2Tx_res_r16_n16	= 3,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberCSIRS_2Tx_res_r16_n32	= 4,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberCSIRS_2Tx_res_r16_n64	= 5
} e_NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberCSIRS_2Tx_res_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberSSB_CSIRS_res_r16 {
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberSSB_CSIRS_res_r16_n8	= 0,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberSSB_CSIRS_res_r16_n16	= 1,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberSSB_CSIRS_res_r16_n32	= 2,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberSSB_CSIRS_res_r16_n64	= 3,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberSSB_CSIRS_res_r16_n128	= 4
} e_NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberSSB_CSIRS_res_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberCSI_IM_NZP_IMR_res_mem_r16 {
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberCSI_IM_NZP_IMR_res_mem_r16_n8	= 0,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberCSI_IM_NZP_IMR_res_mem_r16_n16	= 1,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberCSI_IM_NZP_IMR_res_mem_r16_n32	= 2,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberCSI_IM_NZP_IMR_res_mem_r16_n64	= 3,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberCSI_IM_NZP_IMR_res_mem_r16_n128	= 4
} e_NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberCSI_IM_NZP_IMR_res_mem_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__supportedCSI_RS_Density_CMR_r16 {
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__supportedCSI_RS_Density_CMR_r16_one	= 0,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__supportedCSI_RS_Density_CMR_r16_three	= 1,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__supportedCSI_RS_Density_CMR_r16_oneAndThree	= 2
} e_NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__supportedCSI_RS_Density_CMR_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberAperiodicCSI_RS_Res_r16 {
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberAperiodicCSI_RS_Res_r16_n2	= 0,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberAperiodicCSI_RS_Res_r16_n4	= 1,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberAperiodicCSI_RS_Res_r16_n8	= 2,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberAperiodicCSI_RS_Res_r16_n16	= 3,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberAperiodicCSI_RS_Res_r16_n32	= 4,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberAperiodicCSI_RS_Res_r16_n64	= 5
} e_NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__maxNumberAperiodicCSI_RS_Res_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__supportedSINR_meas_r16 {
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__supportedSINR_meas_r16_ssbWithCSI_IM	= 0,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__supportedSINR_meas_r16_ssbWithNZP_IMR	= 1,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__supportedSINR_meas_r16_csirsWithNZP_IMR	= 2,
	NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__supportedSINR_meas_r16_csi_RSWithoutIMR	= 3
} e_NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16__supportedSINR_meas_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__nonGroupSINR_reporting_r16 {
	NR_MIMO_ParametersPerBand__ext2__nonGroupSINR_reporting_r16_n1	= 0,
	NR_MIMO_ParametersPerBand__ext2__nonGroupSINR_reporting_r16_n2	= 1,
	NR_MIMO_ParametersPerBand__ext2__nonGroupSINR_reporting_r16_n4	= 2
} e_NR_MIMO_ParametersPerBand__ext2__nonGroupSINR_reporting_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__groupSINR_reporting_r16 {
	NR_MIMO_ParametersPerBand__ext2__groupSINR_reporting_r16_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext2__groupSINR_reporting_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__overlapPDSCHsInTimePartiallyFreq_r16 {
	NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__overlapPDSCHsInTimePartiallyFreq_r16_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__overlapPDSCHsInTimePartiallyFreq_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__outOfOrderOperationDL_r16__supportPDCCH_ToPDSCH_r16 {
	NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__outOfOrderOperationDL_r16__supportPDCCH_ToPDSCH_r16_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__outOfOrderOperationDL_r16__supportPDCCH_ToPDSCH_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__outOfOrderOperationDL_r16__supportPDSCH_ToHARQ_ACK_r16 {
	NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__outOfOrderOperationDL_r16__supportPDSCH_ToHARQ_ACK_r16_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__outOfOrderOperationDL_r16__supportPDSCH_ToHARQ_ACK_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__outOfOrderOperationUL_r16 {
	NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__outOfOrderOperationUL_r16_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__outOfOrderOperationUL_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__separateCRS_RateMatching_r16 {
	NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__separateCRS_RateMatching_r16_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__separateCRS_RateMatching_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__defaultQCL_PerCORESETPoolIndex_r16 {
	NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__defaultQCL_PerCORESETPoolIndex_r16_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__defaultQCL_PerCORESETPoolIndex_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__maxNumberActivatedTCI_States_r16__maxNumberPerCORESET_Pool_r16 {
	NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__maxNumberActivatedTCI_States_r16__maxNumberPerCORESET_Pool_r16_n1	= 0,
	NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__maxNumberActivatedTCI_States_r16__maxNumberPerCORESET_Pool_r16_n2	= 1,
	NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__maxNumberActivatedTCI_States_r16__maxNumberPerCORESET_Pool_r16_n4	= 2,
	NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__maxNumberActivatedTCI_States_r16__maxNumberPerCORESET_Pool_r16_n8	= 3
} e_NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__maxNumberActivatedTCI_States_r16__maxNumberPerCORESET_Pool_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__maxNumberActivatedTCI_States_r16__maxTotalNumberAcrossCORESET_Pool_r16 {
	NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__maxNumberActivatedTCI_States_r16__maxTotalNumberAcrossCORESET_Pool_r16_n2	= 0,
	NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__maxNumberActivatedTCI_States_r16__maxTotalNumberAcrossCORESET_Pool_r16_n4	= 1,
	NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__maxNumberActivatedTCI_States_r16__maxTotalNumberAcrossCORESET_Pool_r16_n8	= 2,
	NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__maxNumberActivatedTCI_States_r16__maxTotalNumberAcrossCORESET_Pool_r16_n16	= 3
} e_NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__maxNumberActivatedTCI_States_r16__maxTotalNumberAcrossCORESET_Pool_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__singleDCI_SDM_scheme_Parameters_r16__supportNewDMRS_Port_r16 {
	NR_MIMO_ParametersPerBand__ext2__singleDCI_SDM_scheme_Parameters_r16__supportNewDMRS_Port_r16_supported1	= 0,
	NR_MIMO_ParametersPerBand__ext2__singleDCI_SDM_scheme_Parameters_r16__supportNewDMRS_Port_r16_supported2	= 1,
	NR_MIMO_ParametersPerBand__ext2__singleDCI_SDM_scheme_Parameters_r16__supportNewDMRS_Port_r16_supported3	= 2
} e_NR_MIMO_ParametersPerBand__ext2__singleDCI_SDM_scheme_Parameters_r16__supportNewDMRS_Port_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__singleDCI_SDM_scheme_Parameters_r16__supportTwoPortDL_PTRS_r16 {
	NR_MIMO_ParametersPerBand__ext2__singleDCI_SDM_scheme_Parameters_r16__supportTwoPortDL_PTRS_r16_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext2__singleDCI_SDM_scheme_Parameters_r16__supportTwoPortDL_PTRS_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__supportFDM_SchemeA_r16 {
	NR_MIMO_ParametersPerBand__ext2__supportFDM_SchemeA_r16_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext2__supportFDM_SchemeA_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__supportCodeWordSoftCombining_r16 {
	NR_MIMO_ParametersPerBand__ext2__supportCodeWordSoftCombining_r16_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext2__supportCodeWordSoftCombining_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__supportTDM_SchemeA_r16 {
	NR_MIMO_ParametersPerBand__ext2__supportTDM_SchemeA_r16_kb3	= 0,
	NR_MIMO_ParametersPerBand__ext2__supportTDM_SchemeA_r16_kb5	= 1,
	NR_MIMO_ParametersPerBand__ext2__supportTDM_SchemeA_r16_kb10	= 2,
	NR_MIMO_ParametersPerBand__ext2__supportTDM_SchemeA_r16_kb20	= 3,
	NR_MIMO_ParametersPerBand__ext2__supportTDM_SchemeA_r16_noRestriction	= 4
} e_NR_MIMO_ParametersPerBand__ext2__supportTDM_SchemeA_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__supportInter_slotTDM_r16__supportRepNumPDSCH_TDRA_r16 {
	NR_MIMO_ParametersPerBand__ext2__supportInter_slotTDM_r16__supportRepNumPDSCH_TDRA_r16_n2	= 0,
	NR_MIMO_ParametersPerBand__ext2__supportInter_slotTDM_r16__supportRepNumPDSCH_TDRA_r16_n3	= 1,
	NR_MIMO_ParametersPerBand__ext2__supportInter_slotTDM_r16__supportRepNumPDSCH_TDRA_r16_n4	= 2,
	NR_MIMO_ParametersPerBand__ext2__supportInter_slotTDM_r16__supportRepNumPDSCH_TDRA_r16_n5	= 3,
	NR_MIMO_ParametersPerBand__ext2__supportInter_slotTDM_r16__supportRepNumPDSCH_TDRA_r16_n6	= 4,
	NR_MIMO_ParametersPerBand__ext2__supportInter_slotTDM_r16__supportRepNumPDSCH_TDRA_r16_n7	= 5,
	NR_MIMO_ParametersPerBand__ext2__supportInter_slotTDM_r16__supportRepNumPDSCH_TDRA_r16_n8	= 6,
	NR_MIMO_ParametersPerBand__ext2__supportInter_slotTDM_r16__supportRepNumPDSCH_TDRA_r16_n16	= 7
} e_NR_MIMO_ParametersPerBand__ext2__supportInter_slotTDM_r16__supportRepNumPDSCH_TDRA_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__supportInter_slotTDM_r16__maxTBS_Size_r16 {
	NR_MIMO_ParametersPerBand__ext2__supportInter_slotTDM_r16__maxTBS_Size_r16_kb3	= 0,
	NR_MIMO_ParametersPerBand__ext2__supportInter_slotTDM_r16__maxTBS_Size_r16_kb5	= 1,
	NR_MIMO_ParametersPerBand__ext2__supportInter_slotTDM_r16__maxTBS_Size_r16_kb10	= 2,
	NR_MIMO_ParametersPerBand__ext2__supportInter_slotTDM_r16__maxTBS_Size_r16_kb20	= 3,
	NR_MIMO_ParametersPerBand__ext2__supportInter_slotTDM_r16__maxTBS_Size_r16_noRestriction	= 4
} e_NR_MIMO_ParametersPerBand__ext2__supportInter_slotTDM_r16__maxTBS_Size_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__lowPAPR_DMRS_PDSCH_r16 {
	NR_MIMO_ParametersPerBand__ext2__lowPAPR_DMRS_PDSCH_r16_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext2__lowPAPR_DMRS_PDSCH_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__lowPAPR_DMRS_PUSCHwithoutPrecoding_r16 {
	NR_MIMO_ParametersPerBand__ext2__lowPAPR_DMRS_PUSCHwithoutPrecoding_r16_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext2__lowPAPR_DMRS_PUSCHwithoutPrecoding_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__lowPAPR_DMRS_PUCCH_r16 {
	NR_MIMO_ParametersPerBand__ext2__lowPAPR_DMRS_PUCCH_r16_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext2__lowPAPR_DMRS_PUCCH_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__lowPAPR_DMRS_PUSCHwithPrecoding_r16 {
	NR_MIMO_ParametersPerBand__ext2__lowPAPR_DMRS_PUSCHwithPrecoding_r16_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext2__lowPAPR_DMRS_PUSCHwithPrecoding_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__beamCorrespondenceSSB_based_r16 {
	NR_MIMO_ParametersPerBand__ext2__beamCorrespondenceSSB_based_r16_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext2__beamCorrespondenceSSB_based_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__beamCorrespondenceCSI_RS_based_r16 {
	NR_MIMO_ParametersPerBand__ext2__beamCorrespondenceCSI_RS_based_r16_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext2__beamCorrespondenceCSI_RS_based_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__beamSwitchTiming_r16__scs_60kHz_r16 {
	NR_MIMO_ParametersPerBand__ext2__beamSwitchTiming_r16__scs_60kHz_r16_sym224	= 0,
	NR_MIMO_ParametersPerBand__ext2__beamSwitchTiming_r16__scs_60kHz_r16_sym336	= 1
} e_NR_MIMO_ParametersPerBand__ext2__beamSwitchTiming_r16__scs_60kHz_r16;
typedef enum NR_MIMO_ParametersPerBand__ext2__beamSwitchTiming_r16__scs_120kHz_r16 {
	NR_MIMO_ParametersPerBand__ext2__beamSwitchTiming_r16__scs_120kHz_r16_sym224	= 0,
	NR_MIMO_ParametersPerBand__ext2__beamSwitchTiming_r16__scs_120kHz_r16_sym336	= 1
} e_NR_MIMO_ParametersPerBand__ext2__beamSwitchTiming_r16__scs_120kHz_r16;
typedef enum NR_MIMO_ParametersPerBand__ext3__semi_PersistentL1_SINR_Report_PUCCH_r16__supportReportFormat1_2OFDM_syms_r16 {
	NR_MIMO_ParametersPerBand__ext3__semi_PersistentL1_SINR_Report_PUCCH_r16__supportReportFormat1_2OFDM_syms_r16_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext3__semi_PersistentL1_SINR_Report_PUCCH_r16__supportReportFormat1_2OFDM_syms_r16;
typedef enum NR_MIMO_ParametersPerBand__ext3__semi_PersistentL1_SINR_Report_PUCCH_r16__supportReportFormat4_14OFDM_syms_r16 {
	NR_MIMO_ParametersPerBand__ext3__semi_PersistentL1_SINR_Report_PUCCH_r16__supportReportFormat4_14OFDM_syms_r16_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext3__semi_PersistentL1_SINR_Report_PUCCH_r16__supportReportFormat4_14OFDM_syms_r16;
typedef enum NR_MIMO_ParametersPerBand__ext3__semi_PersistentL1_SINR_Report_PUSCH_r16 {
	NR_MIMO_ParametersPerBand__ext3__semi_PersistentL1_SINR_Report_PUSCH_r16_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext3__semi_PersistentL1_SINR_Report_PUSCH_r16;
typedef enum NR_MIMO_ParametersPerBand__ext4__spatialRelations_v1640__maxNumberConfiguredSpatialRelations_v1640 {
	NR_MIMO_ParametersPerBand__ext4__spatialRelations_v1640__maxNumberConfiguredSpatialRelations_v1640_n96	= 0,
	NR_MIMO_ParametersPerBand__ext4__spatialRelations_v1640__maxNumberConfiguredSpatialRelations_v1640_n128	= 1,
	NR_MIMO_ParametersPerBand__ext4__spatialRelations_v1640__maxNumberConfiguredSpatialRelations_v1640_n160	= 2,
	NR_MIMO_ParametersPerBand__ext4__spatialRelations_v1640__maxNumberConfiguredSpatialRelations_v1640_n192	= 3,
	NR_MIMO_ParametersPerBand__ext4__spatialRelations_v1640__maxNumberConfiguredSpatialRelations_v1640_n224	= 4,
	NR_MIMO_ParametersPerBand__ext4__spatialRelations_v1640__maxNumberConfiguredSpatialRelations_v1640_n256	= 5,
	NR_MIMO_ParametersPerBand__ext4__spatialRelations_v1640__maxNumberConfiguredSpatialRelations_v1640_n288	= 6,
	NR_MIMO_ParametersPerBand__ext4__spatialRelations_v1640__maxNumberConfiguredSpatialRelations_v1640_n320	= 7
} e_NR_MIMO_ParametersPerBand__ext4__spatialRelations_v1640__maxNumberConfiguredSpatialRelations_v1640;
typedef enum NR_MIMO_ParametersPerBand__ext4__support64CandidateBeamRS_BFR_r16 {
	NR_MIMO_ParametersPerBand__ext4__support64CandidateBeamRS_BFR_r16_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext4__support64CandidateBeamRS_BFR_r16;
typedef enum NR_MIMO_ParametersPerBand__ext5__maxMIMO_LayersForMulti_DCI_mTRP_r16 {
	NR_MIMO_ParametersPerBand__ext5__maxMIMO_LayersForMulti_DCI_mTRP_r16_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext5__maxMIMO_LayersForMulti_DCI_mTRP_r16;
typedef enum NR_MIMO_ParametersPerBand__ext7__srs_increasedRepetition_r17 {
	NR_MIMO_ParametersPerBand__ext7__srs_increasedRepetition_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__srs_increasedRepetition_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__srs_partialFrequencySounding_r17 {
	NR_MIMO_ParametersPerBand__ext7__srs_partialFrequencySounding_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__srs_partialFrequencySounding_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__srs_startRB_locationHoppingPartial_r17 {
	NR_MIMO_ParametersPerBand__ext7__srs_startRB_locationHoppingPartial_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__srs_startRB_locationHoppingPartial_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__srs_combEight_r17 {
	NR_MIMO_ParametersPerBand__ext7__srs_combEight_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__srs_combEight_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__mTRP_PUSCH_twoCSI_RS_r17 {
	NR_MIMO_ParametersPerBand__ext7__mTRP_PUSCH_twoCSI_RS_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__mTRP_PUSCH_twoCSI_RS_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__mTRP_PUCCH_InterSlot_r17 {
	NR_MIMO_ParametersPerBand__ext7__mTRP_PUCCH_InterSlot_r17_pf0_2	= 0,
	NR_MIMO_ParametersPerBand__ext7__mTRP_PUCCH_InterSlot_r17_pf1_3_4	= 1,
	NR_MIMO_ParametersPerBand__ext7__mTRP_PUCCH_InterSlot_r17_pf0_4	= 2
} e_NR_MIMO_ParametersPerBand__ext7__mTRP_PUCCH_InterSlot_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__mTRP_PUCCH_CyclicMapping_r17 {
	NR_MIMO_ParametersPerBand__ext7__mTRP_PUCCH_CyclicMapping_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__mTRP_PUCCH_CyclicMapping_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__mTRP_PUCCH_SecondTPC_r17 {
	NR_MIMO_ParametersPerBand__ext7__mTRP_PUCCH_SecondTPC_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__mTRP_PUCCH_SecondTPC_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__mTRP_BFR_twoBFD_RS_Set_r17__maxBFD_RS_resourcesPerSetPerBWP_r17 {
	NR_MIMO_ParametersPerBand__ext7__mTRP_BFR_twoBFD_RS_Set_r17__maxBFD_RS_resourcesPerSetPerBWP_r17_n1	= 0,
	NR_MIMO_ParametersPerBand__ext7__mTRP_BFR_twoBFD_RS_Set_r17__maxBFD_RS_resourcesPerSetPerBWP_r17_n2	= 1
} e_NR_MIMO_ParametersPerBand__ext7__mTRP_BFR_twoBFD_RS_Set_r17__maxBFD_RS_resourcesPerSetPerBWP_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__mTRP_BFR_twoBFD_RS_Set_r17__maxBFD_RS_resourcesAcrossSetsPerBWP_r17 {
	NR_MIMO_ParametersPerBand__ext7__mTRP_BFR_twoBFD_RS_Set_r17__maxBFD_RS_resourcesAcrossSetsPerBWP_r17_n2	= 0,
	NR_MIMO_ParametersPerBand__ext7__mTRP_BFR_twoBFD_RS_Set_r17__maxBFD_RS_resourcesAcrossSetsPerBWP_r17_n3	= 1,
	NR_MIMO_ParametersPerBand__ext7__mTRP_BFR_twoBFD_RS_Set_r17__maxBFD_RS_resourcesAcrossSetsPerBWP_r17_n4	= 2
} e_NR_MIMO_ParametersPerBand__ext7__mTRP_BFR_twoBFD_RS_Set_r17__maxBFD_RS_resourcesAcrossSetsPerBWP_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__mTRP_BFR_PUCCH_SR_perCG_r17 {
	NR_MIMO_ParametersPerBand__ext7__mTRP_BFR_PUCCH_SR_perCG_r17_n1	= 0,
	NR_MIMO_ParametersPerBand__ext7__mTRP_BFR_PUCCH_SR_perCG_r17_n2	= 1
} e_NR_MIMO_ParametersPerBand__ext7__mTRP_BFR_PUCCH_SR_perCG_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__mTRP_BFR_association_PUCCH_SR_r17 {
	NR_MIMO_ParametersPerBand__ext7__mTRP_BFR_association_PUCCH_SR_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__mTRP_BFR_association_PUCCH_SR_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__sfn_SimulTwoTCI_AcrossMultiCC_r17 {
	NR_MIMO_ParametersPerBand__ext7__sfn_SimulTwoTCI_AcrossMultiCC_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__sfn_SimulTwoTCI_AcrossMultiCC_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__sfn_DefaultDL_BeamSetup_r17 {
	NR_MIMO_ParametersPerBand__ext7__sfn_DefaultDL_BeamSetup_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__sfn_DefaultDL_BeamSetup_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__sfn_DefaultUL_BeamSetup_r17 {
	NR_MIMO_ParametersPerBand__ext7__sfn_DefaultUL_BeamSetup_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__sfn_DefaultUL_BeamSetup_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__srs_TriggeringOffset_r17 {
	NR_MIMO_ParametersPerBand__ext7__srs_TriggeringOffset_r17_n1	= 0,
	NR_MIMO_ParametersPerBand__ext7__srs_TriggeringOffset_r17_n2	= 1,
	NR_MIMO_ParametersPerBand__ext7__srs_TriggeringOffset_r17_n4	= 2
} e_NR_MIMO_ParametersPerBand__ext7__srs_TriggeringOffset_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__srs_TriggeringDCI_r17 {
	NR_MIMO_ParametersPerBand__ext7__srs_TriggeringDCI_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__srs_TriggeringDCI_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_r17__maxConfiguredJointTCI_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_r17__maxConfiguredJointTCI_r17_n8	= 0,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_r17__maxConfiguredJointTCI_r17_n12	= 1,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_r17__maxConfiguredJointTCI_r17_n16	= 2,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_r17__maxConfiguredJointTCI_r17_n24	= 3,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_r17__maxConfiguredJointTCI_r17_n32	= 4,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_r17__maxConfiguredJointTCI_r17_n48	= 5,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_r17__maxConfiguredJointTCI_r17_n64	= 6,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_r17__maxConfiguredJointTCI_r17_n128	= 7
} e_NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_r17__maxConfiguredJointTCI_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_r17__maxActivatedTCIAcrossCC_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_r17__maxActivatedTCIAcrossCC_r17_n1	= 0,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_r17__maxActivatedTCIAcrossCC_r17_n2	= 1,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_r17__maxActivatedTCIAcrossCC_r17_n4	= 2,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_r17__maxActivatedTCIAcrossCC_r17_n8	= 3,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_r17__maxActivatedTCIAcrossCC_r17_n16	= 4
} e_NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_r17__maxActivatedTCIAcrossCC_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_multiMAC_CE_r17__minBeamApplicationTime_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n1	= 0,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n2	= 1,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n4	= 2,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n7	= 3,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n14	= 4,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n28	= 5,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n42	= 6,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n56	= 7,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n70	= 8,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n84	= 9,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n98	= 10,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n112	= 11,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n224	= 12,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n336	= 13
} e_NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_multiMAC_CE_r17__minBeamApplicationTime_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_multiMAC_CE_r17__maxNumMAC_CE_PerCC {
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_multiMAC_CE_r17__maxNumMAC_CE_PerCC_n2	= 0,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_multiMAC_CE_r17__maxNumMAC_CE_PerCC_n3	= 1,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_multiMAC_CE_r17__maxNumMAC_CE_PerCC_n4	= 2,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_multiMAC_CE_r17__maxNumMAC_CE_PerCC_n5	= 3,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_multiMAC_CE_r17__maxNumMAC_CE_PerCC_n6	= 4,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_multiMAC_CE_r17__maxNumMAC_CE_PerCC_n7	= 5,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_multiMAC_CE_r17__maxNumMAC_CE_PerCC_n8	= 6
} e_NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_multiMAC_CE_r17__maxNumMAC_CE_PerCC;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_perBWP_CA_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_perBWP_CA_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_perBWP_CA_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_ListSharingCA_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_ListSharingCA_r17_n1	= 0,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_ListSharingCA_r17_n2	= 1,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_ListSharingCA_r17_n4	= 2,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_ListSharingCA_r17_n8	= 3
} e_NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_ListSharingCA_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_commonMultiCC_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_commonMultiCC_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_commonMultiCC_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_BeamAlignDLRS_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_BeamAlignDLRS_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_BeamAlignDLRS_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_PC_association_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_PC_association_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_PC_association_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_Legacy_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_Legacy_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_Legacy_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_Legacy_SRS_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_Legacy_SRS_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_Legacy_SRS_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_Legacy_CORESET0_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_Legacy_CORESET0_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_Legacy_CORESET0_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_SCellBFR_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_SCellBFR_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_SCellBFR_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_InterCell_r17__additionalMAC_CE_PerCC_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_InterCell_r17__additionalMAC_CE_PerCC_r17_n0	= 0,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_InterCell_r17__additionalMAC_CE_PerCC_r17_n1	= 1,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_InterCell_r17__additionalMAC_CE_PerCC_r17_n2	= 2,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_InterCell_r17__additionalMAC_CE_PerCC_r17_n4	= 3
} e_NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_InterCell_r17__additionalMAC_CE_PerCC_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_InterCell_r17__additionalMAC_CE_AcrossCC_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_InterCell_r17__additionalMAC_CE_AcrossCC_r17_n0	= 0,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_InterCell_r17__additionalMAC_CE_AcrossCC_r17_n1	= 1,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_InterCell_r17__additionalMAC_CE_AcrossCC_r17_n2	= 2,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_InterCell_r17__additionalMAC_CE_AcrossCC_r17_n4	= 3
} e_NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_InterCell_r17__additionalMAC_CE_AcrossCC_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxConfiguredDL_TCI_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxConfiguredDL_TCI_r17_n4	= 0,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxConfiguredDL_TCI_r17_n8	= 1,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxConfiguredDL_TCI_r17_n12	= 2,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxConfiguredDL_TCI_r17_n16	= 3,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxConfiguredDL_TCI_r17_n24	= 4,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxConfiguredDL_TCI_r17_n32	= 5,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxConfiguredDL_TCI_r17_n48	= 6,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxConfiguredDL_TCI_r17_n64	= 7,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxConfiguredDL_TCI_r17_n128	= 8
} e_NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxConfiguredDL_TCI_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxConfiguredUL_TCI_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxConfiguredUL_TCI_r17_n4	= 0,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxConfiguredUL_TCI_r17_n8	= 1,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxConfiguredUL_TCI_r17_n12	= 2,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxConfiguredUL_TCI_r17_n16	= 3,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxConfiguredUL_TCI_r17_n24	= 4,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxConfiguredUL_TCI_r17_n32	= 5,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxConfiguredUL_TCI_r17_n48	= 6,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxConfiguredUL_TCI_r17_n64	= 7
} e_NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxConfiguredUL_TCI_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxActivatedDL_TCIAcrossCC_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxActivatedDL_TCIAcrossCC_r17_n1	= 0,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxActivatedDL_TCIAcrossCC_r17_n2	= 1,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxActivatedDL_TCIAcrossCC_r17_n4	= 2,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxActivatedDL_TCIAcrossCC_r17_n8	= 3,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxActivatedDL_TCIAcrossCC_r17_n16	= 4
} e_NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxActivatedDL_TCIAcrossCC_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxActivatedUL_TCIAcrossCC_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxActivatedUL_TCIAcrossCC_r17_n1	= 0,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxActivatedUL_TCIAcrossCC_r17_n2	= 1,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxActivatedUL_TCIAcrossCC_r17_n4	= 2,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxActivatedUL_TCIAcrossCC_r17_n8	= 3,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxActivatedUL_TCIAcrossCC_r17_n16	= 4
} e_NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17__maxActivatedUL_TCIAcrossCC_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_multiMAC_CE_r17__minBeamApplicationTime_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n1	= 0,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n2	= 1,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n4	= 2,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n7	= 3,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n14	= 4,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n28	= 5,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n42	= 6,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n56	= 7,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n70	= 8,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n84	= 9,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n98	= 10,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n112	= 11,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n224	= 12,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_multiMAC_CE_r17__minBeamApplicationTime_r17_n336	= 13
} e_NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_multiMAC_CE_r17__minBeamApplicationTime_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_perBWP_CA_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_perBWP_CA_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_perBWP_CA_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_ListSharingCA_r17__maxNumListDL_TCI_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_ListSharingCA_r17__maxNumListDL_TCI_r17_n1	= 0,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_ListSharingCA_r17__maxNumListDL_TCI_r17_n2	= 1,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_ListSharingCA_r17__maxNumListDL_TCI_r17_n4	= 2,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_ListSharingCA_r17__maxNumListDL_TCI_r17_n8	= 3
} e_NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_ListSharingCA_r17__maxNumListDL_TCI_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_ListSharingCA_r17__maxNumListUL_TCI_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_ListSharingCA_r17__maxNumListUL_TCI_r17_n1	= 0,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_ListSharingCA_r17__maxNumListUL_TCI_r17_n2	= 1,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_ListSharingCA_r17__maxNumListUL_TCI_r17_n4	= 2,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_ListSharingCA_r17__maxNumListUL_TCI_r17_n8	= 3
} e_NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_ListSharingCA_r17__maxNumListUL_TCI_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_commonMultiCC_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_commonMultiCC_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_commonMultiCC_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_InterCell_r17__k_DL_PerCC_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_InterCell_r17__k_DL_PerCC_r17_n0	= 0,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_InterCell_r17__k_DL_PerCC_r17_n1	= 1,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_InterCell_r17__k_DL_PerCC_r17_n2	= 2,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_InterCell_r17__k_DL_PerCC_r17_n4	= 3
} e_NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_InterCell_r17__k_DL_PerCC_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_InterCell_r17__k_UL_PerCC_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_InterCell_r17__k_UL_PerCC_r17_n0	= 0,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_InterCell_r17__k_UL_PerCC_r17_n1	= 1,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_InterCell_r17__k_UL_PerCC_r17_n2	= 2,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_InterCell_r17__k_UL_PerCC_r17_n4	= 3
} e_NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_InterCell_r17__k_UL_PerCC_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_InterCell_r17__k_DL_AcrossCC_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_InterCell_r17__k_DL_AcrossCC_r17_n0	= 0,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_InterCell_r17__k_DL_AcrossCC_r17_n1	= 1,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_InterCell_r17__k_DL_AcrossCC_r17_n2	= 2,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_InterCell_r17__k_DL_AcrossCC_r17_n4	= 3
} e_NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_InterCell_r17__k_DL_AcrossCC_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_InterCell_r17__k_UL_AcrossCC_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_InterCell_r17__k_UL_AcrossCC_r17_n0	= 0,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_InterCell_r17__k_UL_AcrossCC_r17_n1	= 1,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_InterCell_r17__k_UL_AcrossCC_r17_n2	= 2,
	NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_InterCell_r17__k_UL_AcrossCC_r17_n4	= 3
} e_NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_InterCell_r17__k_UL_AcrossCC_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_mTRP_InterCell_BM_r17__maxNumSSB_ResourceL1_RSRP_AcrossCC_r17 {
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_mTRP_InterCell_BM_r17__maxNumSSB_ResourceL1_RSRP_AcrossCC_r17_n1	= 0,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_mTRP_InterCell_BM_r17__maxNumSSB_ResourceL1_RSRP_AcrossCC_r17_n2	= 1,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_mTRP_InterCell_BM_r17__maxNumSSB_ResourceL1_RSRP_AcrossCC_r17_n4	= 2,
	NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_mTRP_InterCell_BM_r17__maxNumSSB_ResourceL1_RSRP_AcrossCC_r17_n8	= 3
} e_NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_mTRP_InterCell_BM_r17__maxNumSSB_ResourceL1_RSRP_AcrossCC_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__mpe_Mitigation_r17__maxNumConfRS_r17 {
	NR_MIMO_ParametersPerBand__ext7__mpe_Mitigation_r17__maxNumConfRS_r17_n1	= 0,
	NR_MIMO_ParametersPerBand__ext7__mpe_Mitigation_r17__maxNumConfRS_r17_n2	= 1,
	NR_MIMO_ParametersPerBand__ext7__mpe_Mitigation_r17__maxNumConfRS_r17_n4	= 2,
	NR_MIMO_ParametersPerBand__ext7__mpe_Mitigation_r17__maxNumConfRS_r17_n8	= 3,
	NR_MIMO_ParametersPerBand__ext7__mpe_Mitigation_r17__maxNumConfRS_r17_n12	= 4,
	NR_MIMO_ParametersPerBand__ext7__mpe_Mitigation_r17__maxNumConfRS_r17_n16	= 5,
	NR_MIMO_ParametersPerBand__ext7__mpe_Mitigation_r17__maxNumConfRS_r17_n28	= 6,
	NR_MIMO_ParametersPerBand__ext7__mpe_Mitigation_r17__maxNumConfRS_r17_n32	= 7,
	NR_MIMO_ParametersPerBand__ext7__mpe_Mitigation_r17__maxNumConfRS_r17_n48	= 8,
	NR_MIMO_ParametersPerBand__ext7__mpe_Mitigation_r17__maxNumConfRS_r17_n64	= 9
} e_NR_MIMO_ParametersPerBand__ext7__mpe_Mitigation_r17__maxNumConfRS_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__srs_PortReport_r17__capVal1_r17 {
	NR_MIMO_ParametersPerBand__ext7__srs_PortReport_r17__capVal1_r17_n1	= 0,
	NR_MIMO_ParametersPerBand__ext7__srs_PortReport_r17__capVal1_r17_n2	= 1,
	NR_MIMO_ParametersPerBand__ext7__srs_PortReport_r17__capVal1_r17_n4	= 2
} e_NR_MIMO_ParametersPerBand__ext7__srs_PortReport_r17__capVal1_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__srs_PortReport_r17__capVal2_r17 {
	NR_MIMO_ParametersPerBand__ext7__srs_PortReport_r17__capVal2_r17_n1	= 0,
	NR_MIMO_ParametersPerBand__ext7__srs_PortReport_r17__capVal2_r17_n2	= 1,
	NR_MIMO_ParametersPerBand__ext7__srs_PortReport_r17__capVal2_r17_n4	= 2
} e_NR_MIMO_ParametersPerBand__ext7__srs_PortReport_r17__capVal2_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__srs_PortReport_r17__capVal3_r17 {
	NR_MIMO_ParametersPerBand__ext7__srs_PortReport_r17__capVal3_r17_n1	= 0,
	NR_MIMO_ParametersPerBand__ext7__srs_PortReport_r17__capVal3_r17_n2	= 1,
	NR_MIMO_ParametersPerBand__ext7__srs_PortReport_r17__capVal3_r17_n4	= 2
} e_NR_MIMO_ParametersPerBand__ext7__srs_PortReport_r17__capVal3_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__srs_PortReport_r17__capVal4_r17 {
	NR_MIMO_ParametersPerBand__ext7__srs_PortReport_r17__capVal4_r17_n1	= 0,
	NR_MIMO_ParametersPerBand__ext7__srs_PortReport_r17__capVal4_r17_n2	= 1,
	NR_MIMO_ParametersPerBand__ext7__srs_PortReport_r17__capVal4_r17_n4	= 2
} e_NR_MIMO_ParametersPerBand__ext7__srs_PortReport_r17__capVal4_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__mTRP_PDCCH_individual_r17 {
	NR_MIMO_ParametersPerBand__ext7__mTRP_PDCCH_individual_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__mTRP_PDCCH_individual_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__mTRP_PDCCH_anySpan_3Symbols_r17 {
	NR_MIMO_ParametersPerBand__ext7__mTRP_PDCCH_anySpan_3Symbols_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__mTRP_PDCCH_anySpan_3Symbols_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__mTRP_PDCCH_TwoQCL_TypeD_r17 {
	NR_MIMO_ParametersPerBand__ext7__mTRP_PDCCH_TwoQCL_TypeD_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__mTRP_PDCCH_TwoQCL_TypeD_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__mTRP_PUSCH_cyclicMapping_r17 {
	NR_MIMO_ParametersPerBand__ext7__mTRP_PUSCH_cyclicMapping_r17_typeA	= 0,
	NR_MIMO_ParametersPerBand__ext7__mTRP_PUSCH_cyclicMapping_r17_typeB	= 1,
	NR_MIMO_ParametersPerBand__ext7__mTRP_PUSCH_cyclicMapping_r17_both	= 2
} e_NR_MIMO_ParametersPerBand__ext7__mTRP_PUSCH_cyclicMapping_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__mTRP_PUSCH_secondTPC_r17 {
	NR_MIMO_ParametersPerBand__ext7__mTRP_PUSCH_secondTPC_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__mTRP_PUSCH_secondTPC_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__mTRP_PUSCH_twoPHR_Reporting_r17 {
	NR_MIMO_ParametersPerBand__ext7__mTRP_PUSCH_twoPHR_Reporting_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__mTRP_PUSCH_twoPHR_Reporting_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__mTRP_PUSCH_A_CSI_r17 {
	NR_MIMO_ParametersPerBand__ext7__mTRP_PUSCH_A_CSI_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__mTRP_PUSCH_A_CSI_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__mTRP_PUSCH_SP_CSI_r17 {
	NR_MIMO_ParametersPerBand__ext7__mTRP_PUSCH_SP_CSI_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__mTRP_PUSCH_SP_CSI_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__mTRP_PUSCH_CG_r17 {
	NR_MIMO_ParametersPerBand__ext7__mTRP_PUSCH_CG_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__mTRP_PUSCH_CG_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__mTRP_PUCCH_MAC_CE_r17 {
	NR_MIMO_ParametersPerBand__ext7__mTRP_PUCCH_MAC_CE_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__mTRP_PUCCH_MAC_CE_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__mTRP_GroupBasedL1_RSRP_r17__maxNumRS_WithinSlot_r17 {
	NR_MIMO_ParametersPerBand__ext7__mTRP_GroupBasedL1_RSRP_r17__maxNumRS_WithinSlot_r17_n2	= 0,
	NR_MIMO_ParametersPerBand__ext7__mTRP_GroupBasedL1_RSRP_r17__maxNumRS_WithinSlot_r17_n3	= 1,
	NR_MIMO_ParametersPerBand__ext7__mTRP_GroupBasedL1_RSRP_r17__maxNumRS_WithinSlot_r17_n4	= 2,
	NR_MIMO_ParametersPerBand__ext7__mTRP_GroupBasedL1_RSRP_r17__maxNumRS_WithinSlot_r17_n8	= 3,
	NR_MIMO_ParametersPerBand__ext7__mTRP_GroupBasedL1_RSRP_r17__maxNumRS_WithinSlot_r17_n16	= 4,
	NR_MIMO_ParametersPerBand__ext7__mTRP_GroupBasedL1_RSRP_r17__maxNumRS_WithinSlot_r17_n32	= 5,
	NR_MIMO_ParametersPerBand__ext7__mTRP_GroupBasedL1_RSRP_r17__maxNumRS_WithinSlot_r17_n64	= 6
} e_NR_MIMO_ParametersPerBand__ext7__mTRP_GroupBasedL1_RSRP_r17__maxNumRS_WithinSlot_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__mTRP_GroupBasedL1_RSRP_r17__maxNumRS_AcrossSlot_r17 {
	NR_MIMO_ParametersPerBand__ext7__mTRP_GroupBasedL1_RSRP_r17__maxNumRS_AcrossSlot_r17_n8	= 0,
	NR_MIMO_ParametersPerBand__ext7__mTRP_GroupBasedL1_RSRP_r17__maxNumRS_AcrossSlot_r17_n16	= 1,
	NR_MIMO_ParametersPerBand__ext7__mTRP_GroupBasedL1_RSRP_r17__maxNumRS_AcrossSlot_r17_n32	= 2,
	NR_MIMO_ParametersPerBand__ext7__mTRP_GroupBasedL1_RSRP_r17__maxNumRS_AcrossSlot_r17_n64	= 3,
	NR_MIMO_ParametersPerBand__ext7__mTRP_GroupBasedL1_RSRP_r17__maxNumRS_AcrossSlot_r17_n128	= 4
} e_NR_MIMO_ParametersPerBand__ext7__mTRP_GroupBasedL1_RSRP_r17__maxNumRS_AcrossSlot_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__mTRP_BFD_RS_MAC_CE_r17 {
	NR_MIMO_ParametersPerBand__ext7__mTRP_BFD_RS_MAC_CE_r17_n4	= 0,
	NR_MIMO_ParametersPerBand__ext7__mTRP_BFD_RS_MAC_CE_r17_n8	= 1,
	NR_MIMO_ParametersPerBand__ext7__mTRP_BFD_RS_MAC_CE_r17_n12	= 2,
	NR_MIMO_ParametersPerBand__ext7__mTRP_BFD_RS_MAC_CE_r17_n16	= 3,
	NR_MIMO_ParametersPerBand__ext7__mTRP_BFD_RS_MAC_CE_r17_n32	= 4,
	NR_MIMO_ParametersPerBand__ext7__mTRP_BFD_RS_MAC_CE_r17_n48	= 5,
	NR_MIMO_ParametersPerBand__ext7__mTRP_BFD_RS_MAC_CE_r17_n64	= 6
} e_NR_MIMO_ParametersPerBand__ext7__mTRP_BFD_RS_MAC_CE_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__mTRP_CSI_EnhancementPerBand_r17__cSI_Report_mode_r17 {
	NR_MIMO_ParametersPerBand__ext7__mTRP_CSI_EnhancementPerBand_r17__cSI_Report_mode_r17_mode1	= 0,
	NR_MIMO_ParametersPerBand__ext7__mTRP_CSI_EnhancementPerBand_r17__cSI_Report_mode_r17_mode2	= 1,
	NR_MIMO_ParametersPerBand__ext7__mTRP_CSI_EnhancementPerBand_r17__cSI_Report_mode_r17_both	= 2
} e_NR_MIMO_ParametersPerBand__ext7__mTRP_CSI_EnhancementPerBand_r17__cSI_Report_mode_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__mTRP_CSI_EnhancementPerBand_r17__codebookModeNCJT_r17 {
	NR_MIMO_ParametersPerBand__ext7__mTRP_CSI_EnhancementPerBand_r17__codebookModeNCJT_r17_mode1	= 0,
	NR_MIMO_ParametersPerBand__ext7__mTRP_CSI_EnhancementPerBand_r17__codebookModeNCJT_r17_mode1And2	= 1
} e_NR_MIMO_ParametersPerBand__ext7__mTRP_CSI_EnhancementPerBand_r17__codebookModeNCJT_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__mTRP_CSI_additionalCSI_r17 {
	NR_MIMO_ParametersPerBand__ext7__mTRP_CSI_additionalCSI_r17_x1	= 0,
	NR_MIMO_ParametersPerBand__ext7__mTRP_CSI_additionalCSI_r17_x2	= 1
} e_NR_MIMO_ParametersPerBand__ext7__mTRP_CSI_additionalCSI_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__mTRP_CSI_N_Max2_r17 {
	NR_MIMO_ParametersPerBand__ext7__mTRP_CSI_N_Max2_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__mTRP_CSI_N_Max2_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__mTRP_CSI_CMR_r17 {
	NR_MIMO_ParametersPerBand__ext7__mTRP_CSI_CMR_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__mTRP_CSI_CMR_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__srs_partialFreqSounding_r17 {
	NR_MIMO_ParametersPerBand__ext7__srs_partialFreqSounding_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext7__srs_partialFreqSounding_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__beamSwitchTiming_v1710__scs_480kHz {
	NR_MIMO_ParametersPerBand__ext7__beamSwitchTiming_v1710__scs_480kHz_sym56	= 0,
	NR_MIMO_ParametersPerBand__ext7__beamSwitchTiming_v1710__scs_480kHz_sym112	= 1,
	NR_MIMO_ParametersPerBand__ext7__beamSwitchTiming_v1710__scs_480kHz_sym192	= 2,
	NR_MIMO_ParametersPerBand__ext7__beamSwitchTiming_v1710__scs_480kHz_sym896	= 3,
	NR_MIMO_ParametersPerBand__ext7__beamSwitchTiming_v1710__scs_480kHz_sym1344	= 4
} e_NR_MIMO_ParametersPerBand__ext7__beamSwitchTiming_v1710__scs_480kHz;
typedef enum NR_MIMO_ParametersPerBand__ext7__beamSwitchTiming_v1710__scs_960kHz {
	NR_MIMO_ParametersPerBand__ext7__beamSwitchTiming_v1710__scs_960kHz_sym112	= 0,
	NR_MIMO_ParametersPerBand__ext7__beamSwitchTiming_v1710__scs_960kHz_sym224	= 1,
	NR_MIMO_ParametersPerBand__ext7__beamSwitchTiming_v1710__scs_960kHz_sym384	= 2,
	NR_MIMO_ParametersPerBand__ext7__beamSwitchTiming_v1710__scs_960kHz_sym1792	= 3,
	NR_MIMO_ParametersPerBand__ext7__beamSwitchTiming_v1710__scs_960kHz_sym2688	= 4
} e_NR_MIMO_ParametersPerBand__ext7__beamSwitchTiming_v1710__scs_960kHz;
typedef enum NR_MIMO_ParametersPerBand__ext7__beamSwitchTiming_r17__scs_480kHz_r17 {
	NR_MIMO_ParametersPerBand__ext7__beamSwitchTiming_r17__scs_480kHz_r17_sym896	= 0,
	NR_MIMO_ParametersPerBand__ext7__beamSwitchTiming_r17__scs_480kHz_r17_sym1344	= 1
} e_NR_MIMO_ParametersPerBand__ext7__beamSwitchTiming_r17__scs_480kHz_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__beamSwitchTiming_r17__scs_960kHz_r17 {
	NR_MIMO_ParametersPerBand__ext7__beamSwitchTiming_r17__scs_960kHz_r17_sym1792	= 0,
	NR_MIMO_ParametersPerBand__ext7__beamSwitchTiming_r17__scs_960kHz_r17_sym2688	= 1
} e_NR_MIMO_ParametersPerBand__ext7__beamSwitchTiming_r17__scs_960kHz_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__beamReportTiming_v1710__scs_480kHz_r17 {
	NR_MIMO_ParametersPerBand__ext7__beamReportTiming_v1710__scs_480kHz_r17_sym56	= 0,
	NR_MIMO_ParametersPerBand__ext7__beamReportTiming_v1710__scs_480kHz_r17_sym112	= 1,
	NR_MIMO_ParametersPerBand__ext7__beamReportTiming_v1710__scs_480kHz_r17_sym224	= 2
} e_NR_MIMO_ParametersPerBand__ext7__beamReportTiming_v1710__scs_480kHz_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__beamReportTiming_v1710__scs_960kHz_r17 {
	NR_MIMO_ParametersPerBand__ext7__beamReportTiming_v1710__scs_960kHz_r17_sym112	= 0,
	NR_MIMO_ParametersPerBand__ext7__beamReportTiming_v1710__scs_960kHz_r17_sym224	= 1,
	NR_MIMO_ParametersPerBand__ext7__beamReportTiming_v1710__scs_960kHz_r17_sym448	= 2
} e_NR_MIMO_ParametersPerBand__ext7__beamReportTiming_v1710__scs_960kHz_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__maxNumberRxTxBeamSwitchDL_v1710__scs_480kHz_r17 {
	NR_MIMO_ParametersPerBand__ext7__maxNumberRxTxBeamSwitchDL_v1710__scs_480kHz_r17_n2	= 0,
	NR_MIMO_ParametersPerBand__ext7__maxNumberRxTxBeamSwitchDL_v1710__scs_480kHz_r17_n4	= 1,
	NR_MIMO_ParametersPerBand__ext7__maxNumberRxTxBeamSwitchDL_v1710__scs_480kHz_r17_n7	= 2
} e_NR_MIMO_ParametersPerBand__ext7__maxNumberRxTxBeamSwitchDL_v1710__scs_480kHz_r17;
typedef enum NR_MIMO_ParametersPerBand__ext7__maxNumberRxTxBeamSwitchDL_v1710__scs_960kHz_r17 {
	NR_MIMO_ParametersPerBand__ext7__maxNumberRxTxBeamSwitchDL_v1710__scs_960kHz_r17_n1	= 0,
	NR_MIMO_ParametersPerBand__ext7__maxNumberRxTxBeamSwitchDL_v1710__scs_960kHz_r17_n2	= 1,
	NR_MIMO_ParametersPerBand__ext7__maxNumberRxTxBeamSwitchDL_v1710__scs_960kHz_r17_n4	= 2,
	NR_MIMO_ParametersPerBand__ext7__maxNumberRxTxBeamSwitchDL_v1710__scs_960kHz_r17_n7	= 3
} e_NR_MIMO_ParametersPerBand__ext7__maxNumberRxTxBeamSwitchDL_v1710__scs_960kHz_r17;
typedef enum NR_MIMO_ParametersPerBand__ext8__srs_PortReportSP_AP_r17 {
	NR_MIMO_ParametersPerBand__ext8__srs_PortReportSP_AP_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext8__srs_PortReportSP_AP_r17;
typedef enum NR_MIMO_ParametersPerBand__ext8__sfn_ImplicitRS_twoTCI_r17 {
	NR_MIMO_ParametersPerBand__ext8__sfn_ImplicitRS_twoTCI_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext8__sfn_ImplicitRS_twoTCI_r17;
typedef enum NR_MIMO_ParametersPerBand__ext8__sfn_QCL_TypeD_Collision_twoTCI_r17 {
	NR_MIMO_ParametersPerBand__ext8__sfn_QCL_TypeD_Collision_twoTCI_r17_supported	= 0
} e_NR_MIMO_ParametersPerBand__ext8__sfn_QCL_TypeD_Collision_twoTCI_r17;
typedef enum NR_MIMO_ParametersPerBand__ext8__mTRP_CSI_numCPU_r17 {
	NR_MIMO_ParametersPerBand__ext8__mTRP_CSI_numCPU_r17_n2	= 0,
	NR_MIMO_ParametersPerBand__ext8__mTRP_CSI_numCPU_r17_n3	= 1,
	NR_MIMO_ParametersPerBand__ext8__mTRP_CSI_numCPU_r17_n4	= 2
} e_NR_MIMO_ParametersPerBand__ext8__mTRP_CSI_numCPU_r17;
typedef enum NR_MIMO_ParametersPerBand__ext9__supportRepNumPDSCH_TDRA_DCI_1_2_r17 {
	NR_MIMO_ParametersPerBand__ext9__supportRepNumPDSCH_TDRA_DCI_1_2_r17_n2	= 0,
	NR_MIMO_ParametersPerBand__ext9__supportRepNumPDSCH_TDRA_DCI_1_2_r17_n3	= 1,
	NR_MIMO_ParametersPerBand__ext9__supportRepNumPDSCH_TDRA_DCI_1_2_r17_n4	= 2,
	NR_MIMO_ParametersPerBand__ext9__supportRepNumPDSCH_TDRA_DCI_1_2_r17_n5	= 3,
	NR_MIMO_ParametersPerBand__ext9__supportRepNumPDSCH_TDRA_DCI_1_2_r17_n6	= 4,
	NR_MIMO_ParametersPerBand__ext9__supportRepNumPDSCH_TDRA_DCI_1_2_r17_n7	= 5,
	NR_MIMO_ParametersPerBand__ext9__supportRepNumPDSCH_TDRA_DCI_1_2_r17_n8	= 6,
	NR_MIMO_ParametersPerBand__ext9__supportRepNumPDSCH_TDRA_DCI_1_2_r17_n16	= 7
} e_NR_MIMO_ParametersPerBand__ext9__supportRepNumPDSCH_TDRA_DCI_1_2_r17;

/* Forward declarations */
struct NR_DummyG;
struct NR_SRS_Resources;
struct NR_DummyH;
struct NR_PTRS_DensityRecommendationDL;
struct NR_PTRS_DensityRecommendationUL;
struct NR_BeamManagementSSB_CSI_RS;
struct NR_CodebookParameters;
struct NR_CSI_RS_IM_ReceptionForFeedback;
struct NR_CSI_RS_ProcFrameworkForSRS;
struct NR_CSI_ReportFramework;
struct NR_CSI_RS_ForTracking;
struct NR_SpatialRelations;
struct NR_SupportedCSI_RS_Resource;
struct NR_CodebookParameters_v1610;
struct NR_CSI_ReportFrameworkExt_r16;
struct NR_CodebookParametersAddition_r16;
struct NR_CodebookComboParametersAddition_r16;
struct NR_CodebookParametersfetype2_r17;
struct NR_CodebookComboParameterMixedType_r17;
struct NR_CodebookComboParameterMultiTRP_r17;
struct NR_CSI_MultiTRP_SupportedCombinations_r17;

/* NR_MIMO-ParametersPerBand */
typedef struct NR_MIMO_ParametersPerBand {
	struct NR_MIMO_ParametersPerBand__tci_StatePDSCH {
		long	*maxNumberConfiguredTCI_StatesPerCC;	/* OPTIONAL */
		long	*maxNumberActiveTCI_PerBWP;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *tci_StatePDSCH;
	long	*additionalActiveTCI_StatePDCCH;	/* OPTIONAL */
	long	*pusch_TransCoherence;	/* OPTIONAL */
	long	*beamCorrespondenceWithoutUL_BeamSweeping;	/* OPTIONAL */
	long	*periodicBeamReport;	/* OPTIONAL */
	long	*aperiodicBeamReport;	/* OPTIONAL */
	long	*sp_BeamReportPUCCH;	/* OPTIONAL */
	long	*sp_BeamReportPUSCH;	/* OPTIONAL */
	struct NR_DummyG	*dummy1;	/* OPTIONAL */
	long	*maxNumberRxBeam;	/* OPTIONAL */
	struct NR_MIMO_ParametersPerBand__maxNumberRxTxBeamSwitchDL {
		long	*scs_15kHz;	/* OPTIONAL */
		long	*scs_30kHz;	/* OPTIONAL */
		long	*scs_60kHz;	/* OPTIONAL */
		long	*scs_120kHz;	/* OPTIONAL */
		long	*scs_240kHz;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *maxNumberRxTxBeamSwitchDL;
	long	*maxNumberNonGroupBeamReporting;	/* OPTIONAL */
	long	*groupBeamReporting;	/* OPTIONAL */
	struct NR_MIMO_ParametersPerBand__uplinkBeamManagement {
		long	 maxNumberSRS_ResourcePerSet_BM;
		long	 maxNumberSRS_ResourceSet;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *uplinkBeamManagement;
	long	*maxNumberCSI_RS_BFD;	/* OPTIONAL */
	long	*maxNumberSSB_BFD;	/* OPTIONAL */
	long	*maxNumberCSI_RS_SSB_CBD;	/* OPTIONAL */
	long	*dummy2;	/* OPTIONAL */
	long	*twoPortsPTRS_UL;	/* OPTIONAL */
	struct NR_SRS_Resources	*dummy5;	/* OPTIONAL */
	long	*dummy3;	/* OPTIONAL */
	struct NR_MIMO_ParametersPerBand__beamReportTiming {
		long	*scs_15kHz;	/* OPTIONAL */
		long	*scs_30kHz;	/* OPTIONAL */
		long	*scs_60kHz;	/* OPTIONAL */
		long	*scs_120kHz;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *beamReportTiming;
	struct NR_MIMO_ParametersPerBand__ptrs_DensityRecommendationSetDL {
		struct NR_PTRS_DensityRecommendationDL	*scs_15kHz;	/* OPTIONAL */
		struct NR_PTRS_DensityRecommendationDL	*scs_30kHz;	/* OPTIONAL */
		struct NR_PTRS_DensityRecommendationDL	*scs_60kHz;	/* OPTIONAL */
		struct NR_PTRS_DensityRecommendationDL	*scs_120kHz;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ptrs_DensityRecommendationSetDL;
	struct NR_MIMO_ParametersPerBand__ptrs_DensityRecommendationSetUL {
		struct NR_PTRS_DensityRecommendationUL	*scs_15kHz;	/* OPTIONAL */
		struct NR_PTRS_DensityRecommendationUL	*scs_30kHz;	/* OPTIONAL */
		struct NR_PTRS_DensityRecommendationUL	*scs_60kHz;	/* OPTIONAL */
		struct NR_PTRS_DensityRecommendationUL	*scs_120kHz;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ptrs_DensityRecommendationSetUL;
	struct NR_DummyH	*dummy4;	/* OPTIONAL */
	long	*aperiodicTRS;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	struct NR_MIMO_ParametersPerBand__ext1 {
		long	*dummy6;	/* OPTIONAL */
		struct NR_BeamManagementSSB_CSI_RS	*beamManagementSSB_CSI_RS;	/* OPTIONAL */
		struct NR_MIMO_ParametersPerBand__ext1__beamSwitchTiming {
			long	*scs_60kHz;	/* OPTIONAL */
			long	*scs_120kHz;	/* OPTIONAL */
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *beamSwitchTiming;
		struct NR_CodebookParameters	*codebookParameters;	/* OPTIONAL */
		struct NR_CSI_RS_IM_ReceptionForFeedback	*csi_RS_IM_ReceptionForFeedback;	/* OPTIONAL */
		struct NR_CSI_RS_ProcFrameworkForSRS	*csi_RS_ProcFrameworkForSRS;	/* OPTIONAL */
		struct NR_CSI_ReportFramework	*csi_ReportFramework;	/* OPTIONAL */
		struct NR_CSI_RS_ForTracking	*csi_RS_ForTracking;	/* OPTIONAL */
		struct NR_MIMO_ParametersPerBand__ext1__srs_AssocCSI_RS {
			A_SEQUENCE_OF(struct NR_SupportedCSI_RS_Resource) list;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *srs_AssocCSI_RS;
		struct NR_SpatialRelations	*spatialRelations;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext1;
	struct NR_MIMO_ParametersPerBand__ext2 {
		long	*defaultQCL_TwoTCI_r16;	/* OPTIONAL */
		struct NR_CodebookParameters_v1610	*codebookParametersPerBand_r16;	/* OPTIONAL */
		long	*simul_SpatialRelationUpdatePUCCHResGroup_r16;	/* OPTIONAL */
		long	*maxNumberSCellBFR_r16;	/* OPTIONAL */
		long	*simultaneousReceptionDiffTypeD_r16;	/* OPTIONAL */
		struct NR_MIMO_ParametersPerBand__ext2__ssb_csirs_SINR_measurement_r16 {
			long	 maxNumberSSB_CSIRS_OneTx_CMR_r16;
			long	 maxNumberCSI_IM_NZP_IMR_res_r16;
			long	 maxNumberCSIRS_2Tx_res_r16;
			long	 maxNumberSSB_CSIRS_res_r16;
			long	 maxNumberCSI_IM_NZP_IMR_res_mem_r16;
			long	 supportedCSI_RS_Density_CMR_r16;
			long	 maxNumberAperiodicCSI_RS_Res_r16;
			long	*supportedSINR_meas_r16;	/* OPTIONAL */
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *ssb_csirs_SINR_measurement_r16;
		long	*nonGroupSINR_reporting_r16;	/* OPTIONAL */
		long	*groupSINR_reporting_r16;	/* OPTIONAL */
		struct NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16 {
			long	*overlapPDSCHsFullyFreqTime_r16;	/* OPTIONAL */
			long	*overlapPDSCHsInTimePartiallyFreq_r16;	/* OPTIONAL */
			struct NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__outOfOrderOperationDL_r16 {
				long	*supportPDCCH_ToPDSCH_r16;	/* OPTIONAL */
				long	*supportPDSCH_ToHARQ_ACK_r16;	/* OPTIONAL */
				
				/* Context for parsing across buffer boundaries */
				asn_struct_ctx_t _asn_ctx;
			} *outOfOrderOperationDL_r16;
			long	*outOfOrderOperationUL_r16;	/* OPTIONAL */
			long	*separateCRS_RateMatching_r16;	/* OPTIONAL */
			long	*defaultQCL_PerCORESETPoolIndex_r16;	/* OPTIONAL */
			struct NR_MIMO_ParametersPerBand__ext2__multiDCI_multiTRP_Parameters_r16__maxNumberActivatedTCI_States_r16 {
				long	 maxNumberPerCORESET_Pool_r16;
				long	 maxTotalNumberAcrossCORESET_Pool_r16;
				
				/* Context for parsing across buffer boundaries */
				asn_struct_ctx_t _asn_ctx;
			} *maxNumberActivatedTCI_States_r16;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *multiDCI_multiTRP_Parameters_r16;
		struct NR_MIMO_ParametersPerBand__ext2__singleDCI_SDM_scheme_Parameters_r16 {
			long	*supportNewDMRS_Port_r16;	/* OPTIONAL */
			long	*supportTwoPortDL_PTRS_r16;	/* OPTIONAL */
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *singleDCI_SDM_scheme_Parameters_r16;
		long	*supportFDM_SchemeA_r16;	/* OPTIONAL */
		long	*supportCodeWordSoftCombining_r16;	/* OPTIONAL */
		long	*supportTDM_SchemeA_r16;	/* OPTIONAL */
		struct NR_MIMO_ParametersPerBand__ext2__supportInter_slotTDM_r16 {
			long	 supportRepNumPDSCH_TDRA_r16;
			long	 maxTBS_Size_r16;
			long	 maxNumberTCI_states_r16;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *supportInter_slotTDM_r16;
		long	*lowPAPR_DMRS_PDSCH_r16;	/* OPTIONAL */
		long	*lowPAPR_DMRS_PUSCHwithoutPrecoding_r16;	/* OPTIONAL */
		long	*lowPAPR_DMRS_PUCCH_r16;	/* OPTIONAL */
		long	*lowPAPR_DMRS_PUSCHwithPrecoding_r16;	/* OPTIONAL */
		struct NR_CSI_ReportFrameworkExt_r16	*csi_ReportFrameworkExt_r16;	/* OPTIONAL */
		struct NR_CodebookParametersAddition_r16	*codebookParametersAddition_r16;	/* OPTIONAL */
		struct NR_CodebookComboParametersAddition_r16	*codebookComboParametersAddition_r16;	/* OPTIONAL */
		long	*beamCorrespondenceSSB_based_r16;	/* OPTIONAL */
		long	*beamCorrespondenceCSI_RS_based_r16;	/* OPTIONAL */
		struct NR_MIMO_ParametersPerBand__ext2__beamSwitchTiming_r16 {
			long	*scs_60kHz_r16;	/* OPTIONAL */
			long	*scs_120kHz_r16;	/* OPTIONAL */
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *beamSwitchTiming_r16;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext2;
	struct NR_MIMO_ParametersPerBand__ext3 {
		struct NR_MIMO_ParametersPerBand__ext3__semi_PersistentL1_SINR_Report_PUCCH_r16 {
			long	*supportReportFormat1_2OFDM_syms_r16;	/* OPTIONAL */
			long	*supportReportFormat4_14OFDM_syms_r16;	/* OPTIONAL */
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *semi_PersistentL1_SINR_Report_PUCCH_r16;
		long	*semi_PersistentL1_SINR_Report_PUSCH_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext3;
	struct NR_MIMO_ParametersPerBand__ext4 {
		struct NR_MIMO_ParametersPerBand__ext4__spatialRelations_v1640 {
			long	 maxNumberConfiguredSpatialRelations_v1640;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *spatialRelations_v1640;
		long	*support64CandidateBeamRS_BFR_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext4;
	struct NR_MIMO_ParametersPerBand__ext5 {
		long	*maxMIMO_LayersForMulti_DCI_mTRP_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext5;
	struct NR_MIMO_ParametersPerBand__ext6 {
		BIT_STRING_t	*supportedSINR_meas_v1670;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext6;
	struct NR_MIMO_ParametersPerBand__ext7 {
		long	*srs_increasedRepetition_r17;	/* OPTIONAL */
		long	*srs_partialFrequencySounding_r17;	/* OPTIONAL */
		long	*srs_startRB_locationHoppingPartial_r17;	/* OPTIONAL */
		long	*srs_combEight_r17;	/* OPTIONAL */
		struct NR_CodebookParametersfetype2_r17	*codebookParametersfetype2_r17;	/* OPTIONAL */
		long	*mTRP_PUSCH_twoCSI_RS_r17;	/* OPTIONAL */
		long	*mTRP_PUCCH_InterSlot_r17;	/* OPTIONAL */
		long	*mTRP_PUCCH_CyclicMapping_r17;	/* OPTIONAL */
		long	*mTRP_PUCCH_SecondTPC_r17;	/* OPTIONAL */
		struct NR_MIMO_ParametersPerBand__ext7__mTRP_BFR_twoBFD_RS_Set_r17 {
			long	 maxBFD_RS_resourcesPerSetPerBWP_r17;
			long	 maxBFR_r17;
			long	 maxBFD_RS_resourcesAcrossSetsPerBWP_r17;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *mTRP_BFR_twoBFD_RS_Set_r17;
		long	*mTRP_BFR_PUCCH_SR_perCG_r17;	/* OPTIONAL */
		long	*mTRP_BFR_association_PUCCH_SR_r17;	/* OPTIONAL */
		long	*sfn_SimulTwoTCI_AcrossMultiCC_r17;	/* OPTIONAL */
		long	*sfn_DefaultDL_BeamSetup_r17;	/* OPTIONAL */
		long	*sfn_DefaultUL_BeamSetup_r17;	/* OPTIONAL */
		long	*srs_TriggeringOffset_r17;	/* OPTIONAL */
		long	*srs_TriggeringDCI_r17;	/* OPTIONAL */
		struct NR_CodebookComboParameterMixedType_r17	*codebookComboParameterMixedType_r17;	/* OPTIONAL */
		struct NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_r17 {
			long	 maxConfiguredJointTCI_r17;
			long	 maxActivatedTCIAcrossCC_r17;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *unifiedJointTCI_r17;
		struct NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_multiMAC_CE_r17 {
			long	*minBeamApplicationTime_r17;	/* OPTIONAL */
			long	 maxNumMAC_CE_PerCC;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *unifiedJointTCI_multiMAC_CE_r17;
		long	*unifiedJointTCI_perBWP_CA_r17;	/* OPTIONAL */
		long	*unifiedJointTCI_ListSharingCA_r17;	/* OPTIONAL */
		long	*unifiedJointTCI_commonMultiCC_r17;	/* OPTIONAL */
		long	*unifiedJointTCI_BeamAlignDLRS_r17;	/* OPTIONAL */
		long	*unifiedJointTCI_PC_association_r17;	/* OPTIONAL */
		long	*unifiedJointTCI_Legacy_r17;	/* OPTIONAL */
		long	*unifiedJointTCI_Legacy_SRS_r17;	/* OPTIONAL */
		long	*unifiedJointTCI_Legacy_CORESET0_r17;	/* OPTIONAL */
		long	*unifiedJointTCI_SCellBFR_r17;	/* OPTIONAL */
		struct NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_InterCell_r17 {
			long	 additionalMAC_CE_PerCC_r17;
			long	 additionalMAC_CE_AcrossCC_r17;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *unifiedJointTCI_InterCell_r17;
		struct NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_r17 {
			long	 maxConfiguredDL_TCI_r17;
			long	 maxConfiguredUL_TCI_r17;
			long	 maxActivatedDL_TCIAcrossCC_r17;
			long	 maxActivatedUL_TCIAcrossCC_r17;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *unifiedSeparateTCI_r17;
		struct NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_multiMAC_CE_r17 {
			long	 minBeamApplicationTime_r17;
			long	 maxActivatedDL_TCIPerCC_r17;
			long	 maxActivatedUL_TCIPerCC_r17;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *unifiedSeparateTCI_multiMAC_CE_r17;
		long	*unifiedSeparateTCI_perBWP_CA_r17;	/* OPTIONAL */
		struct NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_ListSharingCA_r17 {
			long	*maxNumListDL_TCI_r17;	/* OPTIONAL */
			long	*maxNumListUL_TCI_r17;	/* OPTIONAL */
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *unifiedSeparateTCI_ListSharingCA_r17;
		long	*unifiedSeparateTCI_commonMultiCC_r17;	/* OPTIONAL */
		struct NR_MIMO_ParametersPerBand__ext7__unifiedSeparateTCI_InterCell_r17 {
			long	 k_DL_PerCC_r17;
			long	 k_UL_PerCC_r17;
			long	 k_DL_AcrossCC_r17;
			long	 k_UL_AcrossCC_r17;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *unifiedSeparateTCI_InterCell_r17;
		struct NR_MIMO_ParametersPerBand__ext7__unifiedJointTCI_mTRP_InterCell_BM_r17 {
			long	 maxNumAdditionalPCI_L1_RSRP_r17;
			long	 maxNumSSB_ResourceL1_RSRP_AcrossCC_r17;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *unifiedJointTCI_mTRP_InterCell_BM_r17;
		struct NR_MIMO_ParametersPerBand__ext7__mpe_Mitigation_r17 {
			long	 maxNumP_MPR_RI_pairs_r17;
			long	 maxNumConfRS_r17;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *mpe_Mitigation_r17;
		struct NR_MIMO_ParametersPerBand__ext7__srs_PortReport_r17 {
			long	*capVal1_r17;	/* OPTIONAL */
			long	*capVal2_r17;	/* OPTIONAL */
			long	*capVal3_r17;	/* OPTIONAL */
			long	*capVal4_r17;	/* OPTIONAL */
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *srs_PortReport_r17;
		long	*mTRP_PDCCH_individual_r17;	/* OPTIONAL */
		long	*mTRP_PDCCH_anySpan_3Symbols_r17;	/* OPTIONAL */
		long	*mTRP_PDCCH_TwoQCL_TypeD_r17;	/* OPTIONAL */
		struct NR_MIMO_ParametersPerBand__ext7__mTRP_PUSCH_CSI_RS_r17 {
			long	 maxNumPeriodicSRS_r17;
			long	 maxNumAperiodicSRS_r17;
			long	 maxNumSP_SRS_r17;
			long	 numSRS_ResourcePerCC_r17;
			long	 numSRS_ResourceNonCodebook_r17;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *mTRP_PUSCH_CSI_RS_r17;
		long	*mTRP_PUSCH_cyclicMapping_r17;	/* OPTIONAL */
		long	*mTRP_PUSCH_secondTPC_r17;	/* OPTIONAL */
		long	*mTRP_PUSCH_twoPHR_Reporting_r17;	/* OPTIONAL */
		long	*mTRP_PUSCH_A_CSI_r17;	/* OPTIONAL */
		long	*mTRP_PUSCH_SP_CSI_r17;	/* OPTIONAL */
		long	*mTRP_PUSCH_CG_r17;	/* OPTIONAL */
		long	*mTRP_PUCCH_MAC_CE_r17;	/* OPTIONAL */
		long	*mTRP_PUCCH_maxNum_PC_FR1_r17;	/* OPTIONAL */
		struct NR_MIMO_ParametersPerBand__ext7__mTRP_inter_Cell_r17 {
			long	 maxNumAdditionalPCI_Case1_r17;
			long	 maxNumAdditionalPCI_Case2_r17;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *mTRP_inter_Cell_r17;
		struct NR_MIMO_ParametersPerBand__ext7__mTRP_GroupBasedL1_RSRP_r17 {
			long	 maxNumBeamGroups_r17;
			long	 maxNumRS_WithinSlot_r17;
			long	 maxNumRS_AcrossSlot_r17;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *mTRP_GroupBasedL1_RSRP_r17;
		long	*mTRP_BFD_RS_MAC_CE_r17;	/* OPTIONAL */
		struct NR_MIMO_ParametersPerBand__ext7__mTRP_CSI_EnhancementPerBand_r17 {
			long	 maxNumNZP_CSI_RS_r17;
			long	 cSI_Report_mode_r17;
			struct NR_MIMO_ParametersPerBand__ext7__mTRP_CSI_EnhancementPerBand_r17__supportedComboAcrossCCs_r17 {
				A_SEQUENCE_OF(struct NR_CSI_MultiTRP_SupportedCombinations_r17) list;
				
				/* Context for parsing across buffer boundaries */
				asn_struct_ctx_t _asn_ctx;
			} supportedComboAcrossCCs_r17;
			long	 codebookModeNCJT_r17;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *mTRP_CSI_EnhancementPerBand_r17;
		struct NR_CodebookComboParameterMultiTRP_r17	*codebookComboParameterMultiTRP_r17;	/* OPTIONAL */
		long	*mTRP_CSI_additionalCSI_r17;	/* OPTIONAL */
		long	*mTRP_CSI_N_Max2_r17;	/* OPTIONAL */
		long	*mTRP_CSI_CMR_r17;	/* OPTIONAL */
		long	*srs_partialFreqSounding_r17;	/* OPTIONAL */
		struct NR_MIMO_ParametersPerBand__ext7__beamSwitchTiming_v1710 {
			long	*scs_480kHz;	/* OPTIONAL */
			long	*scs_960kHz;	/* OPTIONAL */
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *beamSwitchTiming_v1710;
		struct NR_MIMO_ParametersPerBand__ext7__beamSwitchTiming_r17 {
			long	*scs_480kHz_r17;	/* OPTIONAL */
			long	*scs_960kHz_r17;	/* OPTIONAL */
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *beamSwitchTiming_r17;
		struct NR_MIMO_ParametersPerBand__ext7__beamReportTiming_v1710 {
			long	*scs_480kHz_r17;	/* OPTIONAL */
			long	*scs_960kHz_r17;	/* OPTIONAL */
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *beamReportTiming_v1710;
		struct NR_MIMO_ParametersPerBand__ext7__maxNumberRxTxBeamSwitchDL_v1710 {
			long	*scs_480kHz_r17;	/* OPTIONAL */
			long	*scs_960kHz_r17;	/* OPTIONAL */
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *maxNumberRxTxBeamSwitchDL_v1710;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext7;
	struct NR_MIMO_ParametersPerBand__ext8 {
		long	*srs_PortReportSP_AP_r17;	/* OPTIONAL */
		long	*maxNumberRxBeam_v1720;	/* OPTIONAL */
		long	*sfn_ImplicitRS_twoTCI_r17;	/* OPTIONAL */
		long	*sfn_QCL_TypeD_Collision_twoTCI_r17;	/* OPTIONAL */
		long	*mTRP_CSI_numCPU_r17;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext8;
	struct NR_MIMO_ParametersPerBand__ext9 {
		long	*supportRepNumPDSCH_TDRA_DCI_1_2_r17;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext9;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_MIMO_ParametersPerBand_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumberConfiguredTCI_StatesPerCC_3;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumberActiveTCI_PerBWP_10;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_additionalActiveTCI_StatePDCCH_15;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pusch_TransCoherence_17;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_beamCorrespondenceWithoutUL_BeamSweeping_21;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_periodicBeamReport_23;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_aperiodicBeamReport_25;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_sp_BeamReportPUCCH_27;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_sp_BeamReportPUSCH_29;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_15kHz_34;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_30kHz_38;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_60kHz_42;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_120kHz_46;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_240kHz_50;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumberNonGroupBeamReporting_54;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_groupBeamReporting_58;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumberSRS_ResourcePerSet_BM_61;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dummy2_70;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_twoPortsPTRS_UL_72;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_15kHz_77;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_30kHz_81;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_60kHz_86;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_120kHz_90;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_aperiodicTRS_105;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dummy6_109;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_60kHz_113;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_120kHz_119;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_defaultQCL_TwoTCI_r16_134;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_simul_SpatialRelationUpdatePUCCHResGroup_r16_137;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumberSCellBFR_r16_139;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_simultaneousReceptionDiffTypeD_r16_144;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumberSSB_CSIRS_OneTx_CMR_r16_147;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumberCSI_IM_NZP_IMR_res_r16_152;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumberCSIRS_2Tx_res_r16_157;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumberSSB_CSIRS_res_r16_164;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumberCSI_IM_NZP_IMR_res_mem_r16_170;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_supportedCSI_RS_Density_CMR_r16_176;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumberAperiodicCSI_RS_Res_r16_180;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_supportedSINR_meas_r16_187;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_nonGroupSINR_reporting_r16_192;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_groupSINR_reporting_r16_196;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_overlapPDSCHsInTimePartiallyFreq_r16_200;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_supportPDCCH_ToPDSCH_r16_203;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_supportPDSCH_ToHARQ_ACK_r16_205;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_outOfOrderOperationUL_r16_207;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_separateCRS_RateMatching_r16_209;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_defaultQCL_PerCORESETPoolIndex_r16_211;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumberPerCORESET_Pool_r16_214;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxTotalNumberAcrossCORESET_Pool_r16_219;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_supportNewDMRS_Port_r16_225;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_supportTwoPortDL_PTRS_r16_229;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_supportFDM_SchemeA_r16_231;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_supportCodeWordSoftCombining_r16_233;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_supportTDM_SchemeA_r16_235;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_supportRepNumPDSCH_TDRA_r16_242;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxTBS_Size_r16_251;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_lowPAPR_DMRS_PDSCH_r16_258;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_lowPAPR_DMRS_PUSCHwithoutPrecoding_r16_260;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_lowPAPR_DMRS_PUCCH_r16_262;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_lowPAPR_DMRS_PUSCHwithPrecoding_r16_264;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_beamCorrespondenceSSB_based_r16_269;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_beamCorrespondenceCSI_RS_based_r16_271;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_60kHz_r16_274;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_120kHz_r16_277;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_supportReportFormat1_2OFDM_syms_r16_282;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_supportReportFormat4_14OFDM_syms_r16_284;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_semi_PersistentL1_SINR_Report_PUSCH_r16_286;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumberConfiguredSpatialRelations_v1640_290;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_support64CandidateBeamRS_BFR_r16_299;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxMIMO_LayersForMulti_DCI_mTRP_r16_302;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_srs_increasedRepetition_r17_307;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_srs_partialFrequencySounding_r17_309;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_srs_startRB_locationHoppingPartial_r17_311;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_srs_combEight_r17_313;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mTRP_PUSCH_twoCSI_RS_r17_316;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mTRP_PUCCH_InterSlot_r17_318;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mTRP_PUCCH_CyclicMapping_r17_322;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mTRP_PUCCH_SecondTPC_r17_324;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxBFD_RS_resourcesPerSetPerBWP_r17_327;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxBFD_RS_resourcesAcrossSetsPerBWP_r17_331;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mTRP_BFR_PUCCH_SR_perCG_r17_335;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mTRP_BFR_association_PUCCH_SR_r17_338;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_sfn_SimulTwoTCI_AcrossMultiCC_r17_340;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_sfn_DefaultDL_BeamSetup_r17_342;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_sfn_DefaultUL_BeamSetup_r17_344;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_srs_TriggeringOffset_r17_346;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_srs_TriggeringDCI_r17_350;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxConfiguredJointTCI_r17_354;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxActivatedTCIAcrossCC_r17_363;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_minBeamApplicationTime_r17_370;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumMAC_CE_PerCC_385;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_unifiedJointTCI_perBWP_CA_r17_393;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_unifiedJointTCI_ListSharingCA_r17_395;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_unifiedJointTCI_commonMultiCC_r17_400;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_unifiedJointTCI_BeamAlignDLRS_r17_402;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_unifiedJointTCI_PC_association_r17_404;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_unifiedJointTCI_Legacy_r17_406;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_unifiedJointTCI_Legacy_SRS_r17_408;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_unifiedJointTCI_Legacy_CORESET0_r17_410;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_unifiedJointTCI_SCellBFR_r17_412;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_additionalMAC_CE_PerCC_r17_415;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_additionalMAC_CE_AcrossCC_r17_420;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxConfiguredDL_TCI_r17_426;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxConfiguredUL_TCI_r17_436;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxActivatedDL_TCIAcrossCC_r17_445;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxActivatedUL_TCIAcrossCC_r17_451;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_minBeamApplicationTime_r17_458;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_unifiedSeparateTCI_perBWP_CA_r17_475;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumListDL_TCI_r17_478;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumListUL_TCI_r17_483;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_unifiedSeparateTCI_commonMultiCC_r17_488;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_k_DL_PerCC_r17_491;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_k_UL_PerCC_r17_496;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_k_DL_AcrossCC_r17_501;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_k_UL_AcrossCC_r17_506;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumSSB_ResourceL1_RSRP_AcrossCC_r17_513;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumConfRS_r17_520;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_capVal1_r17_532;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_capVal2_r17_536;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_capVal3_r17_540;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_capVal4_r17_544;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mTRP_PDCCH_individual_r17_548;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mTRP_PDCCH_anySpan_3Symbols_r17_550;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mTRP_PDCCH_TwoQCL_TypeD_r17_552;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mTRP_PUSCH_cyclicMapping_r17_560;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mTRP_PUSCH_secondTPC_r17_564;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mTRP_PUSCH_twoPHR_Reporting_r17_566;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mTRP_PUSCH_A_CSI_r17_568;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mTRP_PUSCH_SP_CSI_r17_570;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mTRP_PUSCH_CG_r17_572;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mTRP_PUCCH_MAC_CE_r17_574;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumRS_WithinSlot_r17_582;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumRS_AcrossSlot_r17_590;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mTRP_BFD_RS_MAC_CE_r17_596;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_cSI_Report_mode_r17_606;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_codebookModeNCJT_r17_612;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mTRP_CSI_additionalCSI_r17_616;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mTRP_CSI_N_Max2_r17_619;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mTRP_CSI_CMR_r17_621;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_srs_partialFreqSounding_r17_623;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_480kHz_626;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_960kHz_632;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_480kHz_r17_639;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_960kHz_r17_642;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_480kHz_r17_646;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_960kHz_r17_650;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_480kHz_r17_655;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_scs_960kHz_r17_659;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_srs_PortReportSP_AP_r17_665;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_sfn_ImplicitRS_twoTCI_r17_668;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_sfn_QCL_TypeD_Collision_twoTCI_r17_670;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mTRP_CSI_numCPU_r17_672;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_supportRepNumPDSCH_TDRA_DCI_1_2_r17_677;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_MIMO_ParametersPerBand;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_MIMO_ParametersPerBand_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_MIMO_ParametersPerBand_1[35];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_DummyG.h"
#include "NR_SRS-Resources.h"
#include "NR_DummyH.h"
#include "NR_PTRS-DensityRecommendationDL.h"
#include "NR_PTRS-DensityRecommendationUL.h"
#include "NR_BeamManagementSSB-CSI-RS.h"
#include "NR_CodebookParameters.h"
#include "NR_CSI-RS-IM-ReceptionForFeedback.h"
#include "NR_CSI-RS-ProcFrameworkForSRS.h"
#include "NR_CSI-ReportFramework.h"
#include "NR_CSI-RS-ForTracking.h"
#include "NR_SpatialRelations.h"
#include "NR_SupportedCSI-RS-Resource.h"
#include "NR_CodebookParameters-v1610.h"
#include "NR_CSI-ReportFrameworkExt-r16.h"
#include "NR_CodebookParametersAddition-r16.h"
#include "NR_CodebookComboParametersAddition-r16.h"
#include "NR_CodebookParametersfetype2-r17.h"
#include "NR_CodebookComboParameterMixedType-r17.h"
#include "NR_CodebookComboParameterMultiTRP-r17.h"
#include "NR_CSI-MultiTRP-SupportedCombinations-r17.h"

#endif	/* _NR_MIMO_ParametersPerBand_H_ */
#include <asn_internal.h>
