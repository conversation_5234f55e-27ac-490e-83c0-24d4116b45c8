/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_FeatureSetUplink-v1610.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_twoPorts_r16_constraint_149(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	const BIT_STRING_t *st = (const BIT_STRING_t *)sptr;
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	if(st->size > 0) {
		/* Size in bits */
		size = 8 * st->size - (st->bits_unused & 0x07);
	} else {
		size = 0;
	}
	
	if((size == 2UL)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_maxNumberPUSCH_Tx_r16_constr_3 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  5 }	/* (0..5) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_hoppingScheme_r16_constr_10 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ul_CancellationSelfCarrier_r16_constr_14 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ul_CancellationCrossCarrier_r16_constr_16 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ul_FullPwrMode2_MaxSRS_ResInSet_r16_constr_18 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_scs_15kHz_r16_constr_23 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_scs_30kHz_r16_constr_28 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_scs_60kHz_r16_constr_33 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_scs_120kHz_r16_constr_38 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_scs_15kHz_r16_constr_44 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_scs_30kHz_r16_constr_49 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_scs_60kHz_r16_constr_54 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_scs_120kHz_r16_constr_59 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dummy_constr_66 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_intraFreqTwoTAGs_DAPS_r16_constr_68 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dummy1_constr_70 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dummy2_constr_72 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dummy3_constr_74 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_sub_SlotConfig_NCP_r16_constr_79 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_sub_SlotConfig_ECP_r16_constr_82 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 1,  1,  0,  1 }	/* (0..1) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_twoPUCCH_Type1_r16_constr_85 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_twoPUCCH_Type2_r16_constr_87 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_twoPUCCH_Type3_r16_constr_89 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_twoPUCCH_Type4_r16_constr_91 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_mux_SR_HARQ_ACK_r16_constr_93 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dummy1_constr_95 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_dummy2_constr_97 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_twoPUCCH_Type5_r16_constr_99 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_twoPUCCH_Type6_r16_constr_101 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_twoPUCCH_Type7_r16_constr_103 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_twoPUCCH_Type8_r16_constr_105 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_twoPUCCH_Type9_r16_constr_107 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_twoPUCCH_Type10_r16_constr_109 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_twoPUCCH_Type11_r16_constr_111 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pusch_PreparationLowPriority_r16_constr_114 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_pusch_PreparationHighPriority_r16_constr_118 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ul_FullPwrMode_r16_constr_122 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_scs_15kHz_120kHz_r16_constr_125 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_scs_15kHz_60kHz_r16_constr_129 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_scs_30kHz_120kHz_r16_constr_133 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_scs_15kHz_30kHz_r16_constr_137 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_scs_30kHz_60kHz_r16_constr_139 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_scs_60kHz_120kHz_r16_constr_141 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ul_FullPwrMode1_r16_constr_143 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 0,  0,  0,  0 }	/* (0..0) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ul_FullPwrMode2_SRSConfig_diffNumSRSPorts_r16_constr_145 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_fourPortsNonCoherent_r16_constr_151 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_fourPortsPartialCoherent_r16_constr_156 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  6 }	/* (0..6) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_twoPorts_r16_constr_150 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 0,  0,  2,  2 }	/* (SIZE(2..2)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_maxNumberPUSCH_Tx_r16_value2enum_3[] = {
	{ 0,	2,	"n2" },
	{ 1,	2,	"n3" },
	{ 2,	2,	"n4" },
	{ 3,	2,	"n7" },
	{ 4,	2,	"n8" },
	{ 5,	3,	"n12" }
};
static const unsigned int asn_MAP_NR_maxNumberPUSCH_Tx_r16_enum2value_3[] = {
	5,	/* n12(5) */
	0,	/* n2(0) */
	1,	/* n3(1) */
	2,	/* n4(2) */
	3,	/* n7(3) */
	4	/* n8(4) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_maxNumberPUSCH_Tx_r16_specs_3 = {
	asn_MAP_NR_maxNumberPUSCH_Tx_r16_value2enum_3,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_maxNumberPUSCH_Tx_r16_enum2value_3,	/* N => "tag"; sorted by N */
	6,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_maxNumberPUSCH_Tx_r16_tags_3[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_maxNumberPUSCH_Tx_r16_3 = {
	"maxNumberPUSCH-Tx-r16",
	"maxNumberPUSCH-Tx-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_maxNumberPUSCH_Tx_r16_tags_3,
	sizeof(asn_DEF_NR_maxNumberPUSCH_Tx_r16_tags_3)
		/sizeof(asn_DEF_NR_maxNumberPUSCH_Tx_r16_tags_3[0]) - 1, /* 1 */
	asn_DEF_NR_maxNumberPUSCH_Tx_r16_tags_3,	/* Same as above */
	sizeof(asn_DEF_NR_maxNumberPUSCH_Tx_r16_tags_3)
		/sizeof(asn_DEF_NR_maxNumberPUSCH_Tx_r16_tags_3[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_maxNumberPUSCH_Tx_r16_constr_3,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_maxNumberPUSCH_Tx_r16_specs_3	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_hoppingScheme_r16_value2enum_10[] = {
	{ 0,	16,	"interSlotHopping" },
	{ 1,	22,	"interRepetitionHopping" },
	{ 2,	4,	"both" }
};
static const unsigned int asn_MAP_NR_hoppingScheme_r16_enum2value_10[] = {
	2,	/* both(2) */
	1,	/* interRepetitionHopping(1) */
	0	/* interSlotHopping(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_hoppingScheme_r16_specs_10 = {
	asn_MAP_NR_hoppingScheme_r16_value2enum_10,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_hoppingScheme_r16_enum2value_10,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_hoppingScheme_r16_tags_10[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_hoppingScheme_r16_10 = {
	"hoppingScheme-r16",
	"hoppingScheme-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_hoppingScheme_r16_tags_10,
	sizeof(asn_DEF_NR_hoppingScheme_r16_tags_10)
		/sizeof(asn_DEF_NR_hoppingScheme_r16_tags_10[0]) - 1, /* 1 */
	asn_DEF_NR_hoppingScheme_r16_tags_10,	/* Same as above */
	sizeof(asn_DEF_NR_hoppingScheme_r16_tags_10)
		/sizeof(asn_DEF_NR_hoppingScheme_r16_tags_10[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_hoppingScheme_r16_constr_10,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_hoppingScheme_r16_specs_10	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_pusch_RepetitionTypeB_r16_2[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_FeatureSetUplink_v1610__pusch_RepetitionTypeB_r16, maxNumberPUSCH_Tx_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_maxNumberPUSCH_Tx_r16_3,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"maxNumberPUSCH-Tx-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_FeatureSetUplink_v1610__pusch_RepetitionTypeB_r16, hoppingScheme_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_hoppingScheme_r16_10,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"hoppingScheme-r16"
		},
};
static const ber_tlv_tag_t asn_DEF_NR_pusch_RepetitionTypeB_r16_tags_2[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_pusch_RepetitionTypeB_r16_tag2el_2[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* maxNumberPUSCH-Tx-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* hoppingScheme-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_pusch_RepetitionTypeB_r16_specs_2 = {
	sizeof(struct NR_FeatureSetUplink_v1610__pusch_RepetitionTypeB_r16),
	offsetof(struct NR_FeatureSetUplink_v1610__pusch_RepetitionTypeB_r16, _asn_ctx),
	asn_MAP_NR_pusch_RepetitionTypeB_r16_tag2el_2,
	2,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pusch_RepetitionTypeB_r16_2 = {
	"pusch-RepetitionTypeB-r16",
	"pusch-RepetitionTypeB-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_pusch_RepetitionTypeB_r16_tags_2,
	sizeof(asn_DEF_NR_pusch_RepetitionTypeB_r16_tags_2)
		/sizeof(asn_DEF_NR_pusch_RepetitionTypeB_r16_tags_2[0]) - 1, /* 1 */
	asn_DEF_NR_pusch_RepetitionTypeB_r16_tags_2,	/* Same as above */
	sizeof(asn_DEF_NR_pusch_RepetitionTypeB_r16_tags_2)
		/sizeof(asn_DEF_NR_pusch_RepetitionTypeB_r16_tags_2[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_pusch_RepetitionTypeB_r16_2,
	2,	/* Elements count */
	&asn_SPC_NR_pusch_RepetitionTypeB_r16_specs_2	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_ul_CancellationSelfCarrier_r16_value2enum_14[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_ul_CancellationSelfCarrier_r16_enum2value_14[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ul_CancellationSelfCarrier_r16_specs_14 = {
	asn_MAP_NR_ul_CancellationSelfCarrier_r16_value2enum_14,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ul_CancellationSelfCarrier_r16_enum2value_14,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ul_CancellationSelfCarrier_r16_tags_14[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ul_CancellationSelfCarrier_r16_14 = {
	"ul-CancellationSelfCarrier-r16",
	"ul-CancellationSelfCarrier-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ul_CancellationSelfCarrier_r16_tags_14,
	sizeof(asn_DEF_NR_ul_CancellationSelfCarrier_r16_tags_14)
		/sizeof(asn_DEF_NR_ul_CancellationSelfCarrier_r16_tags_14[0]) - 1, /* 1 */
	asn_DEF_NR_ul_CancellationSelfCarrier_r16_tags_14,	/* Same as above */
	sizeof(asn_DEF_NR_ul_CancellationSelfCarrier_r16_tags_14)
		/sizeof(asn_DEF_NR_ul_CancellationSelfCarrier_r16_tags_14[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ul_CancellationSelfCarrier_r16_constr_14,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ul_CancellationSelfCarrier_r16_specs_14	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_ul_CancellationCrossCarrier_r16_value2enum_16[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_ul_CancellationCrossCarrier_r16_enum2value_16[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ul_CancellationCrossCarrier_r16_specs_16 = {
	asn_MAP_NR_ul_CancellationCrossCarrier_r16_value2enum_16,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ul_CancellationCrossCarrier_r16_enum2value_16,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ul_CancellationCrossCarrier_r16_tags_16[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ul_CancellationCrossCarrier_r16_16 = {
	"ul-CancellationCrossCarrier-r16",
	"ul-CancellationCrossCarrier-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ul_CancellationCrossCarrier_r16_tags_16,
	sizeof(asn_DEF_NR_ul_CancellationCrossCarrier_r16_tags_16)
		/sizeof(asn_DEF_NR_ul_CancellationCrossCarrier_r16_tags_16[0]) - 1, /* 1 */
	asn_DEF_NR_ul_CancellationCrossCarrier_r16_tags_16,	/* Same as above */
	sizeof(asn_DEF_NR_ul_CancellationCrossCarrier_r16_tags_16)
		/sizeof(asn_DEF_NR_ul_CancellationCrossCarrier_r16_tags_16[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ul_CancellationCrossCarrier_r16_constr_16,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ul_CancellationCrossCarrier_r16_specs_16	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_ul_FullPwrMode2_MaxSRS_ResInSet_r16_value2enum_18[] = {
	{ 0,	2,	"n1" },
	{ 1,	2,	"n2" },
	{ 2,	2,	"n4" }
};
static const unsigned int asn_MAP_NR_ul_FullPwrMode2_MaxSRS_ResInSet_r16_enum2value_18[] = {
	0,	/* n1(0) */
	1,	/* n2(1) */
	2	/* n4(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ul_FullPwrMode2_MaxSRS_ResInSet_r16_specs_18 = {
	asn_MAP_NR_ul_FullPwrMode2_MaxSRS_ResInSet_r16_value2enum_18,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ul_FullPwrMode2_MaxSRS_ResInSet_r16_enum2value_18,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ul_FullPwrMode2_MaxSRS_ResInSet_r16_tags_18[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ul_FullPwrMode2_MaxSRS_ResInSet_r16_18 = {
	"ul-FullPwrMode2-MaxSRS-ResInSet-r16",
	"ul-FullPwrMode2-MaxSRS-ResInSet-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ul_FullPwrMode2_MaxSRS_ResInSet_r16_tags_18,
	sizeof(asn_DEF_NR_ul_FullPwrMode2_MaxSRS_ResInSet_r16_tags_18)
		/sizeof(asn_DEF_NR_ul_FullPwrMode2_MaxSRS_ResInSet_r16_tags_18[0]) - 1, /* 1 */
	asn_DEF_NR_ul_FullPwrMode2_MaxSRS_ResInSet_r16_tags_18,	/* Same as above */
	sizeof(asn_DEF_NR_ul_FullPwrMode2_MaxSRS_ResInSet_r16_tags_18)
		/sizeof(asn_DEF_NR_ul_FullPwrMode2_MaxSRS_ResInSet_r16_tags_18[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ul_FullPwrMode2_MaxSRS_ResInSet_r16_constr_18,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ul_FullPwrMode2_MaxSRS_ResInSet_r16_specs_18	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_scs_15kHz_r16_value2enum_23[] = {
	{ 0,	9,	"one-pusch" },
	{ 1,	5,	"upto2" },
	{ 2,	5,	"upto4" },
	{ 3,	5,	"upto7" }
};
static const unsigned int asn_MAP_NR_scs_15kHz_r16_enum2value_23[] = {
	0,	/* one-pusch(0) */
	1,	/* upto2(1) */
	2,	/* upto4(2) */
	3	/* upto7(3) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_scs_15kHz_r16_specs_23 = {
	asn_MAP_NR_scs_15kHz_r16_value2enum_23,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_scs_15kHz_r16_enum2value_23,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_scs_15kHz_r16_tags_23[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_scs_15kHz_r16_23 = {
	"scs-15kHz-r16",
	"scs-15kHz-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_scs_15kHz_r16_tags_23,
	sizeof(asn_DEF_NR_scs_15kHz_r16_tags_23)
		/sizeof(asn_DEF_NR_scs_15kHz_r16_tags_23[0]) - 1, /* 1 */
	asn_DEF_NR_scs_15kHz_r16_tags_23,	/* Same as above */
	sizeof(asn_DEF_NR_scs_15kHz_r16_tags_23)
		/sizeof(asn_DEF_NR_scs_15kHz_r16_tags_23[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_scs_15kHz_r16_constr_23,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_scs_15kHz_r16_specs_23	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_scs_30kHz_r16_value2enum_28[] = {
	{ 0,	9,	"one-pusch" },
	{ 1,	5,	"upto2" },
	{ 2,	5,	"upto4" },
	{ 3,	5,	"upto7" }
};
static const unsigned int asn_MAP_NR_scs_30kHz_r16_enum2value_28[] = {
	0,	/* one-pusch(0) */
	1,	/* upto2(1) */
	2,	/* upto4(2) */
	3	/* upto7(3) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_scs_30kHz_r16_specs_28 = {
	asn_MAP_NR_scs_30kHz_r16_value2enum_28,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_scs_30kHz_r16_enum2value_28,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_scs_30kHz_r16_tags_28[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_scs_30kHz_r16_28 = {
	"scs-30kHz-r16",
	"scs-30kHz-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_scs_30kHz_r16_tags_28,
	sizeof(asn_DEF_NR_scs_30kHz_r16_tags_28)
		/sizeof(asn_DEF_NR_scs_30kHz_r16_tags_28[0]) - 1, /* 1 */
	asn_DEF_NR_scs_30kHz_r16_tags_28,	/* Same as above */
	sizeof(asn_DEF_NR_scs_30kHz_r16_tags_28)
		/sizeof(asn_DEF_NR_scs_30kHz_r16_tags_28[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_scs_30kHz_r16_constr_28,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_scs_30kHz_r16_specs_28	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_scs_60kHz_r16_value2enum_33[] = {
	{ 0,	9,	"one-pusch" },
	{ 1,	5,	"upto2" },
	{ 2,	5,	"upto4" },
	{ 3,	5,	"upto7" }
};
static const unsigned int asn_MAP_NR_scs_60kHz_r16_enum2value_33[] = {
	0,	/* one-pusch(0) */
	1,	/* upto2(1) */
	2,	/* upto4(2) */
	3	/* upto7(3) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_scs_60kHz_r16_specs_33 = {
	asn_MAP_NR_scs_60kHz_r16_value2enum_33,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_scs_60kHz_r16_enum2value_33,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_scs_60kHz_r16_tags_33[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_scs_60kHz_r16_33 = {
	"scs-60kHz-r16",
	"scs-60kHz-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_scs_60kHz_r16_tags_33,
	sizeof(asn_DEF_NR_scs_60kHz_r16_tags_33)
		/sizeof(asn_DEF_NR_scs_60kHz_r16_tags_33[0]) - 1, /* 1 */
	asn_DEF_NR_scs_60kHz_r16_tags_33,	/* Same as above */
	sizeof(asn_DEF_NR_scs_60kHz_r16_tags_33)
		/sizeof(asn_DEF_NR_scs_60kHz_r16_tags_33[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_scs_60kHz_r16_constr_33,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_scs_60kHz_r16_specs_33	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_scs_120kHz_r16_value2enum_38[] = {
	{ 0,	9,	"one-pusch" },
	{ 1,	5,	"upto2" },
	{ 2,	5,	"upto4" },
	{ 3,	5,	"upto7" }
};
static const unsigned int asn_MAP_NR_scs_120kHz_r16_enum2value_38[] = {
	0,	/* one-pusch(0) */
	1,	/* upto2(1) */
	2,	/* upto4(2) */
	3	/* upto7(3) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_scs_120kHz_r16_specs_38 = {
	asn_MAP_NR_scs_120kHz_r16_value2enum_38,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_scs_120kHz_r16_enum2value_38,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_scs_120kHz_r16_tags_38[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_scs_120kHz_r16_38 = {
	"scs-120kHz-r16",
	"scs-120kHz-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_scs_120kHz_r16_tags_38,
	sizeof(asn_DEF_NR_scs_120kHz_r16_tags_38)
		/sizeof(asn_DEF_NR_scs_120kHz_r16_tags_38[0]) - 1, /* 1 */
	asn_DEF_NR_scs_120kHz_r16_tags_38,	/* Same as above */
	sizeof(asn_DEF_NR_scs_120kHz_r16_tags_38)
		/sizeof(asn_DEF_NR_scs_120kHz_r16_tags_38[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_scs_120kHz_r16_constr_38,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_scs_120kHz_r16_specs_38	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16_22[] = {
	{ ATF_POINTER, 4, offsetof(struct NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16, scs_15kHz_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_scs_15kHz_r16_23,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs-15kHz-r16"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16, scs_30kHz_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_scs_30kHz_r16_28,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs-30kHz-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16, scs_60kHz_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_scs_60kHz_r16_33,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs-60kHz-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16, scs_120kHz_r16),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_scs_120kHz_r16_38,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs-120kHz-r16"
		},
};
static const int asn_MAP_NR_cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16_oms_22[] = { 0, 1, 2, 3 };
static const ber_tlv_tag_t asn_DEF_NR_cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16_tags_22[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16_tag2el_22[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* scs-15kHz-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* scs-30kHz-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* scs-60kHz-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 } /* scs-120kHz-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16_specs_22 = {
	sizeof(struct NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16),
	offsetof(struct NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16, _asn_ctx),
	asn_MAP_NR_cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16_tag2el_22,
	4,	/* Count of tags in the map */
	asn_MAP_NR_cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16_oms_22,	/* Optional members */
	4, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16_22 = {
	"cbgPUSCH-ProcessingType1-DifferentTB-PerSlot-r16",
	"cbgPUSCH-ProcessingType1-DifferentTB-PerSlot-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16_tags_22,
	sizeof(asn_DEF_NR_cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16_tags_22)
		/sizeof(asn_DEF_NR_cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16_tags_22[0]) - 1, /* 1 */
	asn_DEF_NR_cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16_tags_22,	/* Same as above */
	sizeof(asn_DEF_NR_cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16_tags_22)
		/sizeof(asn_DEF_NR_cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16_tags_22[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16_22,
	4,	/* Elements count */
	&asn_SPC_NR_cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16_specs_22	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_scs_15kHz_r16_value2enum_44[] = {
	{ 0,	9,	"one-pusch" },
	{ 1,	5,	"upto2" },
	{ 2,	5,	"upto4" },
	{ 3,	5,	"upto7" }
};
static const unsigned int asn_MAP_NR_scs_15kHz_r16_enum2value_44[] = {
	0,	/* one-pusch(0) */
	1,	/* upto2(1) */
	2,	/* upto4(2) */
	3	/* upto7(3) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_scs_15kHz_r16_specs_44 = {
	asn_MAP_NR_scs_15kHz_r16_value2enum_44,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_scs_15kHz_r16_enum2value_44,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_scs_15kHz_r16_tags_44[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_scs_15kHz_r16_44 = {
	"scs-15kHz-r16",
	"scs-15kHz-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_scs_15kHz_r16_tags_44,
	sizeof(asn_DEF_NR_scs_15kHz_r16_tags_44)
		/sizeof(asn_DEF_NR_scs_15kHz_r16_tags_44[0]) - 1, /* 1 */
	asn_DEF_NR_scs_15kHz_r16_tags_44,	/* Same as above */
	sizeof(asn_DEF_NR_scs_15kHz_r16_tags_44)
		/sizeof(asn_DEF_NR_scs_15kHz_r16_tags_44[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_scs_15kHz_r16_constr_44,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_scs_15kHz_r16_specs_44	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_scs_30kHz_r16_value2enum_49[] = {
	{ 0,	9,	"one-pusch" },
	{ 1,	5,	"upto2" },
	{ 2,	5,	"upto4" },
	{ 3,	5,	"upto7" }
};
static const unsigned int asn_MAP_NR_scs_30kHz_r16_enum2value_49[] = {
	0,	/* one-pusch(0) */
	1,	/* upto2(1) */
	2,	/* upto4(2) */
	3	/* upto7(3) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_scs_30kHz_r16_specs_49 = {
	asn_MAP_NR_scs_30kHz_r16_value2enum_49,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_scs_30kHz_r16_enum2value_49,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_scs_30kHz_r16_tags_49[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_scs_30kHz_r16_49 = {
	"scs-30kHz-r16",
	"scs-30kHz-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_scs_30kHz_r16_tags_49,
	sizeof(asn_DEF_NR_scs_30kHz_r16_tags_49)
		/sizeof(asn_DEF_NR_scs_30kHz_r16_tags_49[0]) - 1, /* 1 */
	asn_DEF_NR_scs_30kHz_r16_tags_49,	/* Same as above */
	sizeof(asn_DEF_NR_scs_30kHz_r16_tags_49)
		/sizeof(asn_DEF_NR_scs_30kHz_r16_tags_49[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_scs_30kHz_r16_constr_49,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_scs_30kHz_r16_specs_49	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_scs_60kHz_r16_value2enum_54[] = {
	{ 0,	9,	"one-pusch" },
	{ 1,	5,	"upto2" },
	{ 2,	5,	"upto4" },
	{ 3,	5,	"upto7" }
};
static const unsigned int asn_MAP_NR_scs_60kHz_r16_enum2value_54[] = {
	0,	/* one-pusch(0) */
	1,	/* upto2(1) */
	2,	/* upto4(2) */
	3	/* upto7(3) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_scs_60kHz_r16_specs_54 = {
	asn_MAP_NR_scs_60kHz_r16_value2enum_54,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_scs_60kHz_r16_enum2value_54,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_scs_60kHz_r16_tags_54[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_scs_60kHz_r16_54 = {
	"scs-60kHz-r16",
	"scs-60kHz-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_scs_60kHz_r16_tags_54,
	sizeof(asn_DEF_NR_scs_60kHz_r16_tags_54)
		/sizeof(asn_DEF_NR_scs_60kHz_r16_tags_54[0]) - 1, /* 1 */
	asn_DEF_NR_scs_60kHz_r16_tags_54,	/* Same as above */
	sizeof(asn_DEF_NR_scs_60kHz_r16_tags_54)
		/sizeof(asn_DEF_NR_scs_60kHz_r16_tags_54[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_scs_60kHz_r16_constr_54,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_scs_60kHz_r16_specs_54	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_scs_120kHz_r16_value2enum_59[] = {
	{ 0,	9,	"one-pusch" },
	{ 1,	5,	"upto2" },
	{ 2,	5,	"upto4" },
	{ 3,	5,	"upto7" }
};
static const unsigned int asn_MAP_NR_scs_120kHz_r16_enum2value_59[] = {
	0,	/* one-pusch(0) */
	1,	/* upto2(1) */
	2,	/* upto4(2) */
	3	/* upto7(3) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_scs_120kHz_r16_specs_59 = {
	asn_MAP_NR_scs_120kHz_r16_value2enum_59,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_scs_120kHz_r16_enum2value_59,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_scs_120kHz_r16_tags_59[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_scs_120kHz_r16_59 = {
	"scs-120kHz-r16",
	"scs-120kHz-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_scs_120kHz_r16_tags_59,
	sizeof(asn_DEF_NR_scs_120kHz_r16_tags_59)
		/sizeof(asn_DEF_NR_scs_120kHz_r16_tags_59[0]) - 1, /* 1 */
	asn_DEF_NR_scs_120kHz_r16_tags_59,	/* Same as above */
	sizeof(asn_DEF_NR_scs_120kHz_r16_tags_59)
		/sizeof(asn_DEF_NR_scs_120kHz_r16_tags_59[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_scs_120kHz_r16_constr_59,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_scs_120kHz_r16_specs_59	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16_43[] = {
	{ ATF_POINTER, 4, offsetof(struct NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16, scs_15kHz_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_scs_15kHz_r16_44,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs-15kHz-r16"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16, scs_30kHz_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_scs_30kHz_r16_49,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs-30kHz-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16, scs_60kHz_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_scs_60kHz_r16_54,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs-60kHz-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16, scs_120kHz_r16),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_scs_120kHz_r16_59,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs-120kHz-r16"
		},
};
static const int asn_MAP_NR_cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16_oms_43[] = { 0, 1, 2, 3 };
static const ber_tlv_tag_t asn_DEF_NR_cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16_tags_43[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16_tag2el_43[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* scs-15kHz-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* scs-30kHz-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* scs-60kHz-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 } /* scs-120kHz-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16_specs_43 = {
	sizeof(struct NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16),
	offsetof(struct NR_FeatureSetUplink_v1610__cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16, _asn_ctx),
	asn_MAP_NR_cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16_tag2el_43,
	4,	/* Count of tags in the map */
	asn_MAP_NR_cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16_oms_43,	/* Optional members */
	4, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16_43 = {
	"cbgPUSCH-ProcessingType2-DifferentTB-PerSlot-r16",
	"cbgPUSCH-ProcessingType2-DifferentTB-PerSlot-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16_tags_43,
	sizeof(asn_DEF_NR_cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16_tags_43)
		/sizeof(asn_DEF_NR_cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16_tags_43[0]) - 1, /* 1 */
	asn_DEF_NR_cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16_tags_43,	/* Same as above */
	sizeof(asn_DEF_NR_cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16_tags_43)
		/sizeof(asn_DEF_NR_cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16_tags_43[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16_43,
	4,	/* Elements count */
	&asn_SPC_NR_cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16_specs_43	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dummy_value2enum_66[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_dummy_enum2value_66[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dummy_specs_66 = {
	asn_MAP_NR_dummy_value2enum_66,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dummy_enum2value_66,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dummy_tags_66[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dummy_66 = {
	"dummy",
	"dummy",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dummy_tags_66,
	sizeof(asn_DEF_NR_dummy_tags_66)
		/sizeof(asn_DEF_NR_dummy_tags_66[0]) - 1, /* 1 */
	asn_DEF_NR_dummy_tags_66,	/* Same as above */
	sizeof(asn_DEF_NR_dummy_tags_66)
		/sizeof(asn_DEF_NR_dummy_tags_66[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dummy_constr_66,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dummy_specs_66	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_intraFreqTwoTAGs_DAPS_r16_value2enum_68[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_intraFreqTwoTAGs_DAPS_r16_enum2value_68[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_intraFreqTwoTAGs_DAPS_r16_specs_68 = {
	asn_MAP_NR_intraFreqTwoTAGs_DAPS_r16_value2enum_68,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_intraFreqTwoTAGs_DAPS_r16_enum2value_68,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_intraFreqTwoTAGs_DAPS_r16_tags_68[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_intraFreqTwoTAGs_DAPS_r16_68 = {
	"intraFreqTwoTAGs-DAPS-r16",
	"intraFreqTwoTAGs-DAPS-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_intraFreqTwoTAGs_DAPS_r16_tags_68,
	sizeof(asn_DEF_NR_intraFreqTwoTAGs_DAPS_r16_tags_68)
		/sizeof(asn_DEF_NR_intraFreqTwoTAGs_DAPS_r16_tags_68[0]) - 1, /* 1 */
	asn_DEF_NR_intraFreqTwoTAGs_DAPS_r16_tags_68,	/* Same as above */
	sizeof(asn_DEF_NR_intraFreqTwoTAGs_DAPS_r16_tags_68)
		/sizeof(asn_DEF_NR_intraFreqTwoTAGs_DAPS_r16_tags_68[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_intraFreqTwoTAGs_DAPS_r16_constr_68,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_intraFreqTwoTAGs_DAPS_r16_specs_68	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dummy1_value2enum_70[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_dummy1_enum2value_70[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dummy1_specs_70 = {
	asn_MAP_NR_dummy1_value2enum_70,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dummy1_enum2value_70,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dummy1_tags_70[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dummy1_70 = {
	"dummy1",
	"dummy1",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dummy1_tags_70,
	sizeof(asn_DEF_NR_dummy1_tags_70)
		/sizeof(asn_DEF_NR_dummy1_tags_70[0]) - 1, /* 1 */
	asn_DEF_NR_dummy1_tags_70,	/* Same as above */
	sizeof(asn_DEF_NR_dummy1_tags_70)
		/sizeof(asn_DEF_NR_dummy1_tags_70[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dummy1_constr_70,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dummy1_specs_70	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dummy2_value2enum_72[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_dummy2_enum2value_72[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dummy2_specs_72 = {
	asn_MAP_NR_dummy2_value2enum_72,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dummy2_enum2value_72,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dummy2_tags_72[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dummy2_72 = {
	"dummy2",
	"dummy2",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dummy2_tags_72,
	sizeof(asn_DEF_NR_dummy2_tags_72)
		/sizeof(asn_DEF_NR_dummy2_tags_72[0]) - 1, /* 1 */
	asn_DEF_NR_dummy2_tags_72,	/* Same as above */
	sizeof(asn_DEF_NR_dummy2_tags_72)
		/sizeof(asn_DEF_NR_dummy2_tags_72[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dummy2_constr_72,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dummy2_specs_72	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dummy3_value2enum_74[] = {
	{ 0,	5,	"short" },
	{ 1,	4,	"long" }
};
static const unsigned int asn_MAP_NR_dummy3_enum2value_74[] = {
	1,	/* long(1) */
	0	/* short(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dummy3_specs_74 = {
	asn_MAP_NR_dummy3_value2enum_74,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dummy3_enum2value_74,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dummy3_tags_74[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dummy3_74 = {
	"dummy3",
	"dummy3",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dummy3_tags_74,
	sizeof(asn_DEF_NR_dummy3_tags_74)
		/sizeof(asn_DEF_NR_dummy3_tags_74[0]) - 1, /* 1 */
	asn_DEF_NR_dummy3_tags_74,	/* Same as above */
	sizeof(asn_DEF_NR_dummy3_tags_74)
		/sizeof(asn_DEF_NR_dummy3_tags_74[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dummy3_constr_74,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dummy3_specs_74	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_intraFreqDAPS_UL_r16_65[] = {
	{ ATF_POINTER, 5, offsetof(struct NR_FeatureSetUplink_v1610__intraFreqDAPS_UL_r16, dummy),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dummy_66,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dummy"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_FeatureSetUplink_v1610__intraFreqDAPS_UL_r16, intraFreqTwoTAGs_DAPS_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_intraFreqTwoTAGs_DAPS_r16_68,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"intraFreqTwoTAGs-DAPS-r16"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_FeatureSetUplink_v1610__intraFreqDAPS_UL_r16, dummy1),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dummy1_70,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dummy1"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_FeatureSetUplink_v1610__intraFreqDAPS_UL_r16, dummy2),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dummy2_72,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dummy2"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_FeatureSetUplink_v1610__intraFreqDAPS_UL_r16, dummy3),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dummy3_74,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dummy3"
		},
};
static const int asn_MAP_NR_intraFreqDAPS_UL_r16_oms_65[] = { 0, 1, 2, 3, 4 };
static const ber_tlv_tag_t asn_DEF_NR_intraFreqDAPS_UL_r16_tags_65[] = {
	(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_intraFreqDAPS_UL_r16_tag2el_65[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* dummy */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* intraFreqTwoTAGs-DAPS-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* dummy1 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* dummy2 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 } /* dummy3 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_intraFreqDAPS_UL_r16_specs_65 = {
	sizeof(struct NR_FeatureSetUplink_v1610__intraFreqDAPS_UL_r16),
	offsetof(struct NR_FeatureSetUplink_v1610__intraFreqDAPS_UL_r16, _asn_ctx),
	asn_MAP_NR_intraFreqDAPS_UL_r16_tag2el_65,
	5,	/* Count of tags in the map */
	asn_MAP_NR_intraFreqDAPS_UL_r16_oms_65,	/* Optional members */
	5, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_intraFreqDAPS_UL_r16_65 = {
	"intraFreqDAPS-UL-r16",
	"intraFreqDAPS-UL-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_intraFreqDAPS_UL_r16_tags_65,
	sizeof(asn_DEF_NR_intraFreqDAPS_UL_r16_tags_65)
		/sizeof(asn_DEF_NR_intraFreqDAPS_UL_r16_tags_65[0]) - 1, /* 1 */
	asn_DEF_NR_intraFreqDAPS_UL_r16_tags_65,	/* Same as above */
	sizeof(asn_DEF_NR_intraFreqDAPS_UL_r16_tags_65)
		/sizeof(asn_DEF_NR_intraFreqDAPS_UL_r16_tags_65[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_intraFreqDAPS_UL_r16_65,
	5,	/* Elements count */
	&asn_SPC_NR_intraFreqDAPS_UL_r16_specs_65	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_sub_SlotConfig_NCP_r16_value2enum_79[] = {
	{ 0,	4,	"set1" },
	{ 1,	4,	"set2" }
};
static const unsigned int asn_MAP_NR_sub_SlotConfig_NCP_r16_enum2value_79[] = {
	0,	/* set1(0) */
	1	/* set2(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_sub_SlotConfig_NCP_r16_specs_79 = {
	asn_MAP_NR_sub_SlotConfig_NCP_r16_value2enum_79,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_sub_SlotConfig_NCP_r16_enum2value_79,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_sub_SlotConfig_NCP_r16_tags_79[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_sub_SlotConfig_NCP_r16_79 = {
	"sub-SlotConfig-NCP-r16",
	"sub-SlotConfig-NCP-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_sub_SlotConfig_NCP_r16_tags_79,
	sizeof(asn_DEF_NR_sub_SlotConfig_NCP_r16_tags_79)
		/sizeof(asn_DEF_NR_sub_SlotConfig_NCP_r16_tags_79[0]) - 1, /* 1 */
	asn_DEF_NR_sub_SlotConfig_NCP_r16_tags_79,	/* Same as above */
	sizeof(asn_DEF_NR_sub_SlotConfig_NCP_r16_tags_79)
		/sizeof(asn_DEF_NR_sub_SlotConfig_NCP_r16_tags_79[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_sub_SlotConfig_NCP_r16_constr_79,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_sub_SlotConfig_NCP_r16_specs_79	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_sub_SlotConfig_ECP_r16_value2enum_82[] = {
	{ 0,	4,	"set1" },
	{ 1,	4,	"set2" }
};
static const unsigned int asn_MAP_NR_sub_SlotConfig_ECP_r16_enum2value_82[] = {
	0,	/* set1(0) */
	1	/* set2(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_sub_SlotConfig_ECP_r16_specs_82 = {
	asn_MAP_NR_sub_SlotConfig_ECP_r16_value2enum_82,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_sub_SlotConfig_ECP_r16_enum2value_82,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_sub_SlotConfig_ECP_r16_tags_82[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_sub_SlotConfig_ECP_r16_82 = {
	"sub-SlotConfig-ECP-r16",
	"sub-SlotConfig-ECP-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_sub_SlotConfig_ECP_r16_tags_82,
	sizeof(asn_DEF_NR_sub_SlotConfig_ECP_r16_tags_82)
		/sizeof(asn_DEF_NR_sub_SlotConfig_ECP_r16_tags_82[0]) - 1, /* 1 */
	asn_DEF_NR_sub_SlotConfig_ECP_r16_tags_82,	/* Same as above */
	sizeof(asn_DEF_NR_sub_SlotConfig_ECP_r16_tags_82)
		/sizeof(asn_DEF_NR_sub_SlotConfig_ECP_r16_tags_82[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_sub_SlotConfig_ECP_r16_constr_82,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_sub_SlotConfig_ECP_r16_specs_82	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_multiPUCCH_r16_78[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_FeatureSetUplink_v1610__multiPUCCH_r16, sub_SlotConfig_NCP_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_sub_SlotConfig_NCP_r16_79,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"sub-SlotConfig-NCP-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_FeatureSetUplink_v1610__multiPUCCH_r16, sub_SlotConfig_ECP_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_sub_SlotConfig_ECP_r16_82,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"sub-SlotConfig-ECP-r16"
		},
};
static const int asn_MAP_NR_multiPUCCH_r16_oms_78[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_multiPUCCH_r16_tags_78[] = {
	(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_multiPUCCH_r16_tag2el_78[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* sub-SlotConfig-NCP-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* sub-SlotConfig-ECP-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_multiPUCCH_r16_specs_78 = {
	sizeof(struct NR_FeatureSetUplink_v1610__multiPUCCH_r16),
	offsetof(struct NR_FeatureSetUplink_v1610__multiPUCCH_r16, _asn_ctx),
	asn_MAP_NR_multiPUCCH_r16_tag2el_78,
	2,	/* Count of tags in the map */
	asn_MAP_NR_multiPUCCH_r16_oms_78,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_multiPUCCH_r16_78 = {
	"multiPUCCH-r16",
	"multiPUCCH-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_multiPUCCH_r16_tags_78,
	sizeof(asn_DEF_NR_multiPUCCH_r16_tags_78)
		/sizeof(asn_DEF_NR_multiPUCCH_r16_tags_78[0]) - 1, /* 1 */
	asn_DEF_NR_multiPUCCH_r16_tags_78,	/* Same as above */
	sizeof(asn_DEF_NR_multiPUCCH_r16_tags_78)
		/sizeof(asn_DEF_NR_multiPUCCH_r16_tags_78[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_multiPUCCH_r16_78,
	2,	/* Elements count */
	&asn_SPC_NR_multiPUCCH_r16_specs_78	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_twoPUCCH_Type1_r16_value2enum_85[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_twoPUCCH_Type1_r16_enum2value_85[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_twoPUCCH_Type1_r16_specs_85 = {
	asn_MAP_NR_twoPUCCH_Type1_r16_value2enum_85,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_twoPUCCH_Type1_r16_enum2value_85,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_twoPUCCH_Type1_r16_tags_85[] = {
	(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_twoPUCCH_Type1_r16_85 = {
	"twoPUCCH-Type1-r16",
	"twoPUCCH-Type1-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_twoPUCCH_Type1_r16_tags_85,
	sizeof(asn_DEF_NR_twoPUCCH_Type1_r16_tags_85)
		/sizeof(asn_DEF_NR_twoPUCCH_Type1_r16_tags_85[0]) - 1, /* 1 */
	asn_DEF_NR_twoPUCCH_Type1_r16_tags_85,	/* Same as above */
	sizeof(asn_DEF_NR_twoPUCCH_Type1_r16_tags_85)
		/sizeof(asn_DEF_NR_twoPUCCH_Type1_r16_tags_85[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_twoPUCCH_Type1_r16_constr_85,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_twoPUCCH_Type1_r16_specs_85	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_twoPUCCH_Type2_r16_value2enum_87[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_twoPUCCH_Type2_r16_enum2value_87[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_twoPUCCH_Type2_r16_specs_87 = {
	asn_MAP_NR_twoPUCCH_Type2_r16_value2enum_87,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_twoPUCCH_Type2_r16_enum2value_87,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_twoPUCCH_Type2_r16_tags_87[] = {
	(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_twoPUCCH_Type2_r16_87 = {
	"twoPUCCH-Type2-r16",
	"twoPUCCH-Type2-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_twoPUCCH_Type2_r16_tags_87,
	sizeof(asn_DEF_NR_twoPUCCH_Type2_r16_tags_87)
		/sizeof(asn_DEF_NR_twoPUCCH_Type2_r16_tags_87[0]) - 1, /* 1 */
	asn_DEF_NR_twoPUCCH_Type2_r16_tags_87,	/* Same as above */
	sizeof(asn_DEF_NR_twoPUCCH_Type2_r16_tags_87)
		/sizeof(asn_DEF_NR_twoPUCCH_Type2_r16_tags_87[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_twoPUCCH_Type2_r16_constr_87,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_twoPUCCH_Type2_r16_specs_87	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_twoPUCCH_Type3_r16_value2enum_89[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_twoPUCCH_Type3_r16_enum2value_89[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_twoPUCCH_Type3_r16_specs_89 = {
	asn_MAP_NR_twoPUCCH_Type3_r16_value2enum_89,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_twoPUCCH_Type3_r16_enum2value_89,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_twoPUCCH_Type3_r16_tags_89[] = {
	(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_twoPUCCH_Type3_r16_89 = {
	"twoPUCCH-Type3-r16",
	"twoPUCCH-Type3-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_twoPUCCH_Type3_r16_tags_89,
	sizeof(asn_DEF_NR_twoPUCCH_Type3_r16_tags_89)
		/sizeof(asn_DEF_NR_twoPUCCH_Type3_r16_tags_89[0]) - 1, /* 1 */
	asn_DEF_NR_twoPUCCH_Type3_r16_tags_89,	/* Same as above */
	sizeof(asn_DEF_NR_twoPUCCH_Type3_r16_tags_89)
		/sizeof(asn_DEF_NR_twoPUCCH_Type3_r16_tags_89[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_twoPUCCH_Type3_r16_constr_89,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_twoPUCCH_Type3_r16_specs_89	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_twoPUCCH_Type4_r16_value2enum_91[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_twoPUCCH_Type4_r16_enum2value_91[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_twoPUCCH_Type4_r16_specs_91 = {
	asn_MAP_NR_twoPUCCH_Type4_r16_value2enum_91,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_twoPUCCH_Type4_r16_enum2value_91,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_twoPUCCH_Type4_r16_tags_91[] = {
	(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_twoPUCCH_Type4_r16_91 = {
	"twoPUCCH-Type4-r16",
	"twoPUCCH-Type4-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_twoPUCCH_Type4_r16_tags_91,
	sizeof(asn_DEF_NR_twoPUCCH_Type4_r16_tags_91)
		/sizeof(asn_DEF_NR_twoPUCCH_Type4_r16_tags_91[0]) - 1, /* 1 */
	asn_DEF_NR_twoPUCCH_Type4_r16_tags_91,	/* Same as above */
	sizeof(asn_DEF_NR_twoPUCCH_Type4_r16_tags_91)
		/sizeof(asn_DEF_NR_twoPUCCH_Type4_r16_tags_91[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_twoPUCCH_Type4_r16_constr_91,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_twoPUCCH_Type4_r16_specs_91	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_mux_SR_HARQ_ACK_r16_value2enum_93[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_mux_SR_HARQ_ACK_r16_enum2value_93[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_mux_SR_HARQ_ACK_r16_specs_93 = {
	asn_MAP_NR_mux_SR_HARQ_ACK_r16_value2enum_93,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_mux_SR_HARQ_ACK_r16_enum2value_93,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_mux_SR_HARQ_ACK_r16_tags_93[] = {
	(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_mux_SR_HARQ_ACK_r16_93 = {
	"mux-SR-HARQ-ACK-r16",
	"mux-SR-HARQ-ACK-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_mux_SR_HARQ_ACK_r16_tags_93,
	sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_r16_tags_93)
		/sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_r16_tags_93[0]) - 1, /* 1 */
	asn_DEF_NR_mux_SR_HARQ_ACK_r16_tags_93,	/* Same as above */
	sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_r16_tags_93)
		/sizeof(asn_DEF_NR_mux_SR_HARQ_ACK_r16_tags_93[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_mux_SR_HARQ_ACK_r16_constr_93,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_mux_SR_HARQ_ACK_r16_specs_93	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dummy1_value2enum_95[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_dummy1_enum2value_95[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dummy1_specs_95 = {
	asn_MAP_NR_dummy1_value2enum_95,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dummy1_enum2value_95,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dummy1_tags_95[] = {
	(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dummy1_95 = {
	"dummy1",
	"dummy1",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dummy1_tags_95,
	sizeof(asn_DEF_NR_dummy1_tags_95)
		/sizeof(asn_DEF_NR_dummy1_tags_95[0]) - 1, /* 1 */
	asn_DEF_NR_dummy1_tags_95,	/* Same as above */
	sizeof(asn_DEF_NR_dummy1_tags_95)
		/sizeof(asn_DEF_NR_dummy1_tags_95[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dummy1_constr_95,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dummy1_specs_95	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_dummy2_value2enum_97[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_dummy2_enum2value_97[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_dummy2_specs_97 = {
	asn_MAP_NR_dummy2_value2enum_97,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_dummy2_enum2value_97,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_dummy2_tags_97[] = {
	(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_dummy2_97 = {
	"dummy2",
	"dummy2",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_dummy2_tags_97,
	sizeof(asn_DEF_NR_dummy2_tags_97)
		/sizeof(asn_DEF_NR_dummy2_tags_97[0]) - 1, /* 1 */
	asn_DEF_NR_dummy2_tags_97,	/* Same as above */
	sizeof(asn_DEF_NR_dummy2_tags_97)
		/sizeof(asn_DEF_NR_dummy2_tags_97[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_dummy2_constr_97,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_dummy2_specs_97	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_twoPUCCH_Type5_r16_value2enum_99[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_twoPUCCH_Type5_r16_enum2value_99[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_twoPUCCH_Type5_r16_specs_99 = {
	asn_MAP_NR_twoPUCCH_Type5_r16_value2enum_99,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_twoPUCCH_Type5_r16_enum2value_99,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_twoPUCCH_Type5_r16_tags_99[] = {
	(ASN_TAG_CLASS_CONTEXT | (17 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_twoPUCCH_Type5_r16_99 = {
	"twoPUCCH-Type5-r16",
	"twoPUCCH-Type5-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_twoPUCCH_Type5_r16_tags_99,
	sizeof(asn_DEF_NR_twoPUCCH_Type5_r16_tags_99)
		/sizeof(asn_DEF_NR_twoPUCCH_Type5_r16_tags_99[0]) - 1, /* 1 */
	asn_DEF_NR_twoPUCCH_Type5_r16_tags_99,	/* Same as above */
	sizeof(asn_DEF_NR_twoPUCCH_Type5_r16_tags_99)
		/sizeof(asn_DEF_NR_twoPUCCH_Type5_r16_tags_99[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_twoPUCCH_Type5_r16_constr_99,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_twoPUCCH_Type5_r16_specs_99	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_twoPUCCH_Type6_r16_value2enum_101[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_twoPUCCH_Type6_r16_enum2value_101[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_twoPUCCH_Type6_r16_specs_101 = {
	asn_MAP_NR_twoPUCCH_Type6_r16_value2enum_101,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_twoPUCCH_Type6_r16_enum2value_101,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_twoPUCCH_Type6_r16_tags_101[] = {
	(ASN_TAG_CLASS_CONTEXT | (18 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_twoPUCCH_Type6_r16_101 = {
	"twoPUCCH-Type6-r16",
	"twoPUCCH-Type6-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_twoPUCCH_Type6_r16_tags_101,
	sizeof(asn_DEF_NR_twoPUCCH_Type6_r16_tags_101)
		/sizeof(asn_DEF_NR_twoPUCCH_Type6_r16_tags_101[0]) - 1, /* 1 */
	asn_DEF_NR_twoPUCCH_Type6_r16_tags_101,	/* Same as above */
	sizeof(asn_DEF_NR_twoPUCCH_Type6_r16_tags_101)
		/sizeof(asn_DEF_NR_twoPUCCH_Type6_r16_tags_101[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_twoPUCCH_Type6_r16_constr_101,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_twoPUCCH_Type6_r16_specs_101	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_twoPUCCH_Type7_r16_value2enum_103[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_twoPUCCH_Type7_r16_enum2value_103[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_twoPUCCH_Type7_r16_specs_103 = {
	asn_MAP_NR_twoPUCCH_Type7_r16_value2enum_103,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_twoPUCCH_Type7_r16_enum2value_103,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_twoPUCCH_Type7_r16_tags_103[] = {
	(ASN_TAG_CLASS_CONTEXT | (19 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_twoPUCCH_Type7_r16_103 = {
	"twoPUCCH-Type7-r16",
	"twoPUCCH-Type7-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_twoPUCCH_Type7_r16_tags_103,
	sizeof(asn_DEF_NR_twoPUCCH_Type7_r16_tags_103)
		/sizeof(asn_DEF_NR_twoPUCCH_Type7_r16_tags_103[0]) - 1, /* 1 */
	asn_DEF_NR_twoPUCCH_Type7_r16_tags_103,	/* Same as above */
	sizeof(asn_DEF_NR_twoPUCCH_Type7_r16_tags_103)
		/sizeof(asn_DEF_NR_twoPUCCH_Type7_r16_tags_103[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_twoPUCCH_Type7_r16_constr_103,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_twoPUCCH_Type7_r16_specs_103	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_twoPUCCH_Type8_r16_value2enum_105[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_twoPUCCH_Type8_r16_enum2value_105[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_twoPUCCH_Type8_r16_specs_105 = {
	asn_MAP_NR_twoPUCCH_Type8_r16_value2enum_105,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_twoPUCCH_Type8_r16_enum2value_105,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_twoPUCCH_Type8_r16_tags_105[] = {
	(ASN_TAG_CLASS_CONTEXT | (20 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_twoPUCCH_Type8_r16_105 = {
	"twoPUCCH-Type8-r16",
	"twoPUCCH-Type8-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_twoPUCCH_Type8_r16_tags_105,
	sizeof(asn_DEF_NR_twoPUCCH_Type8_r16_tags_105)
		/sizeof(asn_DEF_NR_twoPUCCH_Type8_r16_tags_105[0]) - 1, /* 1 */
	asn_DEF_NR_twoPUCCH_Type8_r16_tags_105,	/* Same as above */
	sizeof(asn_DEF_NR_twoPUCCH_Type8_r16_tags_105)
		/sizeof(asn_DEF_NR_twoPUCCH_Type8_r16_tags_105[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_twoPUCCH_Type8_r16_constr_105,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_twoPUCCH_Type8_r16_specs_105	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_twoPUCCH_Type9_r16_value2enum_107[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_twoPUCCH_Type9_r16_enum2value_107[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_twoPUCCH_Type9_r16_specs_107 = {
	asn_MAP_NR_twoPUCCH_Type9_r16_value2enum_107,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_twoPUCCH_Type9_r16_enum2value_107,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_twoPUCCH_Type9_r16_tags_107[] = {
	(ASN_TAG_CLASS_CONTEXT | (21 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_twoPUCCH_Type9_r16_107 = {
	"twoPUCCH-Type9-r16",
	"twoPUCCH-Type9-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_twoPUCCH_Type9_r16_tags_107,
	sizeof(asn_DEF_NR_twoPUCCH_Type9_r16_tags_107)
		/sizeof(asn_DEF_NR_twoPUCCH_Type9_r16_tags_107[0]) - 1, /* 1 */
	asn_DEF_NR_twoPUCCH_Type9_r16_tags_107,	/* Same as above */
	sizeof(asn_DEF_NR_twoPUCCH_Type9_r16_tags_107)
		/sizeof(asn_DEF_NR_twoPUCCH_Type9_r16_tags_107[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_twoPUCCH_Type9_r16_constr_107,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_twoPUCCH_Type9_r16_specs_107	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_twoPUCCH_Type10_r16_value2enum_109[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_twoPUCCH_Type10_r16_enum2value_109[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_twoPUCCH_Type10_r16_specs_109 = {
	asn_MAP_NR_twoPUCCH_Type10_r16_value2enum_109,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_twoPUCCH_Type10_r16_enum2value_109,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_twoPUCCH_Type10_r16_tags_109[] = {
	(ASN_TAG_CLASS_CONTEXT | (22 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_twoPUCCH_Type10_r16_109 = {
	"twoPUCCH-Type10-r16",
	"twoPUCCH-Type10-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_twoPUCCH_Type10_r16_tags_109,
	sizeof(asn_DEF_NR_twoPUCCH_Type10_r16_tags_109)
		/sizeof(asn_DEF_NR_twoPUCCH_Type10_r16_tags_109[0]) - 1, /* 1 */
	asn_DEF_NR_twoPUCCH_Type10_r16_tags_109,	/* Same as above */
	sizeof(asn_DEF_NR_twoPUCCH_Type10_r16_tags_109)
		/sizeof(asn_DEF_NR_twoPUCCH_Type10_r16_tags_109[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_twoPUCCH_Type10_r16_constr_109,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_twoPUCCH_Type10_r16_specs_109	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_twoPUCCH_Type11_r16_value2enum_111[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_twoPUCCH_Type11_r16_enum2value_111[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_twoPUCCH_Type11_r16_specs_111 = {
	asn_MAP_NR_twoPUCCH_Type11_r16_value2enum_111,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_twoPUCCH_Type11_r16_enum2value_111,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_twoPUCCH_Type11_r16_tags_111[] = {
	(ASN_TAG_CLASS_CONTEXT | (23 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_twoPUCCH_Type11_r16_111 = {
	"twoPUCCH-Type11-r16",
	"twoPUCCH-Type11-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_twoPUCCH_Type11_r16_tags_111,
	sizeof(asn_DEF_NR_twoPUCCH_Type11_r16_tags_111)
		/sizeof(asn_DEF_NR_twoPUCCH_Type11_r16_tags_111[0]) - 1, /* 1 */
	asn_DEF_NR_twoPUCCH_Type11_r16_tags_111,	/* Same as above */
	sizeof(asn_DEF_NR_twoPUCCH_Type11_r16_tags_111)
		/sizeof(asn_DEF_NR_twoPUCCH_Type11_r16_tags_111[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_twoPUCCH_Type11_r16_constr_111,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_twoPUCCH_Type11_r16_specs_111	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pusch_PreparationLowPriority_r16_value2enum_114[] = {
	{ 0,	4,	"sym0" },
	{ 1,	4,	"sym1" },
	{ 2,	4,	"sym2" }
};
static const unsigned int asn_MAP_NR_pusch_PreparationLowPriority_r16_enum2value_114[] = {
	0,	/* sym0(0) */
	1,	/* sym1(1) */
	2	/* sym2(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pusch_PreparationLowPriority_r16_specs_114 = {
	asn_MAP_NR_pusch_PreparationLowPriority_r16_value2enum_114,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pusch_PreparationLowPriority_r16_enum2value_114,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pusch_PreparationLowPriority_r16_tags_114[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pusch_PreparationLowPriority_r16_114 = {
	"pusch-PreparationLowPriority-r16",
	"pusch-PreparationLowPriority-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pusch_PreparationLowPriority_r16_tags_114,
	sizeof(asn_DEF_NR_pusch_PreparationLowPriority_r16_tags_114)
		/sizeof(asn_DEF_NR_pusch_PreparationLowPriority_r16_tags_114[0]) - 1, /* 1 */
	asn_DEF_NR_pusch_PreparationLowPriority_r16_tags_114,	/* Same as above */
	sizeof(asn_DEF_NR_pusch_PreparationLowPriority_r16_tags_114)
		/sizeof(asn_DEF_NR_pusch_PreparationLowPriority_r16_tags_114[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pusch_PreparationLowPriority_r16_constr_114,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pusch_PreparationLowPriority_r16_specs_114	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_pusch_PreparationHighPriority_r16_value2enum_118[] = {
	{ 0,	4,	"sym0" },
	{ 1,	4,	"sym1" },
	{ 2,	4,	"sym2" }
};
static const unsigned int asn_MAP_NR_pusch_PreparationHighPriority_r16_enum2value_118[] = {
	0,	/* sym0(0) */
	1,	/* sym1(1) */
	2	/* sym2(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_pusch_PreparationHighPriority_r16_specs_118 = {
	asn_MAP_NR_pusch_PreparationHighPriority_r16_value2enum_118,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_pusch_PreparationHighPriority_r16_enum2value_118,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_pusch_PreparationHighPriority_r16_tags_118[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_pusch_PreparationHighPriority_r16_118 = {
	"pusch-PreparationHighPriority-r16",
	"pusch-PreparationHighPriority-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_pusch_PreparationHighPriority_r16_tags_118,
	sizeof(asn_DEF_NR_pusch_PreparationHighPriority_r16_tags_118)
		/sizeof(asn_DEF_NR_pusch_PreparationHighPriority_r16_tags_118[0]) - 1, /* 1 */
	asn_DEF_NR_pusch_PreparationHighPriority_r16_tags_118,	/* Same as above */
	sizeof(asn_DEF_NR_pusch_PreparationHighPriority_r16_tags_118)
		/sizeof(asn_DEF_NR_pusch_PreparationHighPriority_r16_tags_118[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_pusch_PreparationHighPriority_r16_constr_118,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_pusch_PreparationHighPriority_r16_specs_118	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ul_IntraUE_Mux_r16_113[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_FeatureSetUplink_v1610__ul_IntraUE_Mux_r16, pusch_PreparationLowPriority_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pusch_PreparationLowPriority_r16_114,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pusch-PreparationLowPriority-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_FeatureSetUplink_v1610__ul_IntraUE_Mux_r16, pusch_PreparationHighPriority_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_pusch_PreparationHighPriority_r16_118,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pusch-PreparationHighPriority-r16"
		},
};
static const ber_tlv_tag_t asn_DEF_NR_ul_IntraUE_Mux_r16_tags_113[] = {
	(ASN_TAG_CLASS_CONTEXT | (24 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ul_IntraUE_Mux_r16_tag2el_113[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* pusch-PreparationLowPriority-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* pusch-PreparationHighPriority-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ul_IntraUE_Mux_r16_specs_113 = {
	sizeof(struct NR_FeatureSetUplink_v1610__ul_IntraUE_Mux_r16),
	offsetof(struct NR_FeatureSetUplink_v1610__ul_IntraUE_Mux_r16, _asn_ctx),
	asn_MAP_NR_ul_IntraUE_Mux_r16_tag2el_113,
	2,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ul_IntraUE_Mux_r16_113 = {
	"ul-IntraUE-Mux-r16",
	"ul-IntraUE-Mux-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ul_IntraUE_Mux_r16_tags_113,
	sizeof(asn_DEF_NR_ul_IntraUE_Mux_r16_tags_113)
		/sizeof(asn_DEF_NR_ul_IntraUE_Mux_r16_tags_113[0]) - 1, /* 1 */
	asn_DEF_NR_ul_IntraUE_Mux_r16_tags_113,	/* Same as above */
	sizeof(asn_DEF_NR_ul_IntraUE_Mux_r16_tags_113)
		/sizeof(asn_DEF_NR_ul_IntraUE_Mux_r16_tags_113[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ul_IntraUE_Mux_r16_113,
	2,	/* Elements count */
	&asn_SPC_NR_ul_IntraUE_Mux_r16_specs_113	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_ul_FullPwrMode_r16_value2enum_122[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_ul_FullPwrMode_r16_enum2value_122[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ul_FullPwrMode_r16_specs_122 = {
	asn_MAP_NR_ul_FullPwrMode_r16_value2enum_122,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ul_FullPwrMode_r16_enum2value_122,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ul_FullPwrMode_r16_tags_122[] = {
	(ASN_TAG_CLASS_CONTEXT | (25 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ul_FullPwrMode_r16_122 = {
	"ul-FullPwrMode-r16",
	"ul-FullPwrMode-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ul_FullPwrMode_r16_tags_122,
	sizeof(asn_DEF_NR_ul_FullPwrMode_r16_tags_122)
		/sizeof(asn_DEF_NR_ul_FullPwrMode_r16_tags_122[0]) - 1, /* 1 */
	asn_DEF_NR_ul_FullPwrMode_r16_tags_122,	/* Same as above */
	sizeof(asn_DEF_NR_ul_FullPwrMode_r16_tags_122)
		/sizeof(asn_DEF_NR_ul_FullPwrMode_r16_tags_122[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ul_FullPwrMode_r16_constr_122,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ul_FullPwrMode_r16_specs_122	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_scs_15kHz_120kHz_r16_value2enum_125[] = {
	{ 0,	2,	"n1" },
	{ 1,	2,	"n2" },
	{ 2,	2,	"n4" }
};
static const unsigned int asn_MAP_NR_scs_15kHz_120kHz_r16_enum2value_125[] = {
	0,	/* n1(0) */
	1,	/* n2(1) */
	2	/* n4(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_scs_15kHz_120kHz_r16_specs_125 = {
	asn_MAP_NR_scs_15kHz_120kHz_r16_value2enum_125,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_scs_15kHz_120kHz_r16_enum2value_125,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_scs_15kHz_120kHz_r16_tags_125[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_scs_15kHz_120kHz_r16_125 = {
	"scs-15kHz-120kHz-r16",
	"scs-15kHz-120kHz-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_scs_15kHz_120kHz_r16_tags_125,
	sizeof(asn_DEF_NR_scs_15kHz_120kHz_r16_tags_125)
		/sizeof(asn_DEF_NR_scs_15kHz_120kHz_r16_tags_125[0]) - 1, /* 1 */
	asn_DEF_NR_scs_15kHz_120kHz_r16_tags_125,	/* Same as above */
	sizeof(asn_DEF_NR_scs_15kHz_120kHz_r16_tags_125)
		/sizeof(asn_DEF_NR_scs_15kHz_120kHz_r16_tags_125[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_scs_15kHz_120kHz_r16_constr_125,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_scs_15kHz_120kHz_r16_specs_125	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_scs_15kHz_60kHz_r16_value2enum_129[] = {
	{ 0,	2,	"n1" },
	{ 1,	2,	"n2" },
	{ 2,	2,	"n4" }
};
static const unsigned int asn_MAP_NR_scs_15kHz_60kHz_r16_enum2value_129[] = {
	0,	/* n1(0) */
	1,	/* n2(1) */
	2	/* n4(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_scs_15kHz_60kHz_r16_specs_129 = {
	asn_MAP_NR_scs_15kHz_60kHz_r16_value2enum_129,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_scs_15kHz_60kHz_r16_enum2value_129,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_scs_15kHz_60kHz_r16_tags_129[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_scs_15kHz_60kHz_r16_129 = {
	"scs-15kHz-60kHz-r16",
	"scs-15kHz-60kHz-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_scs_15kHz_60kHz_r16_tags_129,
	sizeof(asn_DEF_NR_scs_15kHz_60kHz_r16_tags_129)
		/sizeof(asn_DEF_NR_scs_15kHz_60kHz_r16_tags_129[0]) - 1, /* 1 */
	asn_DEF_NR_scs_15kHz_60kHz_r16_tags_129,	/* Same as above */
	sizeof(asn_DEF_NR_scs_15kHz_60kHz_r16_tags_129)
		/sizeof(asn_DEF_NR_scs_15kHz_60kHz_r16_tags_129[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_scs_15kHz_60kHz_r16_constr_129,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_scs_15kHz_60kHz_r16_specs_129	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_scs_30kHz_120kHz_r16_value2enum_133[] = {
	{ 0,	2,	"n1" },
	{ 1,	2,	"n2" },
	{ 2,	2,	"n4" }
};
static const unsigned int asn_MAP_NR_scs_30kHz_120kHz_r16_enum2value_133[] = {
	0,	/* n1(0) */
	1,	/* n2(1) */
	2	/* n4(2) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_scs_30kHz_120kHz_r16_specs_133 = {
	asn_MAP_NR_scs_30kHz_120kHz_r16_value2enum_133,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_scs_30kHz_120kHz_r16_enum2value_133,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_scs_30kHz_120kHz_r16_tags_133[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_scs_30kHz_120kHz_r16_133 = {
	"scs-30kHz-120kHz-r16",
	"scs-30kHz-120kHz-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_scs_30kHz_120kHz_r16_tags_133,
	sizeof(asn_DEF_NR_scs_30kHz_120kHz_r16_tags_133)
		/sizeof(asn_DEF_NR_scs_30kHz_120kHz_r16_tags_133[0]) - 1, /* 1 */
	asn_DEF_NR_scs_30kHz_120kHz_r16_tags_133,	/* Same as above */
	sizeof(asn_DEF_NR_scs_30kHz_120kHz_r16_tags_133)
		/sizeof(asn_DEF_NR_scs_30kHz_120kHz_r16_tags_133[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_scs_30kHz_120kHz_r16_constr_133,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_scs_30kHz_120kHz_r16_specs_133	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_scs_15kHz_30kHz_r16_value2enum_137[] = {
	{ 0,	2,	"n2" }
};
static const unsigned int asn_MAP_NR_scs_15kHz_30kHz_r16_enum2value_137[] = {
	0	/* n2(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_scs_15kHz_30kHz_r16_specs_137 = {
	asn_MAP_NR_scs_15kHz_30kHz_r16_value2enum_137,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_scs_15kHz_30kHz_r16_enum2value_137,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_scs_15kHz_30kHz_r16_tags_137[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_scs_15kHz_30kHz_r16_137 = {
	"scs-15kHz-30kHz-r16",
	"scs-15kHz-30kHz-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_scs_15kHz_30kHz_r16_tags_137,
	sizeof(asn_DEF_NR_scs_15kHz_30kHz_r16_tags_137)
		/sizeof(asn_DEF_NR_scs_15kHz_30kHz_r16_tags_137[0]) - 1, /* 1 */
	asn_DEF_NR_scs_15kHz_30kHz_r16_tags_137,	/* Same as above */
	sizeof(asn_DEF_NR_scs_15kHz_30kHz_r16_tags_137)
		/sizeof(asn_DEF_NR_scs_15kHz_30kHz_r16_tags_137[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_scs_15kHz_30kHz_r16_constr_137,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_scs_15kHz_30kHz_r16_specs_137	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_scs_30kHz_60kHz_r16_value2enum_139[] = {
	{ 0,	2,	"n2" }
};
static const unsigned int asn_MAP_NR_scs_30kHz_60kHz_r16_enum2value_139[] = {
	0	/* n2(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_scs_30kHz_60kHz_r16_specs_139 = {
	asn_MAP_NR_scs_30kHz_60kHz_r16_value2enum_139,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_scs_30kHz_60kHz_r16_enum2value_139,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_scs_30kHz_60kHz_r16_tags_139[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_scs_30kHz_60kHz_r16_139 = {
	"scs-30kHz-60kHz-r16",
	"scs-30kHz-60kHz-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_scs_30kHz_60kHz_r16_tags_139,
	sizeof(asn_DEF_NR_scs_30kHz_60kHz_r16_tags_139)
		/sizeof(asn_DEF_NR_scs_30kHz_60kHz_r16_tags_139[0]) - 1, /* 1 */
	asn_DEF_NR_scs_30kHz_60kHz_r16_tags_139,	/* Same as above */
	sizeof(asn_DEF_NR_scs_30kHz_60kHz_r16_tags_139)
		/sizeof(asn_DEF_NR_scs_30kHz_60kHz_r16_tags_139[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_scs_30kHz_60kHz_r16_constr_139,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_scs_30kHz_60kHz_r16_specs_139	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_scs_60kHz_120kHz_r16_value2enum_141[] = {
	{ 0,	2,	"n2" }
};
static const unsigned int asn_MAP_NR_scs_60kHz_120kHz_r16_enum2value_141[] = {
	0	/* n2(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_scs_60kHz_120kHz_r16_specs_141 = {
	asn_MAP_NR_scs_60kHz_120kHz_r16_value2enum_141,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_scs_60kHz_120kHz_r16_enum2value_141,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_scs_60kHz_120kHz_r16_tags_141[] = {
	(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_scs_60kHz_120kHz_r16_141 = {
	"scs-60kHz-120kHz-r16",
	"scs-60kHz-120kHz-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_scs_60kHz_120kHz_r16_tags_141,
	sizeof(asn_DEF_NR_scs_60kHz_120kHz_r16_tags_141)
		/sizeof(asn_DEF_NR_scs_60kHz_120kHz_r16_tags_141[0]) - 1, /* 1 */
	asn_DEF_NR_scs_60kHz_120kHz_r16_tags_141,	/* Same as above */
	sizeof(asn_DEF_NR_scs_60kHz_120kHz_r16_tags_141)
		/sizeof(asn_DEF_NR_scs_60kHz_120kHz_r16_tags_141[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_scs_60kHz_120kHz_r16_constr_141,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_scs_60kHz_120kHz_r16_specs_141	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_crossCarrierSchedulingProcessing_DiffSCS_r16_124[] = {
	{ ATF_POINTER, 6, offsetof(struct NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16, scs_15kHz_120kHz_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_scs_15kHz_120kHz_r16_125,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs-15kHz-120kHz-r16"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16, scs_15kHz_60kHz_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_scs_15kHz_60kHz_r16_129,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs-15kHz-60kHz-r16"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16, scs_30kHz_120kHz_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_scs_30kHz_120kHz_r16_133,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs-30kHz-120kHz-r16"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16, scs_15kHz_30kHz_r16),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_scs_15kHz_30kHz_r16_137,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs-15kHz-30kHz-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16, scs_30kHz_60kHz_r16),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_scs_30kHz_60kHz_r16_139,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs-30kHz-60kHz-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16, scs_60kHz_120kHz_r16),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_scs_60kHz_120kHz_r16_141,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"scs-60kHz-120kHz-r16"
		},
};
static const int asn_MAP_NR_crossCarrierSchedulingProcessing_DiffSCS_r16_oms_124[] = { 0, 1, 2, 3, 4, 5 };
static const ber_tlv_tag_t asn_DEF_NR_crossCarrierSchedulingProcessing_DiffSCS_r16_tags_124[] = {
	(ASN_TAG_CLASS_CONTEXT | (26 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_crossCarrierSchedulingProcessing_DiffSCS_r16_tag2el_124[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* scs-15kHz-120kHz-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* scs-15kHz-60kHz-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* scs-30kHz-120kHz-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* scs-15kHz-30kHz-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* scs-30kHz-60kHz-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 } /* scs-60kHz-120kHz-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_crossCarrierSchedulingProcessing_DiffSCS_r16_specs_124 = {
	sizeof(struct NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16),
	offsetof(struct NR_FeatureSetUplink_v1610__crossCarrierSchedulingProcessing_DiffSCS_r16, _asn_ctx),
	asn_MAP_NR_crossCarrierSchedulingProcessing_DiffSCS_r16_tag2el_124,
	6,	/* Count of tags in the map */
	asn_MAP_NR_crossCarrierSchedulingProcessing_DiffSCS_r16_oms_124,	/* Optional members */
	6, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_crossCarrierSchedulingProcessing_DiffSCS_r16_124 = {
	"crossCarrierSchedulingProcessing-DiffSCS-r16",
	"crossCarrierSchedulingProcessing-DiffSCS-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_crossCarrierSchedulingProcessing_DiffSCS_r16_tags_124,
	sizeof(asn_DEF_NR_crossCarrierSchedulingProcessing_DiffSCS_r16_tags_124)
		/sizeof(asn_DEF_NR_crossCarrierSchedulingProcessing_DiffSCS_r16_tags_124[0]) - 1, /* 1 */
	asn_DEF_NR_crossCarrierSchedulingProcessing_DiffSCS_r16_tags_124,	/* Same as above */
	sizeof(asn_DEF_NR_crossCarrierSchedulingProcessing_DiffSCS_r16_tags_124)
		/sizeof(asn_DEF_NR_crossCarrierSchedulingProcessing_DiffSCS_r16_tags_124[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_crossCarrierSchedulingProcessing_DiffSCS_r16_124,
	6,	/* Elements count */
	&asn_SPC_NR_crossCarrierSchedulingProcessing_DiffSCS_r16_specs_124	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_ul_FullPwrMode1_r16_value2enum_143[] = {
	{ 0,	9,	"supported" }
};
static const unsigned int asn_MAP_NR_ul_FullPwrMode1_r16_enum2value_143[] = {
	0	/* supported(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ul_FullPwrMode1_r16_specs_143 = {
	asn_MAP_NR_ul_FullPwrMode1_r16_value2enum_143,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ul_FullPwrMode1_r16_enum2value_143,	/* N => "tag"; sorted by N */
	1,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ul_FullPwrMode1_r16_tags_143[] = {
	(ASN_TAG_CLASS_CONTEXT | (27 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ul_FullPwrMode1_r16_143 = {
	"ul-FullPwrMode1-r16",
	"ul-FullPwrMode1-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ul_FullPwrMode1_r16_tags_143,
	sizeof(asn_DEF_NR_ul_FullPwrMode1_r16_tags_143)
		/sizeof(asn_DEF_NR_ul_FullPwrMode1_r16_tags_143[0]) - 1, /* 1 */
	asn_DEF_NR_ul_FullPwrMode1_r16_tags_143,	/* Same as above */
	sizeof(asn_DEF_NR_ul_FullPwrMode1_r16_tags_143)
		/sizeof(asn_DEF_NR_ul_FullPwrMode1_r16_tags_143[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ul_FullPwrMode1_r16_constr_143,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ul_FullPwrMode1_r16_specs_143	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_ul_FullPwrMode2_SRSConfig_diffNumSRSPorts_r16_value2enum_145[] = {
	{ 0,	4,	"p1-2" },
	{ 1,	4,	"p1-4" },
	{ 2,	6,	"p1-2-4" }
};
static const unsigned int asn_MAP_NR_ul_FullPwrMode2_SRSConfig_diffNumSRSPorts_r16_enum2value_145[] = {
	0,	/* p1-2(0) */
	2,	/* p1-2-4(2) */
	1	/* p1-4(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ul_FullPwrMode2_SRSConfig_diffNumSRSPorts_r16_specs_145 = {
	asn_MAP_NR_ul_FullPwrMode2_SRSConfig_diffNumSRSPorts_r16_value2enum_145,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ul_FullPwrMode2_SRSConfig_diffNumSRSPorts_r16_enum2value_145,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ul_FullPwrMode2_SRSConfig_diffNumSRSPorts_r16_tags_145[] = {
	(ASN_TAG_CLASS_CONTEXT | (28 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ul_FullPwrMode2_SRSConfig_diffNumSRSPorts_r16_145 = {
	"ul-FullPwrMode2-SRSConfig-diffNumSRSPorts-r16",
	"ul-FullPwrMode2-SRSConfig-diffNumSRSPorts-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ul_FullPwrMode2_SRSConfig_diffNumSRSPorts_r16_tags_145,
	sizeof(asn_DEF_NR_ul_FullPwrMode2_SRSConfig_diffNumSRSPorts_r16_tags_145)
		/sizeof(asn_DEF_NR_ul_FullPwrMode2_SRSConfig_diffNumSRSPorts_r16_tags_145[0]) - 1, /* 1 */
	asn_DEF_NR_ul_FullPwrMode2_SRSConfig_diffNumSRSPorts_r16_tags_145,	/* Same as above */
	sizeof(asn_DEF_NR_ul_FullPwrMode2_SRSConfig_diffNumSRSPorts_r16_tags_145)
		/sizeof(asn_DEF_NR_ul_FullPwrMode2_SRSConfig_diffNumSRSPorts_r16_tags_145[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ul_FullPwrMode2_SRSConfig_diffNumSRSPorts_r16_constr_145,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ul_FullPwrMode2_SRSConfig_diffNumSRSPorts_r16_specs_145	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_fourPortsNonCoherent_r16_value2enum_151[] = {
	{ 0,	2,	"g0" },
	{ 1,	2,	"g1" },
	{ 2,	2,	"g2" },
	{ 3,	2,	"g3" }
};
static const unsigned int asn_MAP_NR_fourPortsNonCoherent_r16_enum2value_151[] = {
	0,	/* g0(0) */
	1,	/* g1(1) */
	2,	/* g2(2) */
	3	/* g3(3) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_fourPortsNonCoherent_r16_specs_151 = {
	asn_MAP_NR_fourPortsNonCoherent_r16_value2enum_151,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_fourPortsNonCoherent_r16_enum2value_151,	/* N => "tag"; sorted by N */
	4,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_fourPortsNonCoherent_r16_tags_151[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_fourPortsNonCoherent_r16_151 = {
	"fourPortsNonCoherent-r16",
	"fourPortsNonCoherent-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_fourPortsNonCoherent_r16_tags_151,
	sizeof(asn_DEF_NR_fourPortsNonCoherent_r16_tags_151)
		/sizeof(asn_DEF_NR_fourPortsNonCoherent_r16_tags_151[0]) - 1, /* 1 */
	asn_DEF_NR_fourPortsNonCoherent_r16_tags_151,	/* Same as above */
	sizeof(asn_DEF_NR_fourPortsNonCoherent_r16_tags_151)
		/sizeof(asn_DEF_NR_fourPortsNonCoherent_r16_tags_151[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_fourPortsNonCoherent_r16_constr_151,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_fourPortsNonCoherent_r16_specs_151	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_fourPortsPartialCoherent_r16_value2enum_156[] = {
	{ 0,	2,	"g0" },
	{ 1,	2,	"g1" },
	{ 2,	2,	"g2" },
	{ 3,	2,	"g3" },
	{ 4,	2,	"g4" },
	{ 5,	2,	"g5" },
	{ 6,	2,	"g6" }
};
static const unsigned int asn_MAP_NR_fourPortsPartialCoherent_r16_enum2value_156[] = {
	0,	/* g0(0) */
	1,	/* g1(1) */
	2,	/* g2(2) */
	3,	/* g3(3) */
	4,	/* g4(4) */
	5,	/* g5(5) */
	6	/* g6(6) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_fourPortsPartialCoherent_r16_specs_156 = {
	asn_MAP_NR_fourPortsPartialCoherent_r16_value2enum_156,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_fourPortsPartialCoherent_r16_enum2value_156,	/* N => "tag"; sorted by N */
	7,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_fourPortsPartialCoherent_r16_tags_156[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_fourPortsPartialCoherent_r16_156 = {
	"fourPortsPartialCoherent-r16",
	"fourPortsPartialCoherent-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_fourPortsPartialCoherent_r16_tags_156,
	sizeof(asn_DEF_NR_fourPortsPartialCoherent_r16_tags_156)
		/sizeof(asn_DEF_NR_fourPortsPartialCoherent_r16_tags_156[0]) - 1, /* 1 */
	asn_DEF_NR_fourPortsPartialCoherent_r16_tags_156,	/* Same as above */
	sizeof(asn_DEF_NR_fourPortsPartialCoherent_r16_tags_156)
		/sizeof(asn_DEF_NR_fourPortsPartialCoherent_r16_tags_156[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_fourPortsPartialCoherent_r16_constr_156,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_fourPortsPartialCoherent_r16_specs_156	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ul_FullPwrMode2_TPMIGroup_r16_149[] = {
	{ ATF_POINTER, 3, offsetof(struct NR_FeatureSetUplink_v1610__ul_FullPwrMode2_TPMIGroup_r16, twoPorts_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_BIT_STRING,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_twoPorts_r16_constr_150,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_twoPorts_r16_constraint_149
		},
		0, 0, /* No default value */
		"twoPorts-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_FeatureSetUplink_v1610__ul_FullPwrMode2_TPMIGroup_r16, fourPortsNonCoherent_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_fourPortsNonCoherent_r16_151,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"fourPortsNonCoherent-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_FeatureSetUplink_v1610__ul_FullPwrMode2_TPMIGroup_r16, fourPortsPartialCoherent_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_fourPortsPartialCoherent_r16_156,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"fourPortsPartialCoherent-r16"
		},
};
static const int asn_MAP_NR_ul_FullPwrMode2_TPMIGroup_r16_oms_149[] = { 0, 1, 2 };
static const ber_tlv_tag_t asn_DEF_NR_ul_FullPwrMode2_TPMIGroup_r16_tags_149[] = {
	(ASN_TAG_CLASS_CONTEXT | (29 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ul_FullPwrMode2_TPMIGroup_r16_tag2el_149[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* twoPorts-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* fourPortsNonCoherent-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* fourPortsPartialCoherent-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ul_FullPwrMode2_TPMIGroup_r16_specs_149 = {
	sizeof(struct NR_FeatureSetUplink_v1610__ul_FullPwrMode2_TPMIGroup_r16),
	offsetof(struct NR_FeatureSetUplink_v1610__ul_FullPwrMode2_TPMIGroup_r16, _asn_ctx),
	asn_MAP_NR_ul_FullPwrMode2_TPMIGroup_r16_tag2el_149,
	3,	/* Count of tags in the map */
	asn_MAP_NR_ul_FullPwrMode2_TPMIGroup_r16_oms_149,	/* Optional members */
	3, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ul_FullPwrMode2_TPMIGroup_r16_149 = {
	"ul-FullPwrMode2-TPMIGroup-r16",
	"ul-FullPwrMode2-TPMIGroup-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ul_FullPwrMode2_TPMIGroup_r16_tags_149,
	sizeof(asn_DEF_NR_ul_FullPwrMode2_TPMIGroup_r16_tags_149)
		/sizeof(asn_DEF_NR_ul_FullPwrMode2_TPMIGroup_r16_tags_149[0]) - 1, /* 1 */
	asn_DEF_NR_ul_FullPwrMode2_TPMIGroup_r16_tags_149,	/* Same as above */
	sizeof(asn_DEF_NR_ul_FullPwrMode2_TPMIGroup_r16_tags_149)
		/sizeof(asn_DEF_NR_ul_FullPwrMode2_TPMIGroup_r16_tags_149[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ul_FullPwrMode2_TPMIGroup_r16_149,
	3,	/* Elements count */
	&asn_SPC_NR_ul_FullPwrMode2_TPMIGroup_r16_specs_149	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_FeatureSetUplink_v1610_1[] = {
	{ ATF_POINTER, 30, offsetof(struct NR_FeatureSetUplink_v1610, pusch_RepetitionTypeB_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		0,
		&asn_DEF_NR_pusch_RepetitionTypeB_r16_2,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"pusch-RepetitionTypeB-r16"
		},
	{ ATF_POINTER, 29, offsetof(struct NR_FeatureSetUplink_v1610, ul_CancellationSelfCarrier_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ul_CancellationSelfCarrier_r16_14,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ul-CancellationSelfCarrier-r16"
		},
	{ ATF_POINTER, 28, offsetof(struct NR_FeatureSetUplink_v1610, ul_CancellationCrossCarrier_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ul_CancellationCrossCarrier_r16_16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ul-CancellationCrossCarrier-r16"
		},
	{ ATF_POINTER, 27, offsetof(struct NR_FeatureSetUplink_v1610, ul_FullPwrMode2_MaxSRS_ResInSet_r16),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ul_FullPwrMode2_MaxSRS_ResInSet_r16_18,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ul-FullPwrMode2-MaxSRS-ResInSet-r16"
		},
	{ ATF_POINTER, 26, offsetof(struct NR_FeatureSetUplink_v1610, cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		0,
		&asn_DEF_NR_cbgPUSCH_ProcessingType1_DifferentTB_PerSlot_r16_22,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"cbgPUSCH-ProcessingType1-DifferentTB-PerSlot-r16"
		},
	{ ATF_POINTER, 25, offsetof(struct NR_FeatureSetUplink_v1610, cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		0,
		&asn_DEF_NR_cbgPUSCH_ProcessingType2_DifferentTB_PerSlot_r16_43,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"cbgPUSCH-ProcessingType2-DifferentTB-PerSlot-r16"
		},
	{ ATF_POINTER, 24, offsetof(struct NR_FeatureSetUplink_v1610, supportedSRS_PosResources_r16),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_SRS_AllPosResources_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"supportedSRS-PosResources-r16"
		},
	{ ATF_POINTER, 23, offsetof(struct NR_FeatureSetUplink_v1610, intraFreqDAPS_UL_r16),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		0,
		&asn_DEF_NR_intraFreqDAPS_UL_r16_65,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"intraFreqDAPS-UL-r16"
		},
	{ ATF_POINTER, 22, offsetof(struct NR_FeatureSetUplink_v1610, intraBandFreqSeparationUL_v1620),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_FreqSeparationClassUL_v1620,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"intraBandFreqSeparationUL-v1620"
		},
	{ ATF_POINTER, 21, offsetof(struct NR_FeatureSetUplink_v1610, multiPUCCH_r16),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		0,
		&asn_DEF_NR_multiPUCCH_r16_78,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"multiPUCCH-r16"
		},
	{ ATF_POINTER, 20, offsetof(struct NR_FeatureSetUplink_v1610, twoPUCCH_Type1_r16),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_twoPUCCH_Type1_r16_85,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"twoPUCCH-Type1-r16"
		},
	{ ATF_POINTER, 19, offsetof(struct NR_FeatureSetUplink_v1610, twoPUCCH_Type2_r16),
		(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_twoPUCCH_Type2_r16_87,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"twoPUCCH-Type2-r16"
		},
	{ ATF_POINTER, 18, offsetof(struct NR_FeatureSetUplink_v1610, twoPUCCH_Type3_r16),
		(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_twoPUCCH_Type3_r16_89,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"twoPUCCH-Type3-r16"
		},
	{ ATF_POINTER, 17, offsetof(struct NR_FeatureSetUplink_v1610, twoPUCCH_Type4_r16),
		(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_twoPUCCH_Type4_r16_91,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"twoPUCCH-Type4-r16"
		},
	{ ATF_POINTER, 16, offsetof(struct NR_FeatureSetUplink_v1610, mux_SR_HARQ_ACK_r16),
		(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_mux_SR_HARQ_ACK_r16_93,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"mux-SR-HARQ-ACK-r16"
		},
	{ ATF_POINTER, 15, offsetof(struct NR_FeatureSetUplink_v1610, dummy1),
		(ASN_TAG_CLASS_CONTEXT | (15 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dummy1_95,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dummy1"
		},
	{ ATF_POINTER, 14, offsetof(struct NR_FeatureSetUplink_v1610, dummy2),
		(ASN_TAG_CLASS_CONTEXT | (16 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_dummy2_97,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"dummy2"
		},
	{ ATF_POINTER, 13, offsetof(struct NR_FeatureSetUplink_v1610, twoPUCCH_Type5_r16),
		(ASN_TAG_CLASS_CONTEXT | (17 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_twoPUCCH_Type5_r16_99,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"twoPUCCH-Type5-r16"
		},
	{ ATF_POINTER, 12, offsetof(struct NR_FeatureSetUplink_v1610, twoPUCCH_Type6_r16),
		(ASN_TAG_CLASS_CONTEXT | (18 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_twoPUCCH_Type6_r16_101,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"twoPUCCH-Type6-r16"
		},
	{ ATF_POINTER, 11, offsetof(struct NR_FeatureSetUplink_v1610, twoPUCCH_Type7_r16),
		(ASN_TAG_CLASS_CONTEXT | (19 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_twoPUCCH_Type7_r16_103,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"twoPUCCH-Type7-r16"
		},
	{ ATF_POINTER, 10, offsetof(struct NR_FeatureSetUplink_v1610, twoPUCCH_Type8_r16),
		(ASN_TAG_CLASS_CONTEXT | (20 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_twoPUCCH_Type8_r16_105,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"twoPUCCH-Type8-r16"
		},
	{ ATF_POINTER, 9, offsetof(struct NR_FeatureSetUplink_v1610, twoPUCCH_Type9_r16),
		(ASN_TAG_CLASS_CONTEXT | (21 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_twoPUCCH_Type9_r16_107,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"twoPUCCH-Type9-r16"
		},
	{ ATF_POINTER, 8, offsetof(struct NR_FeatureSetUplink_v1610, twoPUCCH_Type10_r16),
		(ASN_TAG_CLASS_CONTEXT | (22 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_twoPUCCH_Type10_r16_109,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"twoPUCCH-Type10-r16"
		},
	{ ATF_POINTER, 7, offsetof(struct NR_FeatureSetUplink_v1610, twoPUCCH_Type11_r16),
		(ASN_TAG_CLASS_CONTEXT | (23 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_twoPUCCH_Type11_r16_111,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"twoPUCCH-Type11-r16"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_FeatureSetUplink_v1610, ul_IntraUE_Mux_r16),
		(ASN_TAG_CLASS_CONTEXT | (24 << 2)),
		0,
		&asn_DEF_NR_ul_IntraUE_Mux_r16_113,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ul-IntraUE-Mux-r16"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_FeatureSetUplink_v1610, ul_FullPwrMode_r16),
		(ASN_TAG_CLASS_CONTEXT | (25 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ul_FullPwrMode_r16_122,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ul-FullPwrMode-r16"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_FeatureSetUplink_v1610, crossCarrierSchedulingProcessing_DiffSCS_r16),
		(ASN_TAG_CLASS_CONTEXT | (26 << 2)),
		0,
		&asn_DEF_NR_crossCarrierSchedulingProcessing_DiffSCS_r16_124,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"crossCarrierSchedulingProcessing-DiffSCS-r16"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_FeatureSetUplink_v1610, ul_FullPwrMode1_r16),
		(ASN_TAG_CLASS_CONTEXT | (27 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ul_FullPwrMode1_r16_143,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ul-FullPwrMode1-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_FeatureSetUplink_v1610, ul_FullPwrMode2_SRSConfig_diffNumSRSPorts_r16),
		(ASN_TAG_CLASS_CONTEXT | (28 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ul_FullPwrMode2_SRSConfig_diffNumSRSPorts_r16_145,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ul-FullPwrMode2-SRSConfig-diffNumSRSPorts-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_FeatureSetUplink_v1610, ul_FullPwrMode2_TPMIGroup_r16),
		(ASN_TAG_CLASS_CONTEXT | (29 << 2)),
		0,
		&asn_DEF_NR_ul_FullPwrMode2_TPMIGroup_r16_149,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ul-FullPwrMode2-TPMIGroup-r16"
		},
};
static const int asn_MAP_NR_FeatureSetUplink_v1610_oms_1[] = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29 };
static const ber_tlv_tag_t asn_DEF_NR_FeatureSetUplink_v1610_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_FeatureSetUplink_v1610_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* pusch-RepetitionTypeB-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* ul-CancellationSelfCarrier-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* ul-CancellationCrossCarrier-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* ul-FullPwrMode2-MaxSRS-ResInSet-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* cbgPUSCH-ProcessingType1-DifferentTB-PerSlot-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* cbgPUSCH-ProcessingType2-DifferentTB-PerSlot-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* supportedSRS-PosResources-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* intraFreqDAPS-UL-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* intraBandFreqSeparationUL-v1620 */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* multiPUCCH-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 }, /* twoPUCCH-Type1-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (11 << 2)), 11, 0, 0 }, /* twoPUCCH-Type2-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (12 << 2)), 12, 0, 0 }, /* twoPUCCH-Type3-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (13 << 2)), 13, 0, 0 }, /* twoPUCCH-Type4-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (14 << 2)), 14, 0, 0 }, /* mux-SR-HARQ-ACK-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (15 << 2)), 15, 0, 0 }, /* dummy1 */
    { (ASN_TAG_CLASS_CONTEXT | (16 << 2)), 16, 0, 0 }, /* dummy2 */
    { (ASN_TAG_CLASS_CONTEXT | (17 << 2)), 17, 0, 0 }, /* twoPUCCH-Type5-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (18 << 2)), 18, 0, 0 }, /* twoPUCCH-Type6-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (19 << 2)), 19, 0, 0 }, /* twoPUCCH-Type7-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (20 << 2)), 20, 0, 0 }, /* twoPUCCH-Type8-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (21 << 2)), 21, 0, 0 }, /* twoPUCCH-Type9-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (22 << 2)), 22, 0, 0 }, /* twoPUCCH-Type10-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (23 << 2)), 23, 0, 0 }, /* twoPUCCH-Type11-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (24 << 2)), 24, 0, 0 }, /* ul-IntraUE-Mux-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (25 << 2)), 25, 0, 0 }, /* ul-FullPwrMode-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (26 << 2)), 26, 0, 0 }, /* crossCarrierSchedulingProcessing-DiffSCS-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (27 << 2)), 27, 0, 0 }, /* ul-FullPwrMode1-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (28 << 2)), 28, 0, 0 }, /* ul-FullPwrMode2-SRSConfig-diffNumSRSPorts-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (29 << 2)), 29, 0, 0 } /* ul-FullPwrMode2-TPMIGroup-r16 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_FeatureSetUplink_v1610_specs_1 = {
	sizeof(struct NR_FeatureSetUplink_v1610),
	offsetof(struct NR_FeatureSetUplink_v1610, _asn_ctx),
	asn_MAP_NR_FeatureSetUplink_v1610_tag2el_1,
	30,	/* Count of tags in the map */
	asn_MAP_NR_FeatureSetUplink_v1610_oms_1,	/* Optional members */
	30, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_FeatureSetUplink_v1610 = {
	"FeatureSetUplink-v1610",
	"FeatureSetUplink-v1610",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_FeatureSetUplink_v1610_tags_1,
	sizeof(asn_DEF_NR_FeatureSetUplink_v1610_tags_1)
		/sizeof(asn_DEF_NR_FeatureSetUplink_v1610_tags_1[0]), /* 1 */
	asn_DEF_NR_FeatureSetUplink_v1610_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_FeatureSetUplink_v1610_tags_1)
		/sizeof(asn_DEF_NR_FeatureSetUplink_v1610_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_FeatureSetUplink_v1610_1,
	30,	/* Elements count */
	&asn_SPC_NR_FeatureSetUplink_v1610_specs_1	/* Additional specs */
};

