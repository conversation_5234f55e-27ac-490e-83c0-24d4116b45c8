/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#include "NR_RACH-ConfigCommonTwoStepRA-r16.h"

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_four_constraint_4(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 16L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_eight_constraint_4(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 8L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_sixteen_constraint_4(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 4L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_l839_constraint_88(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 837L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_l139_constraint_88(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 137L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_l571_constraint_88(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 569L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_l1151_constraint_88(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 1149L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_ra_PrioritizationForAI_r16_constraint_111(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	const BIT_STRING_t *st = (const BIT_STRING_t *)sptr;
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	if(st->size > 0) {
		/* Size in bits */
		size = 8 * st->size - (st->bits_unused & 0x07);
	} else {
		size = 0;
	}
	
	if((size == 2UL)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_NR_featureCombinationPreamblesList_r17_constraint_124(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	/* Determine the number of elements */
	size = _A_CSEQUENCE_FROM_VOID(sptr)->count;
	
	if((size >= 1UL && size <= 256UL)) {
		/* Perform validation of the inner elements */
		return SEQUENCE_OF_constraint(td, sptr, ctfailcb, app_key);
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_msgA_TotalNumberOfRA_Preambles_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 63L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_msgA_CB_PreamblesPerSSB_PerSharedRO_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 60L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static int
memb_NR_msgA_SSB_SharedRO_MaskIndex_r16_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 1L && value <= 15L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_oneEighth_constr_5 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  15 }	/* (0..15) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_oneFourth_constr_22 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  15 }	/* (0..15) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_oneHalf_constr_39 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  15 }	/* (0..15) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_one_constr_56 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  15 }	/* (0..15) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_two_constr_73 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  7 }	/* (0..7) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_four_constr_82 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  1,  16 }	/* (1..16) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_eight_constr_83 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  1,  8 }	/* (1..8) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_sixteen_constr_84 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  1,  4 }	/* (1..4) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16_constr_4 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  7 }	/* (0..7) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_l839_constr_89 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 10,  10,  0,  837 }	/* (0..837) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_l139_constr_90 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 8,  8,  0,  137 }	/* (0..137) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_l571_constr_91 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 10,  10,  0,  569 }	/* (0..569) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_l1151_constr_92 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 11,  11,  0,  1149 }	/* (0..1149) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_msgA_PRACH_RootSequenceIndex_r16_constr_88 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  3 }	/* (0..3) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_msgA_TransMax_r16_constr_93 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  0,  9 }	/* (0..9) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_msgA_RestrictedSetConfig_r16_constr_107 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_ra_PrioritizationForAI_r16_constr_113 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 0,  0,  2,  2 }	/* (SIZE(2..2)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_ra_ContentionResolutionTimer_r16_constr_114 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 3,  3,  0,  7 }	/* (0..7) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_NR_featureCombinationPreamblesList_r17_constr_126 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 8,  8,  1,  256 }	/* (SIZE(1..256)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_featureCombinationPreamblesList_r17_constr_126 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 8,  8,  1,  256 }	/* (SIZE(1..256)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_msgA_TotalNumberOfRA_Preambles_r16_constr_3 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 6,  6,  1,  63 }	/* (1..63) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_msgA_CB_PreamblesPerSSB_PerSharedRO_r16_constr_85 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 6,  6,  1,  60 }	/* (1..60) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_NR_msgA_SSB_SharedRO_MaskIndex_r16_constr_86 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 4,  4,  1,  15 }	/* (1..15) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_NR_oneEighth_value2enum_5[] = {
	{ 0,	2,	"n4" },
	{ 1,	2,	"n8" },
	{ 2,	3,	"n12" },
	{ 3,	3,	"n16" },
	{ 4,	3,	"n20" },
	{ 5,	3,	"n24" },
	{ 6,	3,	"n28" },
	{ 7,	3,	"n32" },
	{ 8,	3,	"n36" },
	{ 9,	3,	"n40" },
	{ 10,	3,	"n44" },
	{ 11,	3,	"n48" },
	{ 12,	3,	"n52" },
	{ 13,	3,	"n56" },
	{ 14,	3,	"n60" },
	{ 15,	3,	"n64" }
};
static const unsigned int asn_MAP_NR_oneEighth_enum2value_5[] = {
	2,	/* n12(2) */
	3,	/* n16(3) */
	4,	/* n20(4) */
	5,	/* n24(5) */
	6,	/* n28(6) */
	7,	/* n32(7) */
	8,	/* n36(8) */
	0,	/* n4(0) */
	9,	/* n40(9) */
	10,	/* n44(10) */
	11,	/* n48(11) */
	12,	/* n52(12) */
	13,	/* n56(13) */
	14,	/* n60(14) */
	15,	/* n64(15) */
	1	/* n8(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_oneEighth_specs_5 = {
	asn_MAP_NR_oneEighth_value2enum_5,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_oneEighth_enum2value_5,	/* N => "tag"; sorted by N */
	16,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_oneEighth_tags_5[] = {
	(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_oneEighth_5 = {
	"oneEighth",
	"oneEighth",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_oneEighth_tags_5,
	sizeof(asn_DEF_NR_oneEighth_tags_5)
		/sizeof(asn_DEF_NR_oneEighth_tags_5[0]) - 1, /* 1 */
	asn_DEF_NR_oneEighth_tags_5,	/* Same as above */
	sizeof(asn_DEF_NR_oneEighth_tags_5)
		/sizeof(asn_DEF_NR_oneEighth_tags_5[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_oneEighth_constr_5,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_oneEighth_specs_5	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_oneFourth_value2enum_22[] = {
	{ 0,	2,	"n4" },
	{ 1,	2,	"n8" },
	{ 2,	3,	"n12" },
	{ 3,	3,	"n16" },
	{ 4,	3,	"n20" },
	{ 5,	3,	"n24" },
	{ 6,	3,	"n28" },
	{ 7,	3,	"n32" },
	{ 8,	3,	"n36" },
	{ 9,	3,	"n40" },
	{ 10,	3,	"n44" },
	{ 11,	3,	"n48" },
	{ 12,	3,	"n52" },
	{ 13,	3,	"n56" },
	{ 14,	3,	"n60" },
	{ 15,	3,	"n64" }
};
static const unsigned int asn_MAP_NR_oneFourth_enum2value_22[] = {
	2,	/* n12(2) */
	3,	/* n16(3) */
	4,	/* n20(4) */
	5,	/* n24(5) */
	6,	/* n28(6) */
	7,	/* n32(7) */
	8,	/* n36(8) */
	0,	/* n4(0) */
	9,	/* n40(9) */
	10,	/* n44(10) */
	11,	/* n48(11) */
	12,	/* n52(12) */
	13,	/* n56(13) */
	14,	/* n60(14) */
	15,	/* n64(15) */
	1	/* n8(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_oneFourth_specs_22 = {
	asn_MAP_NR_oneFourth_value2enum_22,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_oneFourth_enum2value_22,	/* N => "tag"; sorted by N */
	16,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_oneFourth_tags_22[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_oneFourth_22 = {
	"oneFourth",
	"oneFourth",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_oneFourth_tags_22,
	sizeof(asn_DEF_NR_oneFourth_tags_22)
		/sizeof(asn_DEF_NR_oneFourth_tags_22[0]) - 1, /* 1 */
	asn_DEF_NR_oneFourth_tags_22,	/* Same as above */
	sizeof(asn_DEF_NR_oneFourth_tags_22)
		/sizeof(asn_DEF_NR_oneFourth_tags_22[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_oneFourth_constr_22,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_oneFourth_specs_22	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_oneHalf_value2enum_39[] = {
	{ 0,	2,	"n4" },
	{ 1,	2,	"n8" },
	{ 2,	3,	"n12" },
	{ 3,	3,	"n16" },
	{ 4,	3,	"n20" },
	{ 5,	3,	"n24" },
	{ 6,	3,	"n28" },
	{ 7,	3,	"n32" },
	{ 8,	3,	"n36" },
	{ 9,	3,	"n40" },
	{ 10,	3,	"n44" },
	{ 11,	3,	"n48" },
	{ 12,	3,	"n52" },
	{ 13,	3,	"n56" },
	{ 14,	3,	"n60" },
	{ 15,	3,	"n64" }
};
static const unsigned int asn_MAP_NR_oneHalf_enum2value_39[] = {
	2,	/* n12(2) */
	3,	/* n16(3) */
	4,	/* n20(4) */
	5,	/* n24(5) */
	6,	/* n28(6) */
	7,	/* n32(7) */
	8,	/* n36(8) */
	0,	/* n4(0) */
	9,	/* n40(9) */
	10,	/* n44(10) */
	11,	/* n48(11) */
	12,	/* n52(12) */
	13,	/* n56(13) */
	14,	/* n60(14) */
	15,	/* n64(15) */
	1	/* n8(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_oneHalf_specs_39 = {
	asn_MAP_NR_oneHalf_value2enum_39,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_oneHalf_enum2value_39,	/* N => "tag"; sorted by N */
	16,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_oneHalf_tags_39[] = {
	(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_oneHalf_39 = {
	"oneHalf",
	"oneHalf",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_oneHalf_tags_39,
	sizeof(asn_DEF_NR_oneHalf_tags_39)
		/sizeof(asn_DEF_NR_oneHalf_tags_39[0]) - 1, /* 1 */
	asn_DEF_NR_oneHalf_tags_39,	/* Same as above */
	sizeof(asn_DEF_NR_oneHalf_tags_39)
		/sizeof(asn_DEF_NR_oneHalf_tags_39[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_oneHalf_constr_39,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_oneHalf_specs_39	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_one_value2enum_56[] = {
	{ 0,	2,	"n4" },
	{ 1,	2,	"n8" },
	{ 2,	3,	"n12" },
	{ 3,	3,	"n16" },
	{ 4,	3,	"n20" },
	{ 5,	3,	"n24" },
	{ 6,	3,	"n28" },
	{ 7,	3,	"n32" },
	{ 8,	3,	"n36" },
	{ 9,	3,	"n40" },
	{ 10,	3,	"n44" },
	{ 11,	3,	"n48" },
	{ 12,	3,	"n52" },
	{ 13,	3,	"n56" },
	{ 14,	3,	"n60" },
	{ 15,	3,	"n64" }
};
static const unsigned int asn_MAP_NR_one_enum2value_56[] = {
	2,	/* n12(2) */
	3,	/* n16(3) */
	4,	/* n20(4) */
	5,	/* n24(5) */
	6,	/* n28(6) */
	7,	/* n32(7) */
	8,	/* n36(8) */
	0,	/* n4(0) */
	9,	/* n40(9) */
	10,	/* n44(10) */
	11,	/* n48(11) */
	12,	/* n52(12) */
	13,	/* n56(13) */
	14,	/* n60(14) */
	15,	/* n64(15) */
	1	/* n8(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_one_specs_56 = {
	asn_MAP_NR_one_value2enum_56,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_one_enum2value_56,	/* N => "tag"; sorted by N */
	16,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_one_tags_56[] = {
	(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_one_56 = {
	"one",
	"one",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_one_tags_56,
	sizeof(asn_DEF_NR_one_tags_56)
		/sizeof(asn_DEF_NR_one_tags_56[0]) - 1, /* 1 */
	asn_DEF_NR_one_tags_56,	/* Same as above */
	sizeof(asn_DEF_NR_one_tags_56)
		/sizeof(asn_DEF_NR_one_tags_56[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_one_constr_56,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_one_specs_56	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_two_value2enum_73[] = {
	{ 0,	2,	"n4" },
	{ 1,	2,	"n8" },
	{ 2,	3,	"n12" },
	{ 3,	3,	"n16" },
	{ 4,	3,	"n20" },
	{ 5,	3,	"n24" },
	{ 6,	3,	"n28" },
	{ 7,	3,	"n32" }
};
static const unsigned int asn_MAP_NR_two_enum2value_73[] = {
	2,	/* n12(2) */
	3,	/* n16(3) */
	4,	/* n20(4) */
	5,	/* n24(5) */
	6,	/* n28(6) */
	7,	/* n32(7) */
	0,	/* n4(0) */
	1	/* n8(1) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_two_specs_73 = {
	asn_MAP_NR_two_value2enum_73,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_two_enum2value_73,	/* N => "tag"; sorted by N */
	8,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_two_tags_73[] = {
	(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_two_73 = {
	"two",
	"two",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_two_tags_73,
	sizeof(asn_DEF_NR_two_tags_73)
		/sizeof(asn_DEF_NR_two_tags_73[0]) - 1, /* 1 */
	asn_DEF_NR_two_tags_73,	/* Same as above */
	sizeof(asn_DEF_NR_two_tags_73)
		/sizeof(asn_DEF_NR_two_tags_73[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_two_constr_73,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_two_specs_73	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16_4[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16, choice.oneEighth),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_oneEighth_5,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"oneEighth"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16, choice.oneFourth),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_oneFourth_22,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"oneFourth"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16, choice.oneHalf),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_oneHalf_39,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"oneHalf"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16, choice.one),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_one_56,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"one"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16, choice.two),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_two_73,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"two"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16, choice.four),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_four_constr_82,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_four_constraint_4
		},
		0, 0, /* No default value */
		"four"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16, choice.eight),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_eight_constr_83,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_eight_constraint_4
		},
		0, 0, /* No default value */
		"eight"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16, choice.sixteen),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_sixteen_constr_84,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_sixteen_constraint_4
		},
		0, 0, /* No default value */
		"sixteen"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_NR_msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16_tag2el_4[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* oneEighth */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* oneFourth */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* oneHalf */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* one */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* two */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* four */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* eight */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 } /* sixteen */
};
static asn_CHOICE_specifics_t asn_SPC_NR_msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16_specs_4 = {
	sizeof(struct NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16),
	offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16, _asn_ctx),
	offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16, present),
	sizeof(((struct NR_RACH_ConfigCommonTwoStepRA_r16__msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16 *)0)->present),
	asn_MAP_NR_msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16_tag2el_4,
	8,	/* Count of tags in the map */
	0, 0,
	-1	/* Extensions start */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16_4 = {
	"msgA-SSB-PerRACH-OccasionAndCB-PreamblesPerSSB-r16",
	"msgA-SSB-PerRACH-OccasionAndCB-PreamblesPerSSB-r16",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16_constr_4,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		CHOICE_constraint
	},
	asn_MBR_NR_msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16_4,
	8,	/* Elements count */
	&asn_SPC_NR_msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16_specs_4	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_msgA_PRACH_RootSequenceIndex_r16_88[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16__msgA_PRACH_RootSequenceIndex_r16, choice.l839),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_l839_constr_89,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_l839_constraint_88
		},
		0, 0, /* No default value */
		"l839"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16__msgA_PRACH_RootSequenceIndex_r16, choice.l139),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_l139_constr_90,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_l139_constraint_88
		},
		0, 0, /* No default value */
		"l139"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16__msgA_PRACH_RootSequenceIndex_r16, choice.l571),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_l571_constr_91,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_l571_constraint_88
		},
		0, 0, /* No default value */
		"l571"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16__msgA_PRACH_RootSequenceIndex_r16, choice.l1151),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_l1151_constr_92,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_l1151_constraint_88
		},
		0, 0, /* No default value */
		"l1151"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_NR_msgA_PRACH_RootSequenceIndex_r16_tag2el_88[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* l839 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* l139 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* l571 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 } /* l1151 */
};
static asn_CHOICE_specifics_t asn_SPC_NR_msgA_PRACH_RootSequenceIndex_r16_specs_88 = {
	sizeof(struct NR_RACH_ConfigCommonTwoStepRA_r16__msgA_PRACH_RootSequenceIndex_r16),
	offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16__msgA_PRACH_RootSequenceIndex_r16, _asn_ctx),
	offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16__msgA_PRACH_RootSequenceIndex_r16, present),
	sizeof(((struct NR_RACH_ConfigCommonTwoStepRA_r16__msgA_PRACH_RootSequenceIndex_r16 *)0)->present),
	asn_MAP_NR_msgA_PRACH_RootSequenceIndex_r16_tag2el_88,
	4,	/* Count of tags in the map */
	0, 0,
	-1	/* Extensions start */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_msgA_PRACH_RootSequenceIndex_r16_88 = {
	"msgA-PRACH-RootSequenceIndex-r16",
	"msgA-PRACH-RootSequenceIndex-r16",
	&asn_OP_CHOICE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_msgA_PRACH_RootSequenceIndex_r16_constr_88,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		CHOICE_constraint
	},
	asn_MBR_NR_msgA_PRACH_RootSequenceIndex_r16_88,
	4,	/* Elements count */
	&asn_SPC_NR_msgA_PRACH_RootSequenceIndex_r16_specs_88	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_msgA_TransMax_r16_value2enum_93[] = {
	{ 0,	2,	"n1" },
	{ 1,	2,	"n2" },
	{ 2,	2,	"n4" },
	{ 3,	2,	"n6" },
	{ 4,	2,	"n8" },
	{ 5,	3,	"n10" },
	{ 6,	3,	"n20" },
	{ 7,	3,	"n50" },
	{ 8,	4,	"n100" },
	{ 9,	4,	"n200" }
};
static const unsigned int asn_MAP_NR_msgA_TransMax_r16_enum2value_93[] = {
	0,	/* n1(0) */
	5,	/* n10(5) */
	8,	/* n100(8) */
	1,	/* n2(1) */
	6,	/* n20(6) */
	9,	/* n200(9) */
	2,	/* n4(2) */
	7,	/* n50(7) */
	3,	/* n6(3) */
	4	/* n8(4) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_msgA_TransMax_r16_specs_93 = {
	asn_MAP_NR_msgA_TransMax_r16_value2enum_93,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_msgA_TransMax_r16_enum2value_93,	/* N => "tag"; sorted by N */
	10,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_msgA_TransMax_r16_tags_93[] = {
	(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_msgA_TransMax_r16_93 = {
	"msgA-TransMax-r16",
	"msgA-TransMax-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_msgA_TransMax_r16_tags_93,
	sizeof(asn_DEF_NR_msgA_TransMax_r16_tags_93)
		/sizeof(asn_DEF_NR_msgA_TransMax_r16_tags_93[0]) - 1, /* 1 */
	asn_DEF_NR_msgA_TransMax_r16_tags_93,	/* Same as above */
	sizeof(asn_DEF_NR_msgA_TransMax_r16_tags_93)
		/sizeof(asn_DEF_NR_msgA_TransMax_r16_tags_93[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_msgA_TransMax_r16_constr_93,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_msgA_TransMax_r16_specs_93	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_msgA_RestrictedSetConfig_r16_value2enum_107[] = {
	{ 0,	15,	"unrestrictedSet" },
	{ 1,	18,	"restrictedSetTypeA" },
	{ 2,	18,	"restrictedSetTypeB" }
};
static const unsigned int asn_MAP_NR_msgA_RestrictedSetConfig_r16_enum2value_107[] = {
	1,	/* restrictedSetTypeA(1) */
	2,	/* restrictedSetTypeB(2) */
	0	/* unrestrictedSet(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_msgA_RestrictedSetConfig_r16_specs_107 = {
	asn_MAP_NR_msgA_RestrictedSetConfig_r16_value2enum_107,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_msgA_RestrictedSetConfig_r16_enum2value_107,	/* N => "tag"; sorted by N */
	3,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_msgA_RestrictedSetConfig_r16_tags_107[] = {
	(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_msgA_RestrictedSetConfig_r16_107 = {
	"msgA-RestrictedSetConfig-r16",
	"msgA-RestrictedSetConfig-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_msgA_RestrictedSetConfig_r16_tags_107,
	sizeof(asn_DEF_NR_msgA_RestrictedSetConfig_r16_tags_107)
		/sizeof(asn_DEF_NR_msgA_RestrictedSetConfig_r16_tags_107[0]) - 1, /* 1 */
	asn_DEF_NR_msgA_RestrictedSetConfig_r16_tags_107,	/* Same as above */
	sizeof(asn_DEF_NR_msgA_RestrictedSetConfig_r16_tags_107)
		/sizeof(asn_DEF_NR_msgA_RestrictedSetConfig_r16_tags_107[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_msgA_RestrictedSetConfig_r16_constr_107,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_msgA_RestrictedSetConfig_r16_specs_107	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ra_PrioritizationForAccessIdentityTwoStep_r16_111[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16__ra_PrioritizationForAccessIdentityTwoStep_r16, ra_Prioritization_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_RA_Prioritization,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ra-Prioritization-r16"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16__ra_PrioritizationForAccessIdentityTwoStep_r16, ra_PrioritizationForAI_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_BIT_STRING,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_ra_PrioritizationForAI_r16_constr_113,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_ra_PrioritizationForAI_r16_constraint_111
		},
		0, 0, /* No default value */
		"ra-PrioritizationForAI-r16"
		},
};
static const ber_tlv_tag_t asn_DEF_NR_ra_PrioritizationForAccessIdentityTwoStep_r16_tags_111[] = {
	(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ra_PrioritizationForAccessIdentityTwoStep_r16_tag2el_111[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* ra-Prioritization-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* ra-PrioritizationForAI-r16 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ra_PrioritizationForAccessIdentityTwoStep_r16_specs_111 = {
	sizeof(struct NR_RACH_ConfigCommonTwoStepRA_r16__ra_PrioritizationForAccessIdentityTwoStep_r16),
	offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16__ra_PrioritizationForAccessIdentityTwoStep_r16, _asn_ctx),
	asn_MAP_NR_ra_PrioritizationForAccessIdentityTwoStep_r16_tag2el_111,
	2,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ra_PrioritizationForAccessIdentityTwoStep_r16_111 = {
	"ra-PrioritizationForAccessIdentityTwoStep-r16",
	"ra-PrioritizationForAccessIdentityTwoStep-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ra_PrioritizationForAccessIdentityTwoStep_r16_tags_111,
	sizeof(asn_DEF_NR_ra_PrioritizationForAccessIdentityTwoStep_r16_tags_111)
		/sizeof(asn_DEF_NR_ra_PrioritizationForAccessIdentityTwoStep_r16_tags_111[0]) - 1, /* 1 */
	asn_DEF_NR_ra_PrioritizationForAccessIdentityTwoStep_r16_tags_111,	/* Same as above */
	sizeof(asn_DEF_NR_ra_PrioritizationForAccessIdentityTwoStep_r16_tags_111)
		/sizeof(asn_DEF_NR_ra_PrioritizationForAccessIdentityTwoStep_r16_tags_111[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ra_PrioritizationForAccessIdentityTwoStep_r16_111,
	2,	/* Elements count */
	&asn_SPC_NR_ra_PrioritizationForAccessIdentityTwoStep_r16_specs_111	/* Additional specs */
};

static const asn_INTEGER_enum_map_t asn_MAP_NR_ra_ContentionResolutionTimer_r16_value2enum_114[] = {
	{ 0,	3,	"sf8" },
	{ 1,	4,	"sf16" },
	{ 2,	4,	"sf24" },
	{ 3,	4,	"sf32" },
	{ 4,	4,	"sf40" },
	{ 5,	4,	"sf48" },
	{ 6,	4,	"sf56" },
	{ 7,	4,	"sf64" }
};
static const unsigned int asn_MAP_NR_ra_ContentionResolutionTimer_r16_enum2value_114[] = {
	1,	/* sf16(1) */
	2,	/* sf24(2) */
	3,	/* sf32(3) */
	4,	/* sf40(4) */
	5,	/* sf48(5) */
	6,	/* sf56(6) */
	7,	/* sf64(7) */
	0	/* sf8(0) */
};
static const asn_INTEGER_specifics_t asn_SPC_NR_ra_ContentionResolutionTimer_r16_specs_114 = {
	asn_MAP_NR_ra_ContentionResolutionTimer_r16_value2enum_114,	/* "tag" => N; sorted by tag */
	asn_MAP_NR_ra_ContentionResolutionTimer_r16_enum2value_114,	/* N => "tag"; sorted by N */
	8,	/* Number of elements in the maps */
	0,	/* Enumeration is not extensible */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_NR_ra_ContentionResolutionTimer_r16_tags_114[] = {
	(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ra_ContentionResolutionTimer_r16_114 = {
	"ra-ContentionResolutionTimer-r16",
	"ra-ContentionResolutionTimer-r16",
	&asn_OP_NativeEnumerated,
	asn_DEF_NR_ra_ContentionResolutionTimer_r16_tags_114,
	sizeof(asn_DEF_NR_ra_ContentionResolutionTimer_r16_tags_114)
		/sizeof(asn_DEF_NR_ra_ContentionResolutionTimer_r16_tags_114[0]) - 1, /* 1 */
	asn_DEF_NR_ra_ContentionResolutionTimer_r16_tags_114,	/* Same as above */
	sizeof(asn_DEF_NR_ra_ContentionResolutionTimer_r16_tags_114)
		/sizeof(asn_DEF_NR_ra_ContentionResolutionTimer_r16_tags_114[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_ra_ContentionResolutionTimer_r16_constr_114,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_NR_ra_ContentionResolutionTimer_r16_specs_114	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_featureCombinationPreamblesList_r17_126[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_NR_FeatureCombinationPreambles_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_NR_featureCombinationPreamblesList_r17_tags_126[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static asn_SET_OF_specifics_t asn_SPC_NR_featureCombinationPreamblesList_r17_specs_126 = {
	sizeof(struct NR_RACH_ConfigCommonTwoStepRA_r16__ext1__featureCombinationPreamblesList_r17),
	offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16__ext1__featureCombinationPreamblesList_r17, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_featureCombinationPreamblesList_r17_126 = {
	"featureCombinationPreamblesList-r17",
	"featureCombinationPreamblesList-r17",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_NR_featureCombinationPreamblesList_r17_tags_126,
	sizeof(asn_DEF_NR_featureCombinationPreamblesList_r17_tags_126)
		/sizeof(asn_DEF_NR_featureCombinationPreamblesList_r17_tags_126[0]) - 1, /* 1 */
	asn_DEF_NR_featureCombinationPreamblesList_r17_tags_126,	/* Same as above */
	sizeof(asn_DEF_NR_featureCombinationPreamblesList_r17_tags_126)
		/sizeof(asn_DEF_NR_featureCombinationPreamblesList_r17_tags_126[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_NR_featureCombinationPreamblesList_r17_constr_126,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_NR_featureCombinationPreamblesList_r17_126,
	1,	/* Single element */
	&asn_SPC_NR_featureCombinationPreamblesList_r17_specs_126	/* Additional specs */
};

static asn_TYPE_member_t asn_MBR_NR_ext1_124[] = {
	{ ATF_POINTER, 2, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16__ext1, ra_PrioritizationForSlicingTwoStep_r17),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_RA_PrioritizationForSlicing_r17,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ra-PrioritizationForSlicingTwoStep-r17"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16__ext1, featureCombinationPreamblesList_r17),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		0,
		&asn_DEF_NR_featureCombinationPreamblesList_r17_126,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_featureCombinationPreamblesList_r17_constr_126,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_featureCombinationPreamblesList_r17_constraint_124
		},
		0, 0, /* No default value */
		"featureCombinationPreamblesList-r17"
		},
};
static const int asn_MAP_NR_ext1_oms_124[] = { 0, 1 };
static const ber_tlv_tag_t asn_DEF_NR_ext1_tags_124[] = {
	(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_ext1_tag2el_124[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* ra-PrioritizationForSlicingTwoStep-r17 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 } /* featureCombinationPreamblesList-r17 */
};
static asn_SEQUENCE_specifics_t asn_SPC_NR_ext1_specs_124 = {
	sizeof(struct NR_RACH_ConfigCommonTwoStepRA_r16__ext1),
	offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16__ext1, _asn_ctx),
	asn_MAP_NR_ext1_tag2el_124,
	2,	/* Count of tags in the map */
	asn_MAP_NR_ext1_oms_124,	/* Optional members */
	2, 0,	/* Root/Additions */
	-1,	/* First extension addition */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_NR_ext1_124 = {
	"ext1",
	"ext1",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_ext1_tags_124,
	sizeof(asn_DEF_NR_ext1_tags_124)
		/sizeof(asn_DEF_NR_ext1_tags_124[0]) - 1, /* 1 */
	asn_DEF_NR_ext1_tags_124,	/* Same as above */
	sizeof(asn_DEF_NR_ext1_tags_124)
		/sizeof(asn_DEF_NR_ext1_tags_124[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_ext1_124,
	2,	/* Elements count */
	&asn_SPC_NR_ext1_specs_124	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_NR_RACH_ConfigCommonTwoStepRA_r16_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16, rach_ConfigGenericTwoStepRA_r16),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_RACH_ConfigGenericTwoStepRA_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"rach-ConfigGenericTwoStepRA-r16"
		},
	{ ATF_POINTER, 14, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16, msgA_TotalNumberOfRA_Preambles_r16),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_msgA_TotalNumberOfRA_Preambles_r16_constr_3,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_msgA_TotalNumberOfRA_Preambles_r16_constraint_1
		},
		0, 0, /* No default value */
		"msgA-TotalNumberOfRA-Preambles-r16"
		},
	{ ATF_POINTER, 13, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16, msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_msgA_SSB_PerRACH_OccasionAndCB_PreamblesPerSSB_r16_4,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"msgA-SSB-PerRACH-OccasionAndCB-PreamblesPerSSB-r16"
		},
	{ ATF_POINTER, 12, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16, msgA_CB_PreamblesPerSSB_PerSharedRO_r16),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_msgA_CB_PreamblesPerSSB_PerSharedRO_r16_constr_85,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_msgA_CB_PreamblesPerSSB_PerSharedRO_r16_constraint_1
		},
		0, 0, /* No default value */
		"msgA-CB-PreamblesPerSSB-PerSharedRO-r16"
		},
	{ ATF_POINTER, 11, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16, msgA_SSB_SharedRO_MaskIndex_r16),
		(ASN_TAG_CLASS_CONTEXT | (4 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NativeInteger,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_NR_msgA_SSB_SharedRO_MaskIndex_r16_constr_86,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_NR_msgA_SSB_SharedRO_MaskIndex_r16_constraint_1
		},
		0, 0, /* No default value */
		"msgA-SSB-SharedRO-MaskIndex-r16"
		},
	{ ATF_POINTER, 10, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16, groupB_ConfiguredTwoStepRA_r16),
		(ASN_TAG_CLASS_CONTEXT | (5 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_GroupB_ConfiguredTwoStepRA_r16,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"groupB-ConfiguredTwoStepRA-r16"
		},
	{ ATF_POINTER, 9, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16, msgA_PRACH_RootSequenceIndex_r16),
		(ASN_TAG_CLASS_CONTEXT | (6 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_NR_msgA_PRACH_RootSequenceIndex_r16_88,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"msgA-PRACH-RootSequenceIndex-r16"
		},
	{ ATF_POINTER, 8, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16, msgA_TransMax_r16),
		(ASN_TAG_CLASS_CONTEXT | (7 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_msgA_TransMax_r16_93,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"msgA-TransMax-r16"
		},
	{ ATF_POINTER, 7, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16, msgA_RSRP_Threshold_r16),
		(ASN_TAG_CLASS_CONTEXT | (8 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_RSRP_Range,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"msgA-RSRP-Threshold-r16"
		},
	{ ATF_POINTER, 6, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16, msgA_RSRP_ThresholdSSB_r16),
		(ASN_TAG_CLASS_CONTEXT | (9 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_RSRP_Range,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"msgA-RSRP-ThresholdSSB-r16"
		},
	{ ATF_POINTER, 5, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16, msgA_SubcarrierSpacing_r16),
		(ASN_TAG_CLASS_CONTEXT | (10 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_SubcarrierSpacing,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"msgA-SubcarrierSpacing-r16"
		},
	{ ATF_POINTER, 4, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16, msgA_RestrictedSetConfig_r16),
		(ASN_TAG_CLASS_CONTEXT | (11 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_msgA_RestrictedSetConfig_r16_107,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"msgA-RestrictedSetConfig-r16"
		},
	{ ATF_POINTER, 3, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16, ra_PrioritizationForAccessIdentityTwoStep_r16),
		(ASN_TAG_CLASS_CONTEXT | (12 << 2)),
		0,
		&asn_DEF_NR_ra_PrioritizationForAccessIdentityTwoStep_r16_111,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ra-PrioritizationForAccessIdentityTwoStep-r16"
		},
	{ ATF_POINTER, 2, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16, ra_ContentionResolutionTimer_r16),
		(ASN_TAG_CLASS_CONTEXT | (13 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_NR_ra_ContentionResolutionTimer_r16_114,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ra-ContentionResolutionTimer-r16"
		},
	{ ATF_POINTER, 1, offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16, ext1),
		(ASN_TAG_CLASS_CONTEXT | (14 << 2)),
		0,
		&asn_DEF_NR_ext1_124,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ext1"
		},
};
static const int asn_MAP_NR_RACH_ConfigCommonTwoStepRA_r16_oms_1[] = { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14 };
static const ber_tlv_tag_t asn_DEF_NR_RACH_ConfigCommonTwoStepRA_r16_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_NR_RACH_ConfigCommonTwoStepRA_r16_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* rach-ConfigGenericTwoStepRA-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* msgA-TotalNumberOfRA-Preambles-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* msgA-SSB-PerRACH-OccasionAndCB-PreamblesPerSSB-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 }, /* msgA-CB-PreamblesPerSSB-PerSharedRO-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (4 << 2)), 4, 0, 0 }, /* msgA-SSB-SharedRO-MaskIndex-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (5 << 2)), 5, 0, 0 }, /* groupB-ConfiguredTwoStepRA-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (6 << 2)), 6, 0, 0 }, /* msgA-PRACH-RootSequenceIndex-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (7 << 2)), 7, 0, 0 }, /* msgA-TransMax-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (8 << 2)), 8, 0, 0 }, /* msgA-RSRP-Threshold-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (9 << 2)), 9, 0, 0 }, /* msgA-RSRP-ThresholdSSB-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (10 << 2)), 10, 0, 0 }, /* msgA-SubcarrierSpacing-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (11 << 2)), 11, 0, 0 }, /* msgA-RestrictedSetConfig-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (12 << 2)), 12, 0, 0 }, /* ra-PrioritizationForAccessIdentityTwoStep-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (13 << 2)), 13, 0, 0 }, /* ra-ContentionResolutionTimer-r16 */
    { (ASN_TAG_CLASS_CONTEXT | (14 << 2)), 14, 0, 0 } /* ext1 */
};
asn_SEQUENCE_specifics_t asn_SPC_NR_RACH_ConfigCommonTwoStepRA_r16_specs_1 = {
	sizeof(struct NR_RACH_ConfigCommonTwoStepRA_r16),
	offsetof(struct NR_RACH_ConfigCommonTwoStepRA_r16, _asn_ctx),
	asn_MAP_NR_RACH_ConfigCommonTwoStepRA_r16_tag2el_1,
	15,	/* Count of tags in the map */
	asn_MAP_NR_RACH_ConfigCommonTwoStepRA_r16_oms_1,	/* Optional members */
	13, 1,	/* Root/Additions */
	14,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_NR_RACH_ConfigCommonTwoStepRA_r16 = {
	"RACH-ConfigCommonTwoStepRA-r16",
	"RACH-ConfigCommonTwoStepRA-r16",
	&asn_OP_SEQUENCE,
	asn_DEF_NR_RACH_ConfigCommonTwoStepRA_r16_tags_1,
	sizeof(asn_DEF_NR_RACH_ConfigCommonTwoStepRA_r16_tags_1)
		/sizeof(asn_DEF_NR_RACH_ConfigCommonTwoStepRA_r16_tags_1[0]), /* 1 */
	asn_DEF_NR_RACH_ConfigCommonTwoStepRA_r16_tags_1,	/* Same as above */
	sizeof(asn_DEF_NR_RACH_ConfigCommonTwoStepRA_r16_tags_1)
		/sizeof(asn_DEF_NR_RACH_ConfigCommonTwoStepRA_r16_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_NR_RACH_ConfigCommonTwoStepRA_r16_1,
	15,	/* Elements count */
	&asn_SPC_NR_RACH_ConfigCommonTwoStepRA_r16_specs_1	/* Additional specs */
};

