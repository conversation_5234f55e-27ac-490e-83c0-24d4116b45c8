/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_Phy_ParametersCommon_H_
#define	_NR_Phy_ParametersCommon_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>
#include <constr_CHOICE.h>
#include <NativeInteger.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_Phy_ParametersCommon__csi_RS_CFRA_ForHO {
	NR_Phy_ParametersCommon__csi_RS_CFRA_ForHO_supported	= 0
} e_NR_Phy_ParametersCommon__csi_RS_CFRA_ForHO;
typedef enum NR_Phy_ParametersCommon__dynamicPRB_BundlingDL {
	NR_Phy_ParametersCommon__dynamicPRB_BundlingDL_supported	= 0
} e_NR_Phy_ParametersCommon__dynamicPRB_BundlingDL;
typedef enum NR_Phy_ParametersCommon__sp_CSI_ReportPUCCH {
	NR_Phy_ParametersCommon__sp_CSI_ReportPUCCH_supported	= 0
} e_NR_Phy_ParametersCommon__sp_CSI_ReportPUCCH;
typedef enum NR_Phy_ParametersCommon__sp_CSI_ReportPUSCH {
	NR_Phy_ParametersCommon__sp_CSI_ReportPUSCH_supported	= 0
} e_NR_Phy_ParametersCommon__sp_CSI_ReportPUSCH;
typedef enum NR_Phy_ParametersCommon__nzp_CSI_RS_IntefMgmt {
	NR_Phy_ParametersCommon__nzp_CSI_RS_IntefMgmt_supported	= 0
} e_NR_Phy_ParametersCommon__nzp_CSI_RS_IntefMgmt;
typedef enum NR_Phy_ParametersCommon__type2_SP_CSI_Feedback_LongPUCCH {
	NR_Phy_ParametersCommon__type2_SP_CSI_Feedback_LongPUCCH_supported	= 0
} e_NR_Phy_ParametersCommon__type2_SP_CSI_Feedback_LongPUCCH;
typedef enum NR_Phy_ParametersCommon__precoderGranularityCORESET {
	NR_Phy_ParametersCommon__precoderGranularityCORESET_supported	= 0
} e_NR_Phy_ParametersCommon__precoderGranularityCORESET;
typedef enum NR_Phy_ParametersCommon__dynamicHARQ_ACK_Codebook {
	NR_Phy_ParametersCommon__dynamicHARQ_ACK_Codebook_supported	= 0
} e_NR_Phy_ParametersCommon__dynamicHARQ_ACK_Codebook;
typedef enum NR_Phy_ParametersCommon__semiStaticHARQ_ACK_Codebook {
	NR_Phy_ParametersCommon__semiStaticHARQ_ACK_Codebook_supported	= 0
} e_NR_Phy_ParametersCommon__semiStaticHARQ_ACK_Codebook;
typedef enum NR_Phy_ParametersCommon__spatialBundlingHARQ_ACK {
	NR_Phy_ParametersCommon__spatialBundlingHARQ_ACK_supported	= 0
} e_NR_Phy_ParametersCommon__spatialBundlingHARQ_ACK;
typedef enum NR_Phy_ParametersCommon__dynamicBetaOffsetInd_HARQ_ACK_CSI {
	NR_Phy_ParametersCommon__dynamicBetaOffsetInd_HARQ_ACK_CSI_supported	= 0
} e_NR_Phy_ParametersCommon__dynamicBetaOffsetInd_HARQ_ACK_CSI;
typedef enum NR_Phy_ParametersCommon__pucch_Repetition_F1_3_4 {
	NR_Phy_ParametersCommon__pucch_Repetition_F1_3_4_supported	= 0
} e_NR_Phy_ParametersCommon__pucch_Repetition_F1_3_4;
typedef enum NR_Phy_ParametersCommon__ra_Type0_PUSCH {
	NR_Phy_ParametersCommon__ra_Type0_PUSCH_supported	= 0
} e_NR_Phy_ParametersCommon__ra_Type0_PUSCH;
typedef enum NR_Phy_ParametersCommon__dynamicSwitchRA_Type0_1_PDSCH {
	NR_Phy_ParametersCommon__dynamicSwitchRA_Type0_1_PDSCH_supported	= 0
} e_NR_Phy_ParametersCommon__dynamicSwitchRA_Type0_1_PDSCH;
typedef enum NR_Phy_ParametersCommon__dynamicSwitchRA_Type0_1_PUSCH {
	NR_Phy_ParametersCommon__dynamicSwitchRA_Type0_1_PUSCH_supported	= 0
} e_NR_Phy_ParametersCommon__dynamicSwitchRA_Type0_1_PUSCH;
typedef enum NR_Phy_ParametersCommon__pdsch_MappingTypeA {
	NR_Phy_ParametersCommon__pdsch_MappingTypeA_supported	= 0
} e_NR_Phy_ParametersCommon__pdsch_MappingTypeA;
typedef enum NR_Phy_ParametersCommon__pdsch_MappingTypeB {
	NR_Phy_ParametersCommon__pdsch_MappingTypeB_supported	= 0
} e_NR_Phy_ParametersCommon__pdsch_MappingTypeB;
typedef enum NR_Phy_ParametersCommon__interleavingVRB_ToPRB_PDSCH {
	NR_Phy_ParametersCommon__interleavingVRB_ToPRB_PDSCH_supported	= 0
} e_NR_Phy_ParametersCommon__interleavingVRB_ToPRB_PDSCH;
typedef enum NR_Phy_ParametersCommon__interSlotFreqHopping_PUSCH {
	NR_Phy_ParametersCommon__interSlotFreqHopping_PUSCH_supported	= 0
} e_NR_Phy_ParametersCommon__interSlotFreqHopping_PUSCH;
typedef enum NR_Phy_ParametersCommon__type1_PUSCH_RepetitionMultiSlots {
	NR_Phy_ParametersCommon__type1_PUSCH_RepetitionMultiSlots_supported	= 0
} e_NR_Phy_ParametersCommon__type1_PUSCH_RepetitionMultiSlots;
typedef enum NR_Phy_ParametersCommon__type2_PUSCH_RepetitionMultiSlots {
	NR_Phy_ParametersCommon__type2_PUSCH_RepetitionMultiSlots_supported	= 0
} e_NR_Phy_ParametersCommon__type2_PUSCH_RepetitionMultiSlots;
typedef enum NR_Phy_ParametersCommon__pusch_RepetitionMultiSlots {
	NR_Phy_ParametersCommon__pusch_RepetitionMultiSlots_supported	= 0
} e_NR_Phy_ParametersCommon__pusch_RepetitionMultiSlots;
typedef enum NR_Phy_ParametersCommon__pdsch_RepetitionMultiSlots {
	NR_Phy_ParametersCommon__pdsch_RepetitionMultiSlots_supported	= 0
} e_NR_Phy_ParametersCommon__pdsch_RepetitionMultiSlots;
typedef enum NR_Phy_ParametersCommon__downlinkSPS {
	NR_Phy_ParametersCommon__downlinkSPS_supported	= 0
} e_NR_Phy_ParametersCommon__downlinkSPS;
typedef enum NR_Phy_ParametersCommon__configuredUL_GrantType1 {
	NR_Phy_ParametersCommon__configuredUL_GrantType1_supported	= 0
} e_NR_Phy_ParametersCommon__configuredUL_GrantType1;
typedef enum NR_Phy_ParametersCommon__configuredUL_GrantType2 {
	NR_Phy_ParametersCommon__configuredUL_GrantType2_supported	= 0
} e_NR_Phy_ParametersCommon__configuredUL_GrantType2;
typedef enum NR_Phy_ParametersCommon__pre_EmptIndication_DL {
	NR_Phy_ParametersCommon__pre_EmptIndication_DL_supported	= 0
} e_NR_Phy_ParametersCommon__pre_EmptIndication_DL;
typedef enum NR_Phy_ParametersCommon__cbg_TransIndication_DL {
	NR_Phy_ParametersCommon__cbg_TransIndication_DL_supported	= 0
} e_NR_Phy_ParametersCommon__cbg_TransIndication_DL;
typedef enum NR_Phy_ParametersCommon__cbg_TransIndication_UL {
	NR_Phy_ParametersCommon__cbg_TransIndication_UL_supported	= 0
} e_NR_Phy_ParametersCommon__cbg_TransIndication_UL;
typedef enum NR_Phy_ParametersCommon__cbg_FlushIndication_DL {
	NR_Phy_ParametersCommon__cbg_FlushIndication_DL_supported	= 0
} e_NR_Phy_ParametersCommon__cbg_FlushIndication_DL;
typedef enum NR_Phy_ParametersCommon__dynamicHARQ_ACK_CodeB_CBG_Retx_DL {
	NR_Phy_ParametersCommon__dynamicHARQ_ACK_CodeB_CBG_Retx_DL_supported	= 0
} e_NR_Phy_ParametersCommon__dynamicHARQ_ACK_CodeB_CBG_Retx_DL;
typedef enum NR_Phy_ParametersCommon__rateMatchingResrcSetSemi_Static {
	NR_Phy_ParametersCommon__rateMatchingResrcSetSemi_Static_supported	= 0
} e_NR_Phy_ParametersCommon__rateMatchingResrcSetSemi_Static;
typedef enum NR_Phy_ParametersCommon__rateMatchingResrcSetDynamic {
	NR_Phy_ParametersCommon__rateMatchingResrcSetDynamic_supported	= 0
} e_NR_Phy_ParametersCommon__rateMatchingResrcSetDynamic;
typedef enum NR_Phy_ParametersCommon__bwp_SwitchingDelay {
	NR_Phy_ParametersCommon__bwp_SwitchingDelay_type1	= 0,
	NR_Phy_ParametersCommon__bwp_SwitchingDelay_type2	= 1
} e_NR_Phy_ParametersCommon__bwp_SwitchingDelay;
typedef enum NR_Phy_ParametersCommon__ext1__dummy {
	NR_Phy_ParametersCommon__ext1__dummy_supported	= 0
} e_NR_Phy_ParametersCommon__ext1__dummy;
typedef enum NR_Phy_ParametersCommon__ext2__maxNumberSearchSpaces {
	NR_Phy_ParametersCommon__ext2__maxNumberSearchSpaces_n10	= 0
} e_NR_Phy_ParametersCommon__ext2__maxNumberSearchSpaces;
typedef enum NR_Phy_ParametersCommon__ext2__rateMatchingCtrlResrcSetDynamic {
	NR_Phy_ParametersCommon__ext2__rateMatchingCtrlResrcSetDynamic_supported	= 0
} e_NR_Phy_ParametersCommon__ext2__rateMatchingCtrlResrcSetDynamic;
typedef enum NR_Phy_ParametersCommon__ext2__maxLayersMIMO_Indication {
	NR_Phy_ParametersCommon__ext2__maxLayersMIMO_Indication_supported	= 0
} e_NR_Phy_ParametersCommon__ext2__maxLayersMIMO_Indication;
typedef enum NR_Phy_ParametersCommon__ext4__twoStepRACH_r16 {
	NR_Phy_ParametersCommon__ext4__twoStepRACH_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext4__twoStepRACH_r16;
typedef enum NR_Phy_ParametersCommon__ext4__dci_Format1_2And0_2_r16 {
	NR_Phy_ParametersCommon__ext4__dci_Format1_2And0_2_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext4__dci_Format1_2And0_2_r16;
typedef enum NR_Phy_ParametersCommon__ext4__monitoringDCI_SameSearchSpace_r16 {
	NR_Phy_ParametersCommon__ext4__monitoringDCI_SameSearchSpace_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext4__monitoringDCI_SameSearchSpace_r16;
typedef enum NR_Phy_ParametersCommon__ext4__type2_CG_ReleaseDCI_0_1_r16 {
	NR_Phy_ParametersCommon__ext4__type2_CG_ReleaseDCI_0_1_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext4__type2_CG_ReleaseDCI_0_1_r16;
typedef enum NR_Phy_ParametersCommon__ext4__type2_CG_ReleaseDCI_0_2_r16 {
	NR_Phy_ParametersCommon__ext4__type2_CG_ReleaseDCI_0_2_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext4__type2_CG_ReleaseDCI_0_2_r16;
typedef enum NR_Phy_ParametersCommon__ext4__sps_ReleaseDCI_1_1_r16 {
	NR_Phy_ParametersCommon__ext4__sps_ReleaseDCI_1_1_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext4__sps_ReleaseDCI_1_1_r16;
typedef enum NR_Phy_ParametersCommon__ext4__sps_ReleaseDCI_1_2_r16 {
	NR_Phy_ParametersCommon__ext4__sps_ReleaseDCI_1_2_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext4__sps_ReleaseDCI_1_2_r16;
typedef enum NR_Phy_ParametersCommon__ext4__csi_TriggerStateNon_ActiveBWP_r16 {
	NR_Phy_ParametersCommon__ext4__csi_TriggerStateNon_ActiveBWP_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext4__csi_TriggerStateNon_ActiveBWP_r16;
typedef enum NR_Phy_ParametersCommon__ext4__separateSMTC_InterIAB_Support_r16 {
	NR_Phy_ParametersCommon__ext4__separateSMTC_InterIAB_Support_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext4__separateSMTC_InterIAB_Support_r16;
typedef enum NR_Phy_ParametersCommon__ext4__separateRACH_IAB_Support_r16 {
	NR_Phy_ParametersCommon__ext4__separateRACH_IAB_Support_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext4__separateRACH_IAB_Support_r16;
typedef enum NR_Phy_ParametersCommon__ext4__ul_flexibleDL_SlotFormatSemiStatic_IAB_r16 {
	NR_Phy_ParametersCommon__ext4__ul_flexibleDL_SlotFormatSemiStatic_IAB_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext4__ul_flexibleDL_SlotFormatSemiStatic_IAB_r16;
typedef enum NR_Phy_ParametersCommon__ext4__ul_flexibleDL_SlotFormatDynamics_IAB_r16 {
	NR_Phy_ParametersCommon__ext4__ul_flexibleDL_SlotFormatDynamics_IAB_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext4__ul_flexibleDL_SlotFormatDynamics_IAB_r16;
typedef enum NR_Phy_ParametersCommon__ext4__dft_S_OFDM_WaveformUL_IAB_r16 {
	NR_Phy_ParametersCommon__ext4__dft_S_OFDM_WaveformUL_IAB_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext4__dft_S_OFDM_WaveformUL_IAB_r16;
typedef enum NR_Phy_ParametersCommon__ext4__dci_25_AI_RNTI_Support_IAB_r16 {
	NR_Phy_ParametersCommon__ext4__dci_25_AI_RNTI_Support_IAB_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext4__dci_25_AI_RNTI_Support_IAB_r16;
typedef enum NR_Phy_ParametersCommon__ext4__t_DeltaReceptionSupport_IAB_r16 {
	NR_Phy_ParametersCommon__ext4__t_DeltaReceptionSupport_IAB_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext4__t_DeltaReceptionSupport_IAB_r16;
typedef enum NR_Phy_ParametersCommon__ext4__guardSymbolReportReception_IAB_r16 {
	NR_Phy_ParametersCommon__ext4__guardSymbolReportReception_IAB_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext4__guardSymbolReportReception_IAB_r16;
typedef enum NR_Phy_ParametersCommon__ext4__harqACK_CB_SpatialBundlingPUCCH_Group_r16 {
	NR_Phy_ParametersCommon__ext4__harqACK_CB_SpatialBundlingPUCCH_Group_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext4__harqACK_CB_SpatialBundlingPUCCH_Group_r16;
typedef enum NR_Phy_ParametersCommon__ext4__crossSlotScheduling_r16__non_SharedSpectrumChAccess_r16 {
	NR_Phy_ParametersCommon__ext4__crossSlotScheduling_r16__non_SharedSpectrumChAccess_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext4__crossSlotScheduling_r16__non_SharedSpectrumChAccess_r16;
typedef enum NR_Phy_ParametersCommon__ext4__crossSlotScheduling_r16__sharedSpectrumChAccess_r16 {
	NR_Phy_ParametersCommon__ext4__crossSlotScheduling_r16__sharedSpectrumChAccess_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext4__crossSlotScheduling_r16__sharedSpectrumChAccess_r16;
typedef enum NR_Phy_ParametersCommon__ext4__maxNumberSRS_PosPathLossEstimateAllServingCells_r16 {
	NR_Phy_ParametersCommon__ext4__maxNumberSRS_PosPathLossEstimateAllServingCells_r16_n1	= 0,
	NR_Phy_ParametersCommon__ext4__maxNumberSRS_PosPathLossEstimateAllServingCells_r16_n4	= 1,
	NR_Phy_ParametersCommon__ext4__maxNumberSRS_PosPathLossEstimateAllServingCells_r16_n8	= 2,
	NR_Phy_ParametersCommon__ext4__maxNumberSRS_PosPathLossEstimateAllServingCells_r16_n16	= 3
} e_NR_Phy_ParametersCommon__ext4__maxNumberSRS_PosPathLossEstimateAllServingCells_r16;
typedef enum NR_Phy_ParametersCommon__ext4__extendedCG_Periodicities_r16 {
	NR_Phy_ParametersCommon__ext4__extendedCG_Periodicities_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext4__extendedCG_Periodicities_r16;
typedef enum NR_Phy_ParametersCommon__ext4__extendedSPS_Periodicities_r16 {
	NR_Phy_ParametersCommon__ext4__extendedSPS_Periodicities_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext4__extendedSPS_Periodicities_r16;
typedef enum NR_Phy_ParametersCommon__ext4__pusch_RepetitionTypeA_r16__sharedSpectrumChAccess_r16 {
	NR_Phy_ParametersCommon__ext4__pusch_RepetitionTypeA_r16__sharedSpectrumChAccess_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext4__pusch_RepetitionTypeA_r16__sharedSpectrumChAccess_r16;
typedef enum NR_Phy_ParametersCommon__ext4__pusch_RepetitionTypeA_r16__non_SharedSpectrumChAccess_r16 {
	NR_Phy_ParametersCommon__ext4__pusch_RepetitionTypeA_r16__non_SharedSpectrumChAccess_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext4__pusch_RepetitionTypeA_r16__non_SharedSpectrumChAccess_r16;
typedef enum NR_Phy_ParametersCommon__ext4__dci_DL_PriorityIndicator_r16 {
	NR_Phy_ParametersCommon__ext4__dci_DL_PriorityIndicator_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext4__dci_DL_PriorityIndicator_r16;
typedef enum NR_Phy_ParametersCommon__ext4__dci_UL_PriorityIndicator_r16 {
	NR_Phy_ParametersCommon__ext4__dci_UL_PriorityIndicator_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext4__dci_UL_PriorityIndicator_r16;
typedef enum NR_Phy_ParametersCommon__ext4__maxNumberPathlossRS_Update_r16 {
	NR_Phy_ParametersCommon__ext4__maxNumberPathlossRS_Update_r16_n4	= 0,
	NR_Phy_ParametersCommon__ext4__maxNumberPathlossRS_Update_r16_n8	= 1,
	NR_Phy_ParametersCommon__ext4__maxNumberPathlossRS_Update_r16_n16	= 2,
	NR_Phy_ParametersCommon__ext4__maxNumberPathlossRS_Update_r16_n32	= 3,
	NR_Phy_ParametersCommon__ext4__maxNumberPathlossRS_Update_r16_n64	= 4
} e_NR_Phy_ParametersCommon__ext4__maxNumberPathlossRS_Update_r16;
typedef enum NR_Phy_ParametersCommon__ext4__type2_HARQ_ACK_Codebook_r16 {
	NR_Phy_ParametersCommon__ext4__type2_HARQ_ACK_Codebook_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext4__type2_HARQ_ACK_Codebook_r16;
typedef enum NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16__maxNumberResWithinSlotAcrossCC_AcrossFR_r16 {
	NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16__maxNumberResWithinSlotAcrossCC_AcrossFR_r16_n2	= 0,
	NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16__maxNumberResWithinSlotAcrossCC_AcrossFR_r16_n4	= 1,
	NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16__maxNumberResWithinSlotAcrossCC_AcrossFR_r16_n8	= 2,
	NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16__maxNumberResWithinSlotAcrossCC_AcrossFR_r16_n12	= 3,
	NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16__maxNumberResWithinSlotAcrossCC_AcrossFR_r16_n16	= 4,
	NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16__maxNumberResWithinSlotAcrossCC_AcrossFR_r16_n32	= 5,
	NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16__maxNumberResWithinSlotAcrossCC_AcrossFR_r16_n64	= 6,
	NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16__maxNumberResWithinSlotAcrossCC_AcrossFR_r16_n128	= 7
} e_NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16__maxNumberResWithinSlotAcrossCC_AcrossFR_r16;
typedef enum NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16__maxNumberResAcrossCC_AcrossFR_r16 {
	NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16__maxNumberResAcrossCC_AcrossFR_r16_n2	= 0,
	NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16__maxNumberResAcrossCC_AcrossFR_r16_n4	= 1,
	NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16__maxNumberResAcrossCC_AcrossFR_r16_n8	= 2,
	NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16__maxNumberResAcrossCC_AcrossFR_r16_n12	= 3,
	NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16__maxNumberResAcrossCC_AcrossFR_r16_n16	= 4,
	NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16__maxNumberResAcrossCC_AcrossFR_r16_n32	= 5,
	NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16__maxNumberResAcrossCC_AcrossFR_r16_n40	= 6,
	NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16__maxNumberResAcrossCC_AcrossFR_r16_n48	= 7,
	NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16__maxNumberResAcrossCC_AcrossFR_r16_n64	= 8,
	NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16__maxNumberResAcrossCC_AcrossFR_r16_n72	= 9,
	NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16__maxNumberResAcrossCC_AcrossFR_r16_n80	= 10,
	NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16__maxNumberResAcrossCC_AcrossFR_r16_n96	= 11,
	NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16__maxNumberResAcrossCC_AcrossFR_r16_n128	= 12,
	NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16__maxNumberResAcrossCC_AcrossFR_r16_n256	= 13
} e_NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16__maxNumberResAcrossCC_AcrossFR_r16;
typedef enum NR_Phy_ParametersCommon__ext4__harqACK_separateMultiDCI_MultiTRP_r16__maxNumberLongPUCCHs_r16 {
	NR_Phy_ParametersCommon__ext4__harqACK_separateMultiDCI_MultiTRP_r16__maxNumberLongPUCCHs_r16_longAndLong	= 0,
	NR_Phy_ParametersCommon__ext4__harqACK_separateMultiDCI_MultiTRP_r16__maxNumberLongPUCCHs_r16_longAndShort	= 1,
	NR_Phy_ParametersCommon__ext4__harqACK_separateMultiDCI_MultiTRP_r16__maxNumberLongPUCCHs_r16_shortAndShort	= 2
} e_NR_Phy_ParametersCommon__ext4__harqACK_separateMultiDCI_MultiTRP_r16__maxNumberLongPUCCHs_r16;
typedef enum NR_Phy_ParametersCommon__ext4__harqACK_jointMultiDCI_MultiTRP_r16 {
	NR_Phy_ParametersCommon__ext4__harqACK_jointMultiDCI_MultiTRP_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext4__harqACK_jointMultiDCI_MultiTRP_r16;
typedef enum NR_Phy_ParametersCommon__ext4__bwp_SwitchingMultiCCs_r16_PR {
	NR_Phy_ParametersCommon__ext4__bwp_SwitchingMultiCCs_r16_PR_NOTHING,	/* No components present */
	NR_Phy_ParametersCommon__ext4__bwp_SwitchingMultiCCs_r16_PR_type1_r16,
	NR_Phy_ParametersCommon__ext4__bwp_SwitchingMultiCCs_r16_PR_type2_r16
} NR_Phy_ParametersCommon__ext4__bwp_SwitchingMultiCCs_r16_PR;
typedef enum NR_Phy_ParametersCommon__ext4__bwp_SwitchingMultiCCs_r16__type1_r16 {
	NR_Phy_ParametersCommon__ext4__bwp_SwitchingMultiCCs_r16__type1_r16_us100	= 0,
	NR_Phy_ParametersCommon__ext4__bwp_SwitchingMultiCCs_r16__type1_r16_us200	= 1
} e_NR_Phy_ParametersCommon__ext4__bwp_SwitchingMultiCCs_r16__type1_r16;
typedef enum NR_Phy_ParametersCommon__ext4__bwp_SwitchingMultiCCs_r16__type2_r16 {
	NR_Phy_ParametersCommon__ext4__bwp_SwitchingMultiCCs_r16__type2_r16_us200	= 0,
	NR_Phy_ParametersCommon__ext4__bwp_SwitchingMultiCCs_r16__type2_r16_us400	= 1,
	NR_Phy_ParametersCommon__ext4__bwp_SwitchingMultiCCs_r16__type2_r16_us800	= 2,
	NR_Phy_ParametersCommon__ext4__bwp_SwitchingMultiCCs_r16__type2_r16_us1000	= 3
} e_NR_Phy_ParametersCommon__ext4__bwp_SwitchingMultiCCs_r16__type2_r16;
typedef enum NR_Phy_ParametersCommon__ext5__targetSMTC_SCG_r16 {
	NR_Phy_ParametersCommon__ext5__targetSMTC_SCG_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext5__targetSMTC_SCG_r16;
typedef enum NR_Phy_ParametersCommon__ext5__supportRepetitionZeroOffsetRV_r16 {
	NR_Phy_ParametersCommon__ext5__supportRepetitionZeroOffsetRV_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext5__supportRepetitionZeroOffsetRV_r16;
typedef enum NR_Phy_ParametersCommon__ext5__cbg_TransInOrderPUSCH_UL_r16 {
	NR_Phy_ParametersCommon__ext5__cbg_TransInOrderPUSCH_UL_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext5__cbg_TransInOrderPUSCH_UL_r16;
typedef enum NR_Phy_ParametersCommon__ext6__bwp_SwitchingMultiDormancyCCs_r16_PR {
	NR_Phy_ParametersCommon__ext6__bwp_SwitchingMultiDormancyCCs_r16_PR_NOTHING,	/* No components present */
	NR_Phy_ParametersCommon__ext6__bwp_SwitchingMultiDormancyCCs_r16_PR_type1_r16,
	NR_Phy_ParametersCommon__ext6__bwp_SwitchingMultiDormancyCCs_r16_PR_type2_r16
} NR_Phy_ParametersCommon__ext6__bwp_SwitchingMultiDormancyCCs_r16_PR;
typedef enum NR_Phy_ParametersCommon__ext6__bwp_SwitchingMultiDormancyCCs_r16__type1_r16 {
	NR_Phy_ParametersCommon__ext6__bwp_SwitchingMultiDormancyCCs_r16__type1_r16_us100	= 0,
	NR_Phy_ParametersCommon__ext6__bwp_SwitchingMultiDormancyCCs_r16__type1_r16_us200	= 1
} e_NR_Phy_ParametersCommon__ext6__bwp_SwitchingMultiDormancyCCs_r16__type1_r16;
typedef enum NR_Phy_ParametersCommon__ext6__bwp_SwitchingMultiDormancyCCs_r16__type2_r16 {
	NR_Phy_ParametersCommon__ext6__bwp_SwitchingMultiDormancyCCs_r16__type2_r16_us200	= 0,
	NR_Phy_ParametersCommon__ext6__bwp_SwitchingMultiDormancyCCs_r16__type2_r16_us400	= 1,
	NR_Phy_ParametersCommon__ext6__bwp_SwitchingMultiDormancyCCs_r16__type2_r16_us800	= 2,
	NR_Phy_ParametersCommon__ext6__bwp_SwitchingMultiDormancyCCs_r16__type2_r16_us1000	= 3
} e_NR_Phy_ParametersCommon__ext6__bwp_SwitchingMultiDormancyCCs_r16__type2_r16;
typedef enum NR_Phy_ParametersCommon__ext6__supportRetx_Diff_CoresetPool_Multi_DCI_TRP_r16 {
	NR_Phy_ParametersCommon__ext6__supportRetx_Diff_CoresetPool_Multi_DCI_TRP_r16_notSupported	= 0
} e_NR_Phy_ParametersCommon__ext6__supportRetx_Diff_CoresetPool_Multi_DCI_TRP_r16;
typedef enum NR_Phy_ParametersCommon__ext6__pdcch_MonitoringAnyOccasionsWithSpanGapCrossCarrierSch_r16 {
	NR_Phy_ParametersCommon__ext6__pdcch_MonitoringAnyOccasionsWithSpanGapCrossCarrierSch_r16_mode2	= 0,
	NR_Phy_ParametersCommon__ext6__pdcch_MonitoringAnyOccasionsWithSpanGapCrossCarrierSch_r16_mode3	= 1
} e_NR_Phy_ParametersCommon__ext6__pdcch_MonitoringAnyOccasionsWithSpanGapCrossCarrierSch_r16;
typedef enum NR_Phy_ParametersCommon__ext7__newBeamIdentifications2PortCSI_RS_r16 {
	NR_Phy_ParametersCommon__ext7__newBeamIdentifications2PortCSI_RS_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext7__newBeamIdentifications2PortCSI_RS_r16;
typedef enum NR_Phy_ParametersCommon__ext7__pathlossEstimation2PortCSI_RS_r16 {
	NR_Phy_ParametersCommon__ext7__pathlossEstimation2PortCSI_RS_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext7__pathlossEstimation2PortCSI_RS_r16;
typedef enum NR_Phy_ParametersCommon__ext8__mux_HARQ_ACK_withoutPUCCH_onPUSCH_r16 {
	NR_Phy_ParametersCommon__ext8__mux_HARQ_ACK_withoutPUCCH_onPUSCH_r16_supported	= 0
} e_NR_Phy_ParametersCommon__ext8__mux_HARQ_ACK_withoutPUCCH_onPUSCH_r16;
typedef enum NR_Phy_ParametersCommon__ext9__guardSymbolReportReception_IAB_r17 {
	NR_Phy_ParametersCommon__ext9__guardSymbolReportReception_IAB_r17_supported	= 0
} e_NR_Phy_ParametersCommon__ext9__guardSymbolReportReception_IAB_r17;
typedef enum NR_Phy_ParametersCommon__ext9__restricted_IAB_DU_BeamReception_r17 {
	NR_Phy_ParametersCommon__ext9__restricted_IAB_DU_BeamReception_r17_supported	= 0
} e_NR_Phy_ParametersCommon__ext9__restricted_IAB_DU_BeamReception_r17;
typedef enum NR_Phy_ParametersCommon__ext9__recommended_IAB_MT_BeamTransmission_r17 {
	NR_Phy_ParametersCommon__ext9__recommended_IAB_MT_BeamTransmission_r17_supported	= 0
} e_NR_Phy_ParametersCommon__ext9__recommended_IAB_MT_BeamTransmission_r17;
typedef enum NR_Phy_ParametersCommon__ext9__case6_TimingAlignmentReception_IAB_r17 {
	NR_Phy_ParametersCommon__ext9__case6_TimingAlignmentReception_IAB_r17_supported	= 0
} e_NR_Phy_ParametersCommon__ext9__case6_TimingAlignmentReception_IAB_r17;
typedef enum NR_Phy_ParametersCommon__ext9__case7_TimingAlignmentReception_IAB_r17 {
	NR_Phy_ParametersCommon__ext9__case7_TimingAlignmentReception_IAB_r17_supported	= 0
} e_NR_Phy_ParametersCommon__ext9__case7_TimingAlignmentReception_IAB_r17;
typedef enum NR_Phy_ParametersCommon__ext9__dl_tx_PowerAdjustment_IAB_r17 {
	NR_Phy_ParametersCommon__ext9__dl_tx_PowerAdjustment_IAB_r17_supported	= 0
} e_NR_Phy_ParametersCommon__ext9__dl_tx_PowerAdjustment_IAB_r17;
typedef enum NR_Phy_ParametersCommon__ext9__desired_ul_tx_PowerAdjustment_r17 {
	NR_Phy_ParametersCommon__ext9__desired_ul_tx_PowerAdjustment_r17_supported	= 0
} e_NR_Phy_ParametersCommon__ext9__desired_ul_tx_PowerAdjustment_r17;
typedef enum NR_Phy_ParametersCommon__ext9__fdm_SoftResourceAvailability_DynamicIndication_r17 {
	NR_Phy_ParametersCommon__ext9__fdm_SoftResourceAvailability_DynamicIndication_r17_supported	= 0
} e_NR_Phy_ParametersCommon__ext9__fdm_SoftResourceAvailability_DynamicIndication_r17;
typedef enum NR_Phy_ParametersCommon__ext9__updated_T_DeltaRangeRecption_r17 {
	NR_Phy_ParametersCommon__ext9__updated_T_DeltaRangeRecption_r17_supported	= 0
} e_NR_Phy_ParametersCommon__ext9__updated_T_DeltaRangeRecption_r17;
typedef enum NR_Phy_ParametersCommon__ext9__slotBasedDynamicPUCCH_Rep_r17 {
	NR_Phy_ParametersCommon__ext9__slotBasedDynamicPUCCH_Rep_r17_supported	= 0
} e_NR_Phy_ParametersCommon__ext9__slotBasedDynamicPUCCH_Rep_r17;
typedef enum NR_Phy_ParametersCommon__ext9__sps_HARQ_ACK_Deferral_r17__non_SharedSpectrumChAccess_r17 {
	NR_Phy_ParametersCommon__ext9__sps_HARQ_ACK_Deferral_r17__non_SharedSpectrumChAccess_r17_supported	= 0
} e_NR_Phy_ParametersCommon__ext9__sps_HARQ_ACK_Deferral_r17__non_SharedSpectrumChAccess_r17;
typedef enum NR_Phy_ParametersCommon__ext9__sps_HARQ_ACK_Deferral_r17__sharedSpectrumChAccess_r17 {
	NR_Phy_ParametersCommon__ext9__sps_HARQ_ACK_Deferral_r17__sharedSpectrumChAccess_r17_supported	= 0
} e_NR_Phy_ParametersCommon__ext9__sps_HARQ_ACK_Deferral_r17__sharedSpectrumChAccess_r17;
typedef enum NR_Phy_ParametersCommon__ext9__mTRP_PDCCH_singleSpan_r17 {
	NR_Phy_ParametersCommon__ext9__mTRP_PDCCH_singleSpan_r17_supported	= 0
} e_NR_Phy_ParametersCommon__ext9__mTRP_PDCCH_singleSpan_r17;
typedef enum NR_Phy_ParametersCommon__ext9__supportedActivatedPRS_ProcessingWindow_r17 {
	NR_Phy_ParametersCommon__ext9__supportedActivatedPRS_ProcessingWindow_r17_n2	= 0,
	NR_Phy_ParametersCommon__ext9__supportedActivatedPRS_ProcessingWindow_r17_n3	= 1,
	NR_Phy_ParametersCommon__ext9__supportedActivatedPRS_ProcessingWindow_r17_n4	= 2
} e_NR_Phy_ParametersCommon__ext9__supportedActivatedPRS_ProcessingWindow_r17;
typedef enum NR_Phy_ParametersCommon__ext9__cg_TimeDomainAllocationExtension_r17 {
	NR_Phy_ParametersCommon__ext9__cg_TimeDomainAllocationExtension_r17_supported	= 0
} e_NR_Phy_ParametersCommon__ext9__cg_TimeDomainAllocationExtension_r17;
typedef enum NR_Phy_ParametersCommon__ext10__ta_BasedPDC_TN_NonSharedSpectrumChAccess_r17 {
	NR_Phy_ParametersCommon__ext10__ta_BasedPDC_TN_NonSharedSpectrumChAccess_r17_supported	= 0
} e_NR_Phy_ParametersCommon__ext10__ta_BasedPDC_TN_NonSharedSpectrumChAccess_r17;
typedef enum NR_Phy_ParametersCommon__ext10__directionalCollisionDC_IAB_r17 {
	NR_Phy_ParametersCommon__ext10__directionalCollisionDC_IAB_r17_supported	= 0
} e_NR_Phy_ParametersCommon__ext10__directionalCollisionDC_IAB_r17;
typedef enum NR_Phy_ParametersCommon__ext11__priorityIndicatorInDCI_Multicast_r17 {
	NR_Phy_ParametersCommon__ext11__priorityIndicatorInDCI_Multicast_r17_supported	= 0
} e_NR_Phy_ParametersCommon__ext11__priorityIndicatorInDCI_Multicast_r17;
typedef enum NR_Phy_ParametersCommon__ext11__priorityIndicatorInDCI_SPS_Multicast_r17 {
	NR_Phy_ParametersCommon__ext11__priorityIndicatorInDCI_SPS_Multicast_r17_supported	= 0
} e_NR_Phy_ParametersCommon__ext11__priorityIndicatorInDCI_SPS_Multicast_r17;
typedef enum NR_Phy_ParametersCommon__ext11__twoHARQ_ACK_CodebookForUnicastAndMulticast_r17 {
	NR_Phy_ParametersCommon__ext11__twoHARQ_ACK_CodebookForUnicastAndMulticast_r17_supported	= 0
} e_NR_Phy_ParametersCommon__ext11__twoHARQ_ACK_CodebookForUnicastAndMulticast_r17;
typedef enum NR_Phy_ParametersCommon__ext11__multiPUCCH_HARQ_ACK_ForMulticastUnicast_r17 {
	NR_Phy_ParametersCommon__ext11__multiPUCCH_HARQ_ACK_ForMulticastUnicast_r17_supported	= 0
} e_NR_Phy_ParametersCommon__ext11__multiPUCCH_HARQ_ACK_ForMulticastUnicast_r17;
typedef enum NR_Phy_ParametersCommon__ext11__srs_AdditionalRepetition_r17 {
	NR_Phy_ParametersCommon__ext11__srs_AdditionalRepetition_r17_supported	= 0
} e_NR_Phy_ParametersCommon__ext11__srs_AdditionalRepetition_r17;
typedef enum NR_Phy_ParametersCommon__ext11__pusch_Repetition_CG_SDT_r17 {
	NR_Phy_ParametersCommon__ext11__pusch_Repetition_CG_SDT_r17_supported	= 0
} e_NR_Phy_ParametersCommon__ext11__pusch_Repetition_CG_SDT_r17;

/* Forward declarations */
struct NR_CarrierAggregationVariant;
struct NR_CodebookVariantsList_r16;

/* NR_Phy-ParametersCommon */
typedef struct NR_Phy_ParametersCommon {
	long	*csi_RS_CFRA_ForHO;	/* OPTIONAL */
	long	*dynamicPRB_BundlingDL;	/* OPTIONAL */
	long	*sp_CSI_ReportPUCCH;	/* OPTIONAL */
	long	*sp_CSI_ReportPUSCH;	/* OPTIONAL */
	long	*nzp_CSI_RS_IntefMgmt;	/* OPTIONAL */
	long	*type2_SP_CSI_Feedback_LongPUCCH;	/* OPTIONAL */
	long	*precoderGranularityCORESET;	/* OPTIONAL */
	long	*dynamicHARQ_ACK_Codebook;	/* OPTIONAL */
	long	*semiStaticHARQ_ACK_Codebook;	/* OPTIONAL */
	long	*spatialBundlingHARQ_ACK;	/* OPTIONAL */
	long	*dynamicBetaOffsetInd_HARQ_ACK_CSI;	/* OPTIONAL */
	long	*pucch_Repetition_F1_3_4;	/* OPTIONAL */
	long	*ra_Type0_PUSCH;	/* OPTIONAL */
	long	*dynamicSwitchRA_Type0_1_PDSCH;	/* OPTIONAL */
	long	*dynamicSwitchRA_Type0_1_PUSCH;	/* OPTIONAL */
	long	*pdsch_MappingTypeA;	/* OPTIONAL */
	long	*pdsch_MappingTypeB;	/* OPTIONAL */
	long	*interleavingVRB_ToPRB_PDSCH;	/* OPTIONAL */
	long	*interSlotFreqHopping_PUSCH;	/* OPTIONAL */
	long	*type1_PUSCH_RepetitionMultiSlots;	/* OPTIONAL */
	long	*type2_PUSCH_RepetitionMultiSlots;	/* OPTIONAL */
	long	*pusch_RepetitionMultiSlots;	/* OPTIONAL */
	long	*pdsch_RepetitionMultiSlots;	/* OPTIONAL */
	long	*downlinkSPS;	/* OPTIONAL */
	long	*configuredUL_GrantType1;	/* OPTIONAL */
	long	*configuredUL_GrantType2;	/* OPTIONAL */
	long	*pre_EmptIndication_DL;	/* OPTIONAL */
	long	*cbg_TransIndication_DL;	/* OPTIONAL */
	long	*cbg_TransIndication_UL;	/* OPTIONAL */
	long	*cbg_FlushIndication_DL;	/* OPTIONAL */
	long	*dynamicHARQ_ACK_CodeB_CBG_Retx_DL;	/* OPTIONAL */
	long	*rateMatchingResrcSetSemi_Static;	/* OPTIONAL */
	long	*rateMatchingResrcSetDynamic;	/* OPTIONAL */
	long	*bwp_SwitchingDelay;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	struct NR_Phy_ParametersCommon__ext1 {
		long	*dummy;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext1;
	struct NR_Phy_ParametersCommon__ext2 {
		long	*maxNumberSearchSpaces;	/* OPTIONAL */
		long	*rateMatchingCtrlResrcSetDynamic;	/* OPTIONAL */
		long	*maxLayersMIMO_Indication;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext2;
	struct NR_Phy_ParametersCommon__ext3 {
		struct NR_CarrierAggregationVariant	*spCellPlacement;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext3;
	struct NR_Phy_ParametersCommon__ext4 {
		long	*twoStepRACH_r16;	/* OPTIONAL */
		long	*dci_Format1_2And0_2_r16;	/* OPTIONAL */
		long	*monitoringDCI_SameSearchSpace_r16;	/* OPTIONAL */
		long	*type2_CG_ReleaseDCI_0_1_r16;	/* OPTIONAL */
		long	*type2_CG_ReleaseDCI_0_2_r16;	/* OPTIONAL */
		long	*sps_ReleaseDCI_1_1_r16;	/* OPTIONAL */
		long	*sps_ReleaseDCI_1_2_r16;	/* OPTIONAL */
		long	*csi_TriggerStateNon_ActiveBWP_r16;	/* OPTIONAL */
		long	*separateSMTC_InterIAB_Support_r16;	/* OPTIONAL */
		long	*separateRACH_IAB_Support_r16;	/* OPTIONAL */
		long	*ul_flexibleDL_SlotFormatSemiStatic_IAB_r16;	/* OPTIONAL */
		long	*ul_flexibleDL_SlotFormatDynamics_IAB_r16;	/* OPTIONAL */
		long	*dft_S_OFDM_WaveformUL_IAB_r16;	/* OPTIONAL */
		long	*dci_25_AI_RNTI_Support_IAB_r16;	/* OPTIONAL */
		long	*t_DeltaReceptionSupport_IAB_r16;	/* OPTIONAL */
		long	*guardSymbolReportReception_IAB_r16;	/* OPTIONAL */
		long	*harqACK_CB_SpatialBundlingPUCCH_Group_r16;	/* OPTIONAL */
		struct NR_Phy_ParametersCommon__ext4__crossSlotScheduling_r16 {
			long	*non_SharedSpectrumChAccess_r16;	/* OPTIONAL */
			long	*sharedSpectrumChAccess_r16;	/* OPTIONAL */
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *crossSlotScheduling_r16;
		long	*maxNumberSRS_PosPathLossEstimateAllServingCells_r16;	/* OPTIONAL */
		long	*extendedCG_Periodicities_r16;	/* OPTIONAL */
		long	*extendedSPS_Periodicities_r16;	/* OPTIONAL */
		struct NR_CodebookVariantsList_r16	*codebookVariantsList_r16;	/* OPTIONAL */
		struct NR_Phy_ParametersCommon__ext4__pusch_RepetitionTypeA_r16 {
			long	*sharedSpectrumChAccess_r16;	/* OPTIONAL */
			long	*non_SharedSpectrumChAccess_r16;	/* OPTIONAL */
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *pusch_RepetitionTypeA_r16;
		long	*dci_DL_PriorityIndicator_r16;	/* OPTIONAL */
		long	*dci_UL_PriorityIndicator_r16;	/* OPTIONAL */
		long	*maxNumberPathlossRS_Update_r16;	/* OPTIONAL */
		long	*type2_HARQ_ACK_Codebook_r16;	/* OPTIONAL */
		struct NR_Phy_ParametersCommon__ext4__maxTotalResourcesForAcrossFreqRanges_r16 {
			long	*maxNumberResWithinSlotAcrossCC_AcrossFR_r16;	/* OPTIONAL */
			long	*maxNumberResAcrossCC_AcrossFR_r16;	/* OPTIONAL */
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *maxTotalResourcesForAcrossFreqRanges_r16;
		struct NR_Phy_ParametersCommon__ext4__harqACK_separateMultiDCI_MultiTRP_r16 {
			long	*maxNumberLongPUCCHs_r16;	/* OPTIONAL */
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *harqACK_separateMultiDCI_MultiTRP_r16;
		long	*harqACK_jointMultiDCI_MultiTRP_r16;	/* OPTIONAL */
		struct NR_Phy_ParametersCommon__ext4__bwp_SwitchingMultiCCs_r16 {
			NR_Phy_ParametersCommon__ext4__bwp_SwitchingMultiCCs_r16_PR present;
			union NR_Phy_ParametersCommon__NR_ext4__NR_bwp_SwitchingMultiCCs_r16_u {
				long	 type1_r16;
				long	 type2_r16;
			} choice;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *bwp_SwitchingMultiCCs_r16;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext4;
	struct NR_Phy_ParametersCommon__ext5 {
		long	*targetSMTC_SCG_r16;	/* OPTIONAL */
		long	*supportRepetitionZeroOffsetRV_r16;	/* OPTIONAL */
		long	*cbg_TransInOrderPUSCH_UL_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext5;
	struct NR_Phy_ParametersCommon__ext6 {
		struct NR_Phy_ParametersCommon__ext6__bwp_SwitchingMultiDormancyCCs_r16 {
			NR_Phy_ParametersCommon__ext6__bwp_SwitchingMultiDormancyCCs_r16_PR present;
			union NR_Phy_ParametersCommon__NR_ext6__NR_bwp_SwitchingMultiDormancyCCs_r16_u {
				long	 type1_r16;
				long	 type2_r16;
			} choice;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *bwp_SwitchingMultiDormancyCCs_r16;
		long	*supportRetx_Diff_CoresetPool_Multi_DCI_TRP_r16;	/* OPTIONAL */
		long	*pdcch_MonitoringAnyOccasionsWithSpanGapCrossCarrierSch_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext6;
	struct NR_Phy_ParametersCommon__ext7 {
		long	*newBeamIdentifications2PortCSI_RS_r16;	/* OPTIONAL */
		long	*pathlossEstimation2PortCSI_RS_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext7;
	struct NR_Phy_ParametersCommon__ext8 {
		long	*mux_HARQ_ACK_withoutPUCCH_onPUSCH_r16;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext8;
	struct NR_Phy_ParametersCommon__ext9 {
		long	*guardSymbolReportReception_IAB_r17;	/* OPTIONAL */
		long	*restricted_IAB_DU_BeamReception_r17;	/* OPTIONAL */
		long	*recommended_IAB_MT_BeamTransmission_r17;	/* OPTIONAL */
		long	*case6_TimingAlignmentReception_IAB_r17;	/* OPTIONAL */
		long	*case7_TimingAlignmentReception_IAB_r17;	/* OPTIONAL */
		long	*dl_tx_PowerAdjustment_IAB_r17;	/* OPTIONAL */
		long	*desired_ul_tx_PowerAdjustment_r17;	/* OPTIONAL */
		long	*fdm_SoftResourceAvailability_DynamicIndication_r17;	/* OPTIONAL */
		long	*updated_T_DeltaRangeRecption_r17;	/* OPTIONAL */
		long	*slotBasedDynamicPUCCH_Rep_r17;	/* OPTIONAL */
		struct NR_Phy_ParametersCommon__ext9__sps_HARQ_ACK_Deferral_r17 {
			long	*non_SharedSpectrumChAccess_r17;	/* OPTIONAL */
			long	*sharedSpectrumChAccess_r17;	/* OPTIONAL */
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *sps_HARQ_ACK_Deferral_r17;
		long	*unifiedJointTCI_commonUpdate_r17;	/* OPTIONAL */
		long	*mTRP_PDCCH_singleSpan_r17;	/* OPTIONAL */
		long	*supportedActivatedPRS_ProcessingWindow_r17;	/* OPTIONAL */
		long	*cg_TimeDomainAllocationExtension_r17;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext9;
	struct NR_Phy_ParametersCommon__ext10 {
		long	*ta_BasedPDC_TN_NonSharedSpectrumChAccess_r17;	/* OPTIONAL */
		long	*directionalCollisionDC_IAB_r17;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext10;
	struct NR_Phy_ParametersCommon__ext11 {
		long	*priorityIndicatorInDCI_Multicast_r17;	/* OPTIONAL */
		long	*priorityIndicatorInDCI_SPS_Multicast_r17;	/* OPTIONAL */
		long	*twoHARQ_ACK_CodebookForUnicastAndMulticast_r17;	/* OPTIONAL */
		long	*multiPUCCH_HARQ_ACK_ForMulticastUnicast_r17;	/* OPTIONAL */
		long	*srs_AdditionalRepetition_r17;	/* OPTIONAL */
		long	*pusch_Repetition_CG_SDT_r17;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext11;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_Phy_ParametersCommon_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_csi_RS_CFRA_ForHO_2;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dynamicPRB_BundlingDL_4;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_sp_CSI_ReportPUCCH_6;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_sp_CSI_ReportPUSCH_8;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_nzp_CSI_RS_IntefMgmt_10;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_type2_SP_CSI_Feedback_LongPUCCH_12;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_precoderGranularityCORESET_14;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dynamicHARQ_ACK_Codebook_16;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_semiStaticHARQ_ACK_Codebook_18;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_spatialBundlingHARQ_ACK_20;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dynamicBetaOffsetInd_HARQ_ACK_CSI_22;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pucch_Repetition_F1_3_4_24;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_ra_Type0_PUSCH_26;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dynamicSwitchRA_Type0_1_PDSCH_28;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dynamicSwitchRA_Type0_1_PUSCH_30;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pdsch_MappingTypeA_32;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pdsch_MappingTypeB_34;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_interleavingVRB_ToPRB_PDSCH_36;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_interSlotFreqHopping_PUSCH_38;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_type1_PUSCH_RepetitionMultiSlots_40;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_type2_PUSCH_RepetitionMultiSlots_42;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pusch_RepetitionMultiSlots_44;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pdsch_RepetitionMultiSlots_46;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_downlinkSPS_48;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_configuredUL_GrantType1_50;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_configuredUL_GrantType2_52;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pre_EmptIndication_DL_54;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_cbg_TransIndication_DL_56;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_cbg_TransIndication_UL_58;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_cbg_FlushIndication_DL_60;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dynamicHARQ_ACK_CodeB_CBG_Retx_DL_62;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_rateMatchingResrcSetSemi_Static_64;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_rateMatchingResrcSetDynamic_66;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_bwp_SwitchingDelay_68;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dummy_73;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumberSearchSpaces_76;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_rateMatchingCtrlResrcSetDynamic_78;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxLayersMIMO_Indication_80;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_twoStepRACH_r16_85;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dci_Format1_2And0_2_r16_87;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_monitoringDCI_SameSearchSpace_r16_89;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_type2_CG_ReleaseDCI_0_1_r16_91;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_type2_CG_ReleaseDCI_0_2_r16_93;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_sps_ReleaseDCI_1_1_r16_95;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_sps_ReleaseDCI_1_2_r16_97;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_csi_TriggerStateNon_ActiveBWP_r16_99;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_separateSMTC_InterIAB_Support_r16_101;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_separateRACH_IAB_Support_r16_103;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_ul_flexibleDL_SlotFormatSemiStatic_IAB_r16_105;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_ul_flexibleDL_SlotFormatDynamics_IAB_r16_107;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dft_S_OFDM_WaveformUL_IAB_r16_109;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dci_25_AI_RNTI_Support_IAB_r16_111;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_t_DeltaReceptionSupport_IAB_r16_113;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_guardSymbolReportReception_IAB_r16_115;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_harqACK_CB_SpatialBundlingPUCCH_Group_r16_117;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_non_SharedSpectrumChAccess_r16_120;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_sharedSpectrumChAccess_r16_122;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumberSRS_PosPathLossEstimateAllServingCells_r16_124;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_extendedCG_Periodicities_r16_129;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_extendedSPS_Periodicities_r16_131;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_sharedSpectrumChAccess_r16_135;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_non_SharedSpectrumChAccess_r16_137;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dci_DL_PriorityIndicator_r16_139;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dci_UL_PriorityIndicator_r16_141;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumberPathlossRS_Update_r16_143;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_type2_HARQ_ACK_Codebook_r16_149;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumberResWithinSlotAcrossCC_AcrossFR_r16_152;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumberResAcrossCC_AcrossFR_r16_161;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumberLongPUCCHs_r16_177;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_harqACK_jointMultiDCI_MultiTRP_r16_181;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_type1_r16_184;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_type2_r16_187;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_targetSMTC_SCG_r16_193;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_supportRepetitionZeroOffsetRV_r16_195;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_cbg_TransInOrderPUSCH_UL_r16_197;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_type1_r16_201;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_type2_r16_204;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_supportRetx_Diff_CoresetPool_Multi_DCI_TRP_r16_209;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pdcch_MonitoringAnyOccasionsWithSpanGapCrossCarrierSch_r16_211;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_newBeamIdentifications2PortCSI_RS_r16_215;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pathlossEstimation2PortCSI_RS_r16_217;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mux_HARQ_ACK_withoutPUCCH_onPUSCH_r16_220;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_guardSymbolReportReception_IAB_r17_223;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_restricted_IAB_DU_BeamReception_r17_225;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_recommended_IAB_MT_BeamTransmission_r17_227;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_case6_TimingAlignmentReception_IAB_r17_229;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_case7_TimingAlignmentReception_IAB_r17_231;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_dl_tx_PowerAdjustment_IAB_r17_233;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_desired_ul_tx_PowerAdjustment_r17_235;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_fdm_SoftResourceAvailability_DynamicIndication_r17_237;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_updated_T_DeltaRangeRecption_r17_239;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_slotBasedDynamicPUCCH_Rep_r17_241;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_non_SharedSpectrumChAccess_r17_244;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_sharedSpectrumChAccess_r17_246;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mTRP_PDCCH_singleSpan_r17_249;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_supportedActivatedPRS_ProcessingWindow_r17_251;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_cg_TimeDomainAllocationExtension_r17_255;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_ta_BasedPDC_TN_NonSharedSpectrumChAccess_r17_258;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_directionalCollisionDC_IAB_r17_260;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_priorityIndicatorInDCI_Multicast_r17_263;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_priorityIndicatorInDCI_SPS_Multicast_r17_265;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_twoHARQ_ACK_CodebookForUnicastAndMulticast_r17_267;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_multiPUCCH_HARQ_ACK_ForMulticastUnicast_r17_269;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_srs_AdditionalRepetition_r17_271;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pusch_Repetition_CG_SDT_r17_273;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_Phy_ParametersCommon;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_Phy_ParametersCommon_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_Phy_ParametersCommon_1[45];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_CarrierAggregationVariant.h"
#include "NR_CodebookVariantsList-r16.h"

#endif	/* _NR_Phy_ParametersCommon_H_ */
#include <asn_internal.h>
