/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_EUTRA_Parameters_H_
#define	_NR_EUTRA_Parameters_H_


#include <asn_application.h>

/* Including external dependencies */
#include "NR_FreqBandIndicatorEUTRA.h"
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct NR_EUTRA_ParametersCommon;
struct NR_EUTRA_ParametersXDD_Diff;

/* NR_EUTRA-Parameters */
typedef struct NR_EUTRA_Parameters {
	struct NR_EUTRA_Parameters__supportedBandListEUTRA {
		A_SEQUENCE_OF(NR_FreqBandIndicatorEUTRA_t) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} supportedBandListEUTRA;
	struct NR_EUTRA_ParametersCommon	*eutra_ParametersCommon;	/* OPTIONAL */
	struct NR_EUTRA_ParametersXDD_Diff	*eutra_ParametersXDD_Diff;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_EUTRA_Parameters_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_NR_EUTRA_Parameters;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_EUTRA_Parameters_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_EUTRA_Parameters_1[3];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_EUTRA-ParametersCommon.h"
#include "NR_EUTRA-ParametersXDD-Diff.h"

#endif	/* _NR_EUTRA_Parameters_H_ */
#include <asn_internal.h>
