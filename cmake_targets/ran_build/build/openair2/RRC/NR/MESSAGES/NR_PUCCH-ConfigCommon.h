/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_PUCCH_ConfigCommon_H_
#define	_NR_PUCCH_ConfigCommon_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_PUCCH_ConfigCommon__pucch_GroupHopping {
	NR_PUCCH_ConfigCommon__pucch_GroupHopping_neither	= 0,
	NR_PUCCH_ConfigCommon__pucch_GroupHopping_enable	= 1,
	NR_PUCCH_ConfigCommon__pucch_GroupHopping_disable	= 2
} e_NR_PUCCH_ConfigCommon__pucch_GroupHopping;
typedef enum NR_PUCCH_ConfigCommon__ext1__intra_SlotFH_r17 {
	NR_PUCCH_ConfigCommon__ext1__intra_SlotFH_r17_fromLowerEdge	= 0,
	NR_PUCCH_ConfigCommon__ext1__intra_SlotFH_r17_fromUpperEdge	= 1
} e_NR_PUCCH_ConfigCommon__ext1__intra_SlotFH_r17;
typedef enum NR_PUCCH_ConfigCommon__ext1__additionalPRBOffset_r17 {
	NR_PUCCH_ConfigCommon__ext1__additionalPRBOffset_r17_n2	= 0,
	NR_PUCCH_ConfigCommon__ext1__additionalPRBOffset_r17_n3	= 1,
	NR_PUCCH_ConfigCommon__ext1__additionalPRBOffset_r17_n4	= 2,
	NR_PUCCH_ConfigCommon__ext1__additionalPRBOffset_r17_n6	= 3,
	NR_PUCCH_ConfigCommon__ext1__additionalPRBOffset_r17_n8	= 4,
	NR_PUCCH_ConfigCommon__ext1__additionalPRBOffset_r17_n9	= 5,
	NR_PUCCH_ConfigCommon__ext1__additionalPRBOffset_r17_n10	= 6,
	NR_PUCCH_ConfigCommon__ext1__additionalPRBOffset_r17_n12	= 7
} e_NR_PUCCH_ConfigCommon__ext1__additionalPRBOffset_r17;

/* NR_PUCCH-ConfigCommon */
typedef struct NR_PUCCH_ConfigCommon {
	long	*pucch_ResourceCommon;	/* OPTIONAL */
	long	 pucch_GroupHopping;
	long	*hoppingId;	/* OPTIONAL */
	long	*p0_nominal;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	struct NR_PUCCH_ConfigCommon__ext1 {
		long	*nrofPRBs;	/* OPTIONAL */
		long	*intra_SlotFH_r17;	/* OPTIONAL */
		long	*pucch_ResourceCommonRedCap_r17;	/* OPTIONAL */
		long	*additionalPRBOffset_r17;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext1;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_PUCCH_ConfigCommon_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_pucch_GroupHopping_3;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_intra_SlotFH_r17_12;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_additionalPRBOffset_r17_16;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_PUCCH_ConfigCommon;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_PUCCH_ConfigCommon_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_PUCCH_ConfigCommon_1[5];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_PUCCH_ConfigCommon_H_ */
#include <asn_internal.h>
