/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "PC5-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_RRCReconfigurationSidelink_r16_IEs_H_
#define	_NR_RRCReconfigurationSidelink_r16_IEs_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <NativeInteger.h>
#include <OCTET_STRING.h>
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include "NR_SLRB-PC5-ConfigIndex-r16.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_RRCReconfigurationSidelink_r16_IEs__sl_ResetConfig_r16 {
	NR_RRCReconfigurationSidelink_r16_IEs__sl_ResetConfig_r16_true	= 0
} e_NR_RRCReconfigurationSidelink_r16_IEs__sl_ResetConfig_r16;

/* Forward declarations */
struct NR_SetupRelease_SL_MeasConfig_r16;
struct NR_SetupRelease_SL_CSI_RS_Config_r16;
struct NR_RRCReconfigurationSidelink_v1700_IEs;
struct NR_SLRB_Config_r16;

/* NR_RRCReconfigurationSidelink-r16-IEs */
typedef struct NR_RRCReconfigurationSidelink_r16_IEs {
	struct NR_RRCReconfigurationSidelink_r16_IEs__slrb_ConfigToAddModList_r16 {
		A_SEQUENCE_OF(struct NR_SLRB_Config_r16) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *slrb_ConfigToAddModList_r16;
	struct NR_RRCReconfigurationSidelink_r16_IEs__slrb_ConfigToReleaseList_r16 {
		A_SEQUENCE_OF(NR_SLRB_PC5_ConfigIndex_r16_t) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *slrb_ConfigToReleaseList_r16;
	struct NR_SetupRelease_SL_MeasConfig_r16	*sl_MeasConfig_r16;	/* OPTIONAL */
	struct NR_SetupRelease_SL_CSI_RS_Config_r16	*sl_CSI_RS_Config_r16;	/* OPTIONAL */
	long	*sl_ResetConfig_r16;	/* OPTIONAL */
	long	*sl_LatencyBoundCSI_Report_r16;	/* OPTIONAL */
	OCTET_STRING_t	*lateNonCriticalExtension;	/* OPTIONAL */
	struct NR_RRCReconfigurationSidelink_v1700_IEs	*nonCriticalExtension;	/* OPTIONAL */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_RRCReconfigurationSidelink_r16_IEs_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_sl_ResetConfig_r16_8;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_RRCReconfigurationSidelink_r16_IEs;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_RRCReconfigurationSidelink_r16_IEs_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_RRCReconfigurationSidelink_r16_IEs_1[8];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_SetupRelease.h"
#include "NR_RRCReconfigurationSidelink-v1700-IEs.h"
#include "NR_SLRB-Config-r16.h"

#endif	/* _NR_RRCReconfigurationSidelink_r16_IEs_H_ */
#include <asn_internal.h>
