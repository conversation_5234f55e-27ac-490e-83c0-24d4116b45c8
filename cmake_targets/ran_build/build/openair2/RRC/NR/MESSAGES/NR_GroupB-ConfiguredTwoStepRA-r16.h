/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_GroupB_ConfiguredTwoStepRA_r16_H_
#define	_NR_GroupB_ConfiguredTwoStepRA_r16_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <NativeInteger.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_GroupB_ConfiguredTwoStepRA_r16__ra_MsgA_SizeGroupA {
	NR_GroupB_ConfiguredTwoStepRA_r16__ra_MsgA_SizeGroupA_b56	= 0,
	NR_GroupB_ConfiguredTwoStepRA_r16__ra_MsgA_SizeGroupA_b144	= 1,
	NR_GroupB_ConfiguredTwoStepRA_r16__ra_MsgA_SizeGroupA_b208	= 2,
	NR_GroupB_ConfiguredTwoStepRA_r16__ra_MsgA_SizeGroupA_b256	= 3,
	NR_GroupB_ConfiguredTwoStepRA_r16__ra_MsgA_SizeGroupA_b282	= 4,
	NR_GroupB_ConfiguredTwoStepRA_r16__ra_MsgA_SizeGroupA_b480	= 5,
	NR_GroupB_ConfiguredTwoStepRA_r16__ra_MsgA_SizeGroupA_b640	= 6,
	NR_GroupB_ConfiguredTwoStepRA_r16__ra_MsgA_SizeGroupA_b800	= 7,
	NR_GroupB_ConfiguredTwoStepRA_r16__ra_MsgA_SizeGroupA_b1000	= 8,
	NR_GroupB_ConfiguredTwoStepRA_r16__ra_MsgA_SizeGroupA_b72	= 9,
	NR_GroupB_ConfiguredTwoStepRA_r16__ra_MsgA_SizeGroupA_spare6	= 10,
	NR_GroupB_ConfiguredTwoStepRA_r16__ra_MsgA_SizeGroupA_spare5	= 11,
	NR_GroupB_ConfiguredTwoStepRA_r16__ra_MsgA_SizeGroupA_spare4	= 12,
	NR_GroupB_ConfiguredTwoStepRA_r16__ra_MsgA_SizeGroupA_spare3	= 13,
	NR_GroupB_ConfiguredTwoStepRA_r16__ra_MsgA_SizeGroupA_spare2	= 14,
	NR_GroupB_ConfiguredTwoStepRA_r16__ra_MsgA_SizeGroupA_spare1	= 15
} e_NR_GroupB_ConfiguredTwoStepRA_r16__ra_MsgA_SizeGroupA;
typedef enum NR_GroupB_ConfiguredTwoStepRA_r16__messagePowerOffsetGroupB {
	NR_GroupB_ConfiguredTwoStepRA_r16__messagePowerOffsetGroupB_minusinfinity	= 0,
	NR_GroupB_ConfiguredTwoStepRA_r16__messagePowerOffsetGroupB_dB0	= 1,
	NR_GroupB_ConfiguredTwoStepRA_r16__messagePowerOffsetGroupB_dB5	= 2,
	NR_GroupB_ConfiguredTwoStepRA_r16__messagePowerOffsetGroupB_dB8	= 3,
	NR_GroupB_ConfiguredTwoStepRA_r16__messagePowerOffsetGroupB_dB10	= 4,
	NR_GroupB_ConfiguredTwoStepRA_r16__messagePowerOffsetGroupB_dB12	= 5,
	NR_GroupB_ConfiguredTwoStepRA_r16__messagePowerOffsetGroupB_dB15	= 6,
	NR_GroupB_ConfiguredTwoStepRA_r16__messagePowerOffsetGroupB_dB18	= 7
} e_NR_GroupB_ConfiguredTwoStepRA_r16__messagePowerOffsetGroupB;

/* NR_GroupB-ConfiguredTwoStepRA-r16 */
typedef struct NR_GroupB_ConfiguredTwoStepRA_r16 {
	long	 ra_MsgA_SizeGroupA;
	long	 messagePowerOffsetGroupB;
	long	 numberOfRA_PreamblesGroupA;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_GroupB_ConfiguredTwoStepRA_r16_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_ra_MsgA_SizeGroupA_2;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_messagePowerOffsetGroupB_19;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_GroupB_ConfiguredTwoStepRA_r16;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_GroupB_ConfiguredTwoStepRA_r16_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_GroupB_ConfiguredTwoStepRA_r16_1[3];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_GroupB_ConfiguredTwoStepRA_r16_H_ */
#include <asn_internal.h>
