/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_DummyA_H_
#define	_NR_DummyA_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC {
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p2	= 0,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p4	= 1,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p8	= 2,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p12	= 3,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p16	= 4,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p24	= 5,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p32	= 6,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p40	= 7,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p48	= 8,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p56	= 9,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p64	= 10,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p72	= 11,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p80	= 12,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p88	= 13,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p96	= 14,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p104	= 15,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p112	= 16,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p120	= 17,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p128	= 18,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p136	= 19,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p144	= 20,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p152	= 21,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p160	= 22,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p168	= 23,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p176	= 24,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p184	= 25,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p192	= 26,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p200	= 27,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p208	= 28,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p216	= 29,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p224	= 30,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p232	= 31,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p240	= 32,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p248	= 33,
	NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC_p256	= 34
} e_NR_DummyA__maxNumberPortsAcrossNZP_CSI_RS_PerCC;
typedef enum NR_DummyA__maxNumberCS_IM_PerCC {
	NR_DummyA__maxNumberCS_IM_PerCC_n1	= 0,
	NR_DummyA__maxNumberCS_IM_PerCC_n2	= 1,
	NR_DummyA__maxNumberCS_IM_PerCC_n4	= 2,
	NR_DummyA__maxNumberCS_IM_PerCC_n8	= 3,
	NR_DummyA__maxNumberCS_IM_PerCC_n16	= 4,
	NR_DummyA__maxNumberCS_IM_PerCC_n32	= 5
} e_NR_DummyA__maxNumberCS_IM_PerCC;
typedef enum NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC {
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n5	= 0,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n6	= 1,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n7	= 2,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n8	= 3,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n9	= 4,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n10	= 5,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n12	= 6,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n14	= 7,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n16	= 8,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n18	= 9,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n20	= 10,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n22	= 11,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n24	= 12,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n26	= 13,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n28	= 14,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n30	= 15,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n32	= 16,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n34	= 17,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n36	= 18,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n38	= 19,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n40	= 20,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n42	= 21,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n44	= 22,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n46	= 23,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n48	= 24,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n50	= 25,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n52	= 26,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n54	= 27,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n56	= 28,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n58	= 29,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n60	= 30,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n62	= 31,
	NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC_n64	= 32
} e_NR_DummyA__maxNumberSimultaneousCSI_RS_ActBWP_AllCC;
typedef enum NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC {
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p8	= 0,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p12	= 1,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p16	= 2,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p24	= 3,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p32	= 4,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p40	= 5,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p48	= 6,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p56	= 7,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p64	= 8,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p72	= 9,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p80	= 10,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p88	= 11,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p96	= 12,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p104	= 13,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p112	= 14,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p120	= 15,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p128	= 16,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p136	= 17,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p144	= 18,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p152	= 19,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p160	= 20,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p168	= 21,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p176	= 22,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p184	= 23,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p192	= 24,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p200	= 25,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p208	= 26,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p216	= 27,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p224	= 28,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p232	= 29,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p240	= 30,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p248	= 31,
	NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_p256	= 32
} e_NR_DummyA__totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC;

/* NR_DummyA */
typedef struct NR_DummyA {
	long	 maxNumberNZP_CSI_RS_PerCC;
	long	 maxNumberPortsAcrossNZP_CSI_RS_PerCC;
	long	 maxNumberCS_IM_PerCC;
	long	 maxNumberSimultaneousCSI_RS_ActBWP_AllCC;
	long	 totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_DummyA_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumberPortsAcrossNZP_CSI_RS_PerCC_3;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumberCS_IM_PerCC_39;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_maxNumberSimultaneousCSI_RS_ActBWP_AllCC_46;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_totalNumberPortsSimultaneousCSI_RS_ActBWP_AllCC_80;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_DummyA;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_DummyA_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_DummyA_1[5];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_DummyA_H_ */
#include <asn_internal.h>
