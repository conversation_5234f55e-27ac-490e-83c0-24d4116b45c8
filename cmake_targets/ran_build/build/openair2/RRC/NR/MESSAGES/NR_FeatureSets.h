/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_FeatureSets_H_
#define	_NR_FeatureSets_H_


#include <asn_application.h>

/* Including external dependencies */
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct NR_FeatureSetDownlink;
struct NR_FeatureSetDownlinkPerCC;
struct NR_FeatureSetUplink;
struct NR_FeatureSetUplinkPerCC;
struct NR_FeatureSetDownlink_v1540;
struct NR_FeatureSetUplink_v1540;
struct NR_FeatureSetUplinkPerCC_v1540;
struct NR_FeatureSetDownlink_v15a0;
struct NR_FeatureSetDownlink_v1610;
struct NR_FeatureSetUplink_v1610;
struct NR_FeatureSetDownlinkPerCC_v1620;
struct NR_FeatureSetUplink_v1630;
struct NR_FeatureSetUplink_v1640;
struct NR_FeatureSetDownlink_v1700;
struct NR_FeatureSetDownlinkPerCC_v1700;
struct NR_FeatureSetUplink_v1710;
struct NR_FeatureSetUplinkPerCC_v1700;
struct NR_FeatureSetDownlink_v1720;
struct NR_FeatureSetDownlinkPerCC_v1720;
struct NR_FeatureSetUplink_v1720;
struct NR_FeatureSetDownlink_v1730;
struct NR_FeatureSetDownlinkPerCC_v1730;

/* NR_FeatureSets */
typedef struct NR_FeatureSets {
	struct NR_FeatureSets__featureSetsDownlink {
		A_SEQUENCE_OF(struct NR_FeatureSetDownlink) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *featureSetsDownlink;
	struct NR_FeatureSets__featureSetsDownlinkPerCC {
		A_SEQUENCE_OF(struct NR_FeatureSetDownlinkPerCC) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *featureSetsDownlinkPerCC;
	struct NR_FeatureSets__featureSetsUplink {
		A_SEQUENCE_OF(struct NR_FeatureSetUplink) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *featureSetsUplink;
	struct NR_FeatureSets__featureSetsUplinkPerCC {
		A_SEQUENCE_OF(struct NR_FeatureSetUplinkPerCC) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *featureSetsUplinkPerCC;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	struct NR_FeatureSets__ext1 {
		struct NR_FeatureSets__ext1__featureSetsDownlink_v1540 {
			A_SEQUENCE_OF(struct NR_FeatureSetDownlink_v1540) list;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *featureSetsDownlink_v1540;
		struct NR_FeatureSets__ext1__featureSetsUplink_v1540 {
			A_SEQUENCE_OF(struct NR_FeatureSetUplink_v1540) list;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *featureSetsUplink_v1540;
		struct NR_FeatureSets__ext1__featureSetsUplinkPerCC_v1540 {
			A_SEQUENCE_OF(struct NR_FeatureSetUplinkPerCC_v1540) list;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *featureSetsUplinkPerCC_v1540;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext1;
	struct NR_FeatureSets__ext2 {
		struct NR_FeatureSets__ext2__featureSetsDownlink_v15a0 {
			A_SEQUENCE_OF(struct NR_FeatureSetDownlink_v15a0) list;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *featureSetsDownlink_v15a0;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext2;
	struct NR_FeatureSets__ext3 {
		struct NR_FeatureSets__ext3__featureSetsDownlink_v1610 {
			A_SEQUENCE_OF(struct NR_FeatureSetDownlink_v1610) list;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *featureSetsDownlink_v1610;
		struct NR_FeatureSets__ext3__featureSetsUplink_v1610 {
			A_SEQUENCE_OF(struct NR_FeatureSetUplink_v1610) list;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *featureSetsUplink_v1610;
		struct NR_FeatureSets__ext3__featureSetDownlinkPerCC_v1620 {
			A_SEQUENCE_OF(struct NR_FeatureSetDownlinkPerCC_v1620) list;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *featureSetDownlinkPerCC_v1620;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext3;
	struct NR_FeatureSets__ext4 {
		struct NR_FeatureSets__ext4__featureSetsUplink_v1630 {
			A_SEQUENCE_OF(struct NR_FeatureSetUplink_v1630) list;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *featureSetsUplink_v1630;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext4;
	struct NR_FeatureSets__ext5 {
		struct NR_FeatureSets__ext5__featureSetsUplink_v1640 {
			A_SEQUENCE_OF(struct NR_FeatureSetUplink_v1640) list;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *featureSetsUplink_v1640;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext5;
	struct NR_FeatureSets__ext6 {
		struct NR_FeatureSets__ext6__featureSetsDownlink_v1700 {
			A_SEQUENCE_OF(struct NR_FeatureSetDownlink_v1700) list;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *featureSetsDownlink_v1700;
		struct NR_FeatureSets__ext6__featureSetsDownlinkPerCC_v1700 {
			A_SEQUENCE_OF(struct NR_FeatureSetDownlinkPerCC_v1700) list;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *featureSetsDownlinkPerCC_v1700;
		struct NR_FeatureSets__ext6__featureSetsUplink_v1710 {
			A_SEQUENCE_OF(struct NR_FeatureSetUplink_v1710) list;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *featureSetsUplink_v1710;
		struct NR_FeatureSets__ext6__featureSetsUplinkPerCC_v1700 {
			A_SEQUENCE_OF(struct NR_FeatureSetUplinkPerCC_v1700) list;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *featureSetsUplinkPerCC_v1700;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext6;
	struct NR_FeatureSets__ext7 {
		struct NR_FeatureSets__ext7__featureSetsDownlink_v1720 {
			A_SEQUENCE_OF(struct NR_FeatureSetDownlink_v1720) list;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *featureSetsDownlink_v1720;
		struct NR_FeatureSets__ext7__featureSetsDownlinkPerCC_v1720 {
			A_SEQUENCE_OF(struct NR_FeatureSetDownlinkPerCC_v1720) list;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *featureSetsDownlinkPerCC_v1720;
		struct NR_FeatureSets__ext7__featureSetsUplink_v1720 {
			A_SEQUENCE_OF(struct NR_FeatureSetUplink_v1720) list;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *featureSetsUplink_v1720;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext7;
	struct NR_FeatureSets__ext8 {
		struct NR_FeatureSets__ext8__featureSetsDownlink_v1730 {
			A_SEQUENCE_OF(struct NR_FeatureSetDownlink_v1730) list;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *featureSetsDownlink_v1730;
		struct NR_FeatureSets__ext8__featureSetsDownlinkPerCC_v1730 {
			A_SEQUENCE_OF(struct NR_FeatureSetDownlinkPerCC_v1730) list;
			
			/* Context for parsing across buffer boundaries */
			asn_struct_ctx_t _asn_ctx;
		} *featureSetsDownlinkPerCC_v1730;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *ext8;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_FeatureSets_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_NR_FeatureSets;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_FeatureSets_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_FeatureSets_1[12];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_FeatureSetDownlink.h"
#include "NR_FeatureSetDownlinkPerCC.h"
#include "NR_FeatureSetUplink.h"
#include "NR_FeatureSetUplinkPerCC.h"
#include "NR_FeatureSetDownlink-v1540.h"
#include "NR_FeatureSetUplink-v1540.h"
#include "NR_FeatureSetUplinkPerCC-v1540.h"
#include "NR_FeatureSetDownlink-v15a0.h"
#include "NR_FeatureSetDownlink-v1610.h"
#include "NR_FeatureSetUplink-v1610.h"
#include "NR_FeatureSetDownlinkPerCC-v1620.h"
#include "NR_FeatureSetUplink-v1630.h"
#include "NR_FeatureSetUplink-v1640.h"
#include "NR_FeatureSetDownlink-v1700.h"
#include "NR_FeatureSetDownlinkPerCC-v1700.h"
#include "NR_FeatureSetUplink-v1710.h"
#include "NR_FeatureSetUplinkPerCC-v1700.h"
#include "NR_FeatureSetDownlink-v1720.h"
#include "NR_FeatureSetDownlinkPerCC-v1720.h"
#include "NR_FeatureSetUplink-v1720.h"
#include "NR_FeatureSetDownlink-v1730.h"
#include "NR_FeatureSetDownlinkPerCC-v1730.h"

#endif	/* _NR_FeatureSets_H_ */
#include <asn_internal.h>
