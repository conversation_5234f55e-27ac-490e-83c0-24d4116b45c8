/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_DataInactivityTimer_H_
#define	_NR_DataInactivityTimer_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_DataInactivityTimer {
	NR_DataInactivityTimer_s1	= 0,
	NR_DataInactivityTimer_s2	= 1,
	NR_DataInactivityTimer_s3	= 2,
	NR_DataInactivityTimer_s5	= 3,
	NR_DataInactivityTimer_s7	= 4,
	NR_DataInactivityTimer_s10	= 5,
	NR_DataInactivityTimer_s15	= 6,
	NR_DataInactivityTimer_s20	= 7,
	NR_DataInactivityTimer_s40	= 8,
	NR_DataInactivityTimer_s50	= 9,
	NR_DataInactivityTimer_s60	= 10,
	NR_DataInactivityTimer_s80	= 11,
	NR_DataInactivityTimer_s100	= 12,
	NR_DataInactivityTimer_s120	= 13,
	NR_DataInactivityTimer_s150	= 14,
	NR_DataInactivityTimer_s180	= 15
} e_NR_DataInactivityTimer;

/* NR_DataInactivityTimer */
typedef long	 NR_DataInactivityTimer_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_NR_DataInactivityTimer_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_NR_DataInactivityTimer;
extern const asn_INTEGER_specifics_t asn_SPC_NR_DataInactivityTimer_specs_1;
asn_struct_free_f NR_DataInactivityTimer_free;
asn_struct_print_f NR_DataInactivityTimer_print;
asn_constr_check_f NR_DataInactivityTimer_constraint;
xer_type_decoder_f NR_DataInactivityTimer_decode_xer;
xer_type_encoder_f NR_DataInactivityTimer_encode_xer;
per_type_decoder_f NR_DataInactivityTimer_decode_uper;
per_type_encoder_f NR_DataInactivityTimer_encode_uper;
per_type_decoder_f NR_DataInactivityTimer_decode_aper;
per_type_encoder_f NR_DataInactivityTimer_encode_aper;

#ifdef __cplusplus
}
#endif

#endif	/* _NR_DataInactivityTimer_H_ */
#include <asn_internal.h>
