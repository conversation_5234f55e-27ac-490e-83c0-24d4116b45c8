/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_FeatureSetUplinkPerCC_v1700_H_
#define	_NR_FeatureSetUplinkPerCC_v1700_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_FeatureSetUplinkPerCC_v1700__mTRP_PUSCH_RepetitionTypeB_r17 {
	NR_FeatureSetUplinkPerCC_v1700__mTRP_PUSCH_RepetitionTypeB_r17_n1	= 0,
	NR_FeatureSetUplinkPerCC_v1700__mTRP_PUSCH_RepetitionTypeB_r17_n2	= 1,
	NR_FeatureSetUplinkPerCC_v1700__mTRP_PUSCH_RepetitionTypeB_r17_n3	= 2,
	NR_FeatureSetUplinkPerCC_v1700__mTRP_PUSCH_RepetitionTypeB_r17_n4	= 3
} e_NR_FeatureSetUplinkPerCC_v1700__mTRP_PUSCH_RepetitionTypeB_r17;
typedef enum NR_FeatureSetUplinkPerCC_v1700__mTRP_PUSCH_TypeB_CB_r17 {
	NR_FeatureSetUplinkPerCC_v1700__mTRP_PUSCH_TypeB_CB_r17_n1	= 0,
	NR_FeatureSetUplinkPerCC_v1700__mTRP_PUSCH_TypeB_CB_r17_n2	= 1,
	NR_FeatureSetUplinkPerCC_v1700__mTRP_PUSCH_TypeB_CB_r17_n4	= 2
} e_NR_FeatureSetUplinkPerCC_v1700__mTRP_PUSCH_TypeB_CB_r17;

/* Forward declarations */
struct NR_SupportedBandwidth_v1700;

/* NR_FeatureSetUplinkPerCC-v1700 */
typedef struct NR_FeatureSetUplinkPerCC_v1700 {
	struct NR_SupportedBandwidth_v1700	*supportedMinBandwidthUL_r17;	/* OPTIONAL */
	long	*mTRP_PUSCH_RepetitionTypeB_r17;	/* OPTIONAL */
	long	*mTRP_PUSCH_TypeB_CB_r17;	/* OPTIONAL */
	struct NR_SupportedBandwidth_v1700	*supportedBandwidthUL_v1710;	/* OPTIONAL */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_FeatureSetUplinkPerCC_v1700_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mTRP_PUSCH_RepetitionTypeB_r17_3;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_mTRP_PUSCH_TypeB_CB_r17_8;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_FeatureSetUplinkPerCC_v1700;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_FeatureSetUplinkPerCC_v1700_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_FeatureSetUplinkPerCC_v1700_1[4];

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NR_SupportedBandwidth-v1700.h"

#endif	/* _NR_FeatureSetUplinkPerCC_v1700_H_ */
#include <asn_internal.h>
