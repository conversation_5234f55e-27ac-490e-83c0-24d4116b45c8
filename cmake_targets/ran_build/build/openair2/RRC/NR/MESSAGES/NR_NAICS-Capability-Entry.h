/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_NAICS_Capability_Entry_H_
#define	_NR_NAICS_Capability_Entry_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_NAICS_Capability_Entry__numberOfAggregatedPRB {
	NR_NAICS_Capability_Entry__numberOfAggregatedPRB_n50	= 0,
	NR_NAICS_Capability_Entry__numberOfAggregatedPRB_n75	= 1,
	NR_NAICS_Capability_Entry__numberOfAggregatedPRB_n100	= 2,
	NR_NAICS_Capability_Entry__numberOfAggregatedPRB_n125	= 3,
	NR_NAICS_Capability_Entry__numberOfAggregatedPRB_n150	= 4,
	NR_NAICS_Capability_Entry__numberOfAggregatedPRB_n175	= 5,
	NR_NAICS_Capability_Entry__numberOfAggregatedPRB_n200	= 6,
	NR_NAICS_Capability_Entry__numberOfAggregatedPRB_n225	= 7,
	NR_NAICS_Capability_Entry__numberOfAggregatedPRB_n250	= 8,
	NR_NAICS_Capability_Entry__numberOfAggregatedPRB_n275	= 9,
	NR_NAICS_Capability_Entry__numberOfAggregatedPRB_n300	= 10,
	NR_NAICS_Capability_Entry__numberOfAggregatedPRB_n350	= 11,
	NR_NAICS_Capability_Entry__numberOfAggregatedPRB_n400	= 12,
	NR_NAICS_Capability_Entry__numberOfAggregatedPRB_n450	= 13,
	NR_NAICS_Capability_Entry__numberOfAggregatedPRB_n500	= 14,
	NR_NAICS_Capability_Entry__numberOfAggregatedPRB_spare	= 15
} e_NR_NAICS_Capability_Entry__numberOfAggregatedPRB;

/* NR_NAICS-Capability-Entry */
typedef struct NR_NAICS_Capability_Entry {
	long	 numberOfNAICS_CapableCC;
	long	 numberOfAggregatedPRB;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_NAICS_Capability_Entry_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_numberOfAggregatedPRB_3;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_NAICS_Capability_Entry;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_NAICS_Capability_Entry_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_NAICS_Capability_Entry_1[2];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_NAICS_Capability_Entry_H_ */
#include <asn_internal.h>
