/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "NR-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/NR/MESSAGES/ASN.1/nr-rrc-17.3.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -findirect-choice -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/NR/MESSAGES`
 */

#ifndef	_NR_MinSchedulingOffsetPreferenceExt_r17_H_
#define	_NR_MinSchedulingOffsetPreferenceExt_r17_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum NR_MinSchedulingOffsetPreferenceExt_r17__preferredK0_r17__preferredK0_SCS_480kHz_r17 {
	NR_MinSchedulingOffsetPreferenceExt_r17__preferredK0_r17__preferredK0_SCS_480kHz_r17_sl8	= 0,
	NR_MinSchedulingOffsetPreferenceExt_r17__preferredK0_r17__preferredK0_SCS_480kHz_r17_sl16	= 1,
	NR_MinSchedulingOffsetPreferenceExt_r17__preferredK0_r17__preferredK0_SCS_480kHz_r17_sl32	= 2,
	NR_MinSchedulingOffsetPreferenceExt_r17__preferredK0_r17__preferredK0_SCS_480kHz_r17_sl48	= 3
} e_NR_MinSchedulingOffsetPreferenceExt_r17__preferredK0_r17__preferredK0_SCS_480kHz_r17;
typedef enum NR_MinSchedulingOffsetPreferenceExt_r17__preferredK0_r17__preferredK0_SCS_960kHz_r17 {
	NR_MinSchedulingOffsetPreferenceExt_r17__preferredK0_r17__preferredK0_SCS_960kHz_r17_sl8	= 0,
	NR_MinSchedulingOffsetPreferenceExt_r17__preferredK0_r17__preferredK0_SCS_960kHz_r17_sl16	= 1,
	NR_MinSchedulingOffsetPreferenceExt_r17__preferredK0_r17__preferredK0_SCS_960kHz_r17_sl32	= 2,
	NR_MinSchedulingOffsetPreferenceExt_r17__preferredK0_r17__preferredK0_SCS_960kHz_r17_sl48	= 3
} e_NR_MinSchedulingOffsetPreferenceExt_r17__preferredK0_r17__preferredK0_SCS_960kHz_r17;
typedef enum NR_MinSchedulingOffsetPreferenceExt_r17__preferredK2_r17__preferredK2_SCS_480kHz_r17 {
	NR_MinSchedulingOffsetPreferenceExt_r17__preferredK2_r17__preferredK2_SCS_480kHz_r17_sl8	= 0,
	NR_MinSchedulingOffsetPreferenceExt_r17__preferredK2_r17__preferredK2_SCS_480kHz_r17_sl16	= 1,
	NR_MinSchedulingOffsetPreferenceExt_r17__preferredK2_r17__preferredK2_SCS_480kHz_r17_sl32	= 2,
	NR_MinSchedulingOffsetPreferenceExt_r17__preferredK2_r17__preferredK2_SCS_480kHz_r17_sl48	= 3
} e_NR_MinSchedulingOffsetPreferenceExt_r17__preferredK2_r17__preferredK2_SCS_480kHz_r17;
typedef enum NR_MinSchedulingOffsetPreferenceExt_r17__preferredK2_r17__preferredK2_SCS_960kHz_r17 {
	NR_MinSchedulingOffsetPreferenceExt_r17__preferredK2_r17__preferredK2_SCS_960kHz_r17_sl8	= 0,
	NR_MinSchedulingOffsetPreferenceExt_r17__preferredK2_r17__preferredK2_SCS_960kHz_r17_sl16	= 1,
	NR_MinSchedulingOffsetPreferenceExt_r17__preferredK2_r17__preferredK2_SCS_960kHz_r17_sl32	= 2,
	NR_MinSchedulingOffsetPreferenceExt_r17__preferredK2_r17__preferredK2_SCS_960kHz_r17_sl48	= 3
} e_NR_MinSchedulingOffsetPreferenceExt_r17__preferredK2_r17__preferredK2_SCS_960kHz_r17;

/* NR_MinSchedulingOffsetPreferenceExt-r17 */
typedef struct NR_MinSchedulingOffsetPreferenceExt_r17 {
	struct NR_MinSchedulingOffsetPreferenceExt_r17__preferredK0_r17 {
		long	*preferredK0_SCS_480kHz_r17;	/* OPTIONAL */
		long	*preferredK0_SCS_960kHz_r17;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *preferredK0_r17;
	struct NR_MinSchedulingOffsetPreferenceExt_r17__preferredK2_r17 {
		long	*preferredK2_SCS_480kHz_r17;	/* OPTIONAL */
		long	*preferredK2_SCS_960kHz_r17;	/* OPTIONAL */
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *preferredK2_r17;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NR_MinSchedulingOffsetPreferenceExt_r17_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_preferredK0_SCS_480kHz_r17_3;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_preferredK0_SCS_960kHz_r17_8;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_preferredK2_SCS_480kHz_r17_14;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_NR_preferredK2_SCS_960kHz_r17_19;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_NR_MinSchedulingOffsetPreferenceExt_r17;
extern asn_SEQUENCE_specifics_t asn_SPC_NR_MinSchedulingOffsetPreferenceExt_r17_specs_1;
extern asn_TYPE_member_t asn_MBR_NR_MinSchedulingOffsetPreferenceExt_r17_1[2];

#ifdef __cplusplus
}
#endif

#endif	/* _NR_MinSchedulingOffsetPreferenceExt_r17_H_ */
#include <asn_internal.h>
