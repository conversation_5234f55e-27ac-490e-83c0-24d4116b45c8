/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "EUTRA-RRC-Definitions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/RRC/LTE/MESSAGES/ASN.1/lte-rrc-16.13.0.asn1"
 * 	`asn1c -pdu=all -fcompound-names -gen-UPER -no-gen-BER -no-gen-JER -no-gen-OER -gen-APER -no-gen-example -D /home/<USER>/openairinterface5g/cmake_targets/ran_build/build/openair2/RRC/LTE/MESSAGES`
 */

#ifndef	_LTE_BarringPerACDC_Category_r13_H_
#define	_LTE_BarringPerACDC_Category_r13_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringFactor_r13 {
	LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringFactor_r13_p00	= 0,
	LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringFactor_r13_p05	= 1,
	LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringFactor_r13_p10	= 2,
	LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringFactor_r13_p15	= 3,
	LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringFactor_r13_p20	= 4,
	LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringFactor_r13_p25	= 5,
	LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringFactor_r13_p30	= 6,
	LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringFactor_r13_p40	= 7,
	LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringFactor_r13_p50	= 8,
	LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringFactor_r13_p60	= 9,
	LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringFactor_r13_p70	= 10,
	LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringFactor_r13_p75	= 11,
	LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringFactor_r13_p80	= 12,
	LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringFactor_r13_p85	= 13,
	LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringFactor_r13_p90	= 14,
	LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringFactor_r13_p95	= 15
} e_LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringFactor_r13;
typedef enum LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringTime_r13 {
	LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringTime_r13_s4	= 0,
	LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringTime_r13_s8	= 1,
	LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringTime_r13_s16	= 2,
	LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringTime_r13_s32	= 3,
	LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringTime_r13_s64	= 4,
	LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringTime_r13_s128	= 5,
	LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringTime_r13_s256	= 6,
	LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringTime_r13_s512	= 7
} e_LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13__ac_BarringTime_r13;

/* LTE_BarringPerACDC-Category-r13 */
typedef struct LTE_BarringPerACDC_Category_r13 {
	long	 acdc_Category_r13;
	struct LTE_BarringPerACDC_Category_r13__acdc_BarringConfig_r13 {
		long	 ac_BarringFactor_r13;
		long	 ac_BarringTime_r13;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} *acdc_BarringConfig_r13;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} LTE_BarringPerACDC_Category_r13_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_LTE_ac_BarringFactor_r13_4;	// (Use -fall-defs-global to expose) */
/* extern asn_TYPE_descriptor_t asn_DEF_LTE_ac_BarringTime_r13_21;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_LTE_BarringPerACDC_Category_r13;
extern asn_SEQUENCE_specifics_t asn_SPC_LTE_BarringPerACDC_Category_r13_specs_1;
extern asn_TYPE_member_t asn_MBR_LTE_BarringPerACDC_Category_r13_1[2];

#ifdef __cplusplus
}
#endif

#endif	/* _LTE_BarringPerACDC_Category_r13_H_ */
#include <asn_internal.h>
