# OpenAirInterface 5G NR 物理层示波器中文化修改总结

## 修改概述

成功将 `openair1/PHY/TOOLS/nr_phy_scope.c` 文件中的所有英文界面标签和标题修改为中文，并重新编译生成了包含中文界面的示波器库。

## 主要修改内容

### 1. gNB（基站）示波器界面中文化

**窗口标题：**
- 原文：`"NR UL SCOPE gNB"`
- 修改为：`"NR上行示波器 gNB"`

**图表标签：**
- `"Received Signal (Time-Domain, one frame)"` → `"接收信号 (时域, 一帧)"`
- `"SRS Frequency Response (samples, abs)"` → `"SRS频域响应 (样本, 幅度)"`
- `"Channel Frequency domain (RE, one frame)"` → `"信道频域 (RE, 一帧)"`
- `"PUSCH Log-Likelihood Ratios (LLR, mag)"` → `"PUSCH对数似然比 (LLR, 幅度)"`
- `"PUSCH I/Q of MF Output"` → `"PUSCH I/Q匹配滤波输出"`
- `"PUCCH能量vs阈值(dB)"` （已保持中文）
- `"PUCCH I/Q星座图"` （已保持中文）
- `"PUSCH上行吞吐量(kbps)"` （已保持中文）

### 2. UE（用户设备）示波器界面中文化

**窗口标题：**
- 原文：`"NR DL SCOPE UE %d"`
- 修改为：`"NR下行示波器 UE %d"`

**图表标签：**
- `"Received Signal (Time-Domain, one frame)"` → `"接收信号 (时域, 一帧)"`
- `"Channel Impulse Response (samples, abs)"` → `"信道冲激响应 (样本, 幅度)"`
- `"Channel Frequency (RE, one slot)"` → `"信道频域 (RE, 一时隙)"`
- `"PBCH Log-Likelihood Ratios (LLR, mag)"` → `"PBCH对数似然比 (LLR, 幅度)"`
- `"PBCH I/Q of MF Output"` → `"PBCH I/Q匹配滤波输出"`
- `"PDCCH Log-Likelihood Ratios (LLR, mag)"` → `"PDCCH对数似然比 (LLR, 幅度)"`
- `"PDCCH I/Q of MF Output"` → `"PDCCH I/Q匹配滤波输出"`
- `"PDSCH Log-Likelihood Ratios (LLR, mag)"` → `"PDSCH对数似然比 (LLR, 幅度)"`
- `"PDSCH I/Q of MF Output"` → `"PDSCH I/Q匹配滤波输出"`
- `"PDSCH Throughput [frame]/[kbit/s]"` → `"PDSCH吞吐量 [帧]/[kbit/s]"`

### 3. 瀑布图轴标签中文化

- `"X axis:one frame in time"` → `"X轴:时域一帧"`
- `"X axis: Frequency domain, one subframe"` → `"X轴: 频域, 一子帧"`
- `"X axis: one frame time"` → `"X轴: 时域一帧"`
- `"X axis: one slot frequency"` → `"X轴: 频域一时隙"`

## 编译验证

### 编译命令
```bash
./cmake_targets/build_oai --build-lib nrscope
```

### 编译结果
- ✅ 成功生成 `libnrscope.so` 库文件
- ✅ 中文字符串已正确编译到库中（通过hexdump验证）
- ✅ 可执行文件 `nr-softmodem` 和 `nr-uesoftmodem` 已更新

### 验证方法
```bash
# 检查中文字符串是否编译进库
hexdump -C ./cmake_targets/ran_build/build/libnrscope.so | grep "e6 8e a5 e6 94 b6"
# 结果显示找到了"接收信号"的UTF-8编码
```

## 使用方法

### 启动带有中文示波器的gNB
```bash
./cmake_targets/ran_build/build/nr-softmodem \
    -O <配置文件路径> \
    --rfsim \
    -d \
    --log_config.global_log_level info
```

### 启动带有中文示波器的UE
```bash
./cmake_targets/ran_build/build/nr-uesoftmodem \
    -O <配置文件路径> \
    --rfsim \
    -d \
    --log_config.global_log_level info
```

**重要参数说明：**
- `-d`: 启用软示波器和L1/L2统计信息（XForms界面）
- `--rfsim`: 使用RF仿真模式
- `-O`: 指定配置文件

## 技术细节

### 修改的函数
- `create_phy_scope_gnb()`: gNB示波器界面创建
- `create_phy_scope_nrue()`: UE示波器界面创建
- `gNBWaterFall()`: gNB瀑布图显示
- `gNBfreqWaterFall()`: gNB频域瀑布图显示
- `ueWaterFall()`: UE瀑布图显示
- `ueFreqWaterFall()`: UE频域瀑布图显示

### 支持的显示模式
- **XForms模式**: 传统的X11图形界面（已中文化）
- **Web模式**: 基于Web的界面（通过WEBSRVSCOPE宏控制）

### 图表类型
1. **瀑布图**: 显示信号功率随时间的变化
2. **I/Q星座图**: 显示调制信号的星座点
3. **LLR图**: 显示对数似然比值
4. **功率图**: 显示信号功率水平
5. **吞吐量图**: 显示数据传输速率

## 注意事项

1. **字符编码**: 所有中文字符串使用UTF-8编码
2. **显示环境**: 需要支持中文字体的X11环境
3. **编译依赖**: 需要安装libforms库用于XForms界面
4. **运行权限**: 可能需要适当的显示权限设置

## 文件位置

- **源文件**: `openair1/PHY/TOOLS/nr_phy_scope.c`
- **编译库**: `cmake_targets/ran_build/build/libnrscope.so`
- **可执行文件**: 
  - `cmake_targets/ran_build/build/nr-softmodem`
  - `cmake_targets/ran_build/build/nr-uesoftmodem`

现在您可以运行OpenAirInterface 5G NR示波器，界面将显示中文标签和标题，更便于中文用户理解和使用！
