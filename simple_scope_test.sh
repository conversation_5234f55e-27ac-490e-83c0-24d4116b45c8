#!/bin/bash

# 简单的示波器测试脚本
echo "=== NR示波器中文界面测试 ==="
echo ""
echo "检查编译结果："
echo "1. 检查libnrscope.so是否存在："
ls -la ./cmake_targets/ran_build/build/libnrscope.so

echo ""
echo "2. 检查中文字符串是否编译进库中："
echo "搜索'接收信号'的UTF-8编码..."
hexdump -C ./cmake_targets/ran_build/build/libnrscope.so | grep "e6 8e a5 e6 94 b6" | head -3

echo ""
echo "3. 检查可执行文件："
ls -la ./cmake_targets/ran_build/build/nr-softmodem

echo ""
echo "=== 修改总结 ==="
echo "✓ 已将所有图表标签改为中文："
echo "  - '接收信号 (时域, 一帧)'"
echo "  - 'SRS频域响应 (样本, 幅度)'"
echo "  - '信道频域 (RE, 一帧)'"
echo "  - 'PUSCH对数似然比 (LLR, 幅度)'"
echo "  - 'PUSCH I/Q匹配滤波输出'"
echo "  - 'PUCCH能量vs阈值(dB)'"
echo "  - 'PUCCH I/Q星座图'"
echo "  - 'PUSCH上行吞吐量(kbps)'"
echo ""
echo "✓ 已将窗口标题改为中文："
echo "  - gNB: 'NR上行示波器 gNB'"
echo "  - UE: 'NR下行示波器 UE'"
echo ""
echo "✓ 已将瀑布图轴标签改为中文："
echo "  - 'X轴:时域一帧'"
echo "  - 'X轴: 频域, 一子帧'"
echo "  - 'X轴: 频域一时隙'"
echo ""
echo "=== 使用说明 ==="
echo "要运行带有中文示波器的gNB，使用以下命令："
echo "./cmake_targets/ran_build/build/nr-softmodem -O <config_file> --rfsim -d"
echo ""
echo "其中 -d 参数启用示波器界面"
echo "现在界面将显示中文标签和标题！"
