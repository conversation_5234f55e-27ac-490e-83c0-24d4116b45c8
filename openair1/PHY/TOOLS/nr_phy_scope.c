/*
 * Licensed to the OpenAirInterface (OAI) Software Alliance under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The OpenAirInterface Software Alliance licenses this file to You under
 * the OAI Public License, Version 1.1  (the "License"); you may not use this file
 * except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.openairinterface.org/?page_id=698
 *
 * Author and copyright: <PERSON>, open-cells.com
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *-------------------------------------------------------------------------------
 * For more information about the OpenAirInterface (OAI) Software Alliance:
 *      <EMAIL>
 */

/**
 * @file nr_phy_scope.c
 * @brief 5G NR物理层示波器工具 - 用于可视化和调试5G物理层信号
 *
 * 本文件实现了5G NR物理层的实时信号可视化工具，支持：
 * - gNB（基站）侧信号显示：接收信号、信道响应、PUSCH/PUCCH信号等
 * - UE（用户设备）侧信号显示：接收信号、信道估计、PBCH/PDCCH/PDSCH信号等
 * - 支持XForms GUI和Web界面两种显示方式
 * - 实时显示I/Q星座图、LLR、功率谱、瀑布图等
 *
 * 主要功能模块：
 * 1. 图形界面创建和管理
 * 2. 信号数据采集和处理
 * 3. 实时图表更新和显示
 * 4. Web服务器接口支持
 *
 * 文件结构说明：
 *
 * 【头文件和宏定义】(行43-101)
 * - 条件编译控制Web/XForms模式
 * - 颜色配置和常量定义
 *
 * 【Web服务器专用函数】(行145-400)
 * - websrv_cpiqbuff_tomsg(): I/Q数据转Web消息
 * - websrv_cpllrbuff_tomsg(): LLR数据转Web消息
 * - websrv_cplinebuff_tomsg(): 线性数据转Web消息
 * - websrv_get_WF_buffers(): 瀑布图缓冲区管理
 *
 * 【图表创建和管理】(行401-528)
 * - commonGraph(): 通用图表创建
 * - gNBcommonGraph(): gNB专用图表创建
 * - nrUEcommonGraph(): UE专用图表创建
 *
 * 【XForms专用函数】(行529-678)
 * - setRange(): 自动缩放功能
 * - oai_xygraph_getbuff(): 获取图表缓冲区
 * - oai_xygraph(): 通用XY图表绘制
 *
 * 【通用绘制函数】(行680-877)
 * - genericWaterFall(): 瀑布图绘制
 * - genericPowerPerAntena(): 多天线功率显示
 *
 * 【gNB信号显示函数】(行878-1596)
 * - gNBWaterFall(): gNB时域瀑布图
 * - timeResponse(): SRS时域响应
 * - gNBfreqWaterFall(): gNB频域瀑布图
 * - puschLLR(): PUSCH LLR显示
 * - puschIQ(): PUSCH I/Q星座图
 * - pucchEnergy(): PUCCH能量显示
 * - pucchIQ(): PUCCH I/Q星座图
 * - puschThroughtput(): PUSCH吞吐量显示
 *
 * 【gNB界面创建和管理】(行1332-1596)
 * - create_phy_scope_gnb(): 创建gNB示波器界面
 * - phy_scope_gNB(): gNB示波器主更新函数
 * - scope_thread_gNB(): gNB显示线程
 * - gNBinitScope(): gNB示波器初始化
 *
 * 【UE信号显示函数】(行1597-1985)
 * - ueWaterFall(): UE时域瀑布图
 * - ueChannelResponse(): UE信道响应
 * - ueFreqWaterFall(): UE频域瀑布图
 * - uePbchLLR(): PBCH LLR显示
 * - uePbchIQ(): PBCH I/Q星座图
 * - uePcchLLR(): PDCCH LLR显示
 * - uePcchIQ(): PDCCH I/Q星座图
 * - uePdschLLR(): PDSCH LLR显示
 * - uePdschIQ(): PDSCH I/Q星座图
 * - uePdschThroughput(): PDSCH吞吐量显示
 *
 * 【UE界面创建和管理】(行1986-2127)
 * - create_phy_scope_nrue(): 创建UE示波器界面
 * - phy_scope_nrUE(): UE示波器主更新函数
 * - nrUEscopeThread(): UE显示线程
 * - nrUEinitScope(): UE示波器初始化
 * - nrscope_autoinit(): 自动初始化函数
 *
 * 【预留统计功能】(行2128-2213)
 * - 统计显示和重置功能（已禁用）
 */

/* Form definition file generated by fdesign */

// ============================================================================
// 头文件包含和宏定义
// ============================================================================

#include <stdlib.h>
#include "executables/softmodem-common.h"
#include "executables/nr-softmodem-common.h"

#ifdef WEBSRVSCOPE
// Web服务器模式：使用Ulfius HTTP框架提供Web界面
#include <ulfius.h>
#include "common/utils/websrv/websrv.h"
#include "common/utils/websrv/websrv_noforms.h"
#include "common/utils/LOG/log.h"

/* STATICFORXSCOPE宏控制函数可见性：
 * - Web模式下为空（函数为非静态，可被Web服务器调用）
 * - XForms模式下为static（函数为静态，仅内部使用）
 */
#define STATICFORXSCOPE

// 如果尚未定义，则定义缺失的HARQ进程常量
// HARQ（混合自动重传请求）进程数量，用于上行链路调度
#ifndef NR_MAX_ULSCH_HARQ_PROCESSES
#define NR_MAX_ULSCH_HARQ_PROCESSES 16
#endif

// Web模式下重定义XForms函数为Web服务器对应函数
#define fl_add_canvas websrv_fl_add_canvas
#define fl_add_xyplot websrv_fl_add_xyplot
#define fl_get_xyplot_data_pointer websrv_fl_get_xyplot_data_pointer

#else
// XForms GUI模式：使用传统的XForms图形界面库
#include <forms.h>
#define STATICFORXSCOPE static
static const int scope_enb_num_ue = 1;  // XForms模式下默认显示1个UE
#endif

#include "nr_phy_scope.h"  // NR PHY示波器相关定义
#include "phy_scope.h"     // 通用PHY示波器定义

// ============================================================================
// 全局常量定义
// ============================================================================

// 接收天线颜色配置：用于区分不同天线的信号显示
// 输入：天线索引（0-3）
// 输出：对应的颜色值，用于图表显示中区分不同天线
// 作用：提高多天线信号的可视化区分度
// 可修改：可以更改颜色值以适应不同的显示需求
const FL_COLOR rx_antenna_colors[4] = {FL_RED,FL_BLUE,FL_GREEN,FL_YELLOW};

// 瀑布图颜色配置：用于功率谱瀑布图的颜色映射
// 输入：功率级别（0-3，对应不同的功率阈值）
// 输出：对应的颜色值，表示不同的信号强度
// 作用：在瀑布图中用颜色表示信号功率强度
// 可修改：可以调整颜色映射以获得更好的视觉效果
const FL_COLOR water_colors[4] = {FL_BLUE,FL_GREEN,FL_YELLOW,FL_RED};

// ============================================================================
// 图形绘制辅助函数
// ============================================================================

/**
 * @brief 绘制图表符号的回调函数
 * @param obj XForms对象指针
 * @param id 符号ID
 * @param p 点坐标数组
 * @param n 点的数量
 * @param w 宽度（未使用）
 * @param h 高度（未使用）
 *
 * 输入数据：来自XForms图表系统的绘制请求
 * 功能：在图表上绘制黄色的点符号
 * 作用：用于星座图等散点图的符号显示
 * 可修改：可以更改颜色或符号样式
 */
static void drawsymbol(FL_OBJECT *obj, int id,
                       FL_POINT *p, int n, int w, int h) {
  fl_points( p, n, FL_YELLOW);  // 绘制黄色点
}

// 按钮回调函数示例（已禁用）
// 这是一个下行流量开关按钮的实现示例，目前被注释掉
#if 0
static void dl_traffic_on_off( FL_OBJECT *button, long arg) {
  if (fl_get_button(button)) {
    fl_set_object_label(button, "DL Traffic ON");
    otg_enabled = 1;
    fl_set_object_color(button, FL_GREEN, FL_GREEN);
  } else {
    fl_set_object_label(button, "DL Traffic OFF");
    otg_enabled = 0;
    fl_set_object_color(button, FL_RED, FL_RED);
  }
}
#endif

// 瀑布图类型标识符
// 用于区分瀑布图和普通XY图表类型
#define WATERFALL 10000
#ifdef WEBSRVSCOPE
// ============================================================================
// Web服务器模式专用函数
// ============================================================================

/**
 * @brief 将I/Q复数样本数据复制到Web消息缓冲区
 * @param graph 图表对象指针
 * @param c I/Q样本数组指针
 * @param n 样本数量
 * @param id 数据集ID
 * @param base 基础偏移量
 * @return 实际复制的样本数量
 *
 * 输入数据：来自PHY层的I/Q复数样本（如PUSCH、PDSCH解调后的数据）
 * 功能：将复数样本转换为Web前端可显示的格式，并进行排序优化
 * 作用：为Web界面的星座图显示准备数据
 * 参数含义：
 * - graph: 目标图表对象，包含显示配置信息
 * - c: 源I/Q样本数据，每个样本包含实部(r)和虚部(i)
 * - n: 要处理的样本总数
 * - id: 数据集标识符，用于区分不同的信号源
 * - base: 在消息缓冲区中的起始偏移位置
 * 可修改：可以调整过滤条件和排序算法以优化性能
 */
int websrv_cpiqbuff_tomsg(OAIgraph_t *graph, scopeSample_t *c, int n, int id, int base)
{
  int I = 0;  // 输出缓冲区索引
  websrv_scope_params_t *WP = websrv_scope_getparams();  // 获取Web界面显示参数
  int newn = n;  // 实际输出的样本数
  websrv_scopedata_msg_t *msg;
  websrv_nf_getdata(graph->graph, id, &msg);  // 获取消息缓冲区

  // 检查缓冲区大小限制
  if (n > MAX_NIQ_WEBSOCKMSG) {
    LOG_E(UTIL, "Buffer id %i too small for %i iqs...\n", id, n);
    return 0;
  }

  /* 复制并按x轴排序图表数据以提高前端性能 */
  int16_t *data_xy = msg->data_xy + base;  // 指向数据缓冲区
  int16_t max_x = INT16_MIN;  // 跟踪最大x值用于排序

  for (int i = 0; i < n; i++) {
    // 过滤超出显示范围的点以减少数据传输量
    if (c[i].r < WP->xmin || c[i].i < WP->ymin || c[i].r > WP->xmax || c[i].i > WP->ymax) {
      newn--;  // 减少有效样本计数
      continue;
    }

    // 如果当前点的x值大于等于最大值，直接添加到末尾
    if (max_x <= c[i].r) {
      data_xy[I] = max_x = c[i].r;      // 存储I分量（实部）
      data_xy[I + 1] = c[i].i;          // 存储Q分量（虚部）
      I = I + 2;
    } else {
      // 需要插入排序：找到正确的插入位置
      for (int j = I + (base * 2); j >= 0; j = j - 2) {
        if (msg->data_xy[j] <= c[i].r || j == 0) {
          // 移动后续元素为新元素腾出空间
          for (int k = I + (base * 2); k > j; k = k - 2) {
            msg->data_xy[k + 2] = msg->data_xy[k];
            msg->data_xy[k + 3] = msg->data_xy[k + 1];
          }
          // 插入新元素
          msg->data_xy[j + 2] = c[i].r;
          msg->data_xy[j + 3] = c[i].i;
          I = I + 2;
          break;
        }
      }
    }
  }
  return newn;  // 返回实际处理的样本数
}

/**
 * @brief 将LLR（对数似然比）数据复制到Web消息缓冲区
 * @param graph 图表对象指针
 * @param llrs LLR数据数组指针
 * @param n LLR数据点数量
 * @param id 数据集ID
 * @param iteration 当前迭代次数（用于LDPC解码等多次迭代场景）
 * @param max_iteration 最大迭代次数
 * @return 实际复制的数据点数量
 *
 * 输入数据：来自信道解码器的LLR值（如PUSCH、PDSCH、PBCH的LLR）
 * 功能：将LLR数据压缩并传输给Web前端，通过阈值过滤减少数据量
 * 作用：为Web界面的LLR图表显示准备数据，支持多次迭代显示
 * 参数含义：
 * - llrs: 源LLR数据数组，每个值表示比特的可靠性
 * - iteration: 当前迭代轮次，用于LDPC等迭代解码算法
 * - max_iteration: 最大迭代次数，用于数据布局
 * 可修改：可以调整阈值参数以平衡显示精度和传输效率
 */
int websrv_cpllrbuff_tomsg(OAIgraph_t *graph, int16_t *llrs, int n, int id, int iteration, int max_iteration)
{
  websrv_scopedata_msg_t *msg;
  websrv_nf_getdata(graph->graph, id, &msg);  // 获取消息缓冲区
  websrv_scope_params_t *WP = websrv_scope_getparams();  // 获取Web显示参数

  // 检查缓冲区大小限制
  if (n > MAX_LLR_WEBSOCKMSG) {
    LOG_E(UTIL, "Buffer id %i too small for %i iqs...\n", id, n);
    return 0;
  }

  /* 在缓冲区开始处保存点数(xmax)，以尽量减少发送的数据量 */
  /* 所有迭代（UE PDSCH情况）具有相同的n（数据点数量） */
  if (iteration == 0) {
    int32_t *iptr = (int32_t *)(msg->data_xy);
    *iptr = n;  // 保存总点数
  }

  /* 对于每个点，我们保存LLR和相应的偏移量（如果低于配置阈值的LLR被跳过） */
  /* 偏移量保存在8位中，因此当偏移量达到8位最大值时总是传输LLR。偏移量相对于先前传输点的x */
  int16_t *dptr = msg->data_xy;
  int newn = 2; // 前2个int16用于保存最大值
  int xoffset = 1;  // x轴偏移量
  int xres = (n / 1000 > CHAR_MAX) ? CHAR_MAX : (n / 1000);  // x轴分辨率
  int16_t latestllr = 0;  // 最近传输的LLR值
  int imin = WP->llrxmin;  // 显示范围最小值
  int imax = WP->llrxmax;  // 显示范围最大值

  // 边界检查和调整
  if (imax > n)
    imax = n;
  if (imin > imax)
    imin = imax;

  /* 为保持数据排序，我们在每次迭代的x位置插入点 */
  for (int i = imin; i < imax; i++) {
    // 只有当LLR变化超过阈值或x偏移量达到分辨率限制时才传输点
    if (((llrs[i] - latestllr) >= WP->llr_ythresh) ||
        ((latestllr - llrs[i]) >= WP->llr_ythresh) ||
        (xoffset >= xres)) {
      dptr[newn + iteration] = llrs[i];  // 存储LLR值
      latestllr = llrs[i];  // 更新最近LLR值
      dptr[newn + iteration + 1] = (int16_t)xoffset;  // 存储x偏移量
      xoffset = 1;  // 重置偏移量
      newn = newn + max_iteration + 2;  // 更新缓冲区位置
    } else {
      xoffset++;  // 增加偏移量
    }
  }

  /* 丢弃在图表上无法区分的点：x和LLR具有几乎相同的值
   * 这部分代码被注释掉，但展示了进一步优化的思路
   */
  return newn;  // 返回实际数据点数量
}

/**
 * @brief 将线性缓冲区数据复制到Web消息，用于线性图表（吞吐量、能量图）
 * @param graph 图表对象指针
 * @param line_data 线性数据数组指针
 * @param n 数据点数量
 * @param id 数据集ID
 * @param base 基础偏移量（当前未使用）
 * @return 实际复制的数据点数量
 *
 * 输入数据：来自性能监控模块的线性数据（如吞吐量统计、功率测量等）
 * 功能：将一维数组数据转换为Web前端的x-y坐标对格式
 * 作用：为Web界面的线性图表（时间序列图）显示准备数据
 * 参数含义：
 * - line_data: 源数据数组，通常是时间序列数据
 * - n: 数据点数量，对应时间窗口大小
 * - id: 数据集标识符，用于区分不同的测量指标
 * 可修改：可以调整缩放因子以适应不同的数据范围
 */
int websrv_cplinebuff_tomsg(OAIgraph_t *graph, float *line_data, int n, int id, int base)
{
  websrv_scopedata_msg_t *msg;
  websrv_nf_getdata(graph->graph, id, &msg);  // 获取消息缓冲区
  websrv_scope_params_t *WP = websrv_scope_getparams();  // 获取Web显示参数

  // 检查缓冲区大小（每个点需要2个值：x和y坐标）
  if (n > MAX_NIQ_WEBSOCKMSG / 2) {
    LOG_E(UTIL, "缓冲区id %i 对于 %i 个线性点来说太小...\n", id, n);
    return 0;
  }

  // 在缓冲区开始处保存点数，用于前端解析
  int32_t *iptr = (int32_t *)(msg->data_xy);
  *iptr = n;

  // 将线性数据转换为x-y坐标对
  int16_t *dptr = msg->data_xy + 2; // 跳过点数计数字段
  for (int i = 0; i < n; i++) {
    dptr[2*i] = (int16_t)i;                           // x坐标（时间索引或样本索引）
    dptr[2*i + 1] = (int16_t)(line_data[i] * 10);     // y坐标（放大10倍以提高精度）
  }

  // 设置消息头信息
  msg->header.msgtype = SCOPEMSG_TYPE_DATA;    // 消息类型：数据
  msg->header.chartid = graph->chartid;        // 图表ID
  msg->header.datasetid = id;                  // 数据集ID
  msg->header.update = 1;                      // 标记需要更新显示

  // 发送数据到Web前端
  websrv_scope_senddata(1, sizeof(websrv_scopedata_msg_t), msg);
  return n;  // 返回处理的数据点数量
}

/**
 * @brief 获取瀑布图的Web消息缓冲区数组
 * @param graph 图表对象指针
 * @param msgp 消息指针数组，用于存储不同颜色层的消息缓冲区
 *
 * 输入数据：图表配置信息
 * 功能：为瀑布图的每个颜色层准备独立的消息缓冲区
 * 作用：支持瀑布图的多色彩显示，每种颜色代表不同的功率级别
 * 参数含义：
 * - msgp: 输出参数，返回每个颜色层对应的消息缓冲区指针
 * 可修改：颜色层数量由water_colors数组大小决定，可以扩展
 */
void websrv_get_WF_buffers(OAIgraph_t *graph, websrv_scopedata_msg_t *msgp[])
{
  // 为每个颜色层创建消息缓冲区
  for (int i = 0; i < sizeof(water_colors) / sizeof(FL_COLOR); i++) {
    websrv_scopedata_msg_t *msg;
    websrv_nf_getdata(graph->graph, i, &msg);  // 获取第i层的消息缓冲区

    // 设置消息头信息
    msg->header.msgtype = SCOPEMSG_TYPE_DATA;    // 消息类型：数据
    msg->header.chartid = graph->chartid;        // 图表ID
    msg->header.datasetid = i;                   // 数据集ID（对应颜色层）
    // 只有最后一层需要触发更新显示
    msg->header.update = (i == (sizeof(water_colors) / sizeof(FL_COLOR) - 1)) ? 1 : 0;
    msg->data_xy[0] = 0;  // 初始化点计数为0
    msgp[i] = msg;        // 返回消息指针
  }
}

/**
 * @brief 在Web消息缓冲区中设置一个点
 * @param x x坐标值
 * @param y y坐标值
 * @param msg 目标消息缓冲区指针
 *
 * 输入数据：坐标值和目标缓冲区
 * 功能：向消息缓冲区添加一个坐标点
 * 作用：用于瀑布图中逐点添加显示数据
 * 参数含义：
 * - x, y: 要添加的点的坐标
 * - msg: 目标消息缓冲区，包含点计数和坐标数据
 * 可修改：坐标范围和精度可以根据需要调整
 */
void websrv_setpoint(int x, int y, websrv_scopedata_msg_t *msg)
{
  msg->data_xy[0]++;                              // 增加点计数
  msg->data_xy[msg->data_xy[0]] = (int16_t)x;     // 存储x坐标
  msg->data_xy[0]++;                              // 再次增加计数（为y坐标）
  msg->data_xy[msg->data_xy[0]] = (int16_t)y;     // 存储y坐标
}
#endif
// ============================================================================
// 图表创建和管理函数
// ============================================================================

/**
 * @brief 创建通用图表对象的核心函数
 * @param graph 图表对象指针（输出参数）
 * @param type 图表类型（WATERFALL或XY图表类型）
 * @param x 图表x坐标位置
 * @param y 图表y坐标位置
 * @param w 图表宽度
 * @param h 图表高度
 * @param label 图表标签文本
 * @param pointColor 点的颜色
 *
 * 输入数据：图表布局参数和显示配置
 * 功能：初始化图表对象，设置显示属性和内存分配
 * 作用：为所有类型的图表提供统一的创建接口
 * 参数含义：
 * - type: 图表类型，WATERFALL用于瀑布图，其他值用于XY图表
 * - x,y,w,h: 图表在界面中的位置和尺寸
 * - label: 显示在图表上的标题文本
 * - pointColor: 图表中点或线的颜色
 * 可修改：可以添加更多图表类型和显示选项
 */
static void commonGraph(OAIgraph_t *graph, int type, FL_Coord x, FL_Coord y, FL_Coord w, FL_Coord h, const char *label, FL_COLOR pointColor)
{
  // 初始化图表结构体
  memset(graph, 0, sizeof(*graph));

  if (type==WATERFALL) {
    // 瀑布图特殊处理：需要额外的文本区域和平均值缓冲区
    graph->waterFallh=h-15;  // 为底部文本预留15像素
    // 分配平均值缓冲区，用于瀑布图的功率平均计算
    graph->waterFallAvg=malloc(sizeof(*graph->waterFallAvg) * graph->waterFallh);

    // 初始化平均值缓冲区
    for (int i=0; i< graph->waterFallh; i++)
      graph->waterFallAvg[i]=0;

    // 创建画布对象用于瀑布图绘制
    graph->graph=fl_add_canvas(FL_NORMAL_CANVAS, x, y, w, graph->waterFallh, label);
    // 创建底部文本区域显示统计信息
    graph->text=fl_add_text(FL_NORMAL_TEXT, x, y+graph->waterFallh, w, 15, label);
    fl_set_object_lcolor(graph->text,FL_WHITE);      // 文本颜色：白色
    fl_set_object_color(graph->text, FL_BLACK, FL_BLACK);  // 背景颜色：黑色
    fl_set_object_lalign(graph->text, FL_ALIGN_CENTER );   // 文本居中对齐
  } else {
    // 普通XY图表处理
    graph->graph=fl_add_xyplot(type, x, y, w, h, label);
    fl_set_object_lcolor(graph->graph, FL_WHITE );    // 标签颜色：白色
    fl_set_object_color(graph->graph, FL_BLACK, pointColor);  // 背景和点颜色

    // 为所有覆盖层设置符号绘制函数
    for (int i=0; i< FL_MAX_XYPLOTOVERLAY; i++)
      fl_set_xyplot_symbol(graph->graph, i, drawsymbol);
  }

  // 设置图表基本属性
  graph->x=x;           // x坐标
  graph->y=y;           // y坐标
  graph->w=w;           // 宽度
  graph->h=h;           // 高度
  graph->maxX=0;        // x轴最大值（自动缩放用）
  graph->maxY=0;        // y轴最大值（自动缩放用）
  graph->minX=0;        // x轴最小值（自动缩放用）
  graph->minY=0;        // y轴最小值（自动缩放用）
  graph->initDone=false; // 初始化完成标志
  graph->iteration=0;   // 迭代计数器

#ifdef WEBSRVSCOPE
  graph->enabled = false;  // Web模式下默认禁用（由Web界面控制）
#else
  graph->enabled = true;   // XForms模式下默认启用
#endif
}

/**
 * @brief 创建gNB（基站）专用图表对象
 * @param funct 图表数据更新函数指针
 * @param type 图表类型
 * @param x,y,w,h 图表位置和尺寸
 * @param label 图表标签
 * @param pointColor 点颜色
 * @return 配置好的图表对象
 *
 * 输入数据：gNB图表配置参数和回调函数
 * 功能：创建专门用于gNB侧信号显示的图表对象
 * 作用：为gNB的各种信号（PUSCH、PUCCH等）提供显示接口
 * 参数含义：
 * - funct: 数据更新回调函数，负责从gNB获取并处理显示数据
 * - 其他参数与commonGraph相同
 * 可修改：可以添加gNB特有的显示配置
 */
static OAIgraph_t gNBcommonGraph( void (*funct) (OAIgraph_t *graph, scopeData_t *p, int UE_id),
                                  int type, FL_Coord x, FL_Coord y, FL_Coord w, FL_Coord h, const char *label, FL_COLOR pointColor) {
  OAIgraph_t graph;
  commonGraph(&graph, type, x, y, w, h, label, pointColor);  // 调用通用图表创建函数
  graph.gNBfunct=funct;     // 设置gNB数据更新函数
  graph.nrUEfunct=NULL;     // 清空UE函数指针
  return graph;
}

/**
 * @brief 创建NR UE（用户设备）专用图表对象
 * @param funct 图表数据更新函数指针
 * @param type 图表类型
 * @param x,y,w,h 图表位置和尺寸
 * @param label 图表标签
 * @param pointColor 点颜色
 * @return 配置好的图表对象
 *
 * 输入数据：NR UE图表配置参数和回调函数
 * 功能：创建专门用于NR UE侧信号显示的图表对象
 * 作用：为UE的各种信号（PBCH、PDCCH、PDSCH等）提供显示接口
 * 参数含义：
 * - funct: 数据更新回调函数，负责从UE获取并处理显示数据
 * - 其他参数与commonGraph相同
 * 可修改：可以添加UE特有的显示配置
 */
static OAIgraph_t nrUEcommonGraph( void (*funct) (scopeGraphData_t **data, OAIgraph_t *graph, PHY_VARS_NR_UE *phy_vars_ue, int eNB_id, int UE_id),
                                   int type, FL_Coord x, FL_Coord y, FL_Coord w, FL_Coord h, const char *label, FL_COLOR pointColor) {
  OAIgraph_t graph;
  commonGraph(&graph, type, x, y, w, h, label, pointColor);  // 调用通用图表创建函数
  graph.gNBfunct=NULL;      // 清空gNB函数指针
  graph.nrUEfunct=funct;    // 设置UE数据更新函数
  return graph;
}
// ============================================================================
// XForms模式专用函数（仅在非Web模式下编译）
// ============================================================================

#ifndef WEBSRVSCOPE
/**
 * @brief 设置图表的显示范围（自动缩放功能）
 * @param graph 图表对象指针
 * @param minX,maxX x轴最小值和最大值
 * @param minY,maxY y轴最小值和最大值
 *
 * 输入数据：当前数据的边界值
 * 功能：根据数据范围自动调整图表的显示边界
 * 作用：确保所有数据点都能在图表中可见，提供良好的显示效果
 * 参数含义：
 * - minX,maxX: 当前数据的x轴范围
 * - minY,maxY: 当前数据的y轴范围
 * 可修改：可以调整缩放因子（当前为1.2倍）和触发条件
 */
static void setRange(OAIgraph_t *graph, float minX, float maxX, float minY, float maxY) {
  // 检查x轴是否需要重新缩放
  if (maxX > graph->maxX || minX < graph->minX || fabs(maxX - graph->maxX) > fabs(graph->maxX) / 2
      || fabs(maxX - graph->maxX) > fabs(graph->maxX) / 2) {
    graph->maxX/=2;  // 减少历史最大值的影响
    graph->minX/=2;  // 减少历史最小值的影响
    graph->maxX=max(graph->maxX,maxX);  // 更新最大值
    graph->minX=min(graph->minX,minX);  // 更新最小值
    // 设置x轴边界，添加20%的边距以提高可读性
    fl_set_xyplot_xbounds(graph->graph, graph->minX*1.2, graph->maxX*1.2);
  }

  // 检查y轴是否需要重新缩放
  if (maxY > graph->maxY || minY < graph->minY || fabs(maxY - graph->maxY) > fabs(graph->maxY) / 2
      || fabs(maxY - graph->maxY) > fabs(graph->maxY) / 2) {
    graph->maxY/=2;  // 减少历史最大值的影响
    graph->minY/=2;  // 减少历史最小值的影响
    graph->maxY=max(graph->maxY,maxY);  // 更新最大值
    graph->minY=min(graph->minY,minY);  // 更新最小值
    // 设置y轴边界，添加20%的边距以提高可读性
    fl_set_xyplot_ybounds(graph->graph, graph->minY*1.2, graph->maxY*1.2);
  }
}

/**
 * @brief 获取XY图表的数据缓冲区指针
 * @param graph 图表对象指针
 * @param x 输出参数：x轴数据缓冲区指针
 * @param y 输出参数：y轴数据缓冲区指针
 * @param len 所需的数据点数量
 * @param layer 图层索引（用于多天线显示）
 *
 * 输入数据：图表配置和所需缓冲区大小
 * 功能：获取或分配图表数据缓冲区，支持多层显示
 * 作用：为图表数据更新提供内存缓冲区，支持多天线信号叠加显示
 * 参数含义：
 * - len: 需要显示的数据点数量
 * - layer: 图层索引，0为主图层，其他为覆盖层（不同天线）
 * 可修改：可以调整内存分配策略和初始化方式
 */
static void oai_xygraph_getbuff(OAIgraph_t *graph, float **x, float **y, int len, int layer) {
  float *old_x;
  float *old_y;
  int old_len=-1;

  // 如果不是第一次迭代，尝试获取现有缓冲区
  if (graph->iteration >1)
    fl_get_xyplot_data_pointer(graph->graph, layer, &old_x, &old_y, &old_len);

  // 如果缓冲区大小不匹配，需要重新分配
  if (old_len != len) {
    LOG_W(HW, "allocating graph of %d points\n", len);
    float values[len];  // 临时数组用于初始化
    float time[len];    // 时间轴数组

    // 初始化时间轴和数值数组
    for (int i=0; i<len; i++)
      time[i] = values[i] = i;  // 默认x=y=索引值

    if (layer==0)
      // 主图层：设置基础数据
      fl_set_xyplot_data(graph->graph,time,values,len,"","","");
    else
      // 覆盖层：添加到现有图表上，使用不同颜色区分天线
      fl_add_xyplot_overlay(graph->graph,layer,time,values,len,rx_antenna_colors[layer]);

    // 获取新分配的缓冲区指针
    fl_get_xyplot_data_pointer(graph->graph, layer, &old_x, &old_y, &old_len);
    AssertFatal(old_len == len, "graph len %i old_len %i\n", len, old_len);
  }

  // 返回缓冲区指针
  *x=old_x;
  *y=old_y;
}
#endif

/**
 * @brief 通用XY图表绘制函数
 * @param graph 图表对象指针
 * @param x x轴数据数组指针
 * @param y y轴数据数组指针
 * @param len 数据点数量
 * @param layer 图层索引
 * @param NoAutoScale 自动缩放控制（false=禁用，true或数值=每N次迭代缩放一次）
 *
 * 输入数据：待显示的x-y坐标对数据
 * 功能：将数据绘制到图表上，支持Web和XForms两种模式
 * 作用：提供统一的图表绘制接口，处理不同显示后端的差异
 * 参数含义：
 * - x,y: 坐标数据数组，通常x为时间或频率，y为幅度或功率
 * - len: 数据点数量，决定图表的分辨率
 * - layer: 图层索引，用于多信号叠加显示
 * - NoAutoScale: 自动缩放间隔，0=禁用，N=每N次更新时重新缩放
 * 可修改：可以调整缩放策略和显示更新频率
 */
static void oai_xygraph(OAIgraph_t *graph, float *x, float *y, int len, int layer, bool NoAutoScale) {
#ifdef WEBSRVSCOPE
  // Web模式：准备数据并发送到Web前端
  websrv_scopedata_msg_t *msg = NULL;

  websrv_nf_getdata(graph->graph, layer, &msg);  // 获取消息缓冲区
  msg->header.msgtype = SCOPEMSG_TYPE_DATA;       // 设置消息类型
  msg->header.chartid = graph->chartid;           // 设置图表ID
  msg->header.datasetid = graph->datasetid;       // 设置数据集ID
  msg->header.msgseg = 0;                         // 消息段号（用于大数据分段传输）
  msg->header.update = 1;                         // 标记需要更新显示
  // 发送数据，LLR图表使用2字节，其他使用4字节
  websrv_scope_senddata(len, (msg->header.chartid == SCOPEMSG_DATAID_LLR) ? 2 : 4, msg);
#else
  // XForms模式：直接更新图表显示
  fl_redraw_object(graph->graph);  // 重绘图表对象

  // 自动缩放处理：定期重新计算显示范围
  if ( NoAutoScale && graph->iteration%NoAutoScale == 0) {
    float maxX=0, maxY=0, minX=0, minY=0;

    // 遍历所有数据点找出边界值
    for (int k=0; k<len; k++) {
      maxX=max(maxX,x[k]);
      minX=min(minX,x[k]);
      maxY=max(maxY,y[k]);
      minY=min(minY,y[k]);
    }

    // 设置新的显示范围，添加5个单位的边距
    setRange(graph, minX-5, maxX+5, minY-5, maxY+5);
  }
#endif
  graph->iteration++;  // 增加迭代计数器
}

/**
 * @brief 通用瀑布图绘制函数
 * @param graph 图表对象指针
 * @param values I/Q样本数据数组指针
 * @param datasize 数据总大小（样本数量）
 * @param divisions 时间轴分割数（用于显示刻度线）
 * @param label 图表标签文本
 *
 * 输入数据：来自射频前端的I/Q时域或频域样本数据
 * 功能：创建瀑布图显示，将信号功率随时间的变化以颜色编码方式显示
 * 作用：提供信号功率谱的时间演化视图，便于观察信号的时频特性
 * 参数含义：
 * - values: I/Q复数样本数组，每个样本包含实部和虚部
 * - datasize: 样本总数，通常对应一帧或一个时隙的数据
 * - divisions: 时间轴分割数，用于在瀑布图上显示时间刻度
 * - label: 图表标题，描述数据来源和含义
 * 可修改：可以调整颜色阈值和功率计算方法
 */
static void genericWaterFall (OAIgraph_t *graph, scopeSample_t *values, const int datasize, const int divisions, const char *label) {
  if ( values == NULL )
    return;

#ifdef WEBSRVSCOPE
  // Web模式：准备多个颜色层的消息缓冲区
  websrv_scopedata_msg_t *msgp[sizeof(water_colors) / sizeof(FL_COLOR)];
  websrv_get_WF_buffers(graph, msgp);
#endif

  fl_winset(FL_ObjWin(graph->graph));  // 设置绘图窗口
  const int samplesPerPixel=datasize/graph->w;  // 每个像素对应的样本数
  int displayPart=graph->waterFallh-ScaleZone;  // 可显示区域高度
  int row=graph->iteration%displayPart;         // 当前绘制行（循环使用）
  double avg=0;  // 平均功率

  // 计算历史平均功率，用于动态阈值设置
  for (int i=0; i < displayPart; i++)
    avg+=graph->waterFallAvg[i];

  avg/=displayPart;  // 计算平均值
  graph->waterFallAvg[row]=0;  // 重置当前行的平均值

  // 逐像素处理：将多个样本合并为一个像素的功率值
  for (int pix=0; pix<graph->w; pix++) {
    scopeSample_t *end=values+(pix+1)*samplesPerPixel;  // 当前像素对应样本的结束位置
    end-=2;  // 安全边界
    AssertFatal(end <= values+datasize,"diff : %tu", end-values+datasize);
    double val=0;  // 当前像素的功率值

    // 计算当前像素对应样本的总功率
    for (scopeSample_t *s=values+(pix)*samplesPerPixel;
         s <end;
         s++)
      val += SquaredNorm(*s);  // 计算I²+Q²

    val/=samplesPerPixel;  // 计算平均功率
    graph->waterFallAvg[row]+=val/graph->w;  // 累加到行平均值

    // 根据功率级别选择颜色（动态阈值）
    int col=0;  // 默认颜色（最低功率）
    if (val > avg*2 )    // 2倍平均功率
      col=1;
    if (val > avg*10 )   // 10倍平均功率
      col=2;
    if (val > avg*100 )  // 100倍平均功率
      col=3;

#ifdef WEBSRVSCOPE
    // Web模式：将点添加到对应颜色层的消息缓冲区
    websrv_setpoint(pix, graph->iteration % displayPart, msgp[col]);
#else
    // XForms模式：直接绘制彩色点
    fl_point(pix, graph->iteration%displayPart, water_colors[col]);
#endif
  }

#ifdef WEBSRVSCOPE
  // Web模式：发送所有颜色层的数据
  for (int i = 0; i < sizeof(water_colors) / sizeof(FL_COLOR); i++) {
    msgp[i]->header.msgseg = graph->iteration % displayPart;  // 设置行号
    websrv_scope_senddata(msgp[i]->data_xy[0], 2, msgp[i]);  // 发送数据
  }
#else
  // XForms模式：初始化显示和绘制刻度线
  if (graph->initDone==false) {
    // 初始化：将整个显示区域填充为黑色
    for ( int i=0; i < graph->waterFallh; i++ )
      for ( int j = 0 ; j < graph->w ; j++ )
        fl_point(j, i, FL_BLACK);

    // 绘制时间分割线（白色垂直线）
    for ( int i=1; i<divisions; i++)
      for (int j= displayPart; j<graph->waterFallh; j++)
        fl_point(i*(graph->w/divisions),j, FL_WHITE);

    graph->initDone=true;  // 标记初始化完成
  }

  // 更新底部文本显示平均功率信息
  fl_set_object_label_f(graph->text, "%s, avg I/Q pow: %4.1f", label, 0/*sqrt(avg)*/);
#endif
  graph->iteration++;  // 增加迭代计数
}

/**
 * @brief 通用多天线功率显示函数
 * @param graph 图表对象指针
 * @param nb_ant 天线数量
 * @param data 多天线数据指针数组
 * @param len 每个天线的数据长度
 *
 * 输入数据：来自多个接收天线的I/Q样本数据
 * 功能：计算并显示每个天线的信号功率
 * 作用：用于多天线系统的信号质量监控和天线性能比较
 * 参数含义：
 * - nb_ant: 天线数量，通常为1、2、4或8
 * - data: 天线数据指针数组，data[i]指向第i个天线的数据
 * - len: 每个天线的数据点数量
 * 可修改：可以添加不同的功率计算方法（如dB转换）
 */
static void genericPowerPerAntena(OAIgraph_t  *graph, const int nb_ant, const scopeSample_t **data, const int len) {
#ifdef WEBSRVSCOPE
  // Web模式：获取消息缓冲区
  websrv_scopedata_msg_t *msg = NULL;
  websrv_nf_getdata(graph->graph, 0, &msg);
  float *values = (float *)msg->data_xy;
#else
  // XForms模式：获取图表数据缓冲区
  float *values, *time;
  oai_xygraph_getbuff(graph, &time, &values, len, 0);
#endif

  // 遍历所有天线
  for (int ant=0; ant<nb_ant; ant++) {
    if (data[ant] != NULL) {
      // 计算当前天线的功率值
      for (int i=0; i<len; i++) {
        values[i] = SquaredNorm(data[ant][i]);  // 计算|I+jQ|²
      }

#ifdef WEBSRVSCOPE
      // Web模式：准备并发送数据
      msg->header.msgtype = SCOPEMSG_TYPE_DATA;
      msg->header.chartid = graph->chartid;
      msg->header.datasetid = graph->datasetid;
      msg->header.msgseg = 0;
      // 只有最后一个天线需要触发显示更新
      msg->header.update = (ant == (nb_ant - 1)) ? 1 : 0;
      websrv_scope_senddata(len, 4, msg);
#else
      // XForms模式：绘制到图表上，每个天线使用不同的图层
      oai_xygraph(graph,time,values, len, ant, 10);
#endif
    }
  }
}

// ============================================================================
// gNB（基站）专用显示函数
// ============================================================================

/**
 * @brief gNB时域接收信号瀑布图显示
 * @param graph 图表对象指针
 * @param p 示波器数据结构指针
 * @param nb_UEs UE数量（当前未使用）
 *
 * 输入数据：来自RU（射频单元）的时域接收数据
 * 功能：显示gNB接收到的时域信号瀑布图
 * 作用：监控gNB接收信号的时域特性和功率变化
 * 参数含义：
 * - p: 包含gNB和RU数据的示波器数据结构
 * - nb_UEs: 当前连接的UE数量（用于将来扩展）
 * 数据来源：p->ru->common.rxdata[0] - 第一个接收天线的时域数据
 * 可修改：可以选择不同的天线或添加多天线显示
 */
static void gNBWaterFall (OAIgraph_t *graph, scopeData_t *p, int nb_UEs) {
  NR_DL_FRAME_PARMS *frame_parms=&p->gNB->frame_parms;  // 获取帧参数
  // 使用第一个天线的接收数据
  genericWaterFall(graph, (scopeSample_t *)p->ru->common.rxdata[0],
                   frame_parms->samples_per_frame,    // 每帧样本数
                   frame_parms->slots_per_frame,      // 每帧时隙数
                   "X轴:时域一帧");                    // 图表标签
}

/* 已被瀑布图替代的时域信号显示函数
 * 这个函数之前用于显示时域信号的功率，现在被瀑布图取代
 * 保留注释以供参考
static void timeSignal (OAIgraph_t *graph, PHY_VARS_gNB *phy_vars_gnb, RU_t *phy_vars_ru, const int nb_UEs) {
  // 显示接收天线0的时域接收信号
  if (!phy_vars_ru->common.rxdata)
    return;

  NR_DL_FRAME_PARMS *frame_parms=&phy_vars_gnb->frame_parms;
  genericLogPowerPerAntena(graph, frame_parms->nb_antennas_rx,
                           (const scopeSample_t **)phy_vars_ru->common.rxdata,
                           frame_parms->samples_per_frame);
}
*/

/**
 * @brief SRS时域响应显示函数
 * @param graph 图表对象指针
 * @param p 示波器数据结构指针
 * @param nb_UEs UE数量（当前未使用）
 *
 * 输入数据：来自gNB上行延迟测量的SRS信号数据
 * 功能：显示SRS（探测参考信号）的时域冲激响应
 * 作用：用于监控上行信道的时域特性和多径延迟扩展
 * 参数含义：
 * - p: 示波器数据结构，包含实时数据缓冲区
 * - nb_UEs: UE数量（预留用于多UE显示）
 * 数据来源：p->liveData[gNBulDelay] - gNB上行延迟测量数据
 * 可修改：可以添加多UE支持或不同的响应计算方法
 */
static void timeResponse (OAIgraph_t *graph, scopeData_t *p, int nb_UEs) {
  scopeGraphData_t *val = p->liveData[gNBulDelay];  // 获取上行延迟数据
  if (!val || !val->dataSize)
    return;

#ifdef WEBSRVSCOPE
  // Web模式：获取消息缓冲区
  websrv_scopedata_msg_t *msg = NULL;
  websrv_nf_getdata(graph->graph, 0, &msg);
  float *values = (float *)msg->data_xy;
#else
  // XForms模式：获取图表缓冲区
  float *values, *time;
  oai_xygraph_getbuff(graph, &time, &values, val->lineSz, 0);
#endif

  // 随机显示UE数据，使用单一缓冲区
  // 注释：这里可以扩展为显示多个UE的响应
  c16_t *samples = (c16_t *)(val + 1);  // 获取复数样本数据
  for (int i = 0; i < val->lineSz; i++) {
    values[i] = SquaredNorm(samples[i]);  // 计算功率：|I+jQ|²
  }

#ifdef WEBSRVSCOPE
  // Web模式：发送数据
  msg->header.msgtype = SCOPEMSG_TYPE_DATA;
  msg->header.chartid = graph->chartid;
  msg->header.datasetid = graph->datasetid;
  msg->header.msgseg = 0;
  msg->header.update = 1;
  websrv_scope_senddata(val->lineSz, 4, msg);
#else
  // XForms模式：绘制图表
  oai_xygraph(graph, time, values, val->lineSz, 0, 10);
#endif
}

/**
 * @brief gNB频域接收信号瀑布图显示
 * @param graph 图表对象指针
 * @param p 示波器数据结构指针
 * @param nb_UEs UE数量（当前未使用）
 *
 * 输入数据：来自gNB的频域接收数据（经过FFT处理）
 * 功能：显示gNB接收到的频域信号瀑布图
 * 作用：监控频域信号的功率分布和频谱特性
 * 参数含义：
 * - p: 示波器数据结构，包含频域数据
 * - nb_UEs: UE数量（预留用于扩展）
 * 数据来源：p->liveData[gNBRxdataF] - gNB频域接收数据
 * 可修改：可以选择不同的天线或添加频域滤波
 */
static void gNBfreqWaterFall (OAIgraph_t *graph, scopeData_t *p, int nb_UEs) {
  NR_DL_FRAME_PARMS *frame_parms=&p->gNB->frame_parms;  // 获取帧参数
  // 使用第一个天线的频域数据
  genericWaterFall(graph,
                   (scopeSample_t *)p->liveData[gNBRxdataF],      // 频域接收数据
                   frame_parms->samples_per_frame_wCP,            // 每帧样本数（含CP）
                   frame_parms->slots_per_frame,                  // 每帧时隙数
                   "X轴: 频域, 一子帧");                          // 图表标签
}

/* 已注释的频域响应函数
 * 这个函数之前用于显示频域响应，现在被瀑布图取代
 * 保留以供参考
static void frequencyResponse (OAIgraph_t *graph, PHY_VARS_gNB *phy_vars_gnb, RU_t *phy_vars_ru, int nb_UEs) {
  NR_DL_FRAME_PARMS *frame_parms=&phy_vars_gnb->frame_parms;
  genericLogPowerPerAntena(graph, frame_parms->nb_antennas_rx,
                           (const scopeSample_t **)phy_vars_ru->common.rxdataF,
                           frame_parms->samples_per_slot_wCP);
}
*/

/**
 * @brief PUSCH LLR（对数似然比）显示函数
 * @param graph 图表对象指针
 * @param p 示波器数据结构指针
 * @param nb_UEs 连接的UE数量
 *
 * 输入数据：来自PUSCH解调器的LLR值
 * 功能：显示PUSCH信道解码前的LLR值分布
 * 作用：用于调试PUSCH接收性能，分析信道质量和解码可靠性
 * 参数含义：
 * - p: 示波器数据结构，包含gNB的PUSCH处理变量
 * - nb_UEs: 当前连接的UE数量
 * 数据来源：p->gNB->pusch_vars[ue].llr - 每个UE的PUSCH LLR数据
 * LLR含义：正值表示比特为1的可能性大，负值表示比特为0的可能性大，绝对值表示可靠性
 * 可修改：可以调整显示的UE范围或添加LLR统计信息
 */
static void puschLLR (OAIgraph_t *graph, scopeData_t *p, int nb_UEs) {
#ifdef WEBSRVSCOPE
  int uestart = nb_UEs - 1; // Web界面显示一个UE信号，可从GUI选择
#else
  int uestart = 0; // XForms界面设计用于显示多个UE信号
#endif

  NR_DL_FRAME_PARMS *frame_parms=&p->gNB->frame_parms;  // 获取帧参数
  // 计算资源元素数量：上行RB数 × 12子载波 × 每时隙符号数
  int num_re = frame_parms->N_RB_UL*12*frame_parms->symbols_per_slot;
  int Qm = 2;  // 调制阶数（QPSK=2, 16QAM=4, 64QAM=6, 256QAM=8）
  int coded_bits_per_codeword = num_re*Qm;  // 每个码字的编码比特数

  // 遍历指定范围内的UE
  for (int ue = uestart; ue < nb_UEs; ue++) {
    // 检查PUSCH变量和LLR数据是否可用
    if (p->gNB->pusch_vars &&
        p->gNB->pusch_vars[ue].llr ) {
      int16_t *pusch_llr = (int16_t *)p->gNB->pusch_vars[ue].llr;  // 获取LLR数据
      float *llr=NULL, *bit=NULL;  // 显示缓冲区指针
      int nx = coded_bits_per_codeword;  // 数据点数量

#ifdef WEBSRVSCOPE
      // Web模式：将LLR数据复制到Web消息缓冲区
      nx = websrv_cpllrbuff_tomsg(graph, pusch_llr, coded_bits_per_codeword, ue, 0, 0);
#else
      // XForms模式：获取图表缓冲区并复制数据
      oai_xygraph_getbuff(graph, &bit, &llr, coded_bits_per_codeword, ue);

      // 将16位整数LLR转换为浮点数用于显示
      for (int i=0; i<coded_bits_per_codeword; i++) {
        llr[i] = (float) pusch_llr[i];
      }
#endif
      // 绘制LLR图表，每10次迭代进行一次自动缩放
      oai_xygraph(graph, bit, llr, nx, ue, 10);
    }
  }
}

/**
 * @brief PUSCH I/Q星座图显示函数
 * @param graph 图表对象指针
 * @param p 示波器数据结构指针
 * @param nb_UEs 连接的UE数量
 *
 * 输入数据：来自PUSCH匹配滤波器输出的I/Q复数样本
 * 功能：显示PUSCH信号的I/Q星座图
 * 作用：用于分析PUSCH信号质量、调制精度和信道均衡效果
 * 参数含义：
 * - p: 示波器数据结构，包含gNB的PUSCH处理变量
 * - nb_UEs: 当前连接的UE数量
 * 数据来源：p->gNB->pusch_vars[ue].rxdataF_comp[0] - PUSCH频域补偿后的数据
 * 星座图含义：理想情况下点应聚集在标准调制星座点附近，散布程度反映信号质量
 * 可修改：可以添加不同天线的显示或理想星座点参考
 */
static void puschIQ (OAIgraph_t *graph, scopeData_t *p, int nb_UEs) {
  NR_DL_FRAME_PARMS *frame_parms=&p->gNB->frame_parms;  // 获取帧参数
  // 计算数据大小：上行RB数 × 12子载波 × 每时隙符号数
  int sz=frame_parms->N_RB_UL*12*frame_parms->symbols_per_slot;
  int newsz = sz;  // 实际显示的数据点数

#ifdef WEBSRVSCOPE
  int uestart = nb_UEs - 1; // Web界面显示一个UE信号，可从GUI选择
#else
  int uestart = 0; // XForms界面设计用于显示多个UE信号
#endif

  // 遍历指定范围内的UE
  for (int ue = uestart; ue < nb_UEs; ue++) {
    // 检查PUSCH变量和频域补偿数据是否可用
    if (p->gNB->pusch_vars &&
        p->gNB->pusch_vars[ue].rxdataF_comp &&
        p->gNB->pusch_vars[ue].rxdataF_comp[0] ) {
      // 获取PUSCH频域补偿后的复数数据（匹配滤波器输出）
      scopeSample_t *pusch_comp = (scopeSample_t *)p->gNB->pusch_vars[ue].rxdataF_comp[0];
      float *I=NULL, *Q=NULL;  // I/Q分量缓冲区指针

#ifdef WEBSRVSCOPE
      // Web模式：将I/Q数据复制到Web消息缓冲区
      newsz = websrv_cpiqbuff_tomsg(graph, pusch_comp, sz, 0, 0);
#else
      // XForms模式：获取图表缓冲区并分离I/Q分量
      oai_xygraph_getbuff(graph, &I, &Q, sz, ue);

      // 分离复数数据为I和Q分量
      for (int k=0; k<sz; k++ ) {
        I[k] = pusch_comp[k].r;  // 实部（I分量）
        Q[k] = pusch_comp[k].i;  // 虚部（Q分量）
      }
#endif

      // 绘制I/Q星座图，每10次迭代进行一次自动缩放
      oai_xygraph(graph, I, Q, newsz, ue, 10);
    }
  }
}

static void pucchEnergy (OAIgraph_t *graph, scopeData_t *p, int nb_UEs) {
  // PUCCH能量显示 - 显示PUCCH信号能量随时间的测量值
  // 输入数据：来自gNB PHY测量的PUCCH能量统计
  // 功能：显示PUCCH信号能量水平和噪声阈值，用于调试PUCCH接收

  // frame_parms变量在当前实现中未使用，但保留以备将来扩展
  // NR_DL_FRAME_PARMS *frame_parms = &p->gNB->frame_parms;

  // 能量测量的缓冲区大小 - 覆盖多个时隙用于趋势分析
  const int energy_buffer_size = 1024;
  float energy_values[energy_buffer_size];      // PUCCH能量测量值（dB）
  float time_indices[energy_buffer_size];       // x轴的时间索引
  float threshold_line[energy_buffer_size];     // PUCCH检测阈值线

  // 初始化数组
  for (int i = 0; i < energy_buffer_size; i++) {
    time_indices[i] = (float)i;
    energy_values[i] = 0.0f;
    threshold_line[i] = (float)p->gNB->pucch0_thres; // 来自gNB配置的PUCCH检测阈值
  }

  // 从gNB PHY统计中提取PUCCH能量数据
  // 数据源：gNB->phy_stats[ue_id].uci_stats包含PUCCH能量测量值
  for (int ue = 0; ue < nb_UEs && ue < MAX_MOBILES_PER_GNB; ue++) {
    NR_gNB_PHY_STATS_t *phy_stats = &p->gNB->phy_stats[ue];
    if (phy_stats->active) {
      NR_gNB_UCI_STATS_t *uci_stats = &phy_stats->uci_stats;

      // 从当前统计计算PUCCH能量
      // 使用PUCCH0统计，因为它们包含能量测量值
      int pucch_energy_db = 0;
      if (uci_stats->pucch0_sr_trials > 0) {
        // 将线性能量转换为dB刻度用于显示
        pucch_energy_db = dB_fixed(uci_stats->current_pucch0_sr_stat0);
      }

      // 移动能量历史并添加新测量值
      for (int i = 0; i < energy_buffer_size - 1; i++) {
        energy_values[i] = energy_values[i + 1];
      }
      energy_values[energy_buffer_size - 1] = (float)pucch_energy_db;
    }
  }

#ifdef WEBSRVSCOPE
  // Web scope：发送能量数据作为线性图表
  websrv_cplinebuff_tomsg(graph, energy_values, energy_buffer_size, 0, 0);
#else
  // XForms scope：显示带阈值覆盖的能量测量值
  fl_set_xyplot_data(graph->graph, time_indices, energy_values, energy_buffer_size,
                     "时间索引", "PUCCH能量(dB)", "PUCCH能量测量");

  // 添加红色阈值线作为覆盖层
  fl_add_xyplot_overlay(graph->graph, 1, time_indices, threshold_line, energy_buffer_size, FL_RED);

  // 为能量显示设置适当的边界
  fl_set_xyplot_xbounds(graph->graph, 0, energy_buffer_size);
  fl_set_xyplot_ybounds(graph->graph, -20, 80); // 典型的PUCCH能量范围（dB）

  // 更新图表标题以提高可见性
  fl_set_object_label(graph->graph, "PUCCH能量vs阈值");
#endif
}

static void pucchIQ (OAIgraph_t *graph, scopeData_t *p, int nb_UEs) {
  // PUCCH I/Q星座图显示 - 显示PUCCH信号星座点
  // 输入数据：匹配滤波后的PUCCH接收信号I/Q样本
  // 功能：显示用于PUCCH信号质量分析和调试的星座图

  NR_DL_FRAME_PARMS *frame_parms = &p->gNB->frame_parms;

  // I/Q样本的缓冲区大小 - 典型的PUCCH每个PRB有12个子载波
  const int iq_buffer_size = 12 * 14; // 最多12个子载波 * 14个符号
  float I_samples[iq_buffer_size];     // 同相分量
  float Q_samples[iq_buffer_size];     // 正交分量
  int sample_count = 0;

  // 初始化I/Q数组
  for (int i = 0; i < iq_buffer_size; i++) {
    I_samples[i] = 0.0f;
    Q_samples[i] = 0.0f;
  }

#ifdef WEBSRVSCOPE
  int uestart = nb_UEs - 1; // Web scope显示一个UE信号，可从GUI选择
#else
  int uestart = 0; // XForms scope设计用于显示多个UE信号
#endif

  // 从活跃UE中提取PUCCH I/Q数据
  int ue = uestart;  // 声明ue变量在循环外部
  for (ue = uestart; ue < nb_UEs && ue < MAX_MOBILES_PER_GNB; ue++) {
    // 检查此UE是否有PUCCH数据可用
    if (p->gNB->pucch && p->gNB->pucch[ue].active) {
      // 对于PUCCH，我们需要访问接收的频域数据
      // PUCCH处理在解调后将I/Q数据存储在common_vars.rxdataF中

      // 获取此UE的PUCCH配置
      NR_gNB_PUCCH_t *pucch_vars = &p->gNB->pucch[ue];
      nfapi_nr_pucch_pdu_t *pucch_pdu = &pucch_vars->pucch_pdu;

      // 计算PUCCH资源参数
      int pucch_prb_start = pucch_pdu->bwp_start + pucch_pdu->prb_start;
      int pucch_symbols = pucch_pdu->nr_of_symbols;
      int start_symbol = pucch_pdu->start_symbol_index;

      // 从接收的频域数据中提取I/Q样本
      c16_t ***rxdataF = p->gNB->common_vars.rxdataF;
      if (rxdataF && rxdataF[0] && rxdataF[0][0]) {
        int symbol_offset = (pucch_vars->slot & 3) * frame_parms->symbols_per_slot * frame_parms->ofdm_symbol_size;

        for (int symbol = 0; symbol < pucch_symbols && sample_count < iq_buffer_size; symbol++) {
          int symbol_idx = start_symbol + symbol;
          int re_offset = symbol_offset + symbol_idx * frame_parms->ofdm_symbol_size;
          re_offset += (pucch_prb_start * NR_NB_SC_PER_RB + frame_parms->first_carrier_offset) % frame_parms->ofdm_symbol_size;

          // 为此PUCCH PRB提取12个子载波
          for (int sc = 0; sc < NR_NB_SC_PER_RB && sample_count < iq_buffer_size; sc++) {
            int re_idx = (re_offset + sc) % frame_parms->ofdm_symbol_size;

            // 将复数样本转换为浮点I/Q用于显示
            I_samples[sample_count] = (float)rxdataF[0][0][re_idx].r / 32768.0f; // 归一化到[-1,1]
            Q_samples[sample_count] = (float)rxdataF[0][0][re_idx].i / 32768.0f; // 归一化到[-1,1]
            sample_count++;
          }
        }
      }
    }
  }

  // 确保我们有一些数据可以显示
  if (sample_count == 0) {
    sample_count = 1;
    I_samples[0] = 0.0f;
    Q_samples[0] = 0.0f;
  }

#ifdef WEBSRVSCOPE
  // Web scope：发送I/Q数据作为星座图
  websrv_cpiqbuff_tomsg(graph, (scopeSample_t*)I_samples, sample_count, 0, 0);
#else
  // XForms scope：显示I/Q星座图
  oai_xygraph(graph, I_samples, Q_samples, sample_count, ue, 10);

  // Set constellation display bounds
  fl_set_xyplot_xbounds(graph->graph, -1.5, 1.5); // I component range
  fl_set_xyplot_ybounds(graph->graph, -1.5, 1.5); // Q component range

  // Update graph title for better visibility
  fl_set_object_label(graph->graph, "PUCCH I/Q星座图");
#endif
}

static void puschThroughtput (OAIgraph_t *graph, scopeData_t *p, int nb_UEs) {
  // PUSCH吞吐量显示 - 显示上行数据吞吐量随时间变化
  // 输入数据：来自ULSCH统计的PUSCH解码字节和时序信息
  // 功能：计算并显示实时PUSCH吞吐量（kbps），用于性能监控

  // 静态变量用于在函数调用间维护吞吐量历史
  static float tput_time_gnb[MAX_MOBILES_PER_GNB][TPUT_WINDOW_LENGTH] = {{0}};  // 时间戳
  static float tput_gnb[MAX_MOBILES_PER_GNB][TPUT_WINDOW_LENGTH] = {{0}};       // 吞吐量值（kbps）
  static uint32_t last_total_bytes[MAX_MOBILES_PER_GNB] = {0};                  // 用于增量计算的前一字节计数
  static uint32_t last_frame_time[MAX_MOBILES_PER_GNB] = {0};                   // 前一帧时间

  // frame_parms变量在当前实现中未使用，但保留以备将来扩展
  // NR_DL_FRAME_PARMS *frame_parms = &p->gNB->frame_parms;

  // 用于吞吐量计算的当前帧时间
  uint32_t current_frame = 0; // 这应该从当前帧上下文中获取

  // 处理每个活跃UE的吞吐量
  for (int ue = 0; ue < nb_UEs && ue < MAX_MOBILES_PER_GNB; ue++) {
    // 检查UE是否有活跃的ULSCH
    if (p->gNB->ulsch && p->gNB->ulsch[ue].active) {
      NR_gNB_ULSCH_t *ulsch = &p->gNB->ulsch[ue];

      // 获取当前统计信息
      uint32_t current_total_bytes = 0;

      // 汇总此UE所有HARQ进程的字节数
      // 注意：我们使用简化方法，因为HARQ进程结构可能有所不同
      // 在实际实现中，这应该访问实际的HARQ统计信息
      if (ulsch->harq_process) {
        // 从活跃HARQ进程添加TBS（简化方法）
        current_total_bytes += ulsch->harq_process->TBS >> 3; // 将比特转换为字节
      }

      // 如果有之前的测量值，则计算吞吐量
      float current_throughput_kbps = 0.0f;
      if (last_frame_time[ue] > 0 && current_frame > last_frame_time[ue]) {
        uint32_t byte_delta = current_total_bytes - last_total_bytes[ue];
        uint32_t time_delta_ms = current_frame - last_frame_time[ue]; // 假设每帧1ms

        if (time_delta_ms > 0) {
          // 计算kbps吞吐量：(字节 * 8比特/字节 * 1000毫秒/秒) / (时间毫秒 * 1000比特/千比特)
          current_throughput_kbps = (float)(byte_delta * 8) / (float)time_delta_ms;
        }
      }

      // 更新历史数组 - 移动窗口并添加新测量值
      memmove(tput_time_gnb[ue], &tput_time_gnb[ue][1], (TPUT_WINDOW_LENGTH-1) * sizeof(float));
      memmove(tput_gnb[ue], &tput_gnb[ue][1], (TPUT_WINDOW_LENGTH-1) * sizeof(float));

      // 将新测量值添加到窗口末尾
      tput_time_gnb[ue][TPUT_WINDOW_LENGTH-1] = (float)current_frame;
      tput_gnb[ue][TPUT_WINDOW_LENGTH-1] = current_throughput_kbps;

      // 更新前一值用于下次计算
      last_total_bytes[ue] = current_total_bytes;
      last_frame_time[ue] = current_frame;
    } else {
      // 没有活跃的ULSCH - 添加零吞吐量
      memmove(tput_time_gnb[ue], &tput_time_gnb[ue][1], (TPUT_WINDOW_LENGTH-1) * sizeof(float));
      memmove(tput_gnb[ue], &tput_gnb[ue][1], (TPUT_WINDOW_LENGTH-1) * sizeof(float));

      tput_time_gnb[ue][TPUT_WINDOW_LENGTH-1] = (float)current_frame;
      tput_gnb[ue][TPUT_WINDOW_LENGTH-1] = 0.0f;
    }
  }

  // 显示第一个活跃UE的吞吐量（如果没有活跃UE则显示UE 0）
  int display_ue = 0;
  for (int ue = 0; ue < nb_UEs && ue < MAX_MOBILES_PER_GNB; ue++) {
    if (p->gNB->ulsch && p->gNB->ulsch[ue].active) {
      display_ue = ue;
      break;
    }
  }

#ifdef WEBSRVSCOPE
  // Web scope：发送吞吐量数据作为线性图表
  websrv_cplinebuff_tomsg(graph, tput_gnb[display_ue], TPUT_WINDOW_LENGTH, 0, 0);
#else
  // XForms scope：显示随时间变化的吞吐量
  fl_set_xyplot_data(graph->graph, tput_time_gnb[display_ue], tput_gnb[display_ue], TPUT_WINDOW_LENGTH,
                     "帧号", "吞吐量(kbps)", "PUSCH上行吞吐量");

  // 为吞吐量显示设置适当的边界
  float ymax = 1000.0f; // 默认最大吞吐量（kbps）
  for (int i = 0; i < TPUT_WINDOW_LENGTH; i++) {
    if (tput_gnb[display_ue][i] > ymax) {
      ymax = tput_gnb[display_ue][i] * 1.1f; // 添加10%边距
    }
  }

  fl_set_xyplot_ybounds(graph->graph, 0, ymax);

  // 更新图表标题以提高可见性
  fl_set_object_label(graph->graph, "PUSCH吞吐量(kbps)");
#endif
}
// ============================================================================
// gNB示波器界面创建函数
// ============================================================================

/**
 * @brief 创建gNB物理层示波器界面
 * @return 创建的示波器界面结构指针
 *
 * 输入数据：无（静态界面布局）
 * 功能：创建gNB侧的完整示波器界面，包含多个图表窗口
 * 作用：为gNB提供实时信号监控和调试界面
 * 界面布局：
 * - 时域接收信号瀑布图
 * - SRS频域响应图
 * - 频域信道瀑布图
 * - PUSCH LLR图
 * - PUSCH I/Q星座图
 * - PUCCH能量图
 * - PUCCH I/Q星座图
 * - PUSCH吞吐量图
 * 可修改：可以调整图表位置、大小和数量
 */
STATICFORXSCOPE OAI_phy_scope_t *create_phy_scope_gnb(void)
{
  FL_OBJECT *obj;
  OAI_phy_scope_t *fdui = calloc(( sizeof *fdui ),1);  // 分配界面结构内存

  // 定义主窗体（800x800像素）
  fdui->phy_scope = fl_bgn_form( FL_NO_BOX, 800, 800 );
  fl_set_form_dblbuffer(fdui->phy_scope, 1);  // 启用双缓冲以减少闪烁

  // 创建整个UI边框
  obj = fl_add_box( FL_BORDER_BOX, 0, 0, 800, 800, "" );
  fl_set_object_color( obj, FL_BLACK, FL_WHITE );  // 黑色背景，白色边框

  int curY=0,x,y,w,h;  // 布局变量

  // 图表0：时域接收信号瀑布图
  // 显示gNB接收到的时域信号功率随时间的变化
  fdui->graph[0] = gNBcommonGraph( gNBWaterFall, WATERFALL, 0, curY, 400, 100,
                                   "接收信号 (时域, 一帧)", FL_RED );
  fdui->graph[0].chartid = SCOPEMSG_DATAID_WF; // 告诉Web前端使用瀑布图显示
  fdui->graph[0].datasetid = 0; // 瀑布图不使用数据集ID

  // 图表1：SRS时域响应图
  // 显示SRS（探测参考信号）的时域冲激响应
  fdui->graph[1] = gNBcommonGraph( timeResponse, FL_NORMAL_XYPLOT, 410, curY, 400, 100,
                                   "SRS频域响应 (样本, 幅度)", FL_RED );
  fl_get_object_bbox(fdui->graph[1].graph,&x, &y,&w, &h);  // 获取图表边界框
  curY+=h;  // 更新Y坐标位置
  fdui->graph[1].chartid = SCOPEMSG_DATAID_TRESP;  // 设置时域响应图表ID

  // 图表2：频域信道瀑布图
  // 显示频域信道的功率谱随时间的变化
  fdui->graph[2] = gNBcommonGraph( gNBfreqWaterFall, WATERFALL, 0, curY, 800, 100,
                                   "信道频域 (RE, 一帧)", FL_RED );
  fl_get_object_bbox(fdui->graph[2].graph,&x, &y,&w, &h);
  curY+=h+20;  // 添加额外间距
  fdui->graph[2].chartid = SCOPEMSG_DATAID_WF; // 告诉Web前端使用瀑布图显示
  fdui->graph[2].datasetid = 0; // 瀑布图不使用数据集ID

  // 图表3：PUSCH LLR图
  // 显示PUSCH信道解码的对数似然比值
  fdui->graph[3] = gNBcommonGraph( puschLLR, FL_POINTS_XYPLOT, 0, curY, 500, 200,
                                   "PUSCH对数似然比 (LLR, 幅度)", FL_YELLOW );
  fdui->graph[3].chartid = SCOPEMSG_DATAID_LLR; // 告诉Web前端使用LLR图表显示
  fdui->graph[3].datasetid = 0; // 告诉Web前端使用LLR图表的数据集索引0

  // 图表4：PUSCH I/Q星座图
  // 显示PUSCH匹配滤波器输出的I/Q星座点
  fdui->graph[4] = gNBcommonGraph( puschIQ, FL_POINTS_XYPLOT, 500, curY, 300, 200,
                                   "PUSCH I/Q匹配滤波输出", FL_YELLOW );
  fl_get_object_bbox(fdui->graph[3].graph,&x, &y,&w, &h);
  curY+=h;  // 更新Y坐标

  fdui->graph[4].chartid = SCOPEMSG_DATAID_IQ; // 告诉Web前端使用星座图显示
  fdui->graph[4].datasetid = 0; // 告诉Web前端使用星座图的数据集0

  // 图表5：PUCCH能量显示
  // 显示PUCCH信号能量水平和检测阈值，用于调试PUCCH接收
  fdui->graph[5] = gNBcommonGraph( pucchEnergy, FL_POINTS_XYPLOT, 0, curY, 400, 150,
                                   "PUCCH能量vs阈值(dB)", FL_YELLOW );
  fl_set_xyplot_xgrid(fdui->graph[5].graph, FL_GRID_MAJOR); // 添加网格以提高可读性
  fl_get_object_bbox(fdui->graph[5].graph,&x, &y,&w, &h);
  curY+=h;

  // 图表6：PUCCH I/Q星座图显示
  // 显示PUCCH信号的I/Q星座点，用于信号质量分析
  fdui->graph[6] = gNBcommonGraph( pucchIQ, FL_POINTS_XYPLOT, 500, curY, 400, 150,
                                   "PUCCH I/Q星座图", FL_CYAN );
  fl_get_object_bbox(fdui->graph[6].graph,&x, &y,&w, &h);
  curY+=h;
  fdui->graph[6].chartid = SCOPEMSG_DATAID_IQ; // 告诉Web前端使用星座图显示
  fdui->graph[6].datasetid = 1; // 告诉Web前端使用星座图的数据集1

  // 图表7：PUSCH吞吐量显示
  // 显示实时PUSCH上行数据吞吐量，用于性能监控
  fdui->graph[7] = gNBcommonGraph( puschThroughtput, FL_NORMAL_XYPLOT, 0, curY, 600, 150,
                                   "PUSCH上行吞吐量(kbps)", FL_GREEN );
  fdui->graph[8].graph=NULL;  // 图表数组结束标记

  fl_end_form( );  // 结束窗体定义
  if (fdui->phy_scope)
    fdui->phy_scope->fdui = fdui;  // 设置反向引用

  // 显示窗体，标题为中文
  fl_show_form (fdui->phy_scope, FL_PLACE_HOTSPOT, FL_FULLBORDER, "NR上行示波器 gNB");
  return fdui;  // 返回创建的界面结构
}

/**
 * @brief gNB示波器主更新函数
 * @param form 示波器界面结构指针
 * @param p 示波器数据结构指针
 * @param UE_id UE标识符
 *
 * 输入数据：来自gNB PHY层的实时数据
 * 功能：更新所有启用的图表显示
 * 作用：协调所有图表的数据更新和显示刷新
 * 参数含义：
 * - form: 界面结构，包含所有图表对象
 * - p: 数据结构，包含gNB和RU的实时数据
 * - UE_id: 当前处理的UE数量
 * 可修改：可以添加图表选择性更新逻辑
 */
STATICFORXSCOPE void phy_scope_gNB(OAI_phy_scope_t *form, scopeData_t *p, int UE_id)
{
  static OAI_phy_scope_t *rememberForm=NULL;  // 静态变量保存界面指针

  // 界面指针管理：支持NULL参数调用
  if (form==NULL)
    form=rememberForm;  // 使用之前保存的界面
  else
    rememberForm=form;  // 保存当前界面指针

  if (form==NULL)
    return;  // 没有有效界面则退出

  int i=0;

  // 遍历所有图表并更新启用的图表
  while (form->graph[i].graph) {
    if (form->graph[i].enabled)  // 只更新启用的图表
      form->graph[i].gNBfunct(form->graph + i, p, UE_id);  // 调用图表更新函数
    i++;
  }

  //fl_check_forms();  // 可选：检查XForms事件（已注释）
}

// ============================================================================
// XForms模式专用线程函数
// ============================================================================

#ifndef WEBSRVSCOPE
/**
 * @brief gNB示波器线程函数（仅XForms模式）
 * @param arg 示波器数据结构指针
 * @return NULL
 *
 * 输入数据：线程参数（示波器数据结构）
 * 功能：在独立线程中运行XForms界面和数据更新循环
 * 作用：提供非阻塞的实时界面更新
 * 线程特性：
 * - 32MB栈空间（用于处理大量图形数据）
 * - 99ms更新间隔（约10Hz刷新率）
 * - 在oai_exit信号时退出
 * 可修改：可以调整更新频率和栈大小
 */
static void *scope_thread_gNB(void *arg) {
  scopeData_t *p=(scopeData_t *) arg;  // 获取数据结构指针
  size_t stksize=0;
  pthread_attr_t atr;
  pthread_attr_init(&atr);
  pthread_attr_getstacksize(&atr, &stksize);
  pthread_attr_setstacksize(&atr,32*1024*1024 );  // 设置32MB栈空间
  sleep(3); // 等待系统初始化完成（无清洁的线程间屏障）

  // 初始化XForms库
  int fl_argc=1;
  char *name="5G-gNB-scope";
  fl_initialize (&fl_argc, &name, NULL, 0, 0);

  int nb_ue=min(MAX_MOBILES_PER_GNB, scope_enb_num_ue);  // 确定UE数量
  OAI_phy_scope_t  *form_gnb = create_phy_scope_gnb();   // 创建界面

  // 主显示循环
  while (!oai_exit) {
    fl_freeze_form(form_gnb->phy_scope);    // 冻结界面更新
    phy_scope_gNB(form_gnb, p, nb_ue);      // 更新所有图表
    fl_unfreeze_form(form_gnb->phy_scope);  // 解冻界面更新
    fl_redraw_form(form_gnb->phy_scope);    // 重绘界面
    usleep(99*1000);  // 等待99ms（约10Hz更新频率）
  }

  return NULL;
}
#endif

/**
 * @brief gNB示波器更新器回调函数
 * @param plotType 图表类型枚举
 * @param numElt 元素数量
 *
 * 输入数据：图表更新请求
 * 功能：根据图表类型执行特定的更新操作
 * 作用：提供图表类型特定的更新逻辑（当前为占位符实现）
 * 参数含义：
 * - plotType: 要更新的图表类型（PUSCH LLR、PUSCH IQ等）
 * - numElt: 要更新的元素数量
 * 可修改：可以添加具体的更新逻辑实现
 */
static void scopeUpdaterGnb(enum PlotTypeGnbIf plotType, int numElt)
{
  switch (plotType) {
    case puschLLRe:
      /* 更新PUSCH LLR图表 - 可在此添加特定逻辑 */
      break;
    case puschIQe:
      /* 更新PUSCH IQ图表 - 可在此添加特定逻辑 */
      break;
  }
}

/**
 * @brief 初始化gNB示波器系统
 * @param p 示波器参数结构指针
 *
 * 输入数据：gNB、RU和命令行参数
 * 功能：初始化gNB示波器的数据结构和显示线程
 * 作用：设置gNB侧的实时信号监控系统
 * 参数含义：
 * - p: 包含gNB、RU指针和命令行参数的结构
 * 初始化内容：
 * - 分配示波器数据结构内存
 * - 设置数据源指针（gNB、RU）
 * - 启动显示线程（XForms模式）
 * - 初始化数据复制互斥锁
 * 可修改：可以添加更多初始化参数或配置选项
 */
STATICFORXSCOPE void gNBinitScope(scopeParms_t *p)
{
  // 分配示波器数据结构内存
  AssertFatal(p->gNB->scopeData = calloc(sizeof(scopeData_t), 1), "");
  scopeData_t *scope=(scopeData_t *) p->gNB->scopeData;

  // 设置基本参数
  scope->argc=p->argc;        // 命令行参数数量
  scope->argv=p->argv;        // 命令行参数数组
  scope->ru=p->ru;            // RU（射频单元）指针
  scope->gNB=p->gNB;          // gNB指针
  scope->scopeUpdater = scopeUpdaterGnb;  // 设置更新器回调
  scope->copyData = copyData; // 设置数据复制函数

#ifndef WEBSRVSCOPE
  // XForms模式：创建显示线程
  pthread_t forms_thread;
  threadCreate(&forms_thread, scope_thread_gNB, p->gNB->scopeData, "scope", -1, OAI_PRIORITY_RT_LOW);
  copyDataMutexInit(scope);   // 初始化数据复制互斥锁
#endif
}

// ============================================================================
// NR UE（用户设备）专用显示函数
// ============================================================================
/**
 * @brief UE时域接收信号瀑布图显示
 * @param data 示波器数据数组（未使用）
 * @param graph 图表对象指针
 * @param phy_vars_ue UE物理层变量指针
 * @param eNB_id eNB标识符（未使用）
 * @param UE_id UE标识符（未使用）
 *
 * 输入数据：来自UE接收天线的时域数据
 * 功能：显示UE接收到的时域信号瀑布图
 * 作用：监控UE接收信号的时域特性和功率变化
 * 参数含义：
 * - phy_vars_ue: UE物理层结构，包含接收数据和帧参数
 * 数据来源：phy_vars_ue->common_vars.rxdata[0] - 第一个接收天线的时域数据
 * 可修改：可以选择不同的天线或添加多天线显示
 */
static void ueWaterFall  (scopeGraphData_t **data, OAIgraph_t *graph, PHY_VARS_NR_UE *phy_vars_ue, int eNB_id, int UE_id) {
  // 显示接收天线0的时域接收信号
  genericWaterFall(graph,
                   (scopeSample_t *) phy_vars_ue->common_vars.rxdata[0],  // 第一个天线的接收数据
                   phy_vars_ue->frame_parms.samples_per_frame,            // 每帧样本数
                   phy_vars_ue->frame_parms.slots_per_frame,              // 每帧时隙数
                   "X轴: 时域一帧");                                      // 图表标签
}

/* 已被瀑布图替代的UE时域响应函数
 * 这个函数之前用于显示UE时域信号的功率，现在被瀑布图取代
 * 保留注释以供参考
static void ueTimeResponse  (OAIgraph_t *graph, PHY_VARS_NR_UE *phy_vars_ue, int eNB_id, int UE_id) {
  // 显示接收天线0的时域接收信号
  genericLogPowerPerAntena(graph, phy_vars_ue->frame_parms.nb_antennas_rx,
                           (const scopeSample_t **) phy_vars_ue->common_vars.rxdata,
                           phy_vars_ue->frame_parms.samples_per_frame);
}
*/

/**
 * @brief UE信道冲激响应显示
 * @param data 示波器数据数组
 * @param graph 图表对象指针
 * @param phy_vars_ue UE物理层变量指针
 * @param eNB_id eNB标识符（未使用）
 * @param UE_id UE标识符（未使用）
 *
 * 输入数据：来自PBCH或PSBCH信道估计的时域响应数据
 * 功能：显示下行信道的时域冲激响应
 * 作用：用于分析信道的多径特性和延迟扩展
 * 参数含义：
 * - data: 示波器数据数组，包含信道估计结果
 * - phy_vars_ue: UE物理层结构，用于判断工作模式
 * 数据来源：根据UE模式选择PBCH或PSBCH的信道估计数据
 * 可修改：可以添加不同信道类型的支持
 */
static void ueChannelResponse  (scopeGraphData_t **data, OAIgraph_t *graph, PHY_VARS_NR_UE *phy_vars_ue, int eNB_id, int UE_id) {
  // 根据UE工作模式选择数据类型：侧链模式使用PSBCH，否则使用PBCH
  enum scopeDataType typ = (phy_vars_ue->sl_mode) ? psbchDlChEstimateTime : pbchDlChEstimateTime;

  // 信道冲激响应显示
  if (!data[typ])
    return;  // 数据不可用则退出

  const scopeSample_t *tmp = (scopeSample_t *)(data[typ] + 1);  // 获取复数样本数据
  // 显示多天线功率响应
  genericPowerPerAntena(graph, data[typ]->colSz, &tmp, data[typ]->lineSz);
}

/**
 * @brief UE频域接收信号瀑布图显示
 * @param data 示波器数据数组
 * @param graph 图表对象指针
 * @param phy_vars_ue UE物理层变量指针
 * @param eNB_id eNB标识符（未使用）
 * @param UE_id UE标识符（未使用）
 *
 * 输入数据：来自UE的频域接收数据（经过FFT处理）
 * 功能：显示UE接收到的频域信号瀑布图
 * 作用：监控频域信号的功率分布和频谱特性
 * 参数含义：
 * - data: 示波器数据数组，包含频域接收数据
 * 数据来源：data[commonRxdataF] - 通用频域接收数据
 * 可修改：可以选择不同的天线或添加频域滤波
 */
static void ueFreqWaterFall (scopeGraphData_t **data, OAIgraph_t *graph,PHY_VARS_NR_UE *phy_vars_ue, int eNB_id, int UE_id ) {
  // 使用第一个天线的频域数据
  if (!data[commonRxdataF])
    return;  // 数据不可用则退出

  const int sz = data[commonRxdataF]->lineSz;  // 获取数据大小

  scopeSample_t *rxdataF = (scopeSample_t *)(data[commonRxdataF]+1);  // 获取频域数据

  // 显示频域瀑布图
  genericWaterFall(graph,
                   rxdataF,                        // 频域接收数据
                   sz,                             // 数据大小
                   1,                              // 分割数（一个时隙）
                   "X轴: 频域一时隙" );             // 图表标签
}
/*
static void uePbchFrequencyResp  (OAIgraph_t *graph, PHY_VARS_NR_UE *phy_vars_ue, int eNB_id, int UE_id) {
  // Channel Frequency Response (includes 5 complex sample for filter)
  if (!phy_vars_ue->pbch_vars[eNB_id]->dl_ch_estimates)
    return;

  NR_DL_FRAME_PARMS *frame_parms = &phy_vars_ue->frame_parms;
  uint8_t nb_antennas_rx = frame_parms->nb_antennas_rx;
  uint8_t nb_antennas_tx = frame_parms->nb_antenna_ports_gNB;
  scopeSample_t   **chest_f = (scopeSample_t **) phy_vars_ue->pbch_vars[eNB_id]->dl_ch_estimates;
  int ind = 0;
  float *freq, *chest_f_abs;
  oai_xygraph_getbuff(graph, &freq, &chest_f_abs, frame_parms->ofdm_symbol_size, 0);

  for (int atx=0; atx<nb_antennas_tx; atx++) {
    for (int arx=0; arx<nb_antennas_rx; arx++) {
      if (chest_f[(atx<<1)+arx] != NULL) {

        for (int k=0; k<frame_parms->ofdm_symbol_size; k++) {
          freq[ind] = (float)ind;
          chest_f_abs[ind] = (short)10*log10(1.0+SquaredNorm(chest_f[(atx<<1)+arx][6144+k]));
          ind++;
        }
      }
    }
  }

  // tx antenna 0
  //fl_set_xyplot_xbounds(form->chest_f,0,nb_antennas_rx*nb_antennas_tx*nsymb_ce);
  //fl_set_xyplot_xtics(form->chest_f,nb_antennas_rx*nb_antennas_tx*frame_parms->symbols_per_tti,2);
  //        fl_set_xyplot_xtics(form->chest_f,nb_antennas_rx*nb_antennas_tx*2,2);
  //fl_set_xyplot_xgrid(form->chest_f,FL_GRID_MAJOR);
  oai_xygraph(graph,freq,chest_f_abs,frame_parms->ofdm_symbol_size,0,10);
}
*/
static void uePbchLLR  (scopeGraphData_t **data, OAIgraph_t *graph, PHY_VARS_NR_UE *phy_vars_ue, int eNB_id, int UE_id) {
  enum scopeDataType typ = (phy_vars_ue->sl_mode) ? psbchLlr : pbchLlr;

  // PBCH LLRs
  if (!data[typ])
    return;

  const int sz = data[typ]->lineSz;
  // const int antennas=data[typ]->colSz;
  // We take the first antenna only for now
  int16_t *llrs = (int16_t *)(data[typ] + 1);
  float *llr_pbch=NULL, *bit_pbch=NULL;
  int nx = sz;
#ifdef WEBSRVSCOPE
  nx = websrv_cpllrbuff_tomsg(graph, llrs, sz, UE_id, 0, 0);
#else
  oai_xygraph_getbuff(graph, &bit_pbch, &llr_pbch, sz, 0);

  for (int i=0; i<sz; i++) {
    llr_pbch[i] = llrs[i];
  }
#endif
  oai_xygraph(graph, bit_pbch, llr_pbch, nx, 0, 10);
}

static void uePbchIQ  (scopeGraphData_t **data, OAIgraph_t *graph, PHY_VARS_NR_UE *phy_vars_ue, int eNB_id, int UE_id) {
  enum scopeDataType typ = (phy_vars_ue->sl_mode) ? psbchRxdataF_comp : pbchRxdataF_comp;

  // PBCH I/Q of MF Output
  if (!data[typ])
    return;

  scopeSample_t *pbch_comp = (scopeSample_t *)(data[typ] + 1);
  const int sz = data[typ]->lineSz;
  int newsz = sz;
  float *I=NULL, *Q=NULL;
#ifdef WEBSRVSCOPE
  newsz = websrv_cpiqbuff_tomsg(graph, pbch_comp, sz, 0, 0);
#else

  oai_xygraph_getbuff(graph, &I, &Q, sz, 0);

  for (int i=0; i<sz; i++) {
    I[i]=pbch_comp[i].r;
    Q[i]=pbch_comp[i].i;
  }
#endif
  oai_xygraph(graph, I, Q, newsz, 0, true);
}

static void uePcchLLR  (scopeGraphData_t **data, OAIgraph_t *graph, PHY_VARS_NR_UE *phy_vars_ue, int eNB_id, int UE_id) {
  // PDCCH LLRs
  if (!data[pdcchLlr])
    return;

  //int num_re = 4*273*12; // 12*frame_parms->N_RB_DL*num_pdcch_symbols
  //int Qm = 2;
  const int sz=data[pdcchLlr]->lineSz;
  float *llr=NULL, *bit=NULL;
  int nx = sz;
  int16_t *pdcch_llr = (int16_t *)(data[pdcchLlr]+1);

#ifdef WEBSRVSCOPE
  nx = websrv_cpllrbuff_tomsg(graph, pdcch_llr, sz, UE_id, 0, 0);
#else
  oai_xygraph_getbuff(graph, &bit, &llr, sz, 0);

  for (int i=0; i<sz; i++) {
    llr[i] = (float) pdcch_llr[i];
  }
#endif
  oai_xygraph(graph, bit, llr, nx, 0, 10);
}
static void uePcchIQ  (scopeGraphData_t **data, OAIgraph_t *graph, PHY_VARS_NR_UE *phy_vars_ue, int eNB_id, int UE_id) {
  // PDCCH I/Q of MF Output
  if (!data[pdcchRxdataF_comp])
    return;

  const int sz=data[pdcchRxdataF_comp]->lineSz;
  int newsz = sz;
  //const int antennas=data[pdcchRxdataF_comp]->colSz;
  // We take the first antenna only for now
  float *I=NULL, *Q=NULL;
  scopeSample_t *pdcch_comp = (scopeSample_t *) (data[pdcchRxdataF_comp]+1);
#ifdef WEBSRVSCOPE
  newsz = websrv_cpiqbuff_tomsg(graph, pdcch_comp, sz, 0, 0);
#else
  oai_xygraph_getbuff(graph, &I, &Q, sz, 0);

  for (int i=0; i<sz; i++) {
    I[i] = pdcch_comp[i].r;
    Q[i] = pdcch_comp[i].i;
  }
#endif
  oai_xygraph(graph, I, Q, newsz, 0, 10);
}
static void uePdschLLR  (scopeGraphData_t **data, OAIgraph_t *graph, PHY_VARS_NR_UE *phy_vars_ue, int eNB_id, int UE_id) {
  // PDSCH LLRs
  if (!data[pdschLlr])
    return;

  const int sz = data[pdschLlr]->lineSz;
  float *llr=NULL, *bit=NULL;
  int nx = sz;
  int16_t *pdsch_llr = (int16_t *)(data[pdschLlr]+1);

#ifdef WEBSRVSCOPE
  nx = websrv_cpllrbuff_tomsg(graph, pdsch_llr, sz, UE_id, 0, 0);
#else
  oai_xygraph_getbuff(graph, &bit, &llr, sz, 0);

  for (int i=0; i<sz; i++) {
    llr[i] = (float) pdsch_llr[i];
  }
#endif

  //fl_set_xyplot_xbounds(form->pdsch_llr,0,coded_bits_per_codeword);
  oai_xygraph(graph, bit, llr, nx, 0, 10);
}
static void uePdschIQ  (scopeGraphData_t **data, OAIgraph_t *graph, PHY_VARS_NR_UE *phy_vars_ue, int eNB_id, int UE_id) {
  // PDSCH I/Q of MF Output
  if (!data[pdschRxdataF_comp])
    return;

  const int sz=data[pdschRxdataF_comp]->lineSz;
  int nz = sz;
  float *I=NULL, *Q=NULL;
  scopeSample_t *pdsch_comp = (scopeSample_t *) (data[pdschRxdataF_comp]+1);
#ifdef WEBSRVSCOPE
  nz += websrv_cpiqbuff_tomsg(graph, pdsch_comp, sz, 0, 0);
#else
  oai_xygraph_getbuff(graph, &I, &Q, sz, 0);

  for (int s=0; s<sz; s++) {
    I[s] = pdsch_comp[s].r;
    Q[s] = pdsch_comp[s].i;
  }
#endif

  oai_xygraph(graph, I, Q, nz, 0, 10);
}
static void uePdschThroughput  (scopeGraphData_t **data, OAIgraph_t *graph, PHY_VARS_NR_UE *phy_vars_ue, int eNB_id, int UE_id) {
  /*
  float tput_time_ue[MAX_MOBILES_PER_GNB][TPUT_WINDOW_LENGTH] = {{0}};
  float tput_ue[MAX_MOBILES_PER_GNB][TPUT_WINDOW_LENGTH] = {{0}};
  float tput_ue_max[MAX_MOBILES_PER_GNB] = {0};


  // PDSCH Throughput
  memmove( tput_time_ue[UE_id], &tput_time_ue[UE_id][1], (TPUT_WINDOW_LENGTH-1)*sizeof(float) );
  memmove( tput_ue[UE_id],      &tput_ue[UE_id][1],      (TPUT_WINDOW_LENGTH-1)*sizeof(float) );

  tput_time_ue[UE_id][TPUT_WINDOW_LENGTH-1]  = (float) frame;
  tput_ue[UE_id][TPUT_WINDOW_LENGTH-1] = ((float) total_dlsch_bitrate)/1000.0;

  if (tput_ue[UE_id][TPUT_WINDOW_LENGTH-1] > tput_ue_max[UE_id]) {
  tput_ue_max[UE_id] = tput_ue[UE_id][TPUT_WINDOW_LENGTH-1];
  }

  fl_set_xyplot_data(form->pdsch_tput,tput_time_ue[UE_id],tput_ue[UE_id],TPUT_WINDOW_LENGTH,"","","");

  fl_set_xyplot_ybounds(form->pdsch_tput,0,tput_ue_max[UE_id]);
  */
}
STATICFORXSCOPE OAI_phy_scope_t *create_phy_scope_nrue(int ID)
{
  FL_OBJECT *obj;
  OAI_phy_scope_t *fdui = calloc(( sizeof *fdui ),1);
  // Define form
  fdui->phy_scope = fl_bgn_form( FL_NO_BOX, 800, 900 );
  fl_set_form_dblbuffer(fdui->phy_scope, 1);
  // This the whole UI box
  obj = fl_add_box( FL_BORDER_BOX, 0, 0, 800, 900, "" );
  fl_set_object_color( obj, FL_BLACK, FL_BLACK );
  int curY=0,x,y,w,h;
  // 接收信号
  fdui->graph[0] = nrUEcommonGraph(ueWaterFall,
                                   WATERFALL, 0, curY, 400, 100, "接收信号 (时域, 一帧)", FL_RED );
  fdui->graph[0].chartid = SCOPEMSG_DATAID_WF; // tells websrv frontend to use WF chart for displaying
  fdui->graph[0].datasetid = 0; //  not used for WF
  // 时域信道响应
  fdui->graph[1] = nrUEcommonGraph(ueChannelResponse,
                                   FL_NORMAL_XYPLOT, 400, curY, 400, 100, "信道冲激响应 (样本, 幅度)", FL_RED );
  fdui->graph[1].chartid = SCOPEMSG_DATAID_TRESP;
  fl_get_object_bbox(fdui->graph[1].graph,&x, &y,&w, &h);
  curY+=h;
  // 频域信道响应
  fdui->graph[2] = nrUEcommonGraph(ueFreqWaterFall,
                                   WATERFALL, 0, curY, 800, 100, "信道频域 (RE, 一时隙)", FL_RED );
  fl_get_object_bbox(fdui->graph[2].graph,&x, &y,&w, &h);
  curY+=h+20;
  fdui->graph[2].chartid = SCOPEMSG_DATAID_WF; // tells websrv frontend to use WF chart for displaying
  fdui->graph[2].datasetid = 0; //  not used for WF
  // PBCH的LLR
  fdui->graph[3] = nrUEcommonGraph(uePbchLLR,
                                   FL_POINTS_XYPLOT, 0, curY, 500, 100, "PBCH对数似然比 (LLR, 幅度)", FL_GREEN );
  fl_set_xyplot_xgrid(fdui->graph[3].graph,FL_GRID_MAJOR);
  fdui->graph[3].chartid = SCOPEMSG_DATAID_LLR; // tells websrv frontend to use LLR chart for displaying
  fdui->graph[3].datasetid = 0; // tells websrv frontend to use dataset index 0 in LLR chart
  // PBCH I/Q补偿
  fdui->graph[4] = nrUEcommonGraph(uePbchIQ,
                                   FL_POINTS_XYPLOT, 500, curY, 300, 100, "PBCH I/Q匹配滤波输出", FL_GREEN );
  fl_get_object_bbox(fdui->graph[3].graph,&x, &y,&w, &h);
  curY += h;
  fdui->graph[4].chartid = SCOPEMSG_DATAID_IQ; // tells websrv frontend to use constellation chart for displaying
  fdui->graph[4].datasetid = 0; // tells websrv frontend which dataset to use in the window
  // PDCCH的LLR
  fdui->graph[5] = nrUEcommonGraph(uePcchLLR,
                                   FL_POINTS_XYPLOT, 0, curY, 500, 100, "PDCCH对数似然比 (LLR, 幅度)", FL_CYAN );
  fl_set_xyplot_xgrid(fdui->graph[5].graph,FL_GRID_MAJOR);
  fdui->graph[5].chartid = SCOPEMSG_DATAID_LLR; // tells websrv frontend to use LLR chart for displaying
  fdui->graph[5].datasetid = 1; // tells websrv frontend to use dataset index 1 in LLR chart
  // PDCCH I/Q补偿
  fdui->graph[6] = nrUEcommonGraph(uePcchIQ,
                                   FL_POINTS_XYPLOT, 500, curY, 300, 100, "PDCCH I/Q匹配滤波输出", FL_CYAN );
  fl_get_object_bbox(fdui->graph[5].graph,&x, &y,&w, &h);
  curY+=h;
  fdui->graph[6].chartid = SCOPEMSG_DATAID_IQ; // tells websrv frontend to use constellation chart for displaying
  fdui->graph[6].datasetid = 1; // tells websrv frontend which dataset to use in the window
  // PDSCH的LLR
  fdui->graph[7] = nrUEcommonGraph(uePdschLLR,
                                   FL_POINTS_XYPLOT, 0, curY, 500, 200, "PDSCH对数似然比 (LLR, 幅度)", FL_YELLOW );
  fl_set_xyplot_xgrid(fdui->graph[7].graph,FL_GRID_MAJOR);
  fdui->graph[7].chartid = SCOPEMSG_DATAID_LLR; // tells websrv frontend to use LLR chart for displaying
  fdui->graph[7].datasetid = 2; // tells websrv frontend to use dataset index 2 in LLR chart
  // PDSCH I/Q补偿
  fdui->graph[8] = nrUEcommonGraph(uePdschIQ,
                                   FL_POINTS_XYPLOT, 500, curY, 300, 200, "PDSCH I/Q匹配滤波输出", FL_YELLOW );
  fl_get_object_bbox(fdui->graph[8].graph,&x, &y,&w, &h);
  curY+=h;
  fdui->graph[8].chartid = SCOPEMSG_DATAID_IQ; // tells websrv frontend to use constellation chart for displaying
  fdui->graph[8].datasetid = 2; // tells websrv frontend which dataset to use in the window
  // PDSCH吞吐量
  fdui->graph[9] = nrUEcommonGraph(uePdschThroughput,
                                   FL_NORMAL_XYPLOT, 0, curY, 500, 100, "PDSCH吞吐量 [帧]/[kbit/s]", FL_WHITE );
  fdui->graph[10].graph=NULL;
  // Generic UE Button
#if 0
  fdui->button_0 = fl_add_button( FL_PUSH_BUTTON, 540, 720, 240, 40, "" );
  fl_set_object_lalign(fdui->button_0, FL_ALIGN_CENTER );
  //openair_daq_vars.use_ia_receiver = 0;
  fl_set_button(fdui->button_0,0);
  fl_set_object_label(fdui->button_0, "IA Receiver OFF");
  fl_set_object_color(fdui->button_0, FL_RED, FL_RED);
  fl_set_object_callback(fdui->button_0, ia_receiver_on_off, 0 );
  fl_hide_object(fdui->button_0);
#endif
  fl_end_form( );
  if (fdui->phy_scope)
    fdui->phy_scope->fdui = fdui;
  char buf[100];
  sprintf(buf,"NR下行示波器 UE %d", ID);
  fl_show_form (fdui->phy_scope, FL_PLACE_HOTSPOT, FL_FULLBORDER, buf);
  return fdui;
}

/**
 * @brief NR UE示波器主更新函数
 * @param UEliveData UE实时数据数组指针
 * @param form 示波器界面结构指针
 * @param phy_vars_ue UE物理层变量指针
 * @param eNB_id eNB标识符
 * @param UE_id UE标识符
 *
 * 输入数据：来自UE PHY层的实时数据
 * 功能：更新所有启用的UE图表显示
 * 作用：协调所有UE图表的数据更新和显示刷新
 * 参数含义：
 * - UEliveData: UE实时数据数组，包含各种信号数据
 * - form: 界面结构，包含所有图表对象
 * - phy_vars_ue: UE物理层结构，包含接收处理结果
 * - eNB_id: 服务eNB的标识符
 * - UE_id: 当前UE的标识符
 * 可修改：可以添加图表选择性更新逻辑
 */
STATICFORXSCOPE void phy_scope_nrUE(scopeGraphData_t **UEliveData, OAI_phy_scope_t *form, PHY_VARS_NR_UE *phy_vars_ue, int eNB_id, int UE_id)
{
  static OAI_phy_scope_t *remeberForm=NULL;  // 静态变量保存界面指针

  // 界面指针管理：支持NULL参数调用
  if (form==NULL)
    form=remeberForm;  // 使用之前保存的界面
  else
    remeberForm=form;  // 保存当前界面指针

  if (form==NULL)
    return;  // 没有有效界面则退出

  int i=0;

  // 遍历所有图表并更新启用的图表
  while (form->graph[i].graph) {
    if (form->graph[i].enabled)  // 只更新启用的图表
      form->graph[i].nrUEfunct(UEliveData, form->graph + i, phy_vars_ue, eNB_id, UE_id);  // 调用UE图表更新函数
    i++;
  }

  //fl_check_forms();  // 可选：检查XForms事件（已注释）
}

#ifndef WEBSRVSCOPE
/**
 * @brief NR UE示波器线程函数（仅XForms模式）
 * @param arg UE物理层变量指针
 * @return 线程退出参数
 *
 * 输入数据：线程参数（UE物理层结构）
 * 功能：在独立线程中运行UE的XForms界面和数据更新循环
 * 作用：提供非阻塞的实时UE界面更新
 * 线程特性：
 * - 32MB栈空间（用于处理大量图形数据）
 * - 99ms更新间隔（约10Hz刷新率）
 * - 在oai_exit信号时退出
 * 可修改：可以调整更新频率和栈大小
 */
static void *nrUEscopeThread(void *arg) {
  PHY_VARS_NR_UE *ue=(PHY_VARS_NR_UE *)arg;  // 获取UE结构指针
  size_t stksize;
  pthread_attr_t atr= {0};
  pthread_attr_getstacksize(&atr, &stksize);
  pthread_attr_setstacksize(&atr,32*1024*1024 );  // 设置32MB栈空间

  // 初始化XForms库
  int fl_argc=1;
  char *name="5G-UE-scope";
  fl_initialize (&fl_argc, &name, NULL, 0, 0);
  OAI_phy_scope_t  *form_nrue=create_phy_scope_nrue(0);  // 创建UE界面

  // 主显示循环
  while (!oai_exit) {
    fl_freeze_form(form_nrue->phy_scope);    // 冻结界面更新
    phy_scope_nrUE((( scopeData_t *)ue->scopeData)->liveData,  // UE实时数据
                   form_nrue,                // 界面结构
                   ue,                       // UE物理层结构
                   0,0);                     // eNB_id和UE_id
    fl_unfreeze_form(form_nrue->phy_scope);  // 解冻界面更新
    fl_redraw_form(form_nrue->phy_scope);    // 重绘界面
    usleep(99*1000);  // 等待99ms（约10Hz更新频率）
  }

  pthread_exit((void *)arg);  // 线程退出
}
#endif

/**
 * @brief 初始化NR UE示波器系统
 * @param ue UE物理层变量指针
 *
 * 输入数据：UE物理层结构
 * 功能：初始化UE示波器的数据结构和显示线程
 * 作用：设置UE侧的实时信号监控系统
 * 参数含义：
 * - ue: UE物理层结构指针，包含接收处理变量
 * 初始化内容：
 * - 分配示波器数据结构内存
 * - 设置数据复制函数指针
 * - 启动显示线程（XForms模式）
 * - 初始化数据复制互斥锁
 * 可修改：可以添加更多初始化参数或配置选项
 */
STATICFORXSCOPE void nrUEinitScope(PHY_VARS_NR_UE *ue)
{
  // 分配示波器数据结构内存
  AssertFatal(ue->scopeData = calloc(sizeof(scopeData_t), 1), "");
  scopeData_t *scope=(scopeData_t *) ue->scopeData;
  scope->copyData = copyData;  // 设置数据复制函数

#ifndef WEBSRVSCOPE
  // XForms模式：创建显示线程
  pthread_t forms_thread;
  threadCreate(&forms_thread, nrUEscopeThread, ue, "scope", -1, OAI_PRIORITY_RT_LOW);
  copyDataMutexInit(scope);    // 初始化数据复制互斥锁
#endif
}

/**
 * @brief NR示波器自动初始化函数
 * @param dataptr 数据指针（gNB或UE结构）
 *
 * 输入数据：软调制解调器上下文指针
 * 功能：根据软调制解调器类型自动选择初始化gNB或UE示波器
 * 作用：提供统一的示波器初始化接口
 * 参数含义：
 * - dataptr: 指向gNB或UE数据结构的指针
 * 自动检测：
 * - IS_SOFTMODEM_GNB: 如果是gNB模式，调用gNBinitScope
 * - IS_SOFTMODEM_5GUE: 如果是UE模式，调用nrUEinitScope
 * 可修改：可以添加更多软调制解调器类型的支持
 */
void nrscope_autoinit(void *dataptr) {
  // 确保运行在支持的软调制解调器模式下
  AssertFatal((IS_SOFTMODEM_GNB || IS_SOFTMODEM_5GUE), "Scope cannot find NRUE or GNB context");

  if (IS_SOFTMODEM_GNB)
    gNBinitScope(dataptr);    // 初始化gNB示波器
  else
    nrUEinitScope(dataptr);   // 初始化UE示波器
}
// ============================================================================
// 预留的统计功能代码（已禁用，保留以备将来恢复）
// ============================================================================

/*
 * 以下代码段包含统计显示功能的实现，目前被条件编译禁用
 * 这些功能计划在将来的版本中重新启用
 *
 * 主要功能：
 * - gNB统计信息重置
 * - 统计显示窗体创建
 * - HARQ进程统计管理
 * - 错误率和吞吐量统计
 */
#if 0
//FD_stats_form                  *form_stats=NULL,*form_stats_l2=NULL;
//char                            title[255];
//static pthread_t                forms_thread; //xforms

/**
 * @brief gNB统计信息重置回调函数（已禁用）
 * @param button 按钮对象指针
 * @param arg 参数（未使用）
 *
 * 功能：重置gNB的各种统计计数器
 * 包括：DLSCH/ULSCH错误统计、HARQ进程统计等
 */
static void reset_stats_gNB(FL_OBJECT *button,
                            long arg) {
  int i,k;
  //PHY_VARS_gNB *phy_vars_gNB = RC.gNB[0][0];

  // 遍历所有UE的统计信息
  for (i=0; i<MAX_MOBILES_PER_GNB; i++) {
    for (k=0; k<8; k++) { //harq_processes
      /* 重置各种统计计数器（代码已注释）
      for (j=0; j<phy_vars_gNB->dlsch[i][0]->Mlimit; j++) {
              phy_vars_gNB->UE_stats[i].dlsch_NAK[k][j]=0;     // 下行NACK计数
              phy_vars_gNB->UE_stats[i].dlsch_ACK[k][j]=0;     // 下行ACK计数
              phy_vars_gNB->UE_stats[i].dlsch_trials[k][j]=0;  // 下行传输尝试计数
        }
        phy_vars_gNB->UE_stats[i].dlsch_l2_errors[k]=0;        // L2错误计数
        phy_vars_gNB->UE_stats[i].ulsch_errors[k]=0;           // 上行错误计数
        phy_vars_gNB->UE_stats[i].ulsch_consecutive_errors=0;  // 连续错误计数
        phy_vars_gNB->UE_stats[i].dlsch_sliding_cnt=0;         // 滑动窗口计数
        phy_vars_gNB->UE_stats[i].dlsch_NAK_round0=0;          // 首次传输NACK计数
        phy_vars_gNB->UE_stats[i].dlsch_mcs_offset=0;          // MCS偏移量
      */
    }
  }
}

/**
 * @brief 创建统计显示窗体（已禁用）
 * @param ID 窗体标识符
 * @return 创建的统计窗体结构指针
 *
 * 功能：创建用于显示PHY层统计信息的GUI窗体
 * 包含：文本浏览器、重置按钮等界面元素
 */
static FD_stats_form *create_form_stats_form(int ID) {
  FL_OBJECT *obj;
  FD_stats_form *fdui = calloc(( sizeof *fdui ),1);
  fdui->vdata = fdui->cdata = NULL;
  fdui->ldata = 0;

  // 创建主窗体（1115x900像素）
  fdui->stats_form = fl_bgn_form( FL_NO_BOX, 1115, 900 );
  obj = fl_add_box( FL_UP_BOX, 0, 0, 1115, 900, "" );

  // 创建统计信息显示区域（浏览器控件）
  //fdui->stats_text = obj = fl_add_text( FL_NORMAL_TEXT, 60, 50, 1000, 810, "test" );
  //fl_set_object_lsize( obj, FL_TINY_SIZE );
  fdui->stats_text = obj = fl_add_browser( FL_NORMAL_BROWSER, 60, 50, 1000, 810, "test" );
  fl_set_browser_fontsize(obj,FL_TINY_SIZE);  // 设置小字体

  // 创建重置统计按钮
  fdui->stats_button = obj = fl_add_button( FL_PUSH_BUTTON, 60, 10, 130, 30, "Reset Stats" );
  fl_set_object_lalign( obj, FL_ALIGN_CENTER );    // 居中对齐
  fl_set_object_color( obj, FL_GREEN, FL_GREEN);   // 绿色按钮

  fl_end_form( );
  fdui->stats_form->fdui = fdui;  // 设置反向引用
  return fdui;
}
#endif
